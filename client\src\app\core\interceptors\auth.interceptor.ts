import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { environment } from '../../../environments/environment';

export const authInterceptor: HttpInterceptorFn = (req, next) => {
  // AuthUser'dan token'ı al
  const currentUserStr = localStorage.getItem('currentUser');
  let token = null;

  if (currentUserStr) {
    try {
      const currentUser = JSON.parse(currentUserStr);
      token = currentUser.accessToken;
    } catch (e) {
      // Fallback: eski token key'i dene
      token = localStorage.getItem(environment.authTokenKey);
    }
  }

  if (token) {
    const cloned = req.clone({
      headers: req.headers.set('Authorization', `Bearer ${token}`)
    });
    return next(cloned);
  }

  return next(req);
};

﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Documents.Queries.GetAll
{
    public class GetAllDocumentsQueryHandler : IRequestHandler<GetAllDocumentsQuery, List<DocumentGetAllDto>>
    {
        private readonly IApplicationDbContext _context;

        public GetAllDocumentsQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<DocumentGetAllDto>> Handle(GetAllDocumentsQuery request, CancellationToken cancellationToken)
        {
            return await _context.Documents
                .AsNoTracking()
                .Select(d => new DocumentGetAllDto
                {
                    Id = d.Id,
                    FileName = d.FileName,
                    FilePath = d.FilePath,
                    FileSize = d.FileSize,
                    VersionNumber = d.VersionNumber,
                    Type = d.Type.ToString()
                })
                .ToListAsync(cancellationToken);
        }
    }
}

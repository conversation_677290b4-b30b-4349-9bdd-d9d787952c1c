﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using TRtek_EYS.Application.Features.Documents.Commands.Add;
using TRtek_EYS.Application.Features.Documents.Commands.Delete;
using TRtek_EYS.Application.Features.Documents.Commands.Update;
using TRtek_EYS.Application.Features.Documents.Queries.GetAll;
using TRtek_EYS.Application.Features.Documents.Queries.GetById;

namespace TRtek_EYS.WebAPI.Controllers;

[Route("api/[controller]")]
[ApiController]
public class DocumentsController : ApiController
{
    public DocumentsController(IMediator mediator) : base(mediator) { }

    [HttpGet]
    public async Task<IActionResult> GetAll()
    {
        var result = await Mediator.Send(new GetAllDocumentsQuery());
        return Ok(result); // List<DocumentGetAllDto>
    }

    [HttpGet("{id:guid}")]
    public async Task<IActionResult> GetById(Guid id)
    {
        var result = await Mediator.Send(new GetDocumentByIdQuery(id));
        return result is null ? NotFound() : Ok(result); // DocumentGetByIdDto
    }

    [HttpPost]
    public async Task<IActionResult> Add([FromBody] AddDocumentCommand command)
    {
        var id = await Mediator.Send(command);
        return CreatedAtAction(nameof(GetById), new { id }, null);
    }

    [HttpPut("{id:guid}")]
    public async Task<IActionResult> Update(Guid id, [FromBody] UpdateDocumentCommand command)
    {
        if (id != command.Id)
            return BadRequest("URL'deki ID uyuşmuyor.");

        await Mediator.Send(command);
        return NoContent();
    }

    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> Delete(Guid id)
    {
        await Mediator.Send(new DeleteDocumentCommand { Id = id });
        return NoContent();
    }
}

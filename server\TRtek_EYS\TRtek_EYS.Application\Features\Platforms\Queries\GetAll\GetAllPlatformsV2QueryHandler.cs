using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;
using System.Text.Json;

namespace TRtek_EYS.Application.Features.Platforms.Queries.GetAll
{
    public class GetAllPlatformsV2QueryHandler : IRequestHandler<GetAllPlatformsV2Query, List<PlatformGetAllDtoV2>>
    {
        private readonly IApplicationDbContext _context;

        public GetAllPlatformsV2QueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<PlatformGetAllDtoV2>> Handle(GetAllPlatformsV2Query request, CancellationToken cancellationToken)
        {
            var platforms = await _context.Platforms
                .AsNoTracking()
                .ToListAsync(cancellationToken);

            var result = platforms.Select(platform => new PlatformGetAllDtoV2
            {
                Id = platform.Id,
                Type = platform.Type,
                ProjectId = platform.ProjectId,
                
                // Ye<PERSON> alanlar
                Name = platform.Name,
                PlatformCode = platform.PlatformCode,
                Description = platform.Description,
                Category = platform.Category,
                Version = platform.Version,
                Vendor = platform.Vendor,
                Website = platform.Website,
                Documentation = platform.Documentation,
                SupportLevel = platform.SupportLevel,
                LicenseType = platform.LicenseType,
                Cost = platform.Cost,
                Currency = platform.Currency,
                IsActive = platform.IsActive,
                IsSupported = platform.IsSupported,
                ReleaseDate = platform.ReleaseDate,
                EndOfLifeDate = platform.EndOfLifeDate,
                Tags = ParseTags(platform.Tags),
                Notes = platform.Notes,
                ProjectCount = platform.ProjectCount,
                CreatedAt = platform.CreatedAt,
                UpdatedAt = platform.UpdatedAt
            }).ToList();

            return result;
        }

        private string[]? ParseTags(string? tagsJson)
        {
            if (string.IsNullOrEmpty(tagsJson))
                return null;

            try
            {
                return JsonSerializer.Deserialize<string[]>(tagsJson);
            }
            catch
            {
                return null;
            }
        }
    }
}

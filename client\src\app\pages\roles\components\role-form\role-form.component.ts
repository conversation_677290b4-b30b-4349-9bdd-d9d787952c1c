import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { Textarea } from 'primeng/inputtextarea';
import { CheckboxModule } from 'primeng/checkbox';
import { ToastModule } from 'primeng/toast';
import { CardModule } from 'primeng/card';
import { MessageService } from 'primeng/api';
import { Permission, PermissionCategory } from '../../models/role.model';
import { RoleService } from '../../services/role.service';
import { TooltipModule } from 'primeng/tooltip';

@Component({
    selector: 'app-role-form',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        ButtonModule,
        InputTextModule,
        Textarea,
        CheckboxModule,
        ToastModule,
        CardModule,
        TooltipModule
    ],
    providers: [MessageService],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <h5>{{ isEditMode ? 'Rol Düzenle' : 'Yeni Rol' }}</h5>
                        <p-button 
                            label="Geri" 
                            icon="pi pi-arrow-left" 
                            class="p-button-secondary"
                            (onClick)="goBack()">
                        </p-button>
                    </div>

                    <form [formGroup]="roleForm" (ngSubmit)="onSubmit()">
                        <div class="grid">
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="roleCode" class="font-medium">Rol Kodu *</label>
                                    <input 
                                        id="roleCode"
                                        type="text" 
                                        pInputText 
                                        formControlName="roleCode"
                                        [readonly]="isEditMode"
                                        class="w-full"
                                        placeholder="Otomatik oluşturulacak" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="name" class="font-medium">Rol Adı *</label>
                                    <input 
                                        id="name"
                                        type="text" 
                                        pInputText 
                                        formControlName="name"
                                        class="w-full"
                                        placeholder="Rol adı" />
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field">
                                    <label for="description" class="font-medium">Açıklama *</label>
                                    <textarea 
                                        id="description"
                                        pInputTextarea 
                                        formControlName="description"
                                        rows="3"
                                        class="w-full"
                                        placeholder="Rol açıklaması...">
                                    </textarea>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field">
                                    <label class="font-medium">Yetkiler</label>
                                    <div class="mt-2">
                                        <div *ngFor="let category of permissionCategories" class="mb-4">
                                            <p-card>
                                                <ng-template pTemplate="header">
                                                    <div class="flex align-items-center p-3">
                                                        <h6 class="m-0 text-primary">{{ category.name }}</h6>
                                                    </div>
                                                </ng-template>
                                                <div class="grid">
                                                    <div *ngFor="let permission of category.permissions" class="col-12 md:col-6 lg:col-4">
                                                        <div class="field-checkbox flex align-items-start gap-3">
                                                            <div class="flex-grow-1">
                                                                <div class="font-medium">{{ permission.name }}</div>
                                                                <div class="text-sm text-500">{{ permission.description }}</div>
                                                                <div class="flex gap-1 mt-1">
                                                                    <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 border-round">{{ permission.module }}</span>
                                                                    <span class="text-xs bg-green-100 text-green-800 px-2 py-1 border-round">{{ permission.action }}</span>
                                                                </div>
                                                            </div>
                                                            <p-checkbox
                                                                [inputId]="permission.id"
                                                                [value]="permission.id"
                                                                formControlName="permissions">
                                                            </p-checkbox>
                                                        </div>
                                                    </div>
                                                </div>
                                                <ng-template pTemplate="footer">
                                                    <div class="flex justify-content-end gap-2 p-3">
                                                        <p-button
                                                            label="Tümünü Seç"
                                                            icon="pi pi-check"
                                                            class="p-button-text p-button-sm"
                                                            (onClick)="selectAllInCategory(category.name)">
                                                        </p-button>
                                                        <p-button
                                                            label="Tümünü Kaldır"
                                                            icon="pi pi-times"
                                                            class="p-button-text p-button-sm"
                                                            (onClick)="deselectAllInCategory(category.name)">
                                                        </p-button>
                                                    </div>
                                                </ng-template>
                                            </p-card>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field">
                                    <label for="notes" class="font-medium">Notlar</label>
                                    <textarea
                                        id="notes"
                                        pTextarea
                                        formControlName="notes"
                                        rows="3"
                                        class="w-full"
                                        placeholder="Rol hakkında notlar...">
                                    </textarea>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field-checkbox">
                                    <p-checkbox 
                                        inputId="isActive"
                                        formControlName="isActive">
                                    </p-checkbox>
                                    <label for="isActive" class="ml-2 font-medium">Aktif</label>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-content-end gap-2 mt-4">
                            <p-button 
                                icon="pi pi-times"
                                severity="secondary"
                                rounded
                                outlined
                                type="button"
                                pTooltip="İptal"
                                (onClick)="goBack()">
                            </p-button>
                            <p-button 
                                icon="pi pi-check"
                                severity="success"
                                rounded
                                outlined
                                type="submit"
                                [pTooltip]="isEditMode ? 'Güncelle' : 'Kaydet'"
                                [disabled]="roleForm.invalid">
                            </p-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <p-toast></p-toast>
    `
})
export class RoleFormComponent implements OnInit {
    roleForm!: FormGroup;
    isEditMode = false;
    roleId: string = '';
    permissionCategories: { name: string; permissions: Permission[] }[] = [];

    constructor(
        private fb: FormBuilder,
        private route: ActivatedRoute,
        private router: Router,
        private roleService: RoleService,
        private messageService: MessageService
    ) {}

    ngOnInit() {
        this.initForm();
        this.loadPermissions();
        this.checkEditMode();
    }

    initForm() {
        this.roleForm = this.fb.group({
            roleCode: ['', Validators.required],
            name: ['', Validators.required],
            description: ['', Validators.required],
            permissions: [[]],
            isActive: [true],
            notes: ['']
        });

        if (!this.isEditMode) {
            this.generateRoleCode();
        }
    }

    loadPermissions() {
        this.roleService.getPermissionsByCategory().subscribe(categorized => {
            this.permissionCategories = Object.keys(categorized).map(categoryName => ({
                name: categoryName,
                permissions: categorized[categoryName]
            }));
        });
    }

    checkEditMode() {
        this.roleId = this.route.snapshot.params['id'];
        this.isEditMode = !!this.roleId && this.router.url.includes('edit');
        
        if (this.isEditMode) {
            this.loadRole();
        }
    }

    generateRoleCode() {
        const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        const roleCode = `ROLE_${randomNum}`;
        this.roleForm.patchValue({ roleCode });
    }

    loadRole() {
        this.roleService.getRoleById(this.roleId).subscribe(role => {
            if (role) {
                this.roleForm.patchValue({
                    roleCode: role.roleCode,
                    name: role.name,
                    description: role.description,
                    permissions: role.permissions.map(p => p.id),
                    isActive: role.isActive,
                    notes: role.notes
                });
            }
        });
    }

    selectAllInCategory(categoryName: string) {
        const category = this.permissionCategories.find(c => c.name === categoryName);
        if (category) {
            const currentPermissions = this.roleForm.get('permissions')?.value || [];
            const categoryPermissionIds = category.permissions.map(p => p.id);
            const newPermissions = [...new Set([...currentPermissions, ...categoryPermissionIds])];
            this.roleForm.patchValue({ permissions: newPermissions });
        }
    }

    deselectAllInCategory(categoryName: string) {
        const category = this.permissionCategories.find(c => c.name === categoryName);
        if (category) {
            const currentPermissions = this.roleForm.get('permissions')?.value || [];
            const categoryPermissionIds = category.permissions.map(p => p.id);
            const newPermissions = currentPermissions.filter((id: string) => !categoryPermissionIds.includes(id));
            this.roleForm.patchValue({ permissions: newPermissions });
        }
    }

    onSubmit() {
        if (this.roleForm.valid) {
            const formData = this.roleForm.value;
            
            if (this.isEditMode) {
                this.roleService.updateRole(this.roleId, formData).subscribe((result) => {
                    if (result) {
                        this.messageService.add({
                            severity: 'success',
                            summary: 'Başarılı',
                            detail: 'Rol başarıyla güncellendi'
                        });
                        setTimeout(() => {
                            this.router.navigate(['/pages/roles']);
                        }, 1500);
                    } else {
                        this.messageService.add({
                            severity: 'error',
                            summary: 'Hata',
                            detail: 'Sistem rolleri güncellenemez'
                        });
                    }
                });
            } else {
                this.roleService.createRole(formData).subscribe(() => {
                    this.messageService.add({
                        severity: 'success',
                        summary: 'Başarılı',
                        detail: 'Rol başarıyla oluşturuldu'
                    });
                    setTimeout(() => {
                        this.router.navigate(['/pages/roles']);
                    }, 1500);
                });
            }
        }
    }

    goBack() {
        this.router.navigate(['/pages/roles']);
    }
}

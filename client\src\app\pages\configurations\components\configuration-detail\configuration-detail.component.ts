import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { TagModule } from 'primeng/tag';

@Component({
    selector: 'app-configuration-detail',
    standalone: true,
    imports: [
        CommonModule,
        ButtonModule,
        CardModule,
        TagModule
    ],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <div>
                            <h5 class="m-0">Konfigürasyon Detayı</h5>
                            <p class="text-500 mt-1">CFG-2025-001</p>
                        </div>
                        <div class="flex gap-2">
                            <p-button 
                                label="Düzenle" 
                                icon="pi pi-pencil" 
                                class="p-button-warning"
                                (onClick)="editConfiguration()">
                            </p-button>
                            <p-button 
                                label="Geri" 
                                icon="pi pi-arrow-left" 
                                class="p-button-secondary"
                                (onClick)="goBack()">
                            </p-button>
                        </div>
                    </div>

                    <div class="grid">
                        <div class="col-12 md:col-6">
                            <p-card header="Temel Bilgiler">
                                <div class="field">
                                    <label class="font-medium">Konfigürasyon Adı:</label>
                                    <div class="mt-1">Üretim Ortamı Konfigürasyonu</div>
                                </div>
                                <div class="field">
                                    <label class="font-medium">Müşteri:</label>
                                    <div class="mt-1">ABC Teknoloji A.Ş.</div>
                                </div>
                                <div class="field">
                                    <label class="font-medium">Proje:</label>
                                    <div class="mt-1">E-Ticaret Platformu</div>
                                </div>
                                <div class="field">
                                    <label class="font-medium">Ortam:</label>
                                    <div class="mt-1">
                                        <p-tag value="Üretim" severity="danger"></p-tag>
                                    </div>
                                </div>
                                <div class="field">
                                    <label class="font-medium">Durum:</label>
                                    <div class="mt-1">
                                        <p-tag value="Aktif" severity="success"></p-tag>
                                    </div>
                                </div>
                            </p-card>
                        </div>
                        <div class="col-12 md:col-6">
                            <p-card header="Dağıtım Bilgileri">
                                <div class="field">
                                    <label class="font-medium">Server URL:</label>
                                    <div class="mt-1">https://api.abc.com</div>
                                </div>
                                <div class="field">
                                    <label class="font-medium">Veritabanı:</label>
                                    <div class="mt-1">prod-db.abc.com</div>
                                </div>
                                <div class="field">
                                    <label class="font-medium">Son Dağıtım:</label>
                                    <div class="mt-1">10/01/2025</div>
                                </div>
                                <div class="field">
                                    <label class="font-medium">Dağıtım Durumu:</label>
                                    <div class="mt-1">
                                        <p-tag value="Dağıtıldı" severity="success"></p-tag>
                                    </div>
                                </div>
                            </p-card>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `
})
export class ConfigurationDetailComponent implements OnInit {
    configurationId: string = '';

    constructor(
        private route: ActivatedRoute,
        private router: Router
    ) {}

    ngOnInit() {
        this.configurationId = this.route.snapshot.params['id'];
    }

    editConfiguration() {
        this.router.navigate(['/pages/configurations', this.configurationId, 'edit']);
    }

    goBack() {
        this.router.navigate(['/pages/configurations']);
    }
}

import { Routes } from '@angular/router';

export default [
    {
        path: 'login',
        loadComponent: () => import('./components/login/login.component').then(m => m.LoginComponent)
    },
    {
        path: 'register',
        loadComponent: () => import('./components/register/register.component').then(m => m.RegisterComponent)
    },
    {
        path: 'access-denied',
        loadComponent: () => import('./components/access-denied/access-denied.component').then(m => m.AccessDeniedComponent)
    },
    // {
    //     path: 'profile',
    //     loadComponent: () => import('./components/profile/profile.component').then(m => m.ProfileComponent)
    // }
] as Routes;

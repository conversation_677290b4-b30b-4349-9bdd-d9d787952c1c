﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using TRtek_EYS.Application.Features.Teams.Commands.Add;
using TRtek_EYS.Application.Features.Teams.Commands.Delete;
using TRtek_EYS.Application.Features.Teams.Commands.Update;
using TRtek_EYS.Application.Features.Teams.Queries.GetAll;
using TRtek_EYS.Application.Features.Teams.Queries.GetById;

namespace TRtek_EYS.WebAPI.Controllers;

[Route("api/[controller]")]
[ApiController]
public class TeamsController : ApiController
{
    public TeamsController(IMediator mediator) : base(mediator) { }

    [HttpGet]
    public async Task<IActionResult> GetAll()
    {
        var result = await Mediator.Send(new GetAllTeamsQuery());
        return Ok(result); 
    }

    [HttpGet("{id:guid}")]
    public async Task<IActionResult> GetById(Guid id)
    {
        var result = await Mediator.Send(new GetTeamByIdQuery(id));
        return result is null ? NotFound() : Ok(result); // TeamGetByIdDto
    }

    [HttpPost]
    public async Task<IActionResult> Add([FromBody] AddTeamCommand command)
    {
        var id = await Mediator.Send(command);
        return CreatedAtAction(nameof(GetById), new { id }, null);
    }

    [HttpPut("{id:guid}")]
    public async Task<IActionResult> Update(Guid id, [FromBody] UpdateTeamCommand command)
    {
        if (id != command.Id)
            return BadRequest("ID uyuşmazlığı.");

        await Mediator.Send(command);
        return NoContent();
    }

    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> Delete(Guid id)
    {
        await Mediator.Send(new DeleteTeamCommand { Id = id });
        return NoContent();
    }
}

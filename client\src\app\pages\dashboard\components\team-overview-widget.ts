import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject } from 'rxjs';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { AvatarModule } from 'primeng/avatar';
import { AvatarGroupModule } from 'primeng/avatargroup';
import { TooltipModule } from 'primeng/tooltip';
import { SkeletonModule } from 'primeng/skeleton';

interface TeamMember {
    name: string;
    role: string;
}

interface Team {
    name: string;
    members: TeamMember[];
    activeProjects: number;
    completedProjects: number;
    status: string;
}

@Component({
    standalone: true,
    selector: 'app-team-overview-widget',
    imports: [CommonModule, TableModule, TagModule, AvatarModule, AvatarGroupModule, TooltipModule, SkeletonModule],
    template: `<div class="card !mb-8">
        <div class="flex justify-between align-items-center mb-5">
            <div class="font-semibold text-xl"><PERSON>kım Genel Bakış</div>
        </div>

        <div *ngIf="isLoading" class="space-y-4">
            <div *ngFor="let item of [1,2,3,4]">
                <p-skeleton height="3rem"></p-skeleton>
            </div>
        </div>

        <p-table
            *ngIf="!isLoading && teams.length > 0"
            [value]="teams"
            [rows]="5"
            [paginator]="true"
            responsiveLayout="scroll">
            <ng-template pTemplate="header">
                <tr>
                    <th>Takım</th>
                    <th>Üyeler</th>
                    <th>Aktif Projeler</th>
                    <th>Tamamlanan</th>
                    <th>Durum</th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-team>
                <tr>
                    <td style="min-width: 10rem;">
                        <span class="font-medium">{{ team.name }}</span>
                    </td>
                    <td style="min-width: 12rem;">
                        <p-avatarGroup>
                            <p-avatar *ngFor="let member of team.members" 
                                [label]="member.name.charAt(0)"
                                shape="circle"
                                size="normal"
                                [pTooltip]="member.name + ' - ' + member.role"></p-avatar>
                        </p-avatarGroup>
                    </td>
                    <td style="min-width: 8rem;">
                        <span class="text-primary font-medium">{{ team.activeProjects }}</span>
                    </td>
                    <td style="min-width: 8rem;">{{ team.completedProjects }}</td>
                    <td style="min-width: 8rem;">
                        <p-tag [severity]="getStatusSeverity(team.status)" [value]="team.status"></p-tag>
                    </td>
                </tr>
            </ng-template>
        </p-table>

        <div *ngIf="!isLoading && teams.length === 0" class="text-center py-8">
            <i class="pi pi-info-circle text-4xl text-muted-color mb-4"></i>
            <div class="text-muted-color font-medium">Henüz takım bilgisi bulunmuyor</div>
        </div>
    </div>`,
    styles: [`
        .space-y-4 > * + * {
            margin-top: 1rem;
        }
    `]
})
export class TeamOverviewWidget implements OnInit, OnDestroy {
    private destroy$ = new Subject<void>();

    teams: Team[] = [];
    isLoading = true;

    constructor() {}

    ngOnInit() {
        this.loadTeams();
    }

    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
    }

    loadTeams() {
        this.isLoading = true;

        // Simulated team data - gerçek API entegrasyonu için hazır
        setTimeout(() => {
            this.teams = [
                {
                    name: 'Frontend Takımı',
                    members: [
                        { name: 'Ahmet Yılmaz', role: 'Team Lead' },
                        { name: 'Fatma Demir', role: 'Senior Developer' },
                        { name: 'Mehmet Kaya', role: 'Developer' }
                    ],
                    activeProjects: 3,
                    completedProjects: 12,
                    status: 'Aktif'
                },
                {
                    name: 'Backend Takımı',
                    members: [
                        { name: 'Ayşe Özkan', role: 'Team Lead' },
                        { name: 'Can Arslan', role: 'Senior Developer' },
                        { name: 'Zeynep Şahin', role: 'Developer' }
                    ],
                    activeProjects: 4,
                    completedProjects: 18,
                    status: 'Aktif'
                },
                {
                    name: 'DevOps Takımı',
                    members: [
                        { name: 'Emre Çelik', role: 'Team Lead' },
                        { name: 'Selin Aydın', role: 'DevOps Engineer' }
                    ],
                    activeProjects: 2,
                    completedProjects: 8,
                    status: 'Aktif'
                },
                {
                    name: 'QA Takımı',
                    members: [
                        { name: 'Burak Yıldız', role: 'QA Lead' },
                        { name: 'Deniz Kara', role: 'Test Engineer' }
                    ],
                    activeProjects: 5,
                    completedProjects: 15,
                    status: 'Aktif'
                }
            ];
            this.isLoading = false;
        }, 1000);

        // Gerçek API entegrasyonu için:
        /*
        forkJoin({
            users: this.userService.getUsers(),
            roles: this.roleService.getRoles()
        }).pipe(
            takeUntil(this.destroy$)
        ).subscribe(({ users, roles }) => {
            // Rollere göre takımları grupla
            const teamMap = new Map<string, Team>();

            roles.filter(r => r.isActive).forEach(role => {
                const teamUsers = users.filter(u =>
                    u.roles.some(ur => ur.name === role.name) && u.isActive
                );

                if (teamUsers.length > 0) {
                    teamMap.set(role.name, {
                        name: role.name,
                        members: teamUsers.map(u => ({
                            name: `${u.firstName} ${u.lastName}`,
                            role: role.name
                        })),
                        activeProjects: Math.floor(Math.random() * 5) + 1,
                        completedProjects: Math.floor(Math.random() * 20) + 5,
                        status: 'Aktif'
                    });
                }
            });

            this.teams = Array.from(teamMap.values()).slice(0, 5);
            this.isLoading = false;
        });
        */
    }

    getStatusSeverity(status: string): string {
        switch (status) {
            case 'Aktif':
                return 'success';
            case 'İzinli':
                return 'warning';
            case 'Pasif':
                return 'danger';
            default:
                return 'info';
        }
    }
}

import { Component, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MenuItem } from 'primeng/api';
import { MenuModule } from 'primeng/menu';
import { StyleClassModule } from 'primeng/styleclass';
import { AppConfigurator } from './app.configurator';
import { LayoutService } from '../service/layout.service';

@Component({
    selector: 'app-topbar-custom',
    standalone: true,
    imports: [RouterModule, CommonModule, StyleClassModule, AppConfigurator, MenuModule],
    template: ` <div class="layout-topbar">
        <div class="layout-topbar-logo-container">
            <button class="layout-menu-button layout-topbar-action" (click)="layoutService.onMenuToggle()">
                <i class="pi pi-bars"></i>
            </button>
            <a class="layout-topbar-logo" routerLink="/">
                <img src="assets/logo-b.png" alt="TRtek Logo" class="logo-image">
                <span>TRtek EYS</span>
            </a>
        </div>

        <div class="layout-topbar-actions">
            <div class="layout-config-menu">
                <button type="button" class="layout-topbar-action" (click)="toggleDarkMode()">
                    <i [ngClass]="{ 'pi ': true, 'pi-moon': layoutService.isDarkTheme(), 'pi-sun': !layoutService.isDarkTheme() }"></i>
                </button>
                <div class="relative">
                    <button
                        class="layout-topbar-action layout-topbar-action-highlight"
                        pStyleClass="@next"
                        enterFromClass="hidden"
                        enterActiveClass="animate-scalein"
                        leaveToClass="hidden"
                        leaveActiveClass="animate-fadeout"
                        [hideOnOutsideClick]="true"
                    >
                        <i class="pi pi-palette"></i>
                    </button>
                    <app-configurator />
                </div>
            </div>

            <button class="layout-topbar-menu-button layout-topbar-action" pStyleClass="@next" enterFromClass="hidden" enterActiveClass="animate-scalein" leaveToClass="hidden" leaveActiveClass="animate-fadeout" [hideOnOutsideClick]="true">
                <i class="pi pi-ellipsis-v"></i>
            </button>

            <div class="layout-topbar-menu hidden lg:block">
                <div class="layout-topbar-menu-content">
                    <!-- <button type="button" class="layout-topbar-action">
                        <i class="pi pi-calendar"></i>
                        <span>Calendar</span>
                    </button>
                    <button type="button" class="layout-topbar-action">
                        <i class="pi pi-inbox"></i>
                        <span>Messages</span>
                    </button> -->
                    <button type="button" class="layout-topbar-action" (click)="userMenu.toggle($event)">
                        <i class="pi pi-user"></i>
                        <span>{{ currentUserName }}</span>
                    </button>
                    <p-menu #userMenu [popup]="true" [model]="userMenuItems" appendTo="body"></p-menu>
                </div>
            </div>
        </div>
    </div>`,
    styles: [`
        .layout-topbar-logo {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: var(--text-color);
        }

        .logo-image {
            height: 2.5rem;
            width: auto;
            margin-right: 0.75rem;
            object-fit: contain;
        }

        .layout-topbar-logo span {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-color);
        }

        @media (max-width: 768px) {
            .logo-image {
                height: 2rem;
                margin-right: 0.5rem;
            }

            .layout-topbar-logo span {
                font-size: 1.25rem;
            }
        }
    `]
})
export class AppTopbarCustom implements OnInit {
    userMenuItems: MenuItem[] = [];

    constructor(
        public layoutService: LayoutService
    ) {}

    ngOnInit() {
        this.initializeUserMenu();
    }

    private initializeUserMenu() {
        // Dummy data - tasarım için
        this.userMenuItems = [
            {
                label: 'Super Admin',
                items: [
                    {
                        label: 'Profil',
                        icon: 'pi pi-user',
                        command: () => this.goToProfile()
                    },
                    {
                        separator: true
                    },
                    {
                        label: 'Ayarlar',
                        icon: 'pi pi-cog',
                        command: () => this.goToSettings()
                    },
                    {
                        separator: true
                    },
                    {
                        label: 'Çıkış Yap',
                        icon: 'pi pi-power-off',
                        command: () => this.logout()
                    }
                ]
            }
        ];
    }

    get currentUserName(): string {
        // Dummy data - tasarım için
        return 'Super Admin';
    }

    toggleDarkMode() {
        this.layoutService.layoutConfig.update((state) => ({ ...state, darkTheme: !state.darkTheme }));
    }

    goToProfile() {
        console.log('Profil sayfasına git');
        // this.router.navigate(['/profile']);
    }

    goToSettings() {
        console.log('Ayarlar sayfasına git');
        // this.router.navigate(['/settings']);
    }

    logout() {
        console.log('Çıkış yap');
        // localStorage.clear();
        // this.router.navigate(['/auth/login']);
    }
}

import { Component, OnInit } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MenuItem } from 'primeng/api';
import { MenuModule } from 'primeng/menu';
import { AvatarModule } from 'primeng/avatar';
import { StyleClassModule } from 'primeng/styleclass';
import { AppConfigurator } from './app.configurator';
import { LayoutService } from '../service/layout.service';
import { AuthService } from '../../core/services/auth.service';

@Component({
    selector: 'app-topbar-custom',
    standalone: true,
    imports: [RouterModule, CommonModule, StyleClassModule, AppConfigurator, MenuModule, AvatarModule],
    template: ` <div class="layout-topbar">
        <div class="layout-topbar-logo-container">
            <button class="layout-menu-button layout-topbar-action" (click)="layoutService.onMenuToggle()">
                <i class="pi pi-bars"></i>
            </button>
            <a class="layout-topbar-logo" routerLink="/">
                <img src="assets/logo-b.png" alt="TRtek Logo" class="logo-image">
                <span>TRtek EYS</span>
            </a>
        </div>

        <div class="layout-topbar-actions">
            <div class="layout-config-menu">
                <button type="button" class="layout-topbar-action" (click)="toggleDarkMode()">
                    <i [ngClass]="{ 'pi ': true, 'pi-moon': layoutService.isDarkTheme(), 'pi-sun': !layoutService.isDarkTheme() }"></i>
                </button>
                <div class="relative">
                    <button
                        class="layout-topbar-action layout-topbar-action-highlight"
                        pStyleClass="@next"
                        enterFromClass="hidden"
                        enterActiveClass="animate-scalein"
                        leaveToClass="hidden"
                        leaveActiveClass="animate-fadeout"
                        [hideOnOutsideClick]="true"
                    >
                        <i class="pi pi-palette"></i>
                    </button>
                    <app-configurator />
                </div>
            </div>

            <button class="layout-topbar-menu-button layout-topbar-action" pStyleClass="@next" enterFromClass="hidden" enterActiveClass="animate-scalein" leaveToClass="hidden" leaveActiveClass="animate-fadeout" [hideOnOutsideClick]="true">
                <i class="pi pi-ellipsis-v"></i>
            </button>

            <div class="layout-topbar-menu hidden lg:block">
                <div class="layout-topbar-menu-content">
                    <!-- <button type="button" class="layout-topbar-action">
                        <i class="pi pi-calendar"></i>
                        <span>Calendar</span>
                    </button>
                    <button type="button" class="layout-topbar-action">
                        <i class="pi pi-inbox"></i>
                        <span>Messages</span>
                    </button> -->
                    <button type="button" class="layout-topbar-action user-button" (click)="userMenu.toggle($event)">
                        <div class="user-info" *ngIf="currentUser; else loginButton">
                            <p-avatar
                                [label]="getUserInitials()"
                                styleClass="mr-2"
                                size="normal"
                                [style]="{'background-color': '#B71C1C', 'color': 'white'}">
                            </p-avatar>
                            <span class="user-name hidden-mobile">{{ currentUserName }}</span>
                            <i class="pi pi-chevron-down ml-2"></i>
                        </div>
                        <ng-template #loginButton>
                            <i class="pi pi-user mr-2"></i>
                            <span>Giriş Yap</span>
                        </ng-template>
                    </button>
                    <p-menu #userMenu [popup]="true" [model]="userMenuItems" appendTo="body"></p-menu>
                </div>
            </div>
        </div>
    </div>`,
    styles: [`
        .layout-topbar-logo {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: var(--text-color);
        }

        .logo-image {
            height: 2.5rem;
            width: auto;
            margin-right: 0.75rem;
            object-fit: contain;
        }

        .layout-topbar-logo span {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-color);
        }

        .layout-topbar-action {
            &.user-button {
                .user-info {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;

                    .user-name {
                        font-weight: 500;
                        color: var(--text-color);
                        white-space: nowrap;
                        max-width: 150px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    .pi-chevron-down {
                        font-size: 0.875rem;
                        color: var(--text-color-secondary);
                    }
                }
            }
        }

        @media (max-width: 768px) {
            .logo-image {
                height: 2rem;
                margin-right: 0.5rem;
            }

            .layout-topbar-logo span {
                font-size: 1.25rem;
            }

            .hidden-mobile {
                display: none !important;
            }
        }
    `]
})
export class AppTopbarCustom implements OnInit {
    userMenuItems: MenuItem[] = [];
    currentUser: any = null;

    constructor(
        public layoutService: LayoutService,
        private authService: AuthService,
        private router: Router
    ) {}

    ngOnInit() {
        // Auth service'den kullanıcı bilgilerini al
        this.authService.currentUser$.subscribe(user => {
            this.currentUser = user?.user || null;
            this.initializeUserMenu();
        });
    }

    private initializeUserMenu() {
        if (this.currentUser) {
            this.userMenuItems = [
                {
                    label: this.currentUserName,
                    items: [
                        {
                            label: 'Profil',
                            icon: 'pi pi-user',
                            command: () => this.goToProfile()
                        },
                        {
                            label: 'Ayarlar',
                            icon: 'pi pi-cog',
                            command: () => this.goToSettings()
                        },
                        {   
                            separator: true
                        },
                        {
                            label: 'Çıkış Yap',
                            icon: 'pi pi-power-off',
                            command: () => this.logout()
                        }
                    ]
                }
            ];
        } else {
            this.userMenuItems = [
                {
                    label: 'Giriş Yap',
                    icon: 'pi pi-sign-in',
                    command: () => this.router.navigate(['/auth/login'])
                },
                {
                    label: 'Kayıt Ol',
                    icon: 'pi pi-user-plus',
                    command: () => this.router.navigate(['/auth/register'])
                }
            ];
        }
    }

    get currentUserName(): string {
        return this.currentUser ? `${this.currentUser.firstName} ${this.currentUser.lastName}` : 'Kullanıcı';
    }

    getUserInitials(): string {
        if (!this.currentUser) return 'U';

        const firstName = this.currentUser.firstName || '';
        const lastName = this.currentUser.lastName || '';

        return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
    }

    toggleDarkMode() {
        this.layoutService.layoutConfig.update((state) => ({ ...state, darkTheme: !state.darkTheme }));
    }

    goToProfile() {
        console.log('Profil sayfasına git');
        // this.router.navigate(['/profile']);
    }

    goToSettings() {
        console.log('Ayarlar sayfasına git');
        // this.router.navigate(['/settings']);
    }

    logout() {
        this.authService.logout();
        this.router.navigate(['/auth/login']);
    }
}

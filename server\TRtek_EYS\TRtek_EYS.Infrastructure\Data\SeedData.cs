using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Domain.Entities;
using TRtek_EYS.Domain.Enums;
using TRtek_EYS.Infrastructure.Persistence;
using System.Text.Json;

namespace TRtek_EYS.Infrastructure.Data;

public static class SeedData
{
    public static async Task SeedAsync(ApplicationDbContext context)
    {
        // Seed Technologies
        if (!await context.Technologies.AnyAsync())
        {
            await SeedTechnologies(context);
        }

        // Seed Platforms
        if (!await context.Platforms.AnyAsync())
        {
            await SeedPlatforms(context);
        }

        // Seed Customers
        if (!await context.Customers.AnyAsync())
        {
            await SeedCustomers(context);
        }

        // Seed Projects
        if (!await context.Projects.AnyAsync())
        {
            await SeedProjects(context);
        }

        await context.SaveChangesAsync();
    }

    private static async Task SeedTechnologies(ApplicationDbContext context)
    {
        var technologies = new List<Technology>
        {
            new Technology
            {
                Id = Guid.NewGuid(),
                Name = "Angular",
                TechnologyCode = "TECH-ANG-001",
                Description = "Modern web framework for building scalable applications",
                Type = TechnologyType.FRAMEWORK,
                Category = TechnologyCategory.FRONTEND,
                Version = "17.0.0",
                Vendor = "Google",
                Website = "https://angular.io",
                Documentation = "https://angular.io/docs",
                Repository = "https://github.com/angular/angular",
                LicenseType = LicenseType.OPEN_SOURCE,
                IsActive = true,
                IsRecommended = true,
                IsDeprecated = false,
                PopularityScore = 95,
                ProjectCount = 12,
                TeamCount = 8,
                ReleaseDate = DateTime.UtcNow.AddMonths(-6),
                LastUpdateDate = DateTime.UtcNow.AddDays(-15),
                Tags = JsonSerializer.Serialize(new[] { "frontend", "spa", "typescript", "google" }),
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "system"
            },
            new Technology
            {
                Id = Guid.NewGuid(),
                Name = ".NET Core",
                TechnologyCode = "TECH-NET-001",
                Description = "Cross-platform framework for building modern applications",
                Type = TechnologyType.FRAMEWORK,
                Category = TechnologyCategory.BACKEND,
                Version = "8.0.0",
                Vendor = "Microsoft",
                Website = "https://dotnet.microsoft.com",
                Documentation = "https://docs.microsoft.com/dotnet",
                Repository = "https://github.com/dotnet/core",
                LicenseType = LicenseType.OPEN_SOURCE,
                IsActive = true,
                IsRecommended = true,
                IsDeprecated = false,
                PopularityScore = 92,
                ProjectCount = 15,
                TeamCount = 10,
                ReleaseDate = DateTime.UtcNow.AddMonths(-4),
                LastUpdateDate = DateTime.UtcNow.AddDays(-10),
                Tags = JsonSerializer.Serialize(new[] { "backend", "api", "csharp", "microsoft" }),
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "system"
            },
            new Technology
            {
                Id = Guid.NewGuid(),
                Name = "PostgreSQL",
                TechnologyCode = "TECH-PG-001",
                Description = "Advanced open source relational database",
                Type = TechnologyType.DATABASE,
                Category = TechnologyCategory.DATABASE,
                Version = "16.0",
                Vendor = "PostgreSQL Global Development Group",
                Website = "https://postgresql.org",
                Documentation = "https://postgresql.org/docs",
                Repository = "https://github.com/postgres/postgres",
                LicenseType = LicenseType.OPEN_SOURCE,
                IsActive = true,
                IsRecommended = true,
                IsDeprecated = false,
                PopularityScore = 88,
                ProjectCount = 20,
                TeamCount = 12,
                ReleaseDate = DateTime.UtcNow.AddMonths(-8),
                LastUpdateDate = DateTime.UtcNow.AddDays(-5),
                Tags = JsonSerializer.Serialize(new[] { "database", "sql", "relational", "opensource" }),
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "system"
            }
        };

        await context.Technologies.AddRangeAsync(technologies);
    }

    private static async Task SeedPlatforms(ApplicationDbContext context)
    {
        var platforms = new List<Platform>
        {
            new Platform
            {
                Id = Guid.NewGuid(),
                Name = "Microsoft Azure",
                PlatformCode = "PLT-AZURE-001",
                Type = PlatformType.CLOUD,
                Description = "Microsoft's cloud computing platform",
                Category = PlatformCategory.INFRASTRUCTURE,
                Version = "2024.1",
                Vendor = "Microsoft",
                Website = "https://azure.microsoft.com",
                Documentation = "https://docs.microsoft.com/azure",
                SupportLevel = SupportLevel.ENTERPRISE_SUPPORT,
                LicenseType = LicenseType.SUBSCRIPTION,
                Cost = 2500.00m,
                Currency = "USD",
                IsActive = true,
                IsSupported = true,
                ProjectCount = 8,
                ReleaseDate = DateTime.UtcNow.AddMonths(-12),
                Tags = JsonSerializer.Serialize(new[] { "cloud", "infrastructure", "microsoft", "enterprise" }),
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "system"
            },
            new Platform
            {
                Id = Guid.NewGuid(),
                Name = "Docker",
                PlatformCode = "PLT-DOCKER-001",
                Type = PlatformType.CONTAINER,
                Description = "Containerization platform for applications",
                Category = PlatformCategory.CONTAINER,
                Version = "24.0",
                Vendor = "Docker Inc.",
                Website = "https://docker.com",
                Documentation = "https://docs.docker.com",
                SupportLevel = SupportLevel.COMMUNITY_SUPPORT,
                LicenseType = LicenseType.OPEN_SOURCE,
                Cost = 0.00m,
                Currency = "USD",
                IsActive = true,
                IsSupported = true,
                ProjectCount = 15,
                ReleaseDate = DateTime.UtcNow.AddMonths(-6),
                Tags = JsonSerializer.Serialize(new[] { "container", "devops", "deployment", "opensource" }),
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "system"
            }
        };

        await context.Platforms.AddRangeAsync(platforms);
    }

    private static async Task SeedCustomers(ApplicationDbContext context)
    {
        var customers = new List<Customer>
        {
            new Customer
            {
                Id = Guid.NewGuid(),
                Name = "TechCorp A.Ş.",
                CustomerCode = "CUS-TECH-001",
                ContactPerson = "Ahmet Yılmaz",
                Email = "<EMAIL>",
                Phone = "+90 212 123 45 67",
                Address = "Teknoloji Cad. No:123, Beşiktaş, İstanbul",
                City = "İstanbul",
                Country = "Türkiye",
                PostalCode = "34349",
                TaxNumber = "1234567890",
                Website = "www.techcorp.com",
                CustomerType = CustomerType.CORPORATE,
                Industry = IndustryType.TECHNOLOGY,
                Status = CustomerStatus.ACTIVE,
                CompanySize = "100-500",
                CreditLimit = 500000.00m,
                Currency = "TRY",
                PaymentTerms = "30 gün",
                FirstContactDate = DateTime.UtcNow.AddMonths(-12),
                LastContactDate = DateTime.UtcNow.AddDays(-5),
                ProjectCount = 3,
                TotalRevenue = 750000.00m,
                AssignedSalesRep = "Mehmet Özkan",
                Priority = 5,
                IsVip = true,
                IsActive = true,
                Tags = JsonSerializer.Serialize(new[] { "enterprise", "technology", "vip" }),
                Notes = "Stratejik müşteri - özel ilgi gerektirir",
                CreatedAt = DateTime.UtcNow.AddMonths(-12),
                CreatedBy = "system"
            },
            new Customer
            {
                Id = Guid.NewGuid(),
                Name = "StartupXYZ Ltd.",
                CustomerCode = "CUS-START-001",
                ContactPerson = "Ayşe Demir",
                Email = "<EMAIL>",
                Phone = "+90 216 987 65 43",
                Address = "İnovasyon Merkezi, Kadıköy, İstanbul",
                City = "İstanbul",
                Country = "Türkiye",
                PostalCode = "34710",
                TaxNumber = "9876543210",
                Website = "www.startupxyz.com",
                CustomerType = CustomerType.STARTUP,
                Industry = IndustryType.E_COMMERCE,
                Status = CustomerStatus.ACTIVE,
                CompanySize = "10-50",
                CreditLimit = 100000.00m,
                Currency = "TRY",
                PaymentTerms = "15 gün",
                FirstContactDate = DateTime.UtcNow.AddMonths(-6),
                LastContactDate = DateTime.UtcNow.AddDays(-2),
                ProjectCount = 2,
                TotalRevenue = 150000.00m,
                AssignedSalesRep = "Zeynep Arslan",
                Priority = 3,
                IsVip = false,
                IsActive = true,
                Tags = JsonSerializer.Serialize(new[] { "startup", "ecommerce", "growth" }),
                Notes = "Hızla büyüyen startup, potansiyel yüksek",
                CreatedAt = DateTime.UtcNow.AddMonths(-6),
                CreatedBy = "system"
            }
        };

        await context.Customers.AddRangeAsync(customers);
    }

    private static async Task SeedProjects(ApplicationDbContext context)
    {
        // Get first user for CreatedByUserId
        var firstUser = await context.Users.FirstOrDefaultAsync();
        if (firstUser == null) return;

        var projects = new List<Project>
        {
            new Project
            {
                Id = Guid.NewGuid(),
                Name = "E-Ticaret Platformu",
                Code = "PRJ-ECOM-001",
                Description = "Modern e-ticaret çözümü. Ürün yönetimi, sipariş takibi, ödeme entegrasyonu içerir.",
                Status = ProjectStatus.IN_PROGRESS,
                ProjectType = ProjectType.E_COMMERCE,
                Priority = ProjectPriority.HIGH,
                StartDate = DateTime.UtcNow.AddMonths(-3),
                EndDate = DateTime.UtcNow.AddMonths(3),
                ActualStartDate = DateTime.UtcNow.AddMonths(-3),
                Budget = 250000.00m,
                Currency = "TRY",
                EstimatedHours = 2000,
                ActualHours = 800,
                ProgressPercentage = 40,
                ProjectManager = "Mehmet Özkan",
                TechnicalLead = "Ali Çelik",
                ClientContact = "Ahmet Yılmaz",
                Repository = "https://github.com/company/ecommerce-platform",
                Methodology = "Agile",
                TeamSize = 8,
                IsActive = true,
                IsPublic = false,
                Tags = JsonSerializer.Serialize(new[] { "ecommerce", "web", "mobile", "api" }),
                Notes = "Kritik proje - zamanında teslim önemli",
                CreatedByUserId = firstUser.Id,
                CreatedAt = DateTime.UtcNow.AddMonths(-3),
                CreatedBy = "system"
            },
            new Project
            {
                Id = Guid.NewGuid(),
                Name = "CRM Sistemi",
                Code = "PRJ-CRM-001",
                Description = "Müşteri ilişkileri yönetim sistemi. Lead takibi, satış süreci yönetimi içerir.",
                Status = ProjectStatus.TESTING,
                ProjectType = ProjectType.CRM,
                Priority = ProjectPriority.MEDIUM,
                StartDate = DateTime.UtcNow.AddMonths(-6),
                EndDate = DateTime.UtcNow.AddMonths(1),
                ActualStartDate = DateTime.UtcNow.AddMonths(-6),
                Budget = 180000.00m,
                Currency = "TRY",
                EstimatedHours = 1500,
                ActualHours = 1200,
                ProgressPercentage = 80,
                ProjectManager = "Zeynep Arslan",
                TechnicalLead = "Can Kaya",
                ClientContact = "Ayşe Demir",
                Repository = "https://github.com/company/crm-system",
                Methodology = "Scrum",
                TeamSize = 6,
                IsActive = true,
                IsPublic = false,
                Tags = JsonSerializer.Serialize(new[] { "crm", "web", "dashboard", "analytics" }),
                Notes = "Test aşamasında, yakında teslim",
                CreatedByUserId = firstUser.Id,
                CreatedAt = DateTime.UtcNow.AddMonths(-6),
                CreatedBy = "system"
            }
        };

        await context.Projects.AddRangeAsync(projects);
    }
}

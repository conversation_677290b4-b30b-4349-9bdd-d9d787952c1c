﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TRtek_EYS.Application.Features.Projects.Commands.Delete;
using TRtek_EYS.Application.Features.Projects.Commands.Update;
using TRtek_EYS.Application.Features.Projects.Queries.GetAll;
using TRtek_EYS.Application.Features.Projects.Queries.GetById;

namespace TRtek_EYS.WebAPI.Controllers;

[Route("api/[controller]")]
[ApiController]
public class ProjectsController : ApiController
{
    public ProjectsController(IMediator mediator) : base(mediator) { }

    [HttpGet]
    public async Task<IActionResult> GetAll()
    {
        var result = await Mediator.Send(new GetAllProjectsQuery());
        return Ok(result);
    }

    [HttpGet("v2")]
    [Authorize]
    public async Task<IActionResult> GetAllV2()
    {
        var result = await Mediator.Send(new GetAllProjectsV2Query());
        return Ok(result); // List<ProjectGetAllDtoV2>
    }

    [HttpGet("{id:guid}")]
    public async Task<IActionResult> GetById(Guid id)
    {
        var result = await Mediator.Send(new GetProjectByIdQuery(id));
        return result is null ? NotFound() : Ok(result); 
    }

    [HttpPost]
    public async Task<IActionResult> Add([FromBody] AddProjectCommand command)
    {
        var id = await Mediator.Send(command);
        return CreatedAtAction(nameof(GetById), new { id }, null);
    }

    [HttpPut("{id:guid}")]
    public async Task<IActionResult> Update(Guid id, [FromBody] UpdateProjectCommand command)
    {
        if (id != command.Id)
            return BadRequest("URL'deki ID uyuşmuyor.");

        await Mediator.Send(command);
        return NoContent();
    }

    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> Delete(Guid id)
    {
        await Mediator.Send(new DeleteProjectCommand { Id = id });
        return NoContent();
    }
}

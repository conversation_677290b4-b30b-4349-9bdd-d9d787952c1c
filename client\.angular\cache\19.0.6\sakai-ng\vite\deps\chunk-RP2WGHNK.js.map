{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-tree.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, forwardRef, numberAttribute, booleanAttribute, Input, ViewEncapsulation, Component, EventEmitter, ContentChildren, ViewChild, ContentChild, Output, Optional, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport * as i2 from '@angular/forms';\nimport { FormsModule } from '@angular/forms';\nimport { find, hasClass, findSingle, focus, removeAccents, resolveFieldData } from '@primeuix/utils';\nimport * as i3 from 'primeng/api';\nimport { SharedModule, TranslationKeys, PrimeTemplate } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { Checkbox } from 'primeng/checkbox';\nimport { IconField } from 'primeng/iconfield';\nimport { ChevronRightIcon, ChevronDownIcon, SpinnerIcon, SearchIcon } from 'primeng/icons';\nimport { InputIcon } from 'primeng/inputicon';\nimport { InputText } from 'primeng/inputtext';\nimport { Ripple } from 'primeng/ripple';\nimport { Scroller } from 'primeng/scroller';\nimport { BaseStyle } from 'primeng/base';\nimport * as i4 from 'primeng/autofocus';\nimport { AutoFocusModule } from 'primeng/autofocus';\nconst _c0 = a0 => ({\n  height: a0\n});\nconst _c1 = a0 => ({\n  \"p-tree-node-droppoint-active\": a0\n});\nconst _c2 = (a0, a1) => ({\n  $implicit: a0,\n  loading: a1\n});\nconst _c3 = (a0, a1) => ({\n  $implicit: a0,\n  partialSelected: a1,\n  class: \"p-tree-node-checkbox\"\n});\nconst _c4 = a0 => ({\n  $implicit: a0\n});\nfunction UITreeNode_Conditional_0_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 11);\n    i0.ɵɵlistener(\"drop\", function UITreeNode_Conditional_0_li_0_Template_li_drop_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onDropPoint($event, -1));\n    })(\"dragover\", function UITreeNode_Conditional_0_li_0_Template_li_dragover_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onDropPointDragOver($event));\n    })(\"dragenter\", function UITreeNode_Conditional_0_li_0_Template_li_dragenter_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onDropPointDragEnter($event, -1));\n    })(\"dragleave\", function UITreeNode_Conditional_0_li_0_Template_li_dragleave_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onDropPointDragLeave($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c1, ctx_r2.draghoverPrev));\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction UITreeNode_Conditional_0_ng_container_4_ng_container_1_ChevronRightIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\", 13);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-tree-node-toggle-icon\");\n  }\n}\nfunction UITreeNode_Conditional_0_ng_container_4_ng_container_1_ChevronDownIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronDownIcon\", 13);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-tree-node-toggle-icon\");\n  }\n}\nfunction UITreeNode_Conditional_0_ng_container_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, UITreeNode_Conditional_0_ng_container_4_ng_container_1_ChevronRightIcon_1_Template, 1, 1, \"ChevronRightIcon\", 12)(2, UITreeNode_Conditional_0_ng_container_4_ng_container_1_ChevronDownIcon_2_Template, 1, 1, \"ChevronDownIcon\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.node.expanded);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.node.expanded);\n  }\n}\nfunction UITreeNode_Conditional_0_ng_container_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"SpinnerIcon\", 13);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"styleClass\", \"pi-spin p-tree-node-toggle-icon\");\n  }\n}\nfunction UITreeNode_Conditional_0_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, UITreeNode_Conditional_0_ng_container_4_ng_container_1_Template, 3, 2, \"ng-container\", 5)(2, UITreeNode_Conditional_0_ng_container_4_ng_container_2_Template, 2, 1, \"ng-container\", 5);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.node.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loadingMode === \"icon\" && ctx_r2.node.loading);\n  }\n}\nfunction UITreeNode_Conditional_0_span_5_1_ng_template_0_Template(rf, ctx) {}\nfunction UITreeNode_Conditional_0_span_5_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, UITreeNode_Conditional_0_span_5_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction UITreeNode_Conditional_0_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 14);\n    i0.ɵɵtemplate(1, UITreeNode_Conditional_0_span_5_1_Template, 1, 0, null, 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.tree.togglerIconTemplate || ctx_r2.tree._togglerIconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c2, ctx_r2.node.expanded, ctx_r2.node.loading));\n  }\n}\nfunction UITreeNode_Conditional_0_p_checkbox_6_ng_container_1_ng_template_1_0_ng_template_0_Template(rf, ctx) {}\nfunction UITreeNode_Conditional_0_p_checkbox_6_ng_container_1_ng_template_1_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, UITreeNode_Conditional_0_p_checkbox_6_ng_container_1_ng_template_1_0_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction UITreeNode_Conditional_0_p_checkbox_6_ng_container_1_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, UITreeNode_Conditional_0_p_checkbox_6_ng_container_1_ng_template_1_0_Template, 1, 0, null, 15);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.tree.checkboxIconTemplate || ctx_r2.tree._checkboxIconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c3, ctx_r2.isSelected(), ctx_r2.node.partialSelected));\n  }\n}\nfunction UITreeNode_Conditional_0_p_checkbox_6_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, UITreeNode_Conditional_0_p_checkbox_6_ng_container_1_ng_template_1_Template, 1, 5, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction UITreeNode_Conditional_0_p_checkbox_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-checkbox\", 16);\n    i0.ɵɵlistener(\"click\", function UITreeNode_Conditional_0_p_checkbox_6_Template_p_checkbox_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      return i0.ɵɵresetView($event.preventDefault());\n    });\n    i0.ɵɵtemplate(1, UITreeNode_Conditional_0_p_checkbox_6_ng_container_1_Template, 3, 0, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.isSelected())(\"binary\", true)(\"indeterminate\", ctx_r2.node.partialSelected)(\"disabled\", ctx_r2.node.selectable === false)(\"variant\", (ctx_r2.tree == null ? null : ctx_r2.tree.config.inputStyle()) === \"filled\" || (ctx_r2.tree == null ? null : ctx_r2.tree.config.inputVariant()) === \"filled\" ? \"filled\" : \"outlined\")(\"tabindex\", -1);\n    i0.ɵɵattribute(\"data-p-partialchecked\", ctx_r2.node.partialSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.tree.checkboxIconTemplate || ctx_r2.tree._checkboxIconTemplate);\n  }\n}\nfunction UITreeNode_Conditional_0_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\");\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r2.getIcon());\n  }\n}\nfunction UITreeNode_Conditional_0_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.node.label);\n  }\n}\nfunction UITreeNode_Conditional_0_span_10_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction UITreeNode_Conditional_0_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtemplate(1, UITreeNode_Conditional_0_span_10_ng_container_1_Template, 1, 0, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.tree.getTemplateForNode(ctx_r2.node))(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, ctx_r2.node));\n  }\n}\nfunction UITreeNode_Conditional_0_ul_11_p_treeNode_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-treeNode\", 19);\n  }\n  if (rf & 2) {\n    const childNode_r5 = ctx.$implicit;\n    const firstChild_r6 = ctx.first;\n    const lastChild_r7 = ctx.last;\n    const index_r8 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"node\", childNode_r5)(\"parentNode\", ctx_r2.node)(\"firstChild\", firstChild_r6)(\"lastChild\", lastChild_r7)(\"index\", index_r8)(\"itemSize\", ctx_r2.itemSize)(\"level\", ctx_r2.level + 1)(\"loadingMode\", ctx_r2.loadingMode);\n  }\n}\nfunction UITreeNode_Conditional_0_ul_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 17);\n    i0.ɵɵtemplate(1, UITreeNode_Conditional_0_ul_11_p_treeNode_1_Template, 1, 8, \"p-treeNode\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"display\", ctx_r2.node.expanded ? \"flex\" : \"none\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.node.children)(\"ngForTrackBy\", ctx_r2.tree.trackBy.bind(ctx_r2));\n  }\n}\nfunction UITreeNode_Conditional_0_li_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 11);\n    i0.ɵɵlistener(\"drop\", function UITreeNode_Conditional_0_li_12_Template_li_drop_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onDropPoint($event, 1));\n    })(\"dragover\", function UITreeNode_Conditional_0_li_12_Template_li_dragover_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onDropPointDragOver($event));\n    })(\"dragenter\", function UITreeNode_Conditional_0_li_12_Template_li_dragenter_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onDropPointDragEnter($event, 1));\n    })(\"dragleave\", function UITreeNode_Conditional_0_li_12_Template_li_dragleave_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onDropPointDragLeave($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c1, ctx_r2.draghoverNext));\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction UITreeNode_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵtemplate(0, UITreeNode_Conditional_0_li_0_Template, 1, 4, \"li\", 1);\n    i0.ɵɵelementStart(1, \"li\", 2);\n    i0.ɵɵlistener(\"keydown\", function UITreeNode_Conditional_0_Template_li_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onKeyDown($event));\n    });\n    i0.ɵɵelementStart(2, \"div\", 3);\n    i0.ɵɵlistener(\"click\", function UITreeNode_Conditional_0_Template_div_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onNodeClick($event));\n    })(\"contextmenu\", function UITreeNode_Conditional_0_Template_div_contextmenu_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onNodeRightClick($event));\n    })(\"dblclick\", function UITreeNode_Conditional_0_Template_div_dblclick_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onNodeDblClick($event));\n    })(\"touchend\", function UITreeNode_Conditional_0_Template_div_touchend_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onNodeTouchEnd());\n    })(\"drop\", function UITreeNode_Conditional_0_Template_div_drop_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDropNode($event));\n    })(\"dragover\", function UITreeNode_Conditional_0_Template_div_dragover_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDropNodeDragOver($event));\n    })(\"dragenter\", function UITreeNode_Conditional_0_Template_div_dragenter_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDropNodeDragEnter($event));\n    })(\"dragleave\", function UITreeNode_Conditional_0_Template_div_dragleave_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDropNodeDragLeave($event));\n    })(\"dragstart\", function UITreeNode_Conditional_0_Template_div_dragstart_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDragStart($event));\n    })(\"dragend\", function UITreeNode_Conditional_0_Template_div_dragend_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDragStop($event));\n    });\n    i0.ɵɵelementStart(3, \"button\", 4);\n    i0.ɵɵlistener(\"click\", function UITreeNode_Conditional_0_Template_button_click_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggle($event));\n    });\n    i0.ɵɵtemplate(4, UITreeNode_Conditional_0_ng_container_4_Template, 3, 2, \"ng-container\", 5)(5, UITreeNode_Conditional_0_span_5_Template, 2, 5, \"span\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, UITreeNode_Conditional_0_p_checkbox_6_Template, 2, 8, \"p-checkbox\", 7)(7, UITreeNode_Conditional_0_span_7_Template, 1, 2, \"span\", 8);\n    i0.ɵɵelementStart(8, \"span\", 9);\n    i0.ɵɵtemplate(9, UITreeNode_Conditional_0_span_9_Template, 2, 1, \"span\", 5)(10, UITreeNode_Conditional_0_span_10_Template, 2, 4, \"span\", 5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, UITreeNode_Conditional_0_ul_11_Template, 2, 4, \"ul\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, UITreeNode_Conditional_0_li_12_Template, 1, 4, \"li\", 1);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.tree.droppableNodes);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(ctx_r2.node.style);\n    i0.ɵɵclassMap(ctx_r2.node.styleClass);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.nodeClass)(\"ngStyle\", i0.ɵɵpureFunction1(29, _c0, ctx_r2.itemSize + \"px\"));\n    i0.ɵɵattribute(\"aria-label\", ctx_r2.node.label)(\"aria-checked\", ctx_r2.checked)(\"aria-setsize\", ctx_r2.node.children ? ctx_r2.node.children.length : 0)(\"aria-selected\", ctx_r2.selected)(\"aria-expanded\", ctx_r2.node.expanded)(\"aria-posinset\", ctx_r2.index + 1)(\"aria-level\", ctx_r2.level + 1)(\"tabindex\", ctx_r2.index === 0 ? 0 : -1)(\"data-id\", ctx_r2.node.key);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"padding-left\", ctx_r2.level * ctx_r2.indentation + \"rem\");\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.nodeContentClass)(\"draggable\", ctx_r2.tree.draggableNodes);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"data-pc-section\", \"toggler\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.tree.togglerIconTemplate && !ctx_r2.tree._togglerIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.tree.togglerIconTemplate || ctx_r2.tree._togglerIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.tree.selectionMode == \"checkbox\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.node.icon || ctx_r2.node.expandedIcon || ctx_r2.node.collapsedIcon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.tree.getTemplateForNode(ctx_r2.node));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.tree.getTemplateForNode(ctx_r2.node));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.tree.virtualScroll && ctx_r2.node.children && ctx_r2.node.expanded);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.tree.droppableNodes && ctx_r2.lastChild);\n  }\n}\nconst _c5 = [\"filter\"];\nconst _c6 = [\"node\"];\nconst _c7 = [\"header\"];\nconst _c8 = [\"footer\"];\nconst _c9 = [\"loader\"];\nconst _c10 = [\"empty\"];\nconst _c11 = [\"togglericon\"];\nconst _c12 = [\"checkboxicon\"];\nconst _c13 = [\"loadingicon\"];\nconst _c14 = [\"filtericon\"];\nconst _c15 = [\"scroller\"];\nconst _c16 = [\"wrapper\"];\nconst _c17 = a0 => ({\n  options: a0\n});\nfunction Tree_div_1_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\");\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(\"p-tree-loading-icon pi-spin \" + ctx_r0.loadingIcon);\n  }\n}\nfunction Tree_div_1_ng_container_2_SpinnerIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"SpinnerIcon\", 16);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"spin\", true)(\"styleClass\", \"p-tree-loading-icon\");\n  }\n}\nfunction Tree_div_1_ng_container_2_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Tree_div_1_ng_container_2_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Tree_div_1_ng_container_2_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Tree_div_1_ng_container_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 17);\n    i0.ɵɵtemplate(1, Tree_div_1_ng_container_2_span_2_1_Template, 1, 0, null, 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.loadingIconTemplate || ctx_r0._loadingIconTemplate);\n  }\n}\nfunction Tree_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Tree_div_1_ng_container_2_SpinnerIcon_1_Template, 1, 2, \"SpinnerIcon\", 14)(2, Tree_div_1_ng_container_2_span_2_Template, 2, 1, \"span\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loadingIconTemplate && !ctx_r0._loadingIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loadingIconTemplate || ctx_r0._loadingIconTemplate);\n  }\n}\nfunction Tree_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtemplate(1, Tree_div_1_i_1_Template, 1, 2, \"i\", 13)(2, Tree_div_1_ng_container_2_Template, 3, 2, \"ng-container\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loadingIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loadingIcon);\n  }\n}\nfunction Tree_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Tree_Conditional_3_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Tree_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Tree_Conditional_3_ng_container_0_Template, 1, 0, \"ng-container\", 18);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.filterTemplate || ctx_r0._filterTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, ctx_r0.filterOptions));\n  }\n}\nfunction Tree_Conditional_4_p_iconField_0_SearchIcon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"SearchIcon\", 21);\n  }\n}\nfunction Tree_Conditional_4_p_iconField_0_span_5_1_ng_template_0_Template(rf, ctx) {}\nfunction Tree_Conditional_4_p_iconField_0_span_5_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Tree_Conditional_4_p_iconField_0_span_5_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Tree_Conditional_4_p_iconField_0_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtemplate(1, Tree_Conditional_4_p_iconField_0_span_5_1_Template, 1, 0, null, 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.filterIconTemplate || ctx_r0._filterIconTemplate);\n  }\n}\nfunction Tree_Conditional_4_p_iconField_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-iconField\")(1, \"input\", 19, 0);\n    i0.ɵɵlistener(\"keydown.enter\", function Tree_Conditional_4_p_iconField_0_Template_input_keydown_enter_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      return i0.ɵɵresetView($event.preventDefault());\n    })(\"input\", function Tree_Conditional_4_p_iconField_0_Template_input_input_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0._filter($event.target.value));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-inputIcon\");\n    i0.ɵɵtemplate(4, Tree_Conditional_4_p_iconField_0_SearchIcon_4_Template, 1, 0, \"SearchIcon\", 20)(5, Tree_Conditional_4_p_iconField_0_span_5_Template, 2, 1, \"span\", 10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pAutoFocus\", ctx_r0.filterInputAutoFocus);\n    i0.ɵɵattribute(\"placeholder\", ctx_r0.filterPlaceholder);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.filterIconTemplate && !ctx_r0._filterIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.filterIconTemplate || ctx_r0._filterIconTemplate);\n  }\n}\nfunction Tree_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Tree_Conditional_4_p_iconField_0_Template, 6, 4, \"p-iconField\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.filter);\n  }\n}\nfunction Tree_ng_container_5_p_scroller_1_ng_template_2_ul_0_p_treeNode_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-treeNode\", 27, 3);\n  }\n  if (rf & 2) {\n    const rowNode_r4 = ctx.$implicit;\n    const firstChild_r5 = ctx.first;\n    const lastChild_r6 = ctx.last;\n    const index_r7 = ctx.index;\n    const scrollerOptions_r8 = i0.ɵɵnextContext(2).options;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"level\", rowNode_r4.level)(\"rowNode\", rowNode_r4)(\"node\", rowNode_r4.node)(\"parentNode\", rowNode_r4.parent)(\"firstChild\", firstChild_r5)(\"lastChild\", lastChild_r6)(\"index\", ctx_r0.getIndex(scrollerOptions_r8, index_r7))(\"itemSize\", scrollerOptions_r8.itemSize)(\"indentation\", ctx_r0.indentation)(\"loadingMode\", ctx_r0.loadingMode);\n  }\n}\nfunction Tree_ng_container_5_p_scroller_1_ng_template_2_ul_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 25);\n    i0.ɵɵtemplate(1, Tree_ng_container_5_p_scroller_1_ng_template_2_ul_0_p_treeNode_1_Template, 2, 10, \"p-treeNode\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    const items_r10 = ctx_r8.$implicit;\n    const scrollerOptions_r8 = ctx_r8.options;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleMap(scrollerOptions_r8.contentStyle);\n    i0.ɵɵproperty(\"ngClass\", scrollerOptions_r8.contentStyleClass);\n    i0.ɵɵattribute(\"aria-label\", ctx_r0.ariaLabel)(\"aria-labelledby\", ctx_r0.ariaLabelledBy);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", items_r10)(\"ngForTrackBy\", ctx_r0.trackBy);\n  }\n}\nfunction Tree_ng_container_5_p_scroller_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Tree_ng_container_5_p_scroller_1_ng_template_2_ul_0_Template, 2, 7, \"ul\", 24);\n  }\n  if (rf & 2) {\n    const items_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", items_r10);\n  }\n}\nfunction Tree_ng_container_5_p_scroller_1_ng_container_4_ng_template_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Tree_ng_container_5_p_scroller_1_ng_container_4_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Tree_ng_container_5_p_scroller_1_ng_container_4_ng_template_1_ng_container_0_Template, 1, 0, \"ng-container\", 18);\n  }\n  if (rf & 2) {\n    const scrollerOptions_r11 = ctx.options;\n    const ctx_r0 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.loaderTemplate || ctx_r0._loaderTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c17, scrollerOptions_r11));\n  }\n}\nfunction Tree_ng_container_5_p_scroller_1_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Tree_ng_container_5_p_scroller_1_ng_container_4_ng_template_1_Template, 1, 4, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction Tree_ng_container_5_p_scroller_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-scroller\", 23, 1);\n    i0.ɵɵlistener(\"onScroll\", function Tree_ng_container_5_p_scroller_1_Template_p_scroller_onScroll_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onScroll.emit($event));\n    })(\"onScrollIndexChange\", function Tree_ng_container_5_p_scroller_1_Template_p_scroller_onScrollIndexChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onScrollIndexChange.emit($event));\n    })(\"onLazyLoad\", function Tree_ng_container_5_p_scroller_1_Template_p_scroller_onLazyLoad_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onLazyLoad.emit($event));\n    });\n    i0.ɵɵtemplate(2, Tree_ng_container_5_p_scroller_1_ng_template_2_Template, 1, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(4, Tree_ng_container_5_p_scroller_1_ng_container_4_Template, 3, 0, \"ng-container\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction1(9, _c0, ctx_r0.scrollHeight !== \"flex\" ? ctx_r0.scrollHeight : undefined));\n    i0.ɵɵproperty(\"items\", ctx_r0.serializedValue)(\"tabindex\", -1)(\"scrollHeight\", ctx_r0.scrollHeight !== \"flex\" ? undefined : \"100%\")(\"itemSize\", ctx_r0.virtualScrollItemSize || ctx_r0._virtualNodeHeight)(\"lazy\", ctx_r0.lazy)(\"options\", ctx_r0.virtualScrollOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loaderTemplate || ctx_r0._loaderTemplate);\n  }\n}\nfunction Tree_ng_container_5_ng_container_2_ul_3_p_treeNode_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-treeNode\", 32);\n  }\n  if (rf & 2) {\n    const node_r12 = ctx.$implicit;\n    const firstChild_r13 = ctx.first;\n    const lastChild_r14 = ctx.last;\n    const index_r15 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"node\", node_r12)(\"firstChild\", firstChild_r13)(\"lastChild\", lastChild_r14)(\"index\", index_r15)(\"level\", 0)(\"loadingMode\", ctx_r0.loadingMode);\n  }\n}\nfunction Tree_ng_container_5_ng_container_2_ul_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 30);\n    i0.ɵɵtemplate(1, Tree_ng_container_5_ng_container_2_ul_3_p_treeNode_1_Template, 1, 6, \"p-treeNode\", 31);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵattribute(\"aria-label\", ctx_r0.ariaLabel)(\"aria-labelledby\", ctx_r0.ariaLabelledBy);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.getRootNode())(\"ngForTrackBy\", ctx_r0.trackBy.bind(ctx_r0));\n  }\n}\nfunction Tree_ng_container_5_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 28, 5);\n    i0.ɵɵtemplate(3, Tree_ng_container_5_ng_container_2_ul_3_Template, 2, 4, \"ul\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"max-height\", ctx_r0.scrollHeight);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getRootNode());\n  }\n}\nfunction Tree_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Tree_ng_container_5_p_scroller_1_Template, 5, 11, \"p-scroller\", 22)(2, Tree_ng_container_5_ng_container_2_Template, 4, 3, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.virtualScroll);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.virtualScroll);\n  }\n}\nfunction Tree_div_6_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.emptyMessageLabel, \" \");\n  }\n}\nfunction Tree_div_6_2_ng_template_0_Template(rf, ctx) {}\nfunction Tree_div_6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Tree_div_6_2_ng_template_0_Template, 0, 0, \"ng-template\", null, 6, i0.ɵɵtemplateRefExtractor);\n  }\n}\nfunction Tree_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtemplate(1, Tree_div_6_ng_container_1_Template, 2, 1, \"ng-container\", 34)(2, Tree_div_6_2_Template, 2, 0, null, 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.emptyMessageTemplate && !ctx_r0._emptyMessageTemplate)(\"ngIfElse\", ctx_r0.emptyFilter);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.emptyMessageTemplate || ctx_r0._emptyMessageTemplate);\n  }\n}\nfunction Tree_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst theme = ({\n  dt\n}) => `\n.p-tree {\n    background: ${dt('tree.background')};\n    color: ${dt('tree.color')};\n    padding: ${dt('tree.padding')};\n}\n\n.p-tree-root-children,\n.p-tree-node-children {\n    display: flex;\n    list-style-type: none;\n    flex-direction: column;\n    margin: 0;\n    gap: ${dt('tree.gap')};\n}\n\n.p-tree-root-children {\n    padding: 0;\n    padding-block-start: ${dt('tree.gap')};\n}\n\n.p-tree-node-children {\n    padding-block-start: ${dt('tree.gap')};\n    padding-inline-start: ${dt('tree.indent')};\n}\n\n.p-tree-node {\n    padding: 0;\n    outline: 0 none;\n}\n\n.p-tree-node-content {\n    border-radius: ${dt('tree.node.border.radius')};\n    padding: ${dt('tree.node.padding')};\n    display: flex;\n    align-items: center;\n    outline-color: transparent;\n    color: ${dt('tree.node.color')};\n    gap: ${dt('tree.node.gap')};\n    transition: background ${dt('tree.transition.duration')}, color ${dt('tree.transition.duration')}, outline-color ${dt('tree.transition.duration')}, box-shadow ${dt('tree.transition.duration')};\n}\n\n.p-tree-node:focus-visible > .p-tree-node-content {\n    box-shadow: ${dt('tree.node.focus.ring.shadow')};\n    outline: ${dt('tree.node.focus.ring.width')} ${dt('tree.node.focus.ring.style')} ${dt('tree.node.focus.ring.color')};\n    outline-offset: ${dt('tree.node.focus.ring.offset')};\n}\n\n.p-tree-node-content.p-tree-node-selectable:not(.p-tree-node-selected):hover {\n    background: ${dt('tree.node.hover.background')};\n    color: ${dt('tree.node.hover.color')};\n}\n\n.p-tree-node-content.p-tree-node-selectable:not(.p-tree-node-selected):hover .p-tree-node-icon {\n    color: ${dt('tree.node.icon.hover.color')};\n}\n\n.p-tree-node-content.p-tree-node-selected {\n    background: ${dt('tree.node.selected.background')};\n    color: ${dt('tree.node.selected.color')};\n}\n\n.p-tree-node-content.p-tree-node-selected .p-tree-node-toggle-button {\n    color: inherit;\n}\n\n.p-tree-node-toggle-button {\n    cursor: pointer;\n    user-select: none;\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    overflow: hidden;\n    position: relative;\n    flex-shrink: 0;\n    width: ${dt('tree.node.toggle.button.size')};\n    height: ${dt('tree.node.toggle.button.size')};\n    color: ${dt('tree.node.toggle.button.color')};\n    border: 0 none;\n    background: transparent;\n    border-radius: ${dt('tree.node.toggle.button.border.radius')};\n    transition: background ${dt('tree.transition.duration')}, color ${dt('tree.transition.duration')}, border-color ${dt('tree.transition.duration')}, outline-color ${dt('tree.transition.duration')}, box-shadow ${dt('tree.transition.duration')};\n    outline-color: transparent;\n    padding: 0;\n}\n\n.p-tree-node-toggle-button:enabled:hover {\n    background: ${dt('tree.node.toggle.button.hover.background')};\n    color: ${dt('tree.node.toggle.button.hover.color')};\n}\n\n.p-tree-node-content.p-tree-node-selected .p-tree-node-toggle-button:hover {\n    background: ${dt('tree.node.toggle.button.selected.hover.background')};\n    color: ${dt('tree.node.toggle.button.selected.hover.color')};\n}\n\n.p-tree-root {\n    overflow: auto;\n}\n\n.p-tree-node-selectable {\n    cursor: pointer;\n    user-select: none;\n}\n\n.p-tree-node-leaf > .p-tree-node-content .p-tree-node-toggle-button {\n    visibility: hidden;\n}\n\n.p-tree-node-icon {\n    color: ${dt('tree.node.icon.color')};\n    transition: color ${dt('tree.transition.duration')};\n}\n\n.p-tree-node-content.p-tree-node-selected .p-tree-node-icon {\n    color: ${dt('tree.node.icon.selected.color')};\n}\n\n.p-tree-filter-input {\n    width: 100%;\n}\n\n.p-tree-loading {\n    position: relative;\n    height: 100%;\n}\n\n.p-tree-loading-icon {\n    font-size: ${dt('tree.loading.icon.size')};\n    width: ${dt('tree.loading.icon.size')};\n    height: ${dt('tree.loading.icon.size')};\n}\n\n.p-tree .p-tree-mask {\n    position: absolute;\n    z-index: 1;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n}\n\n.p-tree-flex-scrollable {\n    display: flex;\n    flex: 1;\n    height: 100%;\n    flex-direction: column;\n}\n\n.p-tree-flex-scrollable .p-tree-root {\n    flex: 1;\n}\n\n/* For PrimeNG */\n.p-tree .p-tree-node-droppoint {\n    height: 4px;\n    list-style-type: none;\n}\n\n.p-tree .p-tree-node-droppoint-active {\n    border: 0 none;\n    background-color: ${dt('primary.color')};\n}\n\n.p-tree-node-content.p-tree-node-dragover {\n    background: ${dt('tree.node.hover.background')};\n    color: ${dt('tree.node.hover.color')};\n}\n\n.p-tree-node-content.p-tree-node-dragover .p-tree-node-icon {\n    color: ${dt('tree.node.icon.hover.color')};\n}\n\n.p-tree-horizontal {\n    width: auto;\n    padding-inline-start: 0;\n    padding-inline-end: 0;\n    overflow: auto;\n}\n\n.p-tree.p-tree-horizontal table,\n.p-tree.p-tree-horizontal tr,\n.p-tree.p-tree-horizontal td {\n    border-collapse: collapse;\n    margin: 0;\n    padding: 0;\n    vertical-align: middle;\n}\n\n.p-tree-horizontal .p-tree-node-content {\n    font-weight: normal;\n    padding: 0.4em 1em 0.4em 0.2em;\n    display: flex;\n    align-items: center;\n}\n\n.p-tree-horizontal .p-tree-node-parent .p-tree-node-content {\n    font-weight: normal;\n    white-space: nowrap;\n}\n\n.p-tree.p-tree-horizontal .p-tree-node.p-tree-node-leaf,\n.p-tree.p-tree-horizontal .p-tree-node.p-tree-node-collapsed {\n    padding-inline-end: 0;\n}\n\n.p-tree.p-tree-horizontal .p-tree-node-children {\n    padding: 0;\n    margin: 0;\n}\n\n.p-tree.p-tree-horizontal .p-tree-node-connector {\n    width: 1px;\n}\n\n.p-tree.p-tree-horizontal .p-tree-node-connector-table {\n    height: 100%;\n    width: 1px;\n}\n\n.p-tree.p-tree-horizontal table {\n    height: 0;\n}\n`;\nconst classes = {\n  root: ({\n    instance\n  }) => ({\n    'p-tree p-component': true,\n    'p-tree-selectable': instance.selectionMode != null,\n    'p-tree-loading': instance.loading,\n    'p-tree-flex-scrollable': instance.scrollHeight === 'flex',\n    'p-tree-node-dragover': instance.dragHover\n  }),\n  mask: 'p-tree-mask p-overlay-mask',\n  loadingIcon: 'p-tree-loading-icon',\n  pcFilterInput: 'p-tree-filter-input',\n  wrapper: 'p-tree-root',\n  //TODO: discuss\n  rootChildren: 'p-tree-root-children',\n  node: ({\n    instance\n  }) => ({\n    'p-tree-node': true,\n    'p-tree-node-leaf': instance.isLeaf()\n  }),\n  nodeContent: ({\n    instance\n  }) => ({\n    'p-tree-node-content': true,\n    [instance.styleClass]: !!instance.styleClass,\n    'p-tree-node-selectable': instance.selectable,\n    'p-tree-node-dragover': instance.draghoverNode,\n    'p-tree-node-selected': instance.selectionMode === 'checkbox' && instance.tree.highlightOnSelect ? instance.checked : instance.selected\n  }),\n  nodeToggleButton: 'p-tree-node-toggle-button',\n  nodeToggleIcon: 'p-tree-node-toggle-icon',\n  nodeCheckbox: 'p-tree-node-checkbox',\n  nodeIcon: 'p-tree-node-icon',\n  nodeLabel: 'p-tree-node-label',\n  nodeChildren: 'p-tree-node-children'\n};\nclass TreeStyle extends BaseStyle {\n  name = 'tree';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵTreeStyle_BaseFactory;\n    return function TreeStyle_Factory(__ngFactoryType__) {\n      return (ɵTreeStyle_BaseFactory || (ɵTreeStyle_BaseFactory = i0.ɵɵgetInheritedFactory(TreeStyle)))(__ngFactoryType__ || TreeStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: TreeStyle,\n    factory: TreeStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TreeStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * Tree is used to display hierarchical data.\n *\n * [Live Demo](https://www.primeng.org/tree/)\n *\n * @module treestyle\n *\n */\nvar TreeClasses;\n(function (TreeClasses) {\n  /**\n   * Class name of the root element\n   */\n  TreeClasses[\"root\"] = \"p-tree\";\n  /**\n   * Class name of the mask element\n   */\n  TreeClasses[\"mask\"] = \"p-tree-mask\";\n  /**\n   * Class name of the loading icon element\n   */\n  TreeClasses[\"loadingIcon\"] = \"p-tree-loading-icon\";\n  /**\n   * Class name of the filter input element\n   */\n  TreeClasses[\"pcFilterInput\"] = \"p-tree-filter-input\";\n  /**\n   * Class name of the wrapper element\n   */\n  TreeClasses[\"wrapper\"] = \"p-tree-root\";\n  /**\n   * Class name of the root children element\n   */\n  TreeClasses[\"rootChildren\"] = \"p-tree-root-children\";\n  /**\n   * Class name of the node element\n   */\n  TreeClasses[\"node\"] = \"p-tree-node\";\n  /**\n   * Class name of the node content element\n   */\n  TreeClasses[\"nodeContent\"] = \"p-tree-node-content\";\n  /**\n   * Class name of the node toggle button element\n   */\n  TreeClasses[\"nodeToggleButton\"] = \"p-tree-node-toggle-button\";\n  /**\n   * Class name of the node toggle icon element\n   */\n  TreeClasses[\"nodeToggleIcon\"] = \"p-tree-node-toggle-icon\";\n  /**\n   * Class name of the node checkbox element\n   */\n  TreeClasses[\"nodeCheckbox\"] = \"p-tree-node-checkbox\";\n  /**\n   * Class name of the node icon element\n   */\n  TreeClasses[\"nodeIcon\"] = \"p-tree-node-icon\";\n  /**\n   * Class name of the node label element\n   */\n  TreeClasses[\"nodeLabel\"] = \"p-tree-node-label\";\n  /**\n   * Class name of the node children element\n   */\n  TreeClasses[\"nodeChildren\"] = \"p-tree-node-children\";\n})(TreeClasses || (TreeClasses = {}));\nclass UITreeNode extends BaseComponent {\n  static ICON_CLASS = 'p-tree-node-icon ';\n  rowNode;\n  node;\n  parentNode;\n  root;\n  index;\n  firstChild;\n  lastChild;\n  level;\n  indentation;\n  itemSize;\n  loadingMode;\n  tree = inject(forwardRef(() => Tree));\n  timeout;\n  draghoverPrev;\n  draghoverNext;\n  draghoverNode;\n  get selected() {\n    return this.tree.selectionMode === 'single' || this.tree.selectionMode === 'multiple' ? this.isSelected() : undefined;\n  }\n  get checked() {\n    return this.tree.selectionMode === 'checkbox' ? this.isSelected() : undefined;\n  }\n  get nodeClass() {\n    return this.tree._componentStyle.classes.node({\n      instance: this\n    });\n  }\n  get nodeContentClass() {\n    return this.tree._componentStyle.classes.nodeContent({\n      instance: this\n    });\n  }\n  get selectable() {\n    return this.node.selectable === false ? false : this.tree.selectionMode != null;\n  }\n  ngOnInit() {\n    super.ngOnInit();\n    this.node.parent = this.parentNode;\n    const nativeElement = this.tree.el.nativeElement;\n    const pDialogWrapper = nativeElement.closest('p-dialog');\n    if (this.parentNode && !pDialogWrapper) {\n      this.setAllNodesTabIndexes();\n      this.tree.syncNodeOption(this.node, this.tree.value, 'parent', this.tree.getNodeWithKey(this.parentNode.key, this.tree.value));\n    }\n  }\n  getIcon() {\n    let icon;\n    if (this.node.icon) icon = this.node.icon;else icon = this.node.expanded && this.node.children && this.node.children?.length ? this.node.expandedIcon : this.node.collapsedIcon;\n    return UITreeNode.ICON_CLASS + ' ' + icon + ' p-tree-node-icon';\n  }\n  isLeaf() {\n    return this.tree.isNodeLeaf(this.node);\n  }\n  toggle(event) {\n    if (this.node.expanded) this.collapse(event);else this.expand(event);\n    event.stopPropagation();\n  }\n  expand(event) {\n    this.node.expanded = true;\n    if (this.tree.virtualScroll) {\n      this.tree.updateSerializedValue();\n      this.focusVirtualNode();\n    }\n    this.tree.onNodeExpand.emit({\n      originalEvent: event,\n      node: this.node\n    });\n  }\n  collapse(event) {\n    this.node.expanded = false;\n    if (this.tree.virtualScroll) {\n      this.tree.updateSerializedValue();\n      this.focusVirtualNode();\n    }\n    this.tree.onNodeCollapse.emit({\n      originalEvent: event,\n      node: this.node\n    });\n  }\n  onNodeClick(event) {\n    this.tree.onNodeClick(event, this.node);\n  }\n  onNodeKeydown(event) {\n    if (event.key === 'Enter') {\n      this.tree.onNodeClick(event, this.node);\n    }\n  }\n  onNodeTouchEnd() {\n    this.tree.onNodeTouchEnd();\n  }\n  onNodeRightClick(event) {\n    this.tree.onNodeRightClick(event, this.node);\n  }\n  onNodeDblClick(event) {\n    this.tree.onNodeDblClick(event, this.node);\n  }\n  isSelected() {\n    return this.tree.isSelected(this.node);\n  }\n  isSameNode(event) {\n    return event.currentTarget && (event.currentTarget.isSameNode(event.target) || event.currentTarget.isSameNode(event.target.closest('[role=\"treeitem\"]')));\n  }\n  onDropPoint(event, position) {\n    event.preventDefault();\n    let dragNode = this.tree.dragNode;\n    let dragNodeIndex = this.tree.dragNodeIndex;\n    let dragNodeScope = this.tree.dragNodeScope;\n    let isValidDropPointIndex = this.tree.dragNodeTree === this.tree ? position === 1 || dragNodeIndex !== this.index - 1 : true;\n    if (this.tree.allowDrop(dragNode, this.node, dragNodeScope) && isValidDropPointIndex) {\n      let dropParams = {\n        ...this.createDropPointEventMetadata(position)\n      };\n      if (this.tree.validateDrop) {\n        this.tree.onNodeDrop.emit({\n          originalEvent: event,\n          dragNode: dragNode,\n          dropNode: this.node,\n          index: this.index,\n          accept: () => {\n            this.processPointDrop(dropParams);\n          }\n        });\n      } else {\n        this.processPointDrop(dropParams);\n        this.tree.onNodeDrop.emit({\n          originalEvent: event,\n          dragNode: dragNode,\n          dropNode: this.node,\n          index: this.index\n        });\n      }\n    }\n    this.draghoverPrev = false;\n    this.draghoverNext = false;\n  }\n  processPointDrop(event) {\n    let newNodeList = event.dropNode.parent ? event.dropNode.parent.children : this.tree.value;\n    event.dragNodeSubNodes.splice(event.dragNodeIndex, 1);\n    let dropIndex = this.index;\n    if (event.position < 0) {\n      dropIndex = event.dragNodeSubNodes === newNodeList ? event.dragNodeIndex > event.index ? event.index : event.index - 1 : event.index;\n      newNodeList.splice(dropIndex, 0, event.dragNode);\n    } else {\n      dropIndex = newNodeList.length;\n      newNodeList.push(event.dragNode);\n    }\n    this.tree.dragDropService.stopDrag({\n      node: event.dragNode,\n      subNodes: event.dropNode.parent ? event.dropNode.parent.children : this.tree.value,\n      index: event.dragNodeIndex\n    });\n  }\n  createDropPointEventMetadata(position) {\n    return {\n      dragNode: this.tree.dragNode,\n      dragNodeIndex: this.tree.dragNodeIndex,\n      dragNodeSubNodes: this.tree.dragNodeSubNodes,\n      dropNode: this.node,\n      index: this.index,\n      position: position\n    };\n  }\n  onDropPointDragOver(event) {\n    event.dataTransfer.dropEffect = 'move';\n    event.preventDefault();\n  }\n  onDropPointDragEnter(event, position) {\n    if (this.tree.allowDrop(this.tree.dragNode, this.node, this.tree.dragNodeScope)) {\n      if (position < 0) this.draghoverPrev = true;else this.draghoverNext = true;\n    }\n  }\n  onDropPointDragLeave(event) {\n    this.draghoverPrev = false;\n    this.draghoverNext = false;\n  }\n  onDragStart(event) {\n    if (this.tree.draggableNodes && this.node.draggable !== false) {\n      event.dataTransfer.setData('text', 'data');\n      this.tree.dragDropService.startDrag({\n        tree: this,\n        node: this.node,\n        subNodes: this.node?.parent ? this.node.parent.children : this.tree.value,\n        index: this.index,\n        scope: this.tree.draggableScope\n      });\n    } else {\n      event.preventDefault();\n    }\n  }\n  onDragStop(event) {\n    this.tree.dragDropService.stopDrag({\n      node: this.node,\n      subNodes: this.node?.parent ? this.node.parent.children : this.tree.value,\n      index: this.index\n    });\n  }\n  onDropNodeDragOver(event) {\n    event.dataTransfer.dropEffect = 'move';\n    if (this.tree.droppableNodes) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n  }\n  onDropNode(event) {\n    if (this.tree.droppableNodes && this.node?.droppable !== false) {\n      let dragNode = this.tree.dragNode;\n      if (this.tree.allowDrop(dragNode, this.node, this.tree.dragNodeScope)) {\n        let dropParams = {\n          ...this.createDropNodeEventMetadata()\n        };\n        if (this.tree.validateDrop) {\n          this.tree.onNodeDrop.emit({\n            originalEvent: event,\n            dragNode: dragNode,\n            dropNode: this.node,\n            index: this.index,\n            accept: () => {\n              this.processNodeDrop(dropParams);\n            }\n          });\n        } else {\n          this.processNodeDrop(dropParams);\n          this.tree.onNodeDrop.emit({\n            originalEvent: event,\n            dragNode: dragNode,\n            dropNode: this.node,\n            index: this.index\n          });\n        }\n      }\n    }\n    event.preventDefault();\n    event.stopPropagation();\n    this.draghoverNode = false;\n  }\n  createDropNodeEventMetadata() {\n    return {\n      dragNode: this.tree.dragNode,\n      dragNodeIndex: this.tree.dragNodeIndex,\n      dragNodeSubNodes: this.tree.dragNodeSubNodes,\n      dropNode: this.node\n    };\n  }\n  processNodeDrop(event) {\n    let dragNodeIndex = event.dragNodeIndex;\n    event.dragNodeSubNodes.splice(dragNodeIndex, 1);\n    if (event.dropNode.children) event.dropNode.children.push(event.dragNode);else event.dropNode.children = [event.dragNode];\n    this.tree.dragDropService.stopDrag({\n      node: event.dragNode,\n      subNodes: event.dropNode.parent ? event.dropNode.parent.children : this.tree.value,\n      index: dragNodeIndex\n    });\n  }\n  onDropNodeDragEnter(event) {\n    if (this.tree.droppableNodes && this.node?.droppable !== false && this.tree.allowDrop(this.tree.dragNode, this.node, this.tree.dragNodeScope)) {\n      this.draghoverNode = true;\n    }\n  }\n  onDropNodeDragLeave(event) {\n    if (this.tree.droppableNodes) {\n      let rect = event.currentTarget.getBoundingClientRect();\n      if (event.x > rect.left + rect.width || event.x < rect.left || event.y >= Math.floor(rect.top + rect.height) || event.y < rect.top) {\n        this.draghoverNode = false;\n      }\n    }\n  }\n  onKeyDown(event) {\n    if (!this.isSameNode(event) || this.tree.contextMenu && this.tree.contextMenu.containerViewChild?.nativeElement.style.display === 'block') {\n      return;\n    }\n    switch (event.code) {\n      //down arrow\n      case 'ArrowDown':\n        this.onArrowDown(event);\n        break;\n      //up arrow\n      case 'ArrowUp':\n        this.onArrowUp(event);\n        break;\n      //right arrow\n      case 'ArrowRight':\n        this.onArrowRight(event);\n        break;\n      //left arrow\n      case 'ArrowLeft':\n        this.onArrowLeft(event);\n        break;\n      //enter\n      case 'Enter':\n      case 'Space':\n      case 'NumpadEnter':\n        this.onEnter(event);\n        break;\n      //tab\n      case 'Tab':\n        this.setAllNodesTabIndexes();\n        break;\n      default:\n        //no op\n        break;\n    }\n  }\n  onArrowUp(event) {\n    const nodeElement = event.target.getAttribute('data-pc-section') === 'toggler' ? event.target.closest('[role=\"treeitem\"]') : event.target.parentElement;\n    if (nodeElement.previousElementSibling) {\n      this.focusRowChange(nodeElement, nodeElement.previousElementSibling, this.findLastVisibleDescendant(nodeElement.previousElementSibling));\n    } else {\n      let parentNodeElement = this.getParentNodeElement(nodeElement);\n      if (parentNodeElement) {\n        this.focusRowChange(nodeElement, parentNodeElement);\n      }\n    }\n    event.preventDefault();\n  }\n  onArrowDown(event) {\n    const nodeElement = event.target.getAttribute('data-pc-section') === 'toggler' ? event.target.closest('[role=\"treeitem\"]') : event.target;\n    const listElement = nodeElement.children[1];\n    if (listElement && listElement.children.length > 0) {\n      this.focusRowChange(nodeElement, listElement.children[0]);\n    } else {\n      if (nodeElement.parentElement.nextElementSibling) {\n        this.focusRowChange(nodeElement, nodeElement.parentElement.nextElementSibling);\n      } else {\n        let nextSiblingAncestor = this.findNextSiblingOfAncestor(nodeElement.parentElement);\n        if (nextSiblingAncestor) {\n          this.focusRowChange(nodeElement, nextSiblingAncestor);\n        }\n      }\n    }\n    event.preventDefault();\n  }\n  onArrowRight(event) {\n    if (!this.node?.expanded && !this.tree.isNodeLeaf(this.node)) {\n      this.expand(event);\n      event.currentTarget.tabIndex = -1;\n      setTimeout(() => {\n        this.onArrowDown(event);\n      }, 1);\n    }\n    event.preventDefault();\n  }\n  onArrowLeft(event) {\n    const nodeElement = event.target.getAttribute('data-pc-section') === 'toggler' ? event.target.closest('[role=\"treeitem\"]') : event.target;\n    if (this.level === 0 && !this.node?.expanded) {\n      return false;\n    }\n    if (this.node?.expanded) {\n      this.collapse(event);\n      return;\n    }\n    let parentNodeElement = this.getParentNodeElement(nodeElement.parentElement);\n    if (parentNodeElement) {\n      this.focusRowChange(event.currentTarget, parentNodeElement);\n    }\n    event.preventDefault();\n  }\n  onEnter(event) {\n    this.tree.onNodeClick(event, this.node);\n    this.setTabIndexForSelectionMode(event, this.tree.nodeTouched);\n    event.preventDefault();\n  }\n  setAllNodesTabIndexes() {\n    const nodes = find(this.tree.el.nativeElement, '.p-tree-node');\n    const hasSelectedNode = [...nodes].some(node => node.getAttribute('aria-selected') === 'true' || node.getAttribute('aria-checked') === 'true');\n    [...nodes].forEach(node => {\n      node.tabIndex = -1;\n    });\n    if (hasSelectedNode) {\n      const selectedNodes = [...nodes].filter(node => node.getAttribute('aria-selected') === 'true' || node.getAttribute('aria-checked') === 'true');\n      selectedNodes[0].tabIndex = 0;\n      return;\n    }\n    if (nodes.length) {\n      [...nodes][0].tabIndex = 0;\n    }\n  }\n  setTabIndexForSelectionMode(event, nodeTouched) {\n    if (this.tree.selectionMode !== null) {\n      const elements = [...find(this.tree.el.nativeElement, '[role=\"treeitem\"]')];\n      event.currentTarget.tabIndex = nodeTouched === false ? -1 : 0;\n      if (elements.every(element => element.tabIndex === -1)) {\n        elements[0].tabIndex = 0;\n      }\n    }\n  }\n  findNextSiblingOfAncestor(nodeElement) {\n    let parentNodeElement = this.getParentNodeElement(nodeElement);\n    if (parentNodeElement) {\n      if (parentNodeElement.nextElementSibling) return parentNodeElement.nextElementSibling;else return this.findNextSiblingOfAncestor(parentNodeElement);\n    } else {\n      return null;\n    }\n  }\n  findLastVisibleDescendant(nodeElement) {\n    const listElement = Array.from(nodeElement.children).find(el => hasClass(el, 'p-tree-node'));\n    const childrenListElement = listElement?.children[1];\n    if (childrenListElement && childrenListElement.children.length > 0) {\n      const lastChildElement = childrenListElement.children[childrenListElement.children.length - 1];\n      return this.findLastVisibleDescendant(lastChildElement);\n    } else {\n      return nodeElement;\n    }\n  }\n  getParentNodeElement(nodeElement) {\n    const parentNodeElement = nodeElement.parentElement?.parentElement?.parentElement;\n    return parentNodeElement?.tagName === 'P-TREENODE' ? parentNodeElement : null;\n  }\n  focusNode(element) {\n    if (this.tree.droppableNodes) element.children[1].focus();else element.children[0].focus();\n  }\n  focusRowChange(firstFocusableRow, currentFocusedRow, lastVisibleDescendant) {\n    firstFocusableRow.tabIndex = '-1';\n    currentFocusedRow.children[0].tabIndex = '0';\n    this.focusNode(lastVisibleDescendant || currentFocusedRow);\n  }\n  focusVirtualNode() {\n    this.timeout = setTimeout(() => {\n      let node = findSingle(document.body, `[data-id=\"${this.node?.key ?? this.node?.data}\"]`);\n      focus(node);\n    }, 1);\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵUITreeNode_BaseFactory;\n    return function UITreeNode_Factory(__ngFactoryType__) {\n      return (ɵUITreeNode_BaseFactory || (ɵUITreeNode_BaseFactory = i0.ɵɵgetInheritedFactory(UITreeNode)))(__ngFactoryType__ || UITreeNode);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: UITreeNode,\n    selectors: [[\"p-treeNode\"]],\n    inputs: {\n      rowNode: \"rowNode\",\n      node: \"node\",\n      parentNode: \"parentNode\",\n      root: [2, \"root\", \"root\", booleanAttribute],\n      index: [2, \"index\", \"index\", numberAttribute],\n      firstChild: [2, \"firstChild\", \"firstChild\", booleanAttribute],\n      lastChild: [2, \"lastChild\", \"lastChild\", booleanAttribute],\n      level: [2, \"level\", \"level\", numberAttribute],\n      indentation: [2, \"indentation\", \"indentation\", numberAttribute],\n      itemSize: [2, \"itemSize\", \"itemSize\", numberAttribute],\n      loadingMode: \"loadingMode\"\n    },\n    features: [i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature],\n    decls: 1,\n    vars: 1,\n    consts: [[\"icon\", \"\"], [\"class\", \"p-tree-node-droppoint\", 3, \"ngClass\", \"drop\", \"dragover\", \"dragenter\", \"dragleave\", 4, \"ngIf\"], [\"role\", \"treeitem\", 3, \"keydown\", \"ngClass\", \"ngStyle\"], [3, \"click\", \"contextmenu\", \"dblclick\", \"touchend\", \"drop\", \"dragover\", \"dragenter\", \"dragleave\", \"dragstart\", \"dragend\", \"ngClass\", \"draggable\"], [\"type\", \"button\", \"pRipple\", \"\", \"tabindex\", \"-1\", 1, \"p-tree-node-toggle-button\", 3, \"click\"], [4, \"ngIf\"], [\"class\", \"p-tree-node-toggle-icon\", 4, \"ngIf\"], [\"styleClass\", \"p-tree-node-checkbox\", 3, \"ngModel\", \"binary\", \"indeterminate\", \"disabled\", \"variant\", \"tabindex\", \"click\", 4, \"ngIf\"], [3, \"class\", 4, \"ngIf\"], [1, \"p-tree-node-label\"], [\"class\", \"p-tree-node-children\", \"style\", \"display: none;\", \"role\", \"group\", 3, \"display\", 4, \"ngIf\"], [1, \"p-tree-node-droppoint\", 3, \"drop\", \"dragover\", \"dragenter\", \"dragleave\", \"ngClass\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"styleClass\"], [1, \"p-tree-node-toggle-icon\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"styleClass\", \"p-tree-node-checkbox\", 3, \"click\", \"ngModel\", \"binary\", \"indeterminate\", \"disabled\", \"variant\", \"tabindex\"], [\"role\", \"group\", 1, \"p-tree-node-children\", 2, \"display\", \"none\"], [3, \"node\", \"parentNode\", \"firstChild\", \"lastChild\", \"index\", \"itemSize\", \"level\", \"loadingMode\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [3, \"node\", \"parentNode\", \"firstChild\", \"lastChild\", \"index\", \"itemSize\", \"level\", \"loadingMode\"]],\n    template: function UITreeNode_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, UITreeNode_Conditional_0_Template, 13, 31);\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(ctx.node ? 0 : -1);\n      }\n    },\n    dependencies: [UITreeNode, CommonModule, i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, Ripple, Checkbox, FormsModule, i2.NgControlStatus, i2.NgModel, ChevronRightIcon, ChevronDownIcon, SpinnerIcon, SharedModule],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UITreeNode, [{\n    type: Component,\n    args: [{\n      selector: 'p-treeNode',\n      standalone: true,\n      imports: [CommonModule, Ripple, Checkbox, FormsModule, ChevronRightIcon, ChevronDownIcon, SpinnerIcon, SharedModule],\n      template: `\n        @if (node) {\n            <li\n                *ngIf=\"tree.droppableNodes\"\n                class=\"p-tree-node-droppoint\"\n                [attr.aria-hidden]=\"true\"\n                [ngClass]=\"{ 'p-tree-node-droppoint-active': draghoverPrev }\"\n                (drop)=\"onDropPoint($event, -1)\"\n                (dragover)=\"onDropPointDragOver($event)\"\n                (dragenter)=\"onDropPointDragEnter($event, -1)\"\n                (dragleave)=\"onDropPointDragLeave($event)\"\n            ></li>\n            <li\n                [ngClass]=\"nodeClass\"\n                [class]=\"node.styleClass\"\n                [ngStyle]=\"{ height: itemSize + 'px' }\"\n                [style]=\"node.style\"\n                [attr.aria-label]=\"node.label\"\n                [attr.aria-checked]=\"checked\"\n                [attr.aria-setsize]=\"node.children ? node.children.length : 0\"\n                [attr.aria-selected]=\"selected\"\n                [attr.aria-expanded]=\"node.expanded\"\n                [attr.aria-posinset]=\"index + 1\"\n                [attr.aria-level]=\"level + 1\"\n                [attr.tabindex]=\"index === 0 ? 0 : -1\"\n                [attr.data-id]=\"node.key\"\n                role=\"treeitem\"\n                (keydown)=\"onKeyDown($event)\"\n            >\n                <div\n                    [ngClass]=\"nodeContentClass\"\n                    [style.paddingLeft]=\"level * indentation + 'rem'\"\n                    (click)=\"onNodeClick($event)\"\n                    (contextmenu)=\"onNodeRightClick($event)\"\n                    (dblclick)=\"onNodeDblClick($event)\"\n                    (touchend)=\"onNodeTouchEnd()\"\n                    (drop)=\"onDropNode($event)\"\n                    (dragover)=\"onDropNodeDragOver($event)\"\n                    (dragenter)=\"onDropNodeDragEnter($event)\"\n                    (dragleave)=\"onDropNodeDragLeave($event)\"\n                    [draggable]=\"tree.draggableNodes\"\n                    (dragstart)=\"onDragStart($event)\"\n                    (dragend)=\"onDragStop($event)\"\n                >\n                    <button type=\"button\" [attr.data-pc-section]=\"'toggler'\" class=\"p-tree-node-toggle-button\" (click)=\"toggle($event)\" pRipple tabindex=\"-1\">\n                        <ng-container *ngIf=\"!tree.togglerIconTemplate && !tree._togglerIconTemplate\">\n                            <ng-container *ngIf=\"!node.loading\">\n                                <ChevronRightIcon *ngIf=\"!node.expanded\" [styleClass]=\"'p-tree-node-toggle-icon'\" />\n                                <ChevronDownIcon *ngIf=\"node.expanded\" [styleClass]=\"'p-tree-node-toggle-icon'\" />\n                            </ng-container>\n                            <ng-container *ngIf=\"loadingMode === 'icon' && node.loading\">\n                                <SpinnerIcon [styleClass]=\"'pi-spin p-tree-node-toggle-icon'\" />\n                            </ng-container>\n                        </ng-container>\n                        <span *ngIf=\"tree.togglerIconTemplate || tree._togglerIconTemplate\" class=\"p-tree-node-toggle-icon\">\n                            <ng-template *ngTemplateOutlet=\"tree.togglerIconTemplate || tree._togglerIconTemplate; context: { $implicit: node.expanded, loading: node.loading }\"></ng-template>\n                        </span>\n                    </button>\n\n                    <p-checkbox\n                        [ngModel]=\"isSelected()\"\n                        styleClass=\"p-tree-node-checkbox\"\n                        [binary]=\"true\"\n                        [indeterminate]=\"node.partialSelected\"\n                        *ngIf=\"tree.selectionMode == 'checkbox'\"\n                        [disabled]=\"node.selectable === false\"\n                        [variant]=\"tree?.config.inputStyle() === 'filled' || tree?.config.inputVariant() === 'filled' ? 'filled' : 'outlined'\"\n                        [attr.data-p-partialchecked]=\"node.partialSelected\"\n                        [tabindex]=\"-1\"\n                        (click)=\"$event.preventDefault()\"\n                    >\n                        <ng-container *ngIf=\"tree.checkboxIconTemplate || tree._checkboxIconTemplate\">\n                            <ng-template #icon>\n                                <ng-template\n                                    *ngTemplateOutlet=\"\n                                        tree.checkboxIconTemplate || tree._checkboxIconTemplate;\n                                        context: {\n                                            $implicit: isSelected(),\n                                            partialSelected: node.partialSelected,\n                                            class: 'p-tree-node-checkbox'\n                                        }\n                                    \"\n                                ></ng-template>\n                            </ng-template>\n                        </ng-container>\n                    </p-checkbox>\n\n                    <span [class]=\"getIcon()\" *ngIf=\"node.icon || node.expandedIcon || node.collapsedIcon\"></span>\n                    <span class=\"p-tree-node-label\">\n                        <span *ngIf=\"!tree.getTemplateForNode(node)\">{{ node.label }}</span>\n                        <span *ngIf=\"tree.getTemplateForNode(node)\">\n                            <ng-container *ngTemplateOutlet=\"tree.getTemplateForNode(node); context: { $implicit: node }\"></ng-container>\n                        </span>\n                    </span>\n                </div>\n                <ul class=\"p-tree-node-children\" style=\"display: none;\" *ngIf=\"!tree.virtualScroll && node.children && node.expanded\" [style.display]=\"node.expanded ? 'flex' : 'none'\" role=\"group\">\n                    <p-treeNode\n                        *ngFor=\"let childNode of node.children; let firstChild = first; let lastChild = last; let index = index; trackBy: tree.trackBy.bind(this)\"\n                        [node]=\"childNode\"\n                        [parentNode]=\"node\"\n                        [firstChild]=\"firstChild\"\n                        [lastChild]=\"lastChild\"\n                        [index]=\"index\"\n                        [itemSize]=\"itemSize\"\n                        [level]=\"level + 1\"\n                        [loadingMode]=\"loadingMode\"\n                    ></p-treeNode>\n                </ul>\n            </li>\n\n            <li\n                *ngIf=\"tree.droppableNodes && lastChild\"\n                class=\"p-tree-node-droppoint\"\n                [ngClass]=\"{ 'p-tree-node-droppoint-active': draghoverNext }\"\n                (drop)=\"onDropPoint($event, 1)\"\n                [attr.aria-hidden]=\"true\"\n                (dragover)=\"onDropPointDragOver($event)\"\n                (dragenter)=\"onDropPointDragEnter($event, 1)\"\n                (dragleave)=\"onDropPointDragLeave($event)\"\n            ></li>\n        }\n    `,\n      encapsulation: ViewEncapsulation.None\n    }]\n  }], null, {\n    rowNode: [{\n      type: Input\n    }],\n    node: [{\n      type: Input\n    }],\n    parentNode: [{\n      type: Input\n    }],\n    root: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    index: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    firstChild: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    lastChild: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    level: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    indentation: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    itemSize: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    loadingMode: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Tree is used to display hierarchical data.\n * @group Components\n */\nclass Tree extends BaseComponent {\n  dragDropService;\n  /**\n   * An array of treenodes.\n   * @group Props\n   */\n  value;\n  /**\n   * Defines the selection mode.\n   * @group Props\n   */\n  selectionMode;\n  /**\n   * Loading mode display.\n   * @group Props\n   */\n  loadingMode = 'mask';\n  /**\n   * A single treenode instance or an array to refer to the selections.\n   * @group Props\n   */\n  selection;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Context menu instance.\n   * @group Props\n   */\n  contextMenu;\n  /**\n   * Scope of the draggable nodes to match a droppableScope.\n   * @group Props\n   */\n  draggableScope;\n  /**\n   * Scope of the droppable nodes to match a draggableScope.\n   * @group Props\n   */\n  droppableScope;\n  /**\n   * Whether the nodes are draggable.\n   * @group Props\n   */\n  draggableNodes;\n  /**\n   * Whether the nodes are droppable.\n   * @group Props\n   */\n  droppableNodes;\n  /**\n   * Defines how multiple items can be selected, when true metaKey needs to be pressed to select or unselect an item and when set to false selection of each item can be toggled individually. On touch enabled devices, metaKeySelection is turned off automatically.\n   * @group Props\n   */\n  metaKeySelection = false;\n  /**\n   * Whether checkbox selections propagate to ancestor nodes.\n   * @group Props\n   */\n  propagateSelectionUp = true;\n  /**\n   * Whether checkbox selections propagate to descendant nodes.\n   * @group Props\n   */\n  propagateSelectionDown = true;\n  /**\n   * Displays a loader to indicate data load is in progress.\n   * @group Props\n   */\n  loading;\n  /**\n   * The icon to show while indicating data load is in progress.\n   * @group Props\n   */\n  loadingIcon;\n  /**\n   * Text to display when there is no data.\n   * @group Props\n   */\n  emptyMessage = '';\n  /**\n   * Used to define a string that labels the tree.\n   * @group Props\n   */\n  ariaLabel;\n  /**\n   * Defines a string that labels the toggler icon for accessibility.\n   * @group Props\n   */\n  togglerAriaLabel;\n  /**\n   * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n   * @group Props\n   */\n  ariaLabelledBy;\n  /**\n   * When enabled, drop can be accepted or rejected based on condition defined at onNodeDrop.\n   * @group Props\n   */\n  validateDrop;\n  /**\n   * When specified, displays an input field to filter the items.\n   * @group Props\n   */\n  filter;\n  /**\n   * Determines whether the filter input should be automatically focused when the component is rendered.\n   * @group Props\n   */\n  filterInputAutoFocus = false;\n  /**\n   * When filtering is enabled, filterBy decides which field or fields (comma separated) to search against.\n   * @group Props\n   */\n  filterBy = 'label';\n  /**\n   * Mode for filtering valid values are \"lenient\" and \"strict\". Default is lenient.\n   * @group Props\n   */\n  filterMode = 'lenient';\n  /**\n   * Mode for filtering valid values are \"lenient\" and \"strict\". Default is lenient.\n   * @group Props\n   */\n  filterOptions;\n  /**\n   * Placeholder text to show when filter input is empty.\n   * @group Props\n   */\n  filterPlaceholder;\n  /**\n   * Values after the tree nodes are filtered.\n   * @group Props\n   */\n  filteredNodes;\n  /**\n   * Locale to use in filtering. The default locale is the host environment's current locale.\n   * @group Props\n   */\n  filterLocale;\n  /**\n   * Height of the scrollable viewport.\n   * @group Props\n   */\n  scrollHeight;\n  /**\n   * Defines if data is loaded and interacted with in lazy manner.\n   * @group Props\n   */\n  lazy = false;\n  /**\n   * Whether the data should be loaded on demand during scroll.\n   * @group Props\n   */\n  virtualScroll;\n  /**\n   * Height of an item in the list for VirtualScrolling.\n   * @group Props\n   */\n  virtualScrollItemSize;\n  /**\n   * Whether to use the scroller feature. The properties of scroller component can be used like an object in it.\n   * @group Props\n   */\n  virtualScrollOptions;\n  /**\n   * Indentation factor for spacing of the nested node when virtual scrolling is enabled.\n   * @group Props\n   */\n  indentation = 1.5;\n  /**\n   * Custom templates of the component.\n   * @group Props\n   */\n  _templateMap;\n  /**\n   * Function to optimize the node list rendering, default algorithm checks for object identity.\n   * @group Props\n   */\n  trackBy = (index, item) => item;\n  /**\n   * Highlights the node on select.\n   * @group Props\n   */\n  highlightOnSelect = false;\n  /**\n   * Height of the node.\n   * @group Props\n   * @deprecated use virtualScrollItemSize property instead.\n   */\n  _virtualNodeHeight;\n  get virtualNodeHeight() {\n    return this._virtualNodeHeight;\n  }\n  set virtualNodeHeight(val) {\n    this._virtualNodeHeight = val;\n    console.log('The virtualNodeHeight property is deprecated, use virtualScrollItemSize property instead.');\n  }\n  /**\n   * Callback to invoke on selection change.\n   * @param {(TreeNode<any> | TreeNode<any>[] | null)} event - Custom selection change event.\n   * @group Emits\n   */\n  selectionChange = new EventEmitter();\n  /**\n   * Callback to invoke when a node is selected.\n   * @param {TreeNodeSelectEvent} event - Node select event.\n   * @group Emits\n   */\n  onNodeSelect = new EventEmitter();\n  /**\n   * Callback to invoke when a node is unselected.\n   * @param {TreeNodeUnSelectEvent} event - Node unselect event.\n   * @group Emits\n   */\n  onNodeUnselect = new EventEmitter();\n  /**\n   * Callback to invoke when a node is expanded.\n   * @param {TreeNodeExpandEvent} event - Node expand event.\n   * @group Emits\n   */\n  onNodeExpand = new EventEmitter();\n  /**\n   * Callback to invoke when a node is collapsed.\n   * @param {TreeNodeCollapseEvent} event - Node collapse event.\n   * @group Emits\n   */\n  onNodeCollapse = new EventEmitter();\n  /**\n   * Callback to invoke when a node is selected with right click.\n   * @param {onNodeContextMenuSelect} event - Node context menu select event.\n   * @group Emits\n   */\n  onNodeContextMenuSelect = new EventEmitter();\n  /**\n   * Callback to invoke when a node is double clicked.\n   * @param {TreeNodeDoubleClickEvent} event - Node double click event.\n   * @group Emits\n   */\n  onNodeDoubleClick = new EventEmitter();\n  /**\n   * Callback to invoke when a node is dropped.\n   * @param {TreeNodeDropEvent} event - Node drop event.\n   * @group Emits\n   */\n  onNodeDrop = new EventEmitter();\n  /**\n   * Callback to invoke in lazy mode to load new data.\n   * @param {TreeLazyLoadEvent} event - Custom lazy load event.\n   * @group Emits\n   */\n  onLazyLoad = new EventEmitter();\n  /**\n   * Callback to invoke in virtual scroll mode when scroll position changes.\n   * @param {TreeScrollEvent} event - Custom scroll event.\n   * @group Emits\n   */\n  onScroll = new EventEmitter();\n  /**\n   * Callback to invoke in virtual scroll mode when scroll position and item's range in view changes.\n   * @param {TreeScrollIndexChangeEvent} event - Scroll index change event.\n   * @group Emits\n   */\n  onScrollIndexChange = new EventEmitter();\n  /**\n   * Callback to invoke when data is filtered.\n   * @param {TreeFilterEvent} event - Custom filter event.\n   * @group Emits\n   */\n  onFilter = new EventEmitter();\n  /**\n   * Filter template.\n   * @group Templates\n   */\n  filterTemplate;\n  /**\n   * Node template.\n   * @group Templates\n   */\n  nodeTemplate;\n  /**\n   * Header template.\n   * @group Templates\n   */\n  headerTemplate;\n  /**\n   * Footer template.\n   * @group Templates\n   */\n  footerTemplate;\n  /**\n   * Loader template.\n   * @group Templates\n   */\n  loaderTemplate;\n  /**\n   * Empty message template.\n   * @group Templates\n   */\n  emptyMessageTemplate;\n  /**\n   * Toggler icon template.\n   * @group Templates\n   */\n  togglerIconTemplate;\n  /**\n   * Checkbox icon template.\n   * @group Templates\n   */\n  checkboxIconTemplate;\n  /**\n   * Loading icon template.\n   * @group Templates\n   */\n  loadingIconTemplate;\n  /**\n   * Filter icon template.\n   * @group Templates\n   */\n  filterIconTemplate;\n  filterViewChild;\n  scroller;\n  wrapperViewChild;\n  templates;\n  _headerTemplate;\n  _emptyMessageTemplate;\n  _footerTemplate;\n  _loaderTemplate;\n  _togglerIconTemplate;\n  _checkboxIconTemplate;\n  _loadingIconTemplate;\n  _filterIconTemplate;\n  _filterTemplate;\n  ngAfterContentInit() {\n    if (this.templates.length) {\n      this._templateMap = {};\n    }\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'header':\n          this._headerTemplate = item.template;\n          break;\n        case 'empty':\n          this._emptyMessageTemplate = item.template;\n          break;\n        case 'footer':\n          this._footerTemplate = item.template;\n          break;\n        case 'loader':\n          this._loaderTemplate = item.template;\n          break;\n        case 'togglericon':\n          this._togglerIconTemplate = item.template;\n          break;\n        case 'checkboxicon':\n          this._checkboxIconTemplate = item.template;\n          break;\n        case 'loadingicon':\n          this._loadingIconTemplate = item.template;\n          break;\n        case 'filtericon':\n          this._filterIconTemplate = item.template;\n          break;\n        case 'filter':\n          this._filterTemplate = item.template;\n          break;\n        default:\n          this._templateMap[item.name] = item.template;\n          break;\n      }\n    });\n  }\n  serializedValue;\n  nodeTouched;\n  dragNodeTree;\n  dragNode;\n  dragNodeSubNodes;\n  dragNodeIndex;\n  dragNodeScope;\n  dragHover;\n  dragStartSubscription;\n  dragStopSubscription;\n  _componentStyle = inject(TreeStyle);\n  constructor(dragDropService) {\n    super();\n    this.dragDropService = dragDropService;\n  }\n  ngOnInit() {\n    super.ngOnInit();\n    if (this.filterBy) {\n      this.filterOptions = {\n        filter: value => this._filter(value),\n        reset: () => this.resetFilter()\n      };\n    }\n    if (this.droppableNodes) {\n      this.dragStartSubscription = this.dragDropService.dragStart$.subscribe(event => {\n        this.dragNodeTree = event.tree;\n        this.dragNode = event.node;\n        this.dragNodeSubNodes = event.subNodes;\n        this.dragNodeIndex = event.index;\n        this.dragNodeScope = event.scope;\n      });\n      this.dragStopSubscription = this.dragDropService.dragStop$.subscribe(event => {\n        this.dragNodeTree = null;\n        this.dragNode = null;\n        this.dragNodeSubNodes = null;\n        this.dragNodeIndex = null;\n        this.dragNodeScope = null;\n        this.dragHover = false;\n      });\n    }\n  }\n  ngOnChanges(simpleChange) {\n    super.ngOnChanges(simpleChange);\n    if (simpleChange.value) {\n      this.updateSerializedValue();\n      if (this.hasFilterActive()) {\n        this._filter(this.filterViewChild.nativeElement.value);\n      }\n    }\n  }\n  get containerClass() {\n    return this._componentStyle.classes.root({\n      instance: this\n    });\n  }\n  get emptyMessageLabel() {\n    return this.emptyMessage || this.config.getTranslation(TranslationKeys.EMPTY_MESSAGE);\n  }\n  updateSerializedValue() {\n    this.serializedValue = [];\n    this.serializeNodes(null, this.getRootNode(), 0, true);\n  }\n  serializeNodes(parent, nodes, level, visible) {\n    if (nodes && nodes.length) {\n      for (let node of nodes) {\n        node.parent = parent;\n        const rowNode = {\n          node: node,\n          parent: parent,\n          level: level,\n          visible: visible && (parent ? parent.expanded : true)\n        };\n        this.serializedValue.push(rowNode);\n        if (rowNode.visible && node.expanded) {\n          this.serializeNodes(node, node.children, level + 1, rowNode.visible);\n        }\n      }\n    }\n  }\n  onNodeClick(event, node) {\n    let eventTarget = event.target;\n    if (hasClass(eventTarget, 'p-tree-toggler') || hasClass(eventTarget, 'p-tree-toggler-icon')) {\n      return;\n    } else if (this.selectionMode) {\n      if (node.selectable === false) {\n        node.style = '--p-focus-ring-color: none;';\n        return;\n      } else {\n        if (!node.style?.includes('--p-focus-ring-color')) {\n          node.style = node.style ? `${node.style}--p-focus-ring-color: var(--primary-color)` : '--p-focus-ring-color: var(--primary-color)';\n        }\n      }\n      if (this.hasFilteredNodes()) {\n        node = this.getNodeWithKey(node.key, this.filteredNodes);\n        if (!node) {\n          return;\n        }\n      }\n      let index = this.findIndexInSelection(node);\n      let selected = index >= 0;\n      if (this.isCheckboxSelectionMode()) {\n        if (selected) {\n          if (this.propagateSelectionDown) this.propagateDown(node, false);else this.selection = this.selection.filter((val, i) => i != index);\n          if (this.propagateSelectionUp && node.parent) {\n            this.propagateUp(node.parent, false);\n          }\n          this.selectionChange.emit(this.selection);\n          this.onNodeUnselect.emit({\n            originalEvent: event,\n            node: node\n          });\n        } else {\n          if (this.propagateSelectionDown) this.propagateDown(node, true);else this.selection = [...(this.selection || []), node];\n          if (this.propagateSelectionUp && node.parent) {\n            this.propagateUp(node.parent, true);\n          }\n          this.selectionChange.emit(this.selection);\n          this.onNodeSelect.emit({\n            originalEvent: event,\n            node: node\n          });\n        }\n      } else {\n        let metaSelection = this.nodeTouched ? false : this.metaKeySelection;\n        if (metaSelection) {\n          let metaKey = event.metaKey || event.ctrlKey;\n          if (selected && metaKey) {\n            if (this.isSingleSelectionMode()) {\n              this.selectionChange.emit(null);\n            } else {\n              this.selection = this.selection.filter((val, i) => i != index);\n              this.selectionChange.emit(this.selection);\n            }\n            this.onNodeUnselect.emit({\n              originalEvent: event,\n              node: node\n            });\n          } else {\n            if (this.isSingleSelectionMode()) {\n              this.selectionChange.emit(node);\n            } else if (this.isMultipleSelectionMode()) {\n              this.selection = !metaKey ? [] : this.selection || [];\n              this.selection = [...this.selection, node];\n              this.selectionChange.emit(this.selection);\n            }\n            this.onNodeSelect.emit({\n              originalEvent: event,\n              node: node\n            });\n          }\n        } else {\n          if (this.isSingleSelectionMode()) {\n            if (selected) {\n              this.selection = null;\n              this.onNodeUnselect.emit({\n                originalEvent: event,\n                node: node\n              });\n            } else {\n              this.selection = node;\n              setTimeout(() => {\n                this.onNodeSelect.emit({\n                  originalEvent: event,\n                  node: node\n                });\n              });\n            }\n          } else {\n            if (selected) {\n              this.selection = this.selection.filter((val, i) => i != index);\n              this.onNodeUnselect.emit({\n                originalEvent: event,\n                node: node\n              });\n            } else {\n              this.selection = [...(this.selection || []), node];\n              setTimeout(() => {\n                this.onNodeSelect.emit({\n                  originalEvent: event,\n                  node: node\n                });\n              });\n            }\n          }\n          this.selectionChange.emit(this.selection);\n        }\n      }\n    }\n    this.nodeTouched = false;\n  }\n  onNodeTouchEnd() {\n    this.nodeTouched = true;\n  }\n  onNodeRightClick(event, node) {\n    if (this.contextMenu) {\n      let eventTarget = event.target;\n      if (eventTarget.className && eventTarget.className.indexOf('p-tree-toggler') === 0) {\n        return;\n      } else {\n        let index = this.findIndexInSelection(node);\n        let selected = index >= 0;\n        if (!selected) {\n          if (this.isSingleSelectionMode()) this.selectionChange.emit(node);else this.selectionChange.emit([node]);\n        }\n        this.contextMenu.show(event);\n        this.onNodeContextMenuSelect.emit({\n          originalEvent: event,\n          node: node\n        });\n      }\n    }\n  }\n  onNodeDblClick(event, node) {\n    this.onNodeDoubleClick.emit({\n      originalEvent: event,\n      node: node\n    });\n  }\n  findIndexInSelection(node) {\n    let index = -1;\n    if (this.selectionMode && this.selection) {\n      if (this.isSingleSelectionMode()) {\n        let areNodesEqual = this.selection.key && this.selection.key === node.key || this.selection == node;\n        index = areNodesEqual ? 0 : -1;\n      } else {\n        for (let i = 0; i < this.selection.length; i++) {\n          let selectedNode = this.selection[i];\n          let areNodesEqual = selectedNode.key && selectedNode.key === node.key || selectedNode == node;\n          if (areNodesEqual) {\n            index = i;\n            break;\n          }\n        }\n      }\n    }\n    return index;\n  }\n  syncNodeOption(node, parentNodes, option, value) {\n    // to synchronize the node option between the filtered nodes and the original nodes(this.value)\n    const _node = this.hasFilteredNodes() ? this.getNodeWithKey(node.key, parentNodes) : null;\n    if (_node) {\n      _node[option] = value || node[option];\n    }\n  }\n  hasFilteredNodes() {\n    return this.filter && this.filteredNodes && this.filteredNodes.length;\n  }\n  hasFilterActive() {\n    return this.filter && this.filterViewChild?.nativeElement?.value.length > 0;\n  }\n  getNodeWithKey(key, nodes) {\n    for (let node of nodes) {\n      if (node.key === key) {\n        return node;\n      }\n      if (node.children) {\n        let matchedNode = this.getNodeWithKey(key, node.children);\n        if (matchedNode) {\n          return matchedNode;\n        }\n      }\n    }\n  }\n  propagateUp(node, select) {\n    if (node.children && node.children.length) {\n      let selectedCount = 0;\n      let childPartialSelected = false;\n      for (let child of node.children) {\n        if (this.isSelected(child)) {\n          selectedCount++;\n        } else if (child.partialSelected) {\n          childPartialSelected = true;\n        }\n      }\n      if (select && selectedCount == node.children.length) {\n        this.selection = [...(this.selection || []), node];\n        node.partialSelected = false;\n      } else {\n        if (!select) {\n          let index = this.findIndexInSelection(node);\n          if (index >= 0) {\n            this.selection = this.selection.filter((val, i) => i != index);\n          }\n        }\n        if (childPartialSelected || selectedCount > 0 && selectedCount != node.children.length) node.partialSelected = true;else node.partialSelected = false;\n      }\n      this.syncNodeOption(node, this.filteredNodes, 'partialSelected');\n    }\n    let parent = node.parent;\n    if (parent) {\n      this.propagateUp(parent, select);\n    }\n  }\n  propagateDown(node, select) {\n    let index = this.findIndexInSelection(node);\n    if (select && index == -1) {\n      this.selection = [...(this.selection || []), node];\n    } else if (!select && index > -1) {\n      this.selection = this.selection.filter((val, i) => i != index);\n    }\n    node.partialSelected = false;\n    this.syncNodeOption(node, this.filteredNodes, 'partialSelected');\n    if (node.children && node.children.length) {\n      for (let child of node.children) {\n        this.propagateDown(child, select);\n      }\n    }\n  }\n  isSelected(node) {\n    return this.findIndexInSelection(node) != -1;\n  }\n  isSingleSelectionMode() {\n    return this.selectionMode && this.selectionMode == 'single';\n  }\n  isMultipleSelectionMode() {\n    return this.selectionMode && this.selectionMode == 'multiple';\n  }\n  isCheckboxSelectionMode() {\n    return this.selectionMode && this.selectionMode == 'checkbox';\n  }\n  isNodeLeaf(node) {\n    return node.leaf == false ? false : !(node.children && node.children.length);\n  }\n  getRootNode() {\n    return this.filteredNodes ? this.filteredNodes : this.value;\n  }\n  getTemplateForNode(node) {\n    if (this._templateMap) return node.type ? this._templateMap[node.type] : this._templateMap['default'];else return null;\n  }\n  onDragOver(event) {\n    if (this.droppableNodes && (!this.value || this.value.length === 0)) {\n      event.dataTransfer.dropEffect = 'move';\n      event.preventDefault();\n    }\n  }\n  onDrop(event) {\n    if (this.droppableNodes && (!this.value || this.value.length === 0)) {\n      event.preventDefault();\n      let dragNode = this.dragNode;\n      if (this.allowDrop(dragNode, null, this.dragNodeScope)) {\n        let dragNodeIndex = this.dragNodeIndex;\n        this.value = this.value || [];\n        if (this.validateDrop) {\n          this.onNodeDrop.emit({\n            originalEvent: event,\n            dragNode: dragNode,\n            dropNode: null,\n            index: dragNodeIndex,\n            accept: () => {\n              this.processTreeDrop(dragNode, dragNodeIndex);\n            }\n          });\n        } else {\n          this.onNodeDrop.emit({\n            originalEvent: event,\n            dragNode: dragNode,\n            dropNode: null,\n            index: dragNodeIndex\n          });\n          this.processTreeDrop(dragNode, dragNodeIndex);\n        }\n      }\n    }\n  }\n  processTreeDrop(dragNode, dragNodeIndex) {\n    this.dragNodeSubNodes.splice(dragNodeIndex, 1);\n    this.value.push(dragNode);\n    this.dragDropService.stopDrag({\n      node: dragNode\n    });\n  }\n  onDragEnter() {\n    if (this.droppableNodes && this.allowDrop(this.dragNode, null, this.dragNodeScope)) {\n      this.dragHover = true;\n    }\n  }\n  onDragLeave(event) {\n    if (this.droppableNodes) {\n      let rect = event.currentTarget.getBoundingClientRect();\n      if (event.x > rect.left + rect.width || event.x < rect.left || event.y > rect.top + rect.height || event.y < rect.top) {\n        this.dragHover = false;\n      }\n    }\n  }\n  allowDrop(dragNode, dropNode, dragNodeScope) {\n    if (!dragNode) {\n      //prevent random html elements to be dragged\n      return false;\n    } else if (this.isValidDragScope(dragNodeScope)) {\n      let allow = true;\n      if (dropNode) {\n        if (dragNode === dropNode) {\n          allow = false;\n        } else {\n          let parent = dropNode.parent;\n          while (parent != null) {\n            if (parent === dragNode) {\n              allow = false;\n              break;\n            }\n            parent = parent.parent;\n          }\n        }\n      }\n      return allow;\n    } else {\n      return false;\n    }\n  }\n  isValidDragScope(dragScope) {\n    let dropScope = this.droppableScope;\n    if (dropScope) {\n      if (typeof dropScope === 'string') {\n        if (typeof dragScope === 'string') return dropScope === dragScope;else if (Array.isArray(dragScope)) return dragScope.indexOf(dropScope) != -1;\n      } else if (Array.isArray(dropScope)) {\n        if (typeof dragScope === 'string') {\n          return dropScope.indexOf(dragScope) != -1;\n        } else if (Array.isArray(dragScope)) {\n          for (let s of dropScope) {\n            for (let ds of dragScope) {\n              if (s === ds) {\n                return true;\n              }\n            }\n          }\n        }\n      }\n      return false;\n    } else {\n      return true;\n    }\n  }\n  _filter(value) {\n    let filterValue = value;\n    if (filterValue === '') {\n      this.filteredNodes = null;\n    } else {\n      this.filteredNodes = [];\n      const searchFields = this.filterBy.split(',');\n      const filterText = removeAccents(filterValue).toLocaleLowerCase(this.filterLocale);\n      const isStrictMode = this.filterMode === 'strict';\n      for (let node of this.value) {\n        let copyNode = {\n          ...node\n        };\n        let paramsWithoutNode = {\n          searchFields,\n          filterText,\n          isStrictMode\n        };\n        if (isStrictMode && (this.findFilteredNodes(copyNode, paramsWithoutNode) || this.isFilterMatched(copyNode, paramsWithoutNode)) || !isStrictMode && (this.isFilterMatched(copyNode, paramsWithoutNode) || this.findFilteredNodes(copyNode, paramsWithoutNode))) {\n          this.filteredNodes.push(copyNode);\n        }\n      }\n    }\n    this.updateSerializedValue();\n    this.onFilter.emit({\n      filter: filterValue,\n      filteredValue: this.filteredNodes\n    });\n  }\n  /**\n   * Resets filter.\n   * @group Method\n   */\n  resetFilter() {\n    this.filteredNodes = null;\n    if (this.filterViewChild && this.filterViewChild.nativeElement) {\n      this.filterViewChild.nativeElement.value = '';\n    }\n  }\n  /**\n   * Scrolls to virtual index.\n   * @param {number} number - Index to be scrolled.\n   * @group Method\n   */\n  scrollToVirtualIndex(index) {\n    this.virtualScroll && this.scroller?.scrollToIndex(index);\n  }\n  /**\n   * Scrolls to virtual index.\n   * @param {ScrollToOptions} options - Scroll options.\n   * @group Method\n   */\n  scrollTo(options) {\n    if (this.virtualScroll) {\n      this.scroller?.scrollTo(options);\n    } else if (this.wrapperViewChild && this.wrapperViewChild.nativeElement) {\n      if (this.wrapperViewChild.nativeElement.scrollTo) {\n        this.wrapperViewChild.nativeElement.scrollTo(options);\n      } else {\n        this.wrapperViewChild.nativeElement.scrollLeft = options.left;\n        this.wrapperViewChild.nativeElement.scrollTop = options.top;\n      }\n    }\n  }\n  findFilteredNodes(node, paramsWithoutNode) {\n    if (node) {\n      let matched = false;\n      if (node.children) {\n        let childNodes = [...node.children];\n        node.children = [];\n        for (let childNode of childNodes) {\n          let copyChildNode = {\n            ...childNode\n          };\n          if (this.isFilterMatched(copyChildNode, paramsWithoutNode)) {\n            matched = true;\n            node.children.push(copyChildNode);\n          }\n        }\n      }\n      if (matched) {\n        node.expanded = true;\n        return true;\n      }\n    }\n  }\n  isFilterMatched(node, params) {\n    let {\n      searchFields,\n      filterText,\n      isStrictMode\n    } = params;\n    let matched = false;\n    for (let field of searchFields) {\n      let fieldValue = removeAccents(String(resolveFieldData(node, field))).toLocaleLowerCase(this.filterLocale);\n      if (fieldValue.indexOf(filterText) > -1) {\n        matched = true;\n      }\n    }\n    if (!matched || isStrictMode && !this.isNodeLeaf(node)) {\n      matched = this.findFilteredNodes(node, {\n        searchFields,\n        filterText,\n        isStrictMode\n      }) || matched;\n    }\n    return matched;\n  }\n  getIndex(options, index) {\n    const getItemOptions = options['getItemOptions'];\n    return getItemOptions ? getItemOptions(index).index : index;\n  }\n  getBlockableElement() {\n    return this.el.nativeElement.children[0];\n  }\n  ngOnDestroy() {\n    if (this.dragStartSubscription) {\n      this.dragStartSubscription.unsubscribe();\n    }\n    if (this.dragStopSubscription) {\n      this.dragStopSubscription.unsubscribe();\n    }\n    super.ngOnDestroy();\n  }\n  static ɵfac = function Tree_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Tree)(i0.ɵɵdirectiveInject(i3.TreeDragDropService, 8));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Tree,\n    selectors: [[\"p-tree\"]],\n    contentQueries: function Tree_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, _c5, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c6, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c7, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c8, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c9, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c10, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c11, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c12, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c13, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c14, 4);\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.filterTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nodeTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.loaderTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.emptyMessageTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.togglerIconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.checkboxIconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.loadingIconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.filterIconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function Tree_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c5, 5);\n        i0.ɵɵviewQuery(_c15, 5);\n        i0.ɵɵviewQuery(_c16, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.filterViewChild = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scroller = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.wrapperViewChild = _t.first);\n      }\n    },\n    inputs: {\n      value: \"value\",\n      selectionMode: \"selectionMode\",\n      loadingMode: \"loadingMode\",\n      selection: \"selection\",\n      style: \"style\",\n      styleClass: \"styleClass\",\n      contextMenu: \"contextMenu\",\n      draggableScope: \"draggableScope\",\n      droppableScope: \"droppableScope\",\n      draggableNodes: [2, \"draggableNodes\", \"draggableNodes\", booleanAttribute],\n      droppableNodes: [2, \"droppableNodes\", \"droppableNodes\", booleanAttribute],\n      metaKeySelection: [2, \"metaKeySelection\", \"metaKeySelection\", booleanAttribute],\n      propagateSelectionUp: [2, \"propagateSelectionUp\", \"propagateSelectionUp\", booleanAttribute],\n      propagateSelectionDown: [2, \"propagateSelectionDown\", \"propagateSelectionDown\", booleanAttribute],\n      loading: [2, \"loading\", \"loading\", booleanAttribute],\n      loadingIcon: \"loadingIcon\",\n      emptyMessage: \"emptyMessage\",\n      ariaLabel: \"ariaLabel\",\n      togglerAriaLabel: \"togglerAriaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\",\n      validateDrop: [2, \"validateDrop\", \"validateDrop\", booleanAttribute],\n      filter: [2, \"filter\", \"filter\", booleanAttribute],\n      filterInputAutoFocus: [2, \"filterInputAutoFocus\", \"filterInputAutoFocus\", booleanAttribute],\n      filterBy: \"filterBy\",\n      filterMode: \"filterMode\",\n      filterOptions: \"filterOptions\",\n      filterPlaceholder: \"filterPlaceholder\",\n      filteredNodes: \"filteredNodes\",\n      filterLocale: \"filterLocale\",\n      scrollHeight: \"scrollHeight\",\n      lazy: [2, \"lazy\", \"lazy\", booleanAttribute],\n      virtualScroll: [2, \"virtualScroll\", \"virtualScroll\", booleanAttribute],\n      virtualScrollItemSize: [2, \"virtualScrollItemSize\", \"virtualScrollItemSize\", numberAttribute],\n      virtualScrollOptions: \"virtualScrollOptions\",\n      indentation: [2, \"indentation\", \"indentation\", numberAttribute],\n      _templateMap: \"_templateMap\",\n      trackBy: \"trackBy\",\n      highlightOnSelect: [2, \"highlightOnSelect\", \"highlightOnSelect\", booleanAttribute],\n      virtualNodeHeight: \"virtualNodeHeight\"\n    },\n    outputs: {\n      selectionChange: \"selectionChange\",\n      onNodeSelect: \"onNodeSelect\",\n      onNodeUnselect: \"onNodeUnselect\",\n      onNodeExpand: \"onNodeExpand\",\n      onNodeCollapse: \"onNodeCollapse\",\n      onNodeContextMenuSelect: \"onNodeContextMenuSelect\",\n      onNodeDoubleClick: \"onNodeDoubleClick\",\n      onNodeDrop: \"onNodeDrop\",\n      onLazyLoad: \"onLazyLoad\",\n      onScroll: \"onScroll\",\n      onScrollIndexChange: \"onScrollIndexChange\",\n      onFilter: \"onFilter\"\n    },\n    features: [i0.ɵɵProvidersFeature([TreeStyle]), i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n    decls: 8,\n    vars: 10,\n    consts: [[\"filter\", \"\"], [\"scroller\", \"\"], [\"content\", \"\"], [\"treeNode\", \"\"], [\"loader\", \"\"], [\"wrapper\", \"\"], [\"emptyFilter\", \"\"], [3, \"drop\", \"dragover\", \"dragenter\", \"dragleave\", \"ngClass\", \"ngStyle\"], [\"class\", \"p-tree-mask p-overlay-mask\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [4, \"ngIf\"], [\"class\", \"p-tree-empty-message\", 4, \"ngIf\"], [1, \"p-tree-mask\", \"p-overlay-mask\"], [3, \"class\", 4, \"ngIf\"], [3, \"spin\", \"styleClass\", 4, \"ngIf\"], [\"class\", \"p-tree-loading-icon\", 4, \"ngIf\"], [3, \"spin\", \"styleClass\"], [1, \"p-tree-loading-icon\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"pInputText\", \"\", \"type\", \"search\", \"autocomplete\", \"off\", 1, \"p-tree-filter-input\", 3, \"keydown.enter\", \"input\", \"pAutoFocus\"], [\"class\", \"p-tree-filter-icon\", 4, \"ngIf\"], [1, \"p-tree-filter-icon\"], [\"styleClass\", \"p-tree-root\", 3, \"items\", \"tabindex\", \"style\", \"scrollHeight\", \"itemSize\", \"lazy\", \"options\", \"onScroll\", \"onScrollIndexChange\", \"onLazyLoad\", 4, \"ngIf\"], [\"styleClass\", \"p-tree-root\", 3, \"onScroll\", \"onScrollIndexChange\", \"onLazyLoad\", \"items\", \"tabindex\", \"scrollHeight\", \"itemSize\", \"lazy\", \"options\"], [\"class\", \"p-tree-root-children\", \"role\", \"tree\", 3, \"ngClass\", \"style\", 4, \"ngIf\"], [\"role\", \"tree\", 1, \"p-tree-root-children\", 3, \"ngClass\"], [3, \"level\", \"rowNode\", \"node\", \"parentNode\", \"firstChild\", \"lastChild\", \"index\", \"itemSize\", \"indentation\", \"loadingMode\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [3, \"level\", \"rowNode\", \"node\", \"parentNode\", \"firstChild\", \"lastChild\", \"index\", \"itemSize\", \"indentation\", \"loadingMode\"], [1, \"p-tree-root\"], [\"class\", \"p-tree-root-children\", \"role\", \"tree\", 4, \"ngIf\"], [\"role\", \"tree\", 1, \"p-tree-root-children\"], [3, \"node\", \"firstChild\", \"lastChild\", \"index\", \"level\", \"loadingMode\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [3, \"node\", \"firstChild\", \"lastChild\", \"index\", \"level\", \"loadingMode\"], [1, \"p-tree-empty-message\"], [4, \"ngIf\", \"ngIfElse\"]],\n    template: function Tree_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 7);\n        i0.ɵɵlistener(\"drop\", function Tree_Template_div_drop_0_listener($event) {\n          return ctx.onDrop($event);\n        })(\"dragover\", function Tree_Template_div_dragover_0_listener($event) {\n          return ctx.onDragOver($event);\n        })(\"dragenter\", function Tree_Template_div_dragenter_0_listener() {\n          return ctx.onDragEnter();\n        })(\"dragleave\", function Tree_Template_div_dragleave_0_listener($event) {\n          return ctx.onDragLeave($event);\n        });\n        i0.ɵɵtemplate(1, Tree_div_1_Template, 3, 2, \"div\", 8)(2, Tree_ng_container_2_Template, 1, 0, \"ng-container\", 9)(3, Tree_Conditional_3_Template, 1, 4, \"ng-container\")(4, Tree_Conditional_4_Template, 1, 1, \"p-iconField\")(5, Tree_ng_container_5_Template, 3, 2, \"ng-container\", 10)(6, Tree_div_6_Template, 3, 3, \"div\", 11)(7, Tree_ng_container_7_Template, 1, 0, \"ng-container\", 9);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        let tmp_6_0;\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", ctx.containerClass)(\"ngStyle\", ctx.style);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.loading && ctx.loadingMode === \"mask\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.headerTemplate || ctx._headerTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.filterTemplate || ctx._filterTemplate ? 3 : 4);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = ctx.getRootNode()) == null ? null : tmp_6_0.length);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && (ctx.getRootNode() == null || ctx.getRootNode().length === 0));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.footerTemplate || ctx._footerTemplate);\n      }\n    },\n    dependencies: [CommonModule, i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, Scroller, SharedModule, SearchIcon, SpinnerIcon, InputText, FormsModule, IconField, InputIcon, UITreeNode, AutoFocusModule, i4.AutoFocus],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Tree, [{\n    type: Component,\n    args: [{\n      selector: 'p-tree',\n      standalone: true,\n      imports: [CommonModule, Scroller, SharedModule, SearchIcon, SpinnerIcon, InputText, FormsModule, IconField, InputIcon, UITreeNode, AutoFocusModule],\n      template: `\n        <div [ngClass]=\"containerClass\" [ngStyle]=\"style\" [class]=\"styleClass\" (drop)=\"onDrop($event)\" (dragover)=\"onDragOver($event)\" (dragenter)=\"onDragEnter()\" (dragleave)=\"onDragLeave($event)\">\n            <div class=\"p-tree-mask p-overlay-mask\" *ngIf=\"loading && loadingMode === 'mask'\">\n                <i *ngIf=\"loadingIcon\" [class]=\"'p-tree-loading-icon pi-spin ' + loadingIcon\"></i>\n                <ng-container *ngIf=\"!loadingIcon\">\n                    <SpinnerIcon *ngIf=\"!loadingIconTemplate && !_loadingIconTemplate\" [spin]=\"true\" [styleClass]=\"'p-tree-loading-icon'\" />\n                    <span *ngIf=\"loadingIconTemplate || _loadingIconTemplate\" class=\"p-tree-loading-icon\">\n                        <ng-template *ngTemplateOutlet=\"loadingIconTemplate || _loadingIconTemplate\"></ng-template>\n                    </span>\n                </ng-container>\n            </div>\n            <ng-container *ngTemplateOutlet=\"headerTemplate || _headerTemplate\"></ng-container>\n            @if (filterTemplate || _filterTemplate) {\n                <ng-container *ngTemplateOutlet=\"filterTemplate || _filterTemplate; context: { $implicit: filterOptions }\"></ng-container>\n            } @else {\n                <p-iconField *ngIf=\"filter\">\n                    <input\n                        #filter\n                        [pAutoFocus]=\"filterInputAutoFocus\"\n                        pInputText\n                        type=\"search\"\n                        autocomplete=\"off\"\n                        class=\"p-tree-filter-input\"\n                        [attr.placeholder]=\"filterPlaceholder\"\n                        (keydown.enter)=\"$event.preventDefault()\"\n                        (input)=\"_filter($event.target.value)\"\n                    />\n                    <p-inputIcon>\n                        <SearchIcon *ngIf=\"!filterIconTemplate && !_filterIconTemplate\" class=\"p-tree-filter-icon\" />\n                        <span *ngIf=\"filterIconTemplate || _filterIconTemplate\">\n                            <ng-template *ngTemplateOutlet=\"filterIconTemplate || _filterIconTemplate\"></ng-template>\n                        </span>\n                    </p-inputIcon>\n                </p-iconField>\n            }\n\n            <ng-container *ngIf=\"getRootNode()?.length\">\n                <p-scroller\n                    #scroller\n                    *ngIf=\"virtualScroll\"\n                    [items]=\"serializedValue\"\n                    [tabindex]=\"-1\"\n                    styleClass=\"p-tree-root\"\n                    [style]=\"{ height: scrollHeight !== 'flex' ? scrollHeight : undefined }\"\n                    [scrollHeight]=\"scrollHeight !== 'flex' ? undefined : '100%'\"\n                    [itemSize]=\"virtualScrollItemSize || _virtualNodeHeight\"\n                    [lazy]=\"lazy\"\n                    (onScroll)=\"onScroll.emit($event)\"\n                    (onScrollIndexChange)=\"onScrollIndexChange.emit($event)\"\n                    (onLazyLoad)=\"onLazyLoad.emit($event)\"\n                    [options]=\"virtualScrollOptions\"\n                >\n                    <ng-template #content let-items let-scrollerOptions=\"options\">\n                        <ul *ngIf=\"items\" class=\"p-tree-root-children\" [ngClass]=\"scrollerOptions.contentStyleClass\" [style]=\"scrollerOptions.contentStyle\" role=\"tree\" [attr.aria-label]=\"ariaLabel\" [attr.aria-labelledby]=\"ariaLabelledBy\">\n                            <p-treeNode\n                                #treeNode\n                                *ngFor=\"let rowNode of items; let firstChild = first; let lastChild = last; let index = index; trackBy: trackBy\"\n                                [level]=\"rowNode.level\"\n                                [rowNode]=\"rowNode\"\n                                [node]=\"rowNode.node\"\n                                [parentNode]=\"rowNode.parent\"\n                                [firstChild]=\"firstChild\"\n                                [lastChild]=\"lastChild\"\n                                [index]=\"getIndex(scrollerOptions, index)\"\n                                [itemSize]=\"scrollerOptions.itemSize\"\n                                [indentation]=\"indentation\"\n                                [loadingMode]=\"loadingMode\"\n                            ></p-treeNode>\n                        </ul>\n                    </ng-template>\n                    <ng-container *ngIf=\"loaderTemplate || _loaderTemplate\">\n                        <ng-template #loader let-scrollerOptions=\"options\">\n                            <ng-container *ngTemplateOutlet=\"loaderTemplate || _loaderTemplate; context: { options: scrollerOptions }\"></ng-container>\n                        </ng-template>\n                    </ng-container>\n                </p-scroller>\n                <ng-container *ngIf=\"!virtualScroll\">\n                    <div #wrapper class=\"p-tree-root\" [style.max-height]=\"scrollHeight\">\n                        <ul class=\"p-tree-root-children\" *ngIf=\"getRootNode()\" role=\"tree\" [attr.aria-label]=\"ariaLabel\" [attr.aria-labelledby]=\"ariaLabelledBy\">\n                            <p-treeNode\n                                *ngFor=\"let node of getRootNode(); let firstChild = first; let lastChild = last; let index = index; trackBy: trackBy.bind(this)\"\n                                [node]=\"node\"\n                                [firstChild]=\"firstChild\"\n                                [lastChild]=\"lastChild\"\n                                [index]=\"index\"\n                                [level]=\"0\"\n                                [loadingMode]=\"loadingMode\"\n                            ></p-treeNode>\n                        </ul>\n                    </div>\n                </ng-container>\n            </ng-container>\n\n            <div class=\"p-tree-empty-message\" *ngIf=\"!loading && (getRootNode() == null || getRootNode().length === 0)\">\n                <ng-container *ngIf=\"!emptyMessageTemplate && !_emptyMessageTemplate; else emptyFilter\">\n                    {{ emptyMessageLabel }}\n                </ng-container>\n                <ng-template #emptyFilter *ngTemplateOutlet=\"emptyMessageTemplate || _emptyMessageTemplate\"></ng-template>\n            </div>\n            <ng-container *ngTemplateOutlet=\"footerTemplate || _footerTemplate\"></ng-container>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      providers: [TreeStyle]\n    }]\n  }], () => [{\n    type: i3.TreeDragDropService,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    value: [{\n      type: Input\n    }],\n    selectionMode: [{\n      type: Input\n    }],\n    loadingMode: [{\n      type: Input\n    }],\n    selection: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    contextMenu: [{\n      type: Input\n    }],\n    draggableScope: [{\n      type: Input\n    }],\n    droppableScope: [{\n      type: Input\n    }],\n    draggableNodes: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    droppableNodes: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    metaKeySelection: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    propagateSelectionUp: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    propagateSelectionDown: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    loading: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    loadingIcon: [{\n      type: Input\n    }],\n    emptyMessage: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    togglerAriaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    validateDrop: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    filter: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    filterInputAutoFocus: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    filterBy: [{\n      type: Input\n    }],\n    filterMode: [{\n      type: Input\n    }],\n    filterOptions: [{\n      type: Input\n    }],\n    filterPlaceholder: [{\n      type: Input\n    }],\n    filteredNodes: [{\n      type: Input\n    }],\n    filterLocale: [{\n      type: Input\n    }],\n    scrollHeight: [{\n      type: Input\n    }],\n    lazy: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    virtualScroll: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    virtualScrollItemSize: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    virtualScrollOptions: [{\n      type: Input\n    }],\n    indentation: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    _templateMap: [{\n      type: Input\n    }],\n    trackBy: [{\n      type: Input\n    }],\n    highlightOnSelect: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    virtualNodeHeight: [{\n      type: Input\n    }],\n    selectionChange: [{\n      type: Output\n    }],\n    onNodeSelect: [{\n      type: Output\n    }],\n    onNodeUnselect: [{\n      type: Output\n    }],\n    onNodeExpand: [{\n      type: Output\n    }],\n    onNodeCollapse: [{\n      type: Output\n    }],\n    onNodeContextMenuSelect: [{\n      type: Output\n    }],\n    onNodeDoubleClick: [{\n      type: Output\n    }],\n    onNodeDrop: [{\n      type: Output\n    }],\n    onLazyLoad: [{\n      type: Output\n    }],\n    onScroll: [{\n      type: Output\n    }],\n    onScrollIndexChange: [{\n      type: Output\n    }],\n    onFilter: [{\n      type: Output\n    }],\n    filterTemplate: [{\n      type: ContentChild,\n      args: ['filter', {\n        descendants: false\n      }]\n    }],\n    nodeTemplate: [{\n      type: ContentChild,\n      args: ['node', {\n        descendants: false\n      }]\n    }],\n    headerTemplate: [{\n      type: ContentChild,\n      args: ['header', {\n        descendants: false\n      }]\n    }],\n    footerTemplate: [{\n      type: ContentChild,\n      args: ['footer', {\n        descendants: false\n      }]\n    }],\n    loaderTemplate: [{\n      type: ContentChild,\n      args: ['loader', {\n        descendants: false\n      }]\n    }],\n    emptyMessageTemplate: [{\n      type: ContentChild,\n      args: ['empty', {\n        descendants: false\n      }]\n    }],\n    togglerIconTemplate: [{\n      type: ContentChild,\n      args: ['togglericon', {\n        descendants: false\n      }]\n    }],\n    checkboxIconTemplate: [{\n      type: ContentChild,\n      args: ['checkboxicon', {\n        descendants: false\n      }]\n    }],\n    loadingIconTemplate: [{\n      type: ContentChild,\n      args: ['loadingicon', {\n        descendants: false\n      }]\n    }],\n    filterIconTemplate: [{\n      type: ContentChild,\n      args: ['filtericon', {\n        descendants: false\n      }]\n    }],\n    filterViewChild: [{\n      type: ViewChild,\n      args: ['filter']\n    }],\n    scroller: [{\n      type: ViewChild,\n      args: ['scroller']\n    }],\n    wrapperViewChild: [{\n      type: ViewChild,\n      args: ['wrapper']\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass TreeModule {\n  static ɵfac = function TreeModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TreeModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: TreeModule,\n    imports: [Tree, SharedModule],\n    exports: [Tree, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [Tree, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TreeModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Tree, SharedModule],\n      exports: [Tree, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Tree, TreeClasses, TreeModule, TreeStyle, UITreeNode };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA,IAAM,MAAM,SAAO;AAAA,EACjB,QAAQ;AACV;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,gCAAgC;AAClC;AACA,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,WAAW;AAAA,EACX,SAAS;AACX;AACA,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,WAAW;AAAA,EACX,iBAAiB;AAAA,EACjB,OAAO;AACT;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,EAAE;AAC7B,IAAG,WAAW,QAAQ,SAAS,0DAA0D,QAAQ;AAC/F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,YAAY,QAAQ,EAAE,CAAC;AAAA,IACtD,CAAC,EAAE,YAAY,SAAS,8DAA8D,QAAQ;AAC5F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,oBAAoB,MAAM,CAAC;AAAA,IAC1D,CAAC,EAAE,aAAa,SAAS,+DAA+D,QAAQ;AAC9F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,qBAAqB,QAAQ,EAAE,CAAC;AAAA,IAC/D,CAAC,EAAE,aAAa,SAAS,+DAA+D,QAAQ;AAC9F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,qBAAqB,MAAM,CAAC;AAAA,IAC3D,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAc,gBAAgB,GAAG,KAAK,OAAO,aAAa,CAAC;AACzE,IAAG,YAAY,eAAe,IAAI;AAAA,EACpC;AACF;AACA,SAAS,mFAAmF,IAAI,KAAK;AACnG,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,oBAAoB,EAAE;AAAA,EACxC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,yBAAyB;AAAA,EACvD;AACF;AACA,SAAS,kFAAkF,IAAI,KAAK;AAClG,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,mBAAmB,EAAE;AAAA,EACvC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,yBAAyB;AAAA,EACvD;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,oFAAoF,GAAG,GAAG,oBAAoB,EAAE,EAAE,GAAG,mFAAmF,GAAG,GAAG,mBAAmB,EAAE;AACpP,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,KAAK,QAAQ;AAC3C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,KAAK,QAAQ;AAAA,EAC5C;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,UAAU,GAAG,eAAe,EAAE;AACjC,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU;AACb,IAAG,WAAW,cAAc,iCAAiC;AAAA,EAC/D;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,iEAAiE,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,iEAAiE,GAAG,GAAG,gBAAgB,CAAC;AACtM,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,KAAK,OAAO;AAC1C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,gBAAgB,UAAU,OAAO,KAAK,OAAO;AAAA,EAC5E;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AAAC;AAC5E,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,aAAa;AAAA,EAChG;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,WAAW,GAAG,4CAA4C,GAAG,GAAG,MAAM,EAAE;AAC3E,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,KAAK,uBAAuB,OAAO,KAAK,oBAAoB,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,KAAK,UAAU,OAAO,KAAK,OAAO,CAAC;AAAA,EACzM;AACF;AACA,SAAS,4FAA4F,IAAI,KAAK;AAAC;AAC/G,SAAS,8EAA8E,IAAI,KAAK;AAC9F,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,6FAA6F,GAAG,GAAG,aAAa;AAAA,EACnI;AACF;AACA,SAAS,4EAA4E,IAAI,KAAK;AAC5F,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,+EAA+E,GAAG,GAAG,MAAM,EAAE;AAAA,EAChH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,oBAAoB,OAAO,KAAK,wBAAwB,OAAO,KAAK,qBAAqB,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,WAAW,GAAG,OAAO,KAAK,eAAe,CAAC;AAAA,EAClN;AACF;AACA,SAAS,8DAA8D,IAAI,KAAK;AAC9E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,6EAA6E,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AACrJ,IAAG,sBAAsB;AAAA,EAC3B;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,cAAc,EAAE;AACrC,IAAG,WAAW,SAAS,SAAS,2EAA2E,QAAQ;AACjH,MAAG,cAAc,GAAG;AACpB,aAAU,YAAY,OAAO,eAAe,CAAC;AAAA,IAC/C,CAAC;AACD,IAAG,WAAW,GAAG,+DAA+D,GAAG,GAAG,gBAAgB,CAAC;AACvG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,WAAW,CAAC,EAAE,UAAU,IAAI,EAAE,iBAAiB,OAAO,KAAK,eAAe,EAAE,YAAY,OAAO,KAAK,eAAe,KAAK,EAAE,YAAY,OAAO,QAAQ,OAAO,OAAO,OAAO,KAAK,OAAO,WAAW,OAAO,aAAa,OAAO,QAAQ,OAAO,OAAO,OAAO,KAAK,OAAO,aAAa,OAAO,WAAW,WAAW,UAAU,EAAE,YAAY,EAAE;AACzW,IAAG,YAAY,yBAAyB,OAAO,KAAK,eAAe;AACnE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,KAAK,wBAAwB,OAAO,KAAK,qBAAqB;AAAA,EAC7F;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,MAAM;AAAA,EACxB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,QAAQ,CAAC;AAAA,EAChC;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,KAAK,KAAK;AAAA,EACxC;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,gBAAgB,EAAE;AACnG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,KAAK,mBAAmB,OAAO,IAAI,CAAC,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,IAAI,CAAC;AAAA,EACnJ;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,cAAc,EAAE;AAAA,EAClC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,UAAM,gBAAgB,IAAI;AAC1B,UAAM,eAAe,IAAI;AACzB,UAAM,WAAW,IAAI;AACrB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,QAAQ,YAAY,EAAE,cAAc,OAAO,IAAI,EAAE,cAAc,aAAa,EAAE,aAAa,YAAY,EAAE,SAAS,QAAQ,EAAE,YAAY,OAAO,QAAQ,EAAE,SAAS,OAAO,QAAQ,CAAC,EAAE,eAAe,OAAO,WAAW;AAAA,EACrO;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,EAAE;AAC7B,IAAG,WAAW,GAAG,sDAAsD,GAAG,GAAG,cAAc,EAAE;AAC7F,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,WAAW,OAAO,KAAK,WAAW,SAAS,MAAM;AAChE,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,KAAK,QAAQ,EAAE,gBAAgB,OAAO,KAAK,QAAQ,KAAK,MAAM,CAAC;AAAA,EACjG;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,EAAE;AAC7B,IAAG,WAAW,QAAQ,SAAS,2DAA2D,QAAQ;AAChG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,YAAY,QAAQ,CAAC,CAAC;AAAA,IACrD,CAAC,EAAE,YAAY,SAAS,+DAA+D,QAAQ;AAC7F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,oBAAoB,MAAM,CAAC;AAAA,IAC1D,CAAC,EAAE,aAAa,SAAS,gEAAgE,QAAQ;AAC/F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,qBAAqB,QAAQ,CAAC,CAAC;AAAA,IAC9D,CAAC,EAAE,aAAa,SAAS,gEAAgE,QAAQ;AAC/F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,qBAAqB,MAAM,CAAC;AAAA,IAC3D,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAc,gBAAgB,GAAG,KAAK,OAAO,aAAa,CAAC;AACzE,IAAG,YAAY,eAAe,IAAI;AAAA,EACpC;AACF;AACA,SAAS,kCAAkC,IAAI,KAAK;AAClD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,WAAW,GAAG,wCAAwC,GAAG,GAAG,MAAM,CAAC;AACtE,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,WAAW,WAAW,SAAS,wDAAwD,QAAQ;AAChG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,UAAU,MAAM,CAAC;AAAA,IAChD,CAAC;AACD,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,SAAS,SAAS,uDAAuD,QAAQ;AAC7F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,MAAM,CAAC;AAAA,IAClD,CAAC,EAAE,eAAe,SAAS,6DAA6D,QAAQ;AAC9F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC,EAAE,YAAY,SAAS,0DAA0D,QAAQ;AACxF,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,eAAe,MAAM,CAAC;AAAA,IACrD,CAAC,EAAE,YAAY,SAAS,4DAA4D;AAClF,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,eAAe,CAAC;AAAA,IAC/C,CAAC,EAAE,QAAQ,SAAS,sDAAsD,QAAQ;AAChF,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,WAAW,MAAM,CAAC;AAAA,IACjD,CAAC,EAAE,YAAY,SAAS,0DAA0D,QAAQ;AACxF,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,mBAAmB,MAAM,CAAC;AAAA,IACzD,CAAC,EAAE,aAAa,SAAS,2DAA2D,QAAQ;AAC1F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,oBAAoB,MAAM,CAAC;AAAA,IAC1D,CAAC,EAAE,aAAa,SAAS,2DAA2D,QAAQ;AAC1F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,oBAAoB,MAAM,CAAC;AAAA,IAC1D,CAAC,EAAE,aAAa,SAAS,2DAA2D,QAAQ;AAC1F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,MAAM,CAAC;AAAA,IAClD,CAAC,EAAE,WAAW,SAAS,yDAAyD,QAAQ;AACtF,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,WAAW,MAAM,CAAC;AAAA,IACjD,CAAC;AACD,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,0DAA0D,QAAQ;AAChG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,OAAO,MAAM,CAAC;AAAA,IAC7C,CAAC;AACD,IAAG,WAAW,GAAG,kDAAkD,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,0CAA0C,GAAG,GAAG,QAAQ,CAAC;AACxJ,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,cAAc,CAAC,EAAE,GAAG,0CAA0C,GAAG,GAAG,QAAQ,CAAC;AACpJ,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,QAAQ,CAAC,EAAE,IAAI,2CAA2C,GAAG,GAAG,QAAQ,CAAC;AAC1I,IAAG,aAAa,EAAE;AAClB,IAAG,WAAW,IAAI,yCAAyC,GAAG,GAAG,MAAM,EAAE;AACzE,IAAG,aAAa;AAChB,IAAG,WAAW,IAAI,yCAAyC,GAAG,GAAG,MAAM,CAAC;AAAA,EAC1E;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,QAAQ,OAAO,KAAK,cAAc;AAChD,IAAG,UAAU;AACb,IAAG,WAAW,OAAO,KAAK,KAAK;AAC/B,IAAG,WAAW,OAAO,KAAK,UAAU;AACpC,IAAG,WAAW,WAAW,OAAO,SAAS,EAAE,WAAc,gBAAgB,IAAI,KAAK,OAAO,WAAW,IAAI,CAAC;AACzG,IAAG,YAAY,cAAc,OAAO,KAAK,KAAK,EAAE,gBAAgB,OAAO,OAAO,EAAE,gBAAgB,OAAO,KAAK,WAAW,OAAO,KAAK,SAAS,SAAS,CAAC,EAAE,iBAAiB,OAAO,QAAQ,EAAE,iBAAiB,OAAO,KAAK,QAAQ,EAAE,iBAAiB,OAAO,QAAQ,CAAC,EAAE,cAAc,OAAO,QAAQ,CAAC,EAAE,YAAY,OAAO,UAAU,IAAI,IAAI,EAAE,EAAE,WAAW,OAAO,KAAK,GAAG;AACvW,IAAG,UAAU;AACb,IAAG,YAAY,gBAAgB,OAAO,QAAQ,OAAO,cAAc,KAAK;AACxE,IAAG,WAAW,WAAW,OAAO,gBAAgB,EAAE,aAAa,OAAO,KAAK,cAAc;AACzF,IAAG,UAAU;AACb,IAAG,YAAY,mBAAmB,SAAS;AAC3C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,KAAK,uBAAuB,CAAC,OAAO,KAAK,oBAAoB;AAC3F,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,KAAK,uBAAuB,OAAO,KAAK,oBAAoB;AACzF,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,KAAK,iBAAiB,UAAU;AAC7D,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,KAAK,QAAQ,OAAO,KAAK,gBAAgB,OAAO,KAAK,aAAa;AAC/F,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,CAAC,OAAO,KAAK,mBAAmB,OAAO,IAAI,CAAC;AAClE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,KAAK,mBAAmB,OAAO,IAAI,CAAC;AACjE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,KAAK,iBAAiB,OAAO,KAAK,YAAY,OAAO,KAAK,QAAQ;AAChG,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,KAAK,kBAAkB,OAAO,SAAS;AAAA,EACtE;AACF;AACA,IAAM,MAAM,CAAC,QAAQ;AACrB,IAAM,MAAM,CAAC,MAAM;AACnB,IAAM,MAAM,CAAC,QAAQ;AACrB,IAAM,MAAM,CAAC,QAAQ;AACrB,IAAM,MAAM,CAAC,QAAQ;AACrB,IAAM,OAAO,CAAC,OAAO;AACrB,IAAM,OAAO,CAAC,aAAa;AAC3B,IAAM,OAAO,CAAC,cAAc;AAC5B,IAAM,OAAO,CAAC,aAAa;AAC3B,IAAM,OAAO,CAAC,YAAY;AAC1B,IAAM,OAAO,CAAC,UAAU;AACxB,IAAM,OAAO,CAAC,SAAS;AACvB,IAAM,OAAO,SAAO;AAAA,EAClB,SAAS;AACX;AACA,SAAS,wBAAwB,IAAI,KAAK;AACxC,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,GAAG;AAAA,EACrB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,iCAAiC,OAAO,WAAW;AAAA,EACnE;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,eAAe,EAAE;AAAA,EACnC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,QAAQ,IAAI,EAAE,cAAc,qBAAqB;AAAA,EACjE;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAAC;AAC7E,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,2DAA2D,GAAG,GAAG,aAAa;AAAA,EACjG;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,MAAM,CAAC;AAC3E,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,uBAAuB,OAAO,oBAAoB;AAAA,EAC7F;AACF;AACA,SAAS,mCAAmC,IAAI,KAAK;AACnD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,kDAAkD,GAAG,GAAG,eAAe,EAAE,EAAE,GAAG,2CAA2C,GAAG,GAAG,QAAQ,EAAE;AAC1J,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,uBAAuB,CAAC,OAAO,oBAAoB;AACjF,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,uBAAuB,OAAO,oBAAoB;AAAA,EACjF;AACF;AACA,SAAS,oBAAoB,IAAI,KAAK;AACpC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,yBAAyB,GAAG,GAAG,KAAK,EAAE,EAAE,GAAG,oCAAoC,GAAG,GAAG,gBAAgB,EAAE;AACxH,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,WAAW;AACxC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,WAAW;AAAA,EAC3C;AACF;AACA,SAAS,6BAA6B,IAAI,KAAK;AAC7C,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,4BAA4B,IAAI,KAAK;AAC5C,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,4CAA4C,GAAG,GAAG,gBAAgB,EAAE;AAAA,EACvF;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,oBAAoB,OAAO,kBAAkB,OAAO,eAAe,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,aAAa,CAAC;AAAA,EAChK;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,cAAc,EAAE;AAAA,EAClC;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AAAC;AACpF,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,kEAAkE,GAAG,GAAG,aAAa;AAAA,EACxG;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,MAAM,CAAC;AAClF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,sBAAsB,OAAO,mBAAmB;AAAA,EAC3F;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,aAAa,EAAE,GAAG,SAAS,IAAI,CAAC;AACrD,IAAG,WAAW,iBAAiB,SAAS,yEAAyE,QAAQ;AACvH,MAAG,cAAc,GAAG;AACpB,aAAU,YAAY,OAAO,eAAe,CAAC;AAAA,IAC/C,CAAC,EAAE,SAAS,SAAS,iEAAiE,QAAQ;AAC5F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,QAAQ,OAAO,OAAO,KAAK,CAAC;AAAA,IAC3D,CAAC;AACD,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,aAAa;AAClC,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,cAAc,EAAE,EAAE,GAAG,kDAAkD,GAAG,GAAG,QAAQ,EAAE;AACtK,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,cAAc,OAAO,oBAAoB;AACvD,IAAG,YAAY,eAAe,OAAO,iBAAiB;AACtD,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,CAAC,OAAO,sBAAsB,CAAC,OAAO,mBAAmB;AAC/E,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,sBAAsB,OAAO,mBAAmB;AAAA,EAC/E;AACF;AACA,SAAS,4BAA4B,IAAI,KAAK;AAC5C,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,eAAe,EAAE;AAAA,EACrF;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,QAAQ,OAAO,MAAM;AAAA,EACrC;AACF;AACA,SAAS,0EAA0E,IAAI,KAAK;AAC1F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,cAAc,IAAI,CAAC;AAAA,EACrC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAa,IAAI;AACvB,UAAM,gBAAgB,IAAI;AAC1B,UAAM,eAAe,IAAI;AACzB,UAAM,WAAW,IAAI;AACrB,UAAM,qBAAwB,cAAc,CAAC,EAAE;AAC/C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,SAAS,WAAW,KAAK,EAAE,WAAW,UAAU,EAAE,QAAQ,WAAW,IAAI,EAAE,cAAc,WAAW,MAAM,EAAE,cAAc,aAAa,EAAE,aAAa,YAAY,EAAE,SAAS,OAAO,SAAS,oBAAoB,QAAQ,CAAC,EAAE,YAAY,mBAAmB,QAAQ,EAAE,eAAe,OAAO,WAAW,EAAE,eAAe,OAAO,WAAW;AAAA,EACzV;AACF;AACA,SAAS,6DAA6D,IAAI,KAAK;AAC7E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,EAAE;AAC7B,IAAG,WAAW,GAAG,2EAA2E,GAAG,IAAI,cAAc,EAAE;AACnH,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,YAAY,OAAO;AACzB,UAAM,qBAAqB,OAAO;AAClC,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,mBAAmB,YAAY;AAC7C,IAAG,WAAW,WAAW,mBAAmB,iBAAiB;AAC7D,IAAG,YAAY,cAAc,OAAO,SAAS,EAAE,mBAAmB,OAAO,cAAc;AACvF,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,SAAS,EAAE,gBAAgB,OAAO,OAAO;AAAA,EACpE;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,8DAA8D,GAAG,GAAG,MAAM,EAAE;AAAA,EAC/F;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,IAAG,WAAW,QAAQ,SAAS;AAAA,EACjC;AACF;AACA,SAAS,sFAAsF,IAAI,KAAK;AACtG,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,uEAAuE,IAAI,KAAK;AACvF,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,uFAAuF,GAAG,GAAG,gBAAgB,EAAE;AAAA,EAClI;AACA,MAAI,KAAK,GAAG;AACV,UAAM,sBAAsB,IAAI;AAChC,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,oBAAoB,OAAO,kBAAkB,OAAO,eAAe,EAAE,2BAA8B,gBAAgB,GAAG,MAAM,mBAAmB,CAAC;AAAA,EAChK;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,wEAAwE,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAChJ,IAAG,sBAAsB;AAAA,EAC3B;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,cAAc,IAAI,CAAC;AACxC,IAAG,WAAW,YAAY,SAAS,yEAAyE,QAAQ;AAClH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,SAAS,KAAK,MAAM,CAAC;AAAA,IACpD,CAAC,EAAE,uBAAuB,SAAS,oFAAoF,QAAQ;AAC7H,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,oBAAoB,KAAK,MAAM,CAAC;AAAA,IAC/D,CAAC,EAAE,cAAc,SAAS,2EAA2E,QAAQ;AAC3G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,WAAW,KAAK,MAAM,CAAC;AAAA,IACtD,CAAC;AACD,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,0DAA0D,GAAG,GAAG,gBAAgB,EAAE;AACxN,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAc,gBAAgB,GAAG,KAAK,OAAO,iBAAiB,SAAS,OAAO,eAAe,MAAS,CAAC;AAC1G,IAAG,WAAW,SAAS,OAAO,eAAe,EAAE,YAAY,EAAE,EAAE,gBAAgB,OAAO,iBAAiB,SAAS,SAAY,MAAM,EAAE,YAAY,OAAO,yBAAyB,OAAO,kBAAkB,EAAE,QAAQ,OAAO,IAAI,EAAE,WAAW,OAAO,oBAAoB;AACtQ,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,kBAAkB,OAAO,eAAe;AAAA,EACvE;AACF;AACA,SAAS,8DAA8D,IAAI,KAAK;AAC9E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,cAAc,EAAE;AAAA,EAClC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,UAAM,iBAAiB,IAAI;AAC3B,UAAM,gBAAgB,IAAI;AAC1B,UAAM,YAAY,IAAI;AACtB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,QAAQ,QAAQ,EAAE,cAAc,cAAc,EAAE,aAAa,aAAa,EAAE,SAAS,SAAS,EAAE,SAAS,CAAC,EAAE,eAAe,OAAO,WAAW;AAAA,EAC7J;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,EAAE;AAC7B,IAAG,WAAW,GAAG,+DAA+D,GAAG,GAAG,cAAc,EAAE;AACtG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,cAAc,OAAO,SAAS,EAAE,mBAAmB,OAAO,cAAc;AACvF,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,YAAY,CAAC,EAAE,gBAAgB,OAAO,QAAQ,KAAK,MAAM,CAAC;AAAA,EAC5F;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,OAAO,IAAI,CAAC;AACjC,IAAG,WAAW,GAAG,kDAAkD,GAAG,GAAG,MAAM,EAAE;AACjF,IAAG,aAAa;AAChB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,YAAY,cAAc,OAAO,YAAY;AAChD,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,YAAY,CAAC;AAAA,EAC5C;AACF;AACA,SAAS,6BAA6B,IAAI,KAAK;AAC7C,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,2CAA2C,GAAG,IAAI,cAAc,EAAE,EAAE,GAAG,6CAA6C,GAAG,GAAG,gBAAgB,EAAE;AAC7J,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,aAAa;AAC1C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,aAAa;AAAA,EAC7C;AACF;AACA,SAAS,mCAAmC,IAAI,KAAK;AACnD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,mBAAmB,GAAG;AAAA,EAC1D;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AAAC;AACvD,SAAS,sBAAsB,IAAI,KAAK;AACtC,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,qCAAqC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,EAC/G;AACF;AACA,SAAS,oBAAoB,IAAI,KAAK;AACpC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,oCAAoC,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,uBAAuB,GAAG,GAAG,MAAM,CAAC;AACtH,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,wBAAwB,CAAC,OAAO,qBAAqB,EAAE,YAAY,OAAO,WAAW;AACnH,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,wBAAwB,OAAO,qBAAqB;AAAA,EAC/F;AACF;AACA,SAAS,6BAA6B,IAAI,KAAK;AAC7C,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA,kBAEY,GAAG,iBAAiB,CAAC;AAAA,aAC1B,GAAG,YAAY,CAAC;AAAA,eACd,GAAG,cAAc,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WAStB,GAAG,UAAU,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,2BAKE,GAAG,UAAU,CAAC;AAAA;AAAA;AAAA;AAAA,2BAId,GAAG,UAAU,CAAC;AAAA,4BACb,GAAG,aAAa,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBASxB,GAAG,yBAAyB,CAAC;AAAA,eACnC,GAAG,mBAAmB,CAAC;AAAA;AAAA;AAAA;AAAA,aAIzB,GAAG,iBAAiB,CAAC;AAAA,WACvB,GAAG,eAAe,CAAC;AAAA,6BACD,GAAG,0BAA0B,CAAC,WAAW,GAAG,0BAA0B,CAAC,mBAAmB,GAAG,0BAA0B,CAAC,gBAAgB,GAAG,0BAA0B,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIjL,GAAG,6BAA6B,CAAC;AAAA,eACpC,GAAG,4BAA4B,CAAC,IAAI,GAAG,4BAA4B,CAAC,IAAI,GAAG,4BAA4B,CAAC;AAAA,sBACjG,GAAG,6BAA6B,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIrC,GAAG,4BAA4B,CAAC;AAAA,aACrC,GAAG,uBAAuB,CAAC;AAAA;AAAA;AAAA;AAAA,aAI3B,GAAG,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA,kBAI3B,GAAG,+BAA+B,CAAC;AAAA,aACxC,GAAG,0BAA0B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAgB9B,GAAG,8BAA8B,CAAC;AAAA,cACjC,GAAG,8BAA8B,CAAC;AAAA,aACnC,GAAG,+BAA+B,CAAC;AAAA;AAAA;AAAA,qBAG3B,GAAG,uCAAuC,CAAC;AAAA,6BACnC,GAAG,0BAA0B,CAAC,WAAW,GAAG,0BAA0B,CAAC,kBAAkB,GAAG,0BAA0B,CAAC,mBAAmB,GAAG,0BAA0B,CAAC,gBAAgB,GAAG,0BAA0B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAMjO,GAAG,0CAA0C,CAAC;AAAA,aACnD,GAAG,qCAAqC,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIpC,GAAG,mDAAmD,CAAC;AAAA,aAC5D,GAAG,8CAA8C,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAiBlD,GAAG,sBAAsB,CAAC;AAAA,wBACf,GAAG,0BAA0B,CAAC;AAAA;AAAA;AAAA;AAAA,aAIzC,GAAG,+BAA+B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAa/B,GAAG,wBAAwB,CAAC;AAAA,aAChC,GAAG,wBAAwB,CAAC;AAAA,cAC3B,GAAG,wBAAwB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBA8BlB,GAAG,eAAe,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIzB,GAAG,4BAA4B,CAAC;AAAA,aACrC,GAAG,uBAAuB,CAAC;AAAA;AAAA;AAAA;AAAA,aAI3B,GAAG,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsD7C,IAAM,UAAU;AAAA,EACd,MAAM,CAAC;AAAA,IACL;AAAA,EACF,OAAO;AAAA,IACL,sBAAsB;AAAA,IACtB,qBAAqB,SAAS,iBAAiB;AAAA,IAC/C,kBAAkB,SAAS;AAAA,IAC3B,0BAA0B,SAAS,iBAAiB;AAAA,IACpD,wBAAwB,SAAS;AAAA,EACnC;AAAA,EACA,MAAM;AAAA,EACN,aAAa;AAAA,EACb,eAAe;AAAA,EACf,SAAS;AAAA;AAAA,EAET,cAAc;AAAA,EACd,MAAM,CAAC;AAAA,IACL;AAAA,EACF,OAAO;AAAA,IACL,eAAe;AAAA,IACf,oBAAoB,SAAS,OAAO;AAAA,EACtC;AAAA,EACA,aAAa,CAAC;AAAA,IACZ;AAAA,EACF,OAAO;AAAA,IACL,uBAAuB;AAAA,IACvB,CAAC,SAAS,UAAU,GAAG,CAAC,CAAC,SAAS;AAAA,IAClC,0BAA0B,SAAS;AAAA,IACnC,wBAAwB,SAAS;AAAA,IACjC,wBAAwB,SAAS,kBAAkB,cAAc,SAAS,KAAK,oBAAoB,SAAS,UAAU,SAAS;AAAA,EACjI;AAAA,EACA,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,UAAU;AAAA,EACV,WAAW;AAAA,EACX,cAAc;AAChB;AACA,IAAM,YAAN,MAAM,mBAAkB,UAAU;AAAA,EAChC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,kBAAkB,mBAAmB;AACnD,cAAQ,2BAA2B,yBAA4B,sBAAsB,UAAS,IAAI,qBAAqB,UAAS;AAAA,IAClI;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,WAAU;AAAA,EACrB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,cAAa;AAItB,EAAAA,aAAY,MAAM,IAAI;AAItB,EAAAA,aAAY,MAAM,IAAI;AAItB,EAAAA,aAAY,aAAa,IAAI;AAI7B,EAAAA,aAAY,eAAe,IAAI;AAI/B,EAAAA,aAAY,SAAS,IAAI;AAIzB,EAAAA,aAAY,cAAc,IAAI;AAI9B,EAAAA,aAAY,MAAM,IAAI;AAItB,EAAAA,aAAY,aAAa,IAAI;AAI7B,EAAAA,aAAY,kBAAkB,IAAI;AAIlC,EAAAA,aAAY,gBAAgB,IAAI;AAIhC,EAAAA,aAAY,cAAc,IAAI;AAI9B,EAAAA,aAAY,UAAU,IAAI;AAI1B,EAAAA,aAAY,WAAW,IAAI;AAI3B,EAAAA,aAAY,cAAc,IAAI;AAChC,GAAG,gBAAgB,cAAc,CAAC,EAAE;AACpC,IAAM,aAAN,MAAM,oBAAmB,cAAc;AAAA,EACrC,OAAO,aAAa;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAO,OAAO,WAAW,MAAM,IAAI,CAAC;AAAA,EACpC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,KAAK,kBAAkB,YAAY,KAAK,KAAK,kBAAkB,aAAa,KAAK,WAAW,IAAI;AAAA,EAC9G;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK,KAAK,kBAAkB,aAAa,KAAK,WAAW,IAAI;AAAA,EACtE;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,KAAK,gBAAgB,QAAQ,KAAK;AAAA,MAC5C,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AAAA,EACA,IAAI,mBAAmB;AACrB,WAAO,KAAK,KAAK,gBAAgB,QAAQ,YAAY;AAAA,MACnD,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK,KAAK,eAAe,QAAQ,QAAQ,KAAK,KAAK,iBAAiB;AAAA,EAC7E;AAAA,EACA,WAAW;AACT,UAAM,SAAS;AACf,SAAK,KAAK,SAAS,KAAK;AACxB,UAAM,gBAAgB,KAAK,KAAK,GAAG;AACnC,UAAM,iBAAiB,cAAc,QAAQ,UAAU;AACvD,QAAI,KAAK,cAAc,CAAC,gBAAgB;AACtC,WAAK,sBAAsB;AAC3B,WAAK,KAAK,eAAe,KAAK,MAAM,KAAK,KAAK,OAAO,UAAU,KAAK,KAAK,eAAe,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,CAAC;AAAA,IAC/H;AAAA,EACF;AAAA,EACA,UAAU;AACR,QAAI;AACJ,QAAI,KAAK,KAAK,KAAM,QAAO,KAAK,KAAK;AAAA,QAAU,QAAO,KAAK,KAAK,YAAY,KAAK,KAAK,YAAY,KAAK,KAAK,UAAU,SAAS,KAAK,KAAK,eAAe,KAAK,KAAK;AAClK,WAAO,YAAW,aAAa,MAAM,OAAO;AAAA,EAC9C;AAAA,EACA,SAAS;AACP,WAAO,KAAK,KAAK,WAAW,KAAK,IAAI;AAAA,EACvC;AAAA,EACA,OAAO,OAAO;AACZ,QAAI,KAAK,KAAK,SAAU,MAAK,SAAS,KAAK;AAAA,QAAO,MAAK,OAAO,KAAK;AACnE,UAAM,gBAAgB;AAAA,EACxB;AAAA,EACA,OAAO,OAAO;AACZ,SAAK,KAAK,WAAW;AACrB,QAAI,KAAK,KAAK,eAAe;AAC3B,WAAK,KAAK,sBAAsB;AAChC,WAAK,iBAAiB;AAAA,IACxB;AACA,SAAK,KAAK,aAAa,KAAK;AAAA,MAC1B,eAAe;AAAA,MACf,MAAM,KAAK;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,SAAS,OAAO;AACd,SAAK,KAAK,WAAW;AACrB,QAAI,KAAK,KAAK,eAAe;AAC3B,WAAK,KAAK,sBAAsB;AAChC,WAAK,iBAAiB;AAAA,IACxB;AACA,SAAK,KAAK,eAAe,KAAK;AAAA,MAC5B,eAAe;AAAA,MACf,MAAM,KAAK;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,KAAK,YAAY,OAAO,KAAK,IAAI;AAAA,EACxC;AAAA,EACA,cAAc,OAAO;AACnB,QAAI,MAAM,QAAQ,SAAS;AACzB,WAAK,KAAK,YAAY,OAAO,KAAK,IAAI;AAAA,IACxC;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,SAAK,KAAK,eAAe;AAAA,EAC3B;AAAA,EACA,iBAAiB,OAAO;AACtB,SAAK,KAAK,iBAAiB,OAAO,KAAK,IAAI;AAAA,EAC7C;AAAA,EACA,eAAe,OAAO;AACpB,SAAK,KAAK,eAAe,OAAO,KAAK,IAAI;AAAA,EAC3C;AAAA,EACA,aAAa;AACX,WAAO,KAAK,KAAK,WAAW,KAAK,IAAI;AAAA,EACvC;AAAA,EACA,WAAW,OAAO;AAChB,WAAO,MAAM,kBAAkB,MAAM,cAAc,WAAW,MAAM,MAAM,KAAK,MAAM,cAAc,WAAW,MAAM,OAAO,QAAQ,mBAAmB,CAAC;AAAA,EACzJ;AAAA,EACA,YAAY,OAAO,UAAU;AAC3B,UAAM,eAAe;AACrB,QAAI,WAAW,KAAK,KAAK;AACzB,QAAI,gBAAgB,KAAK,KAAK;AAC9B,QAAI,gBAAgB,KAAK,KAAK;AAC9B,QAAI,wBAAwB,KAAK,KAAK,iBAAiB,KAAK,OAAO,aAAa,KAAK,kBAAkB,KAAK,QAAQ,IAAI;AACxH,QAAI,KAAK,KAAK,UAAU,UAAU,KAAK,MAAM,aAAa,KAAK,uBAAuB;AACpF,UAAI,aAAa,mBACZ,KAAK,6BAA6B,QAAQ;AAE/C,UAAI,KAAK,KAAK,cAAc;AAC1B,aAAK,KAAK,WAAW,KAAK;AAAA,UACxB,eAAe;AAAA,UACf;AAAA,UACA,UAAU,KAAK;AAAA,UACf,OAAO,KAAK;AAAA,UACZ,QAAQ,MAAM;AACZ,iBAAK,iBAAiB,UAAU;AAAA,UAClC;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,aAAK,iBAAiB,UAAU;AAChC,aAAK,KAAK,WAAW,KAAK;AAAA,UACxB,eAAe;AAAA,UACf;AAAA,UACA,UAAU,KAAK;AAAA,UACf,OAAO,KAAK;AAAA,QACd,CAAC;AAAA,MACH;AAAA,IACF;AACA,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,iBAAiB,OAAO;AACtB,QAAI,cAAc,MAAM,SAAS,SAAS,MAAM,SAAS,OAAO,WAAW,KAAK,KAAK;AACrF,UAAM,iBAAiB,OAAO,MAAM,eAAe,CAAC;AACpD,QAAI,YAAY,KAAK;AACrB,QAAI,MAAM,WAAW,GAAG;AACtB,kBAAY,MAAM,qBAAqB,cAAc,MAAM,gBAAgB,MAAM,QAAQ,MAAM,QAAQ,MAAM,QAAQ,IAAI,MAAM;AAC/H,kBAAY,OAAO,WAAW,GAAG,MAAM,QAAQ;AAAA,IACjD,OAAO;AACL,kBAAY,YAAY;AACxB,kBAAY,KAAK,MAAM,QAAQ;AAAA,IACjC;AACA,SAAK,KAAK,gBAAgB,SAAS;AAAA,MACjC,MAAM,MAAM;AAAA,MACZ,UAAU,MAAM,SAAS,SAAS,MAAM,SAAS,OAAO,WAAW,KAAK,KAAK;AAAA,MAC7E,OAAO,MAAM;AAAA,IACf,CAAC;AAAA,EACH;AAAA,EACA,6BAA6B,UAAU;AACrC,WAAO;AAAA,MACL,UAAU,KAAK,KAAK;AAAA,MACpB,eAAe,KAAK,KAAK;AAAA,MACzB,kBAAkB,KAAK,KAAK;AAAA,MAC5B,UAAU,KAAK;AAAA,MACf,OAAO,KAAK;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA,EACA,oBAAoB,OAAO;AACzB,UAAM,aAAa,aAAa;AAChC,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,qBAAqB,OAAO,UAAU;AACpC,QAAI,KAAK,KAAK,UAAU,KAAK,KAAK,UAAU,KAAK,MAAM,KAAK,KAAK,aAAa,GAAG;AAC/E,UAAI,WAAW,EAAG,MAAK,gBAAgB;AAAA,UAAU,MAAK,gBAAgB;AAAA,IACxE;AAAA,EACF;AAAA,EACA,qBAAqB,OAAO;AAC1B,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,YAAY,OAAO;AACjB,QAAI,KAAK,KAAK,kBAAkB,KAAK,KAAK,cAAc,OAAO;AAC7D,YAAM,aAAa,QAAQ,QAAQ,MAAM;AACzC,WAAK,KAAK,gBAAgB,UAAU;AAAA,QAClC,MAAM;AAAA,QACN,MAAM,KAAK;AAAA,QACX,UAAU,KAAK,MAAM,SAAS,KAAK,KAAK,OAAO,WAAW,KAAK,KAAK;AAAA,QACpE,OAAO,KAAK;AAAA,QACZ,OAAO,KAAK,KAAK;AAAA,MACnB,CAAC;AAAA,IACH,OAAO;AACL,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,KAAK,gBAAgB,SAAS;AAAA,MACjC,MAAM,KAAK;AAAA,MACX,UAAU,KAAK,MAAM,SAAS,KAAK,KAAK,OAAO,WAAW,KAAK,KAAK;AAAA,MACpE,OAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,OAAO;AACxB,UAAM,aAAa,aAAa;AAChC,QAAI,KAAK,KAAK,gBAAgB;AAC5B,YAAM,eAAe;AACrB,YAAM,gBAAgB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,QAAI,KAAK,KAAK,kBAAkB,KAAK,MAAM,cAAc,OAAO;AAC9D,UAAI,WAAW,KAAK,KAAK;AACzB,UAAI,KAAK,KAAK,UAAU,UAAU,KAAK,MAAM,KAAK,KAAK,aAAa,GAAG;AACrE,YAAI,aAAa,mBACZ,KAAK,4BAA4B;AAEtC,YAAI,KAAK,KAAK,cAAc;AAC1B,eAAK,KAAK,WAAW,KAAK;AAAA,YACxB,eAAe;AAAA,YACf;AAAA,YACA,UAAU,KAAK;AAAA,YACf,OAAO,KAAK;AAAA,YACZ,QAAQ,MAAM;AACZ,mBAAK,gBAAgB,UAAU;AAAA,YACjC;AAAA,UACF,CAAC;AAAA,QACH,OAAO;AACL,eAAK,gBAAgB,UAAU;AAC/B,eAAK,KAAK,WAAW,KAAK;AAAA,YACxB,eAAe;AAAA,YACf;AAAA,YACA,UAAU,KAAK;AAAA,YACf,OAAO,KAAK;AAAA,UACd,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AACA,UAAM,eAAe;AACrB,UAAM,gBAAgB;AACtB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,8BAA8B;AAC5B,WAAO;AAAA,MACL,UAAU,KAAK,KAAK;AAAA,MACpB,eAAe,KAAK,KAAK;AAAA,MACzB,kBAAkB,KAAK,KAAK;AAAA,MAC5B,UAAU,KAAK;AAAA,IACjB;AAAA,EACF;AAAA,EACA,gBAAgB,OAAO;AACrB,QAAI,gBAAgB,MAAM;AAC1B,UAAM,iBAAiB,OAAO,eAAe,CAAC;AAC9C,QAAI,MAAM,SAAS,SAAU,OAAM,SAAS,SAAS,KAAK,MAAM,QAAQ;AAAA,QAAO,OAAM,SAAS,WAAW,CAAC,MAAM,QAAQ;AACxH,SAAK,KAAK,gBAAgB,SAAS;AAAA,MACjC,MAAM,MAAM;AAAA,MACZ,UAAU,MAAM,SAAS,SAAS,MAAM,SAAS,OAAO,WAAW,KAAK,KAAK;AAAA,MAC7E,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB,OAAO;AACzB,QAAI,KAAK,KAAK,kBAAkB,KAAK,MAAM,cAAc,SAAS,KAAK,KAAK,UAAU,KAAK,KAAK,UAAU,KAAK,MAAM,KAAK,KAAK,aAAa,GAAG;AAC7I,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EACA,oBAAoB,OAAO;AACzB,QAAI,KAAK,KAAK,gBAAgB;AAC5B,UAAI,OAAO,MAAM,cAAc,sBAAsB;AACrD,UAAI,MAAM,IAAI,KAAK,OAAO,KAAK,SAAS,MAAM,IAAI,KAAK,QAAQ,MAAM,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,IAAI,KAAK,KAAK;AAClI,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,OAAO;AACf,QAAI,CAAC,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,eAAe,KAAK,KAAK,YAAY,oBAAoB,cAAc,MAAM,YAAY,SAAS;AACzI;AAAA,IACF;AACA,YAAQ,MAAM,MAAM;AAAA,MAElB,KAAK;AACH,aAAK,YAAY,KAAK;AACtB;AAAA,MAEF,KAAK;AACH,aAAK,UAAU,KAAK;AACpB;AAAA,MAEF,KAAK;AACH,aAAK,aAAa,KAAK;AACvB;AAAA,MAEF,KAAK;AACH,aAAK,YAAY,KAAK;AACtB;AAAA,MAEF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,aAAK,QAAQ,KAAK;AAClB;AAAA,MAEF,KAAK;AACH,aAAK,sBAAsB;AAC3B;AAAA,MACF;AAEE;AAAA,IACJ;AAAA,EACF;AAAA,EACA,UAAU,OAAO;AACf,UAAM,cAAc,MAAM,OAAO,aAAa,iBAAiB,MAAM,YAAY,MAAM,OAAO,QAAQ,mBAAmB,IAAI,MAAM,OAAO;AAC1I,QAAI,YAAY,wBAAwB;AACtC,WAAK,eAAe,aAAa,YAAY,wBAAwB,KAAK,0BAA0B,YAAY,sBAAsB,CAAC;AAAA,IACzI,OAAO;AACL,UAAI,oBAAoB,KAAK,qBAAqB,WAAW;AAC7D,UAAI,mBAAmB;AACrB,aAAK,eAAe,aAAa,iBAAiB;AAAA,MACpD;AAAA,IACF;AACA,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,YAAY,OAAO;AACjB,UAAM,cAAc,MAAM,OAAO,aAAa,iBAAiB,MAAM,YAAY,MAAM,OAAO,QAAQ,mBAAmB,IAAI,MAAM;AACnI,UAAM,cAAc,YAAY,SAAS,CAAC;AAC1C,QAAI,eAAe,YAAY,SAAS,SAAS,GAAG;AAClD,WAAK,eAAe,aAAa,YAAY,SAAS,CAAC,CAAC;AAAA,IAC1D,OAAO;AACL,UAAI,YAAY,cAAc,oBAAoB;AAChD,aAAK,eAAe,aAAa,YAAY,cAAc,kBAAkB;AAAA,MAC/E,OAAO;AACL,YAAI,sBAAsB,KAAK,0BAA0B,YAAY,aAAa;AAClF,YAAI,qBAAqB;AACvB,eAAK,eAAe,aAAa,mBAAmB;AAAA,QACtD;AAAA,MACF;AAAA,IACF;AACA,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,aAAa,OAAO;AAClB,QAAI,CAAC,KAAK,MAAM,YAAY,CAAC,KAAK,KAAK,WAAW,KAAK,IAAI,GAAG;AAC5D,WAAK,OAAO,KAAK;AACjB,YAAM,cAAc,WAAW;AAC/B,iBAAW,MAAM;AACf,aAAK,YAAY,KAAK;AAAA,MACxB,GAAG,CAAC;AAAA,IACN;AACA,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,YAAY,OAAO;AACjB,UAAM,cAAc,MAAM,OAAO,aAAa,iBAAiB,MAAM,YAAY,MAAM,OAAO,QAAQ,mBAAmB,IAAI,MAAM;AACnI,QAAI,KAAK,UAAU,KAAK,CAAC,KAAK,MAAM,UAAU;AAC5C,aAAO;AAAA,IACT;AACA,QAAI,KAAK,MAAM,UAAU;AACvB,WAAK,SAAS,KAAK;AACnB;AAAA,IACF;AACA,QAAI,oBAAoB,KAAK,qBAAqB,YAAY,aAAa;AAC3E,QAAI,mBAAmB;AACrB,WAAK,eAAe,MAAM,eAAe,iBAAiB;AAAA,IAC5D;AACA,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,QAAQ,OAAO;AACb,SAAK,KAAK,YAAY,OAAO,KAAK,IAAI;AACtC,SAAK,4BAA4B,OAAO,KAAK,KAAK,WAAW;AAC7D,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,wBAAwB;AACtB,UAAM,QAAQ,KAAK,KAAK,KAAK,GAAG,eAAe,cAAc;AAC7D,UAAM,kBAAkB,CAAC,GAAG,KAAK,EAAE,KAAK,UAAQ,KAAK,aAAa,eAAe,MAAM,UAAU,KAAK,aAAa,cAAc,MAAM,MAAM;AAC7I,KAAC,GAAG,KAAK,EAAE,QAAQ,UAAQ;AACzB,WAAK,WAAW;AAAA,IAClB,CAAC;AACD,QAAI,iBAAiB;AACnB,YAAM,gBAAgB,CAAC,GAAG,KAAK,EAAE,OAAO,UAAQ,KAAK,aAAa,eAAe,MAAM,UAAU,KAAK,aAAa,cAAc,MAAM,MAAM;AAC7I,oBAAc,CAAC,EAAE,WAAW;AAC5B;AAAA,IACF;AACA,QAAI,MAAM,QAAQ;AAChB,OAAC,GAAG,KAAK,EAAE,CAAC,EAAE,WAAW;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,4BAA4B,OAAO,aAAa;AAC9C,QAAI,KAAK,KAAK,kBAAkB,MAAM;AACpC,YAAM,WAAW,CAAC,GAAG,KAAK,KAAK,KAAK,GAAG,eAAe,mBAAmB,CAAC;AAC1E,YAAM,cAAc,WAAW,gBAAgB,QAAQ,KAAK;AAC5D,UAAI,SAAS,MAAM,aAAW,QAAQ,aAAa,EAAE,GAAG;AACtD,iBAAS,CAAC,EAAE,WAAW;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA,EACA,0BAA0B,aAAa;AACrC,QAAI,oBAAoB,KAAK,qBAAqB,WAAW;AAC7D,QAAI,mBAAmB;AACrB,UAAI,kBAAkB,mBAAoB,QAAO,kBAAkB;AAAA,UAAwB,QAAO,KAAK,0BAA0B,iBAAiB;AAAA,IACpJ,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B,aAAa;AACrC,UAAM,cAAc,MAAM,KAAK,YAAY,QAAQ,EAAE,KAAK,QAAM,SAAS,IAAI,aAAa,CAAC;AAC3F,UAAM,sBAAsB,aAAa,SAAS,CAAC;AACnD,QAAI,uBAAuB,oBAAoB,SAAS,SAAS,GAAG;AAClE,YAAM,mBAAmB,oBAAoB,SAAS,oBAAoB,SAAS,SAAS,CAAC;AAC7F,aAAO,KAAK,0BAA0B,gBAAgB;AAAA,IACxD,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,qBAAqB,aAAa;AAChC,UAAM,oBAAoB,YAAY,eAAe,eAAe;AACpE,WAAO,mBAAmB,YAAY,eAAe,oBAAoB;AAAA,EAC3E;AAAA,EACA,UAAU,SAAS;AACjB,QAAI,KAAK,KAAK,eAAgB,SAAQ,SAAS,CAAC,EAAE,MAAM;AAAA,QAAO,SAAQ,SAAS,CAAC,EAAE,MAAM;AAAA,EAC3F;AAAA,EACA,eAAe,mBAAmB,mBAAmB,uBAAuB;AAC1E,sBAAkB,WAAW;AAC7B,sBAAkB,SAAS,CAAC,EAAE,WAAW;AACzC,SAAK,UAAU,yBAAyB,iBAAiB;AAAA,EAC3D;AAAA,EACA,mBAAmB;AACjB,SAAK,UAAU,WAAW,MAAM;AAC9B,UAAI,OAAO,WAAW,SAAS,MAAM,aAAa,KAAK,MAAM,OAAO,KAAK,MAAM,IAAI,IAAI;AACvF,YAAM,IAAI;AAAA,IACZ,GAAG,CAAC;AAAA,EACN;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,mBAAmB,mBAAmB;AACpD,cAAQ,4BAA4B,0BAA6B,sBAAsB,WAAU,IAAI,qBAAqB,WAAU;AAAA,IACtI;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,IAC1B,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,MAAM,CAAC,GAAG,QAAQ,QAAQ,gBAAgB;AAAA,MAC1C,OAAO,CAAC,GAAG,SAAS,SAAS,eAAe;AAAA,MAC5C,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,WAAW,CAAC,GAAG,aAAa,aAAa,gBAAgB;AAAA,MACzD,OAAO,CAAC,GAAG,SAAS,SAAS,eAAe;AAAA,MAC5C,aAAa,CAAC,GAAG,eAAe,eAAe,eAAe;AAAA,MAC9D,UAAU,CAAC,GAAG,YAAY,YAAY,eAAe;AAAA,MACrD,aAAa;AAAA,IACf;AAAA,IACA,UAAU,CAAI,0BAA6B,0BAA0B;AAAA,IACrE,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,SAAS,yBAAyB,GAAG,WAAW,QAAQ,YAAY,aAAa,aAAa,GAAG,MAAM,GAAG,CAAC,QAAQ,YAAY,GAAG,WAAW,WAAW,SAAS,GAAG,CAAC,GAAG,SAAS,eAAe,YAAY,YAAY,QAAQ,YAAY,aAAa,aAAa,aAAa,WAAW,WAAW,WAAW,GAAG,CAAC,QAAQ,UAAU,WAAW,IAAI,YAAY,MAAM,GAAG,6BAA6B,GAAG,OAAO,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,SAAS,2BAA2B,GAAG,MAAM,GAAG,CAAC,cAAc,wBAAwB,GAAG,WAAW,UAAU,iBAAiB,YAAY,WAAW,YAAY,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,SAAS,wBAAwB,SAAS,kBAAkB,QAAQ,SAAS,GAAG,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,yBAAyB,GAAG,QAAQ,YAAY,aAAa,aAAa,SAAS,GAAG,CAAC,GAAG,cAAc,GAAG,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,cAAc,wBAAwB,GAAG,SAAS,WAAW,UAAU,iBAAiB,YAAY,WAAW,UAAU,GAAG,CAAC,QAAQ,SAAS,GAAG,wBAAwB,GAAG,WAAW,MAAM,GAAG,CAAC,GAAG,QAAQ,cAAc,cAAc,aAAa,SAAS,YAAY,SAAS,eAAe,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,GAAG,QAAQ,cAAc,cAAc,aAAa,SAAS,YAAY,SAAS,aAAa,CAAC;AAAA,IAC55C,UAAU,SAAS,oBAAoB,IAAI,KAAK;AAC9C,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,mCAAmC,IAAI,EAAE;AAAA,MAC5D;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,IAAI,OAAO,IAAI,EAAE;AAAA,MACpC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,aAAY,cAAiB,SAAY,SAAY,MAAS,kBAAqB,SAAS,QAAQ,UAAU,aAAgB,iBAAoB,SAAS,kBAAkB,iBAAiB,aAAa,YAAY;AAAA,IACtO,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,QAAQ,UAAU,aAAa,kBAAkB,iBAAiB,aAAa,YAAY;AAAA,MACnH,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA0HV,eAAe,kBAAkB;AAAA,IACnC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,OAAN,MAAM,cAAa,cAAc;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnB,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKP;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,CAAC,OAAO,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3B,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpB;AAAA,EACA,IAAI,oBAAoB;AACtB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,kBAAkB,KAAK;AACzB,SAAK,qBAAqB;AAC1B,YAAQ,IAAI,2FAA2F;AAAA,EACzG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnC,eAAe,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhC,iBAAiB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMlC,eAAe,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhC,iBAAiB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMlC,0BAA0B,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM3C,oBAAoB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrC,aAAa,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM9B,aAAa,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM9B,WAAW,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM5B,sBAAsB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMvC,WAAW,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,UAAU,QAAQ;AACzB,WAAK,eAAe,CAAC;AAAA,IACvB;AACA,SAAK,UAAU,QAAQ,UAAQ;AAC7B,cAAQ,KAAK,QAAQ,GAAG;AAAA,QACtB,KAAK;AACH,eAAK,kBAAkB,KAAK;AAC5B;AAAA,QACF,KAAK;AACH,eAAK,wBAAwB,KAAK;AAClC;AAAA,QACF,KAAK;AACH,eAAK,kBAAkB,KAAK;AAC5B;AAAA,QACF,KAAK;AACH,eAAK,kBAAkB,KAAK;AAC5B;AAAA,QACF,KAAK;AACH,eAAK,uBAAuB,KAAK;AACjC;AAAA,QACF,KAAK;AACH,eAAK,wBAAwB,KAAK;AAClC;AAAA,QACF,KAAK;AACH,eAAK,uBAAuB,KAAK;AACjC;AAAA,QACF,KAAK;AACH,eAAK,sBAAsB,KAAK;AAChC;AAAA,QACF,KAAK;AACH,eAAK,kBAAkB,KAAK;AAC5B;AAAA,QACF;AACE,eAAK,aAAa,KAAK,IAAI,IAAI,KAAK;AACpC;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAkB,OAAO,SAAS;AAAA,EAClC,YAAY,iBAAiB;AAC3B,UAAM;AACN,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,WAAW;AACT,UAAM,SAAS;AACf,QAAI,KAAK,UAAU;AACjB,WAAK,gBAAgB;AAAA,QACnB,QAAQ,WAAS,KAAK,QAAQ,KAAK;AAAA,QACnC,OAAO,MAAM,KAAK,YAAY;AAAA,MAChC;AAAA,IACF;AACA,QAAI,KAAK,gBAAgB;AACvB,WAAK,wBAAwB,KAAK,gBAAgB,WAAW,UAAU,WAAS;AAC9E,aAAK,eAAe,MAAM;AAC1B,aAAK,WAAW,MAAM;AACtB,aAAK,mBAAmB,MAAM;AAC9B,aAAK,gBAAgB,MAAM;AAC3B,aAAK,gBAAgB,MAAM;AAAA,MAC7B,CAAC;AACD,WAAK,uBAAuB,KAAK,gBAAgB,UAAU,UAAU,WAAS;AAC5E,aAAK,eAAe;AACpB,aAAK,WAAW;AAChB,aAAK,mBAAmB;AACxB,aAAK,gBAAgB;AACrB,aAAK,gBAAgB;AACrB,aAAK,YAAY;AAAA,MACnB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAY,cAAc;AACxB,UAAM,YAAY,YAAY;AAC9B,QAAI,aAAa,OAAO;AACtB,WAAK,sBAAsB;AAC3B,UAAI,KAAK,gBAAgB,GAAG;AAC1B,aAAK,QAAQ,KAAK,gBAAgB,cAAc,KAAK;AAAA,MACvD;AAAA,IACF;AAAA,EACF;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO,KAAK,gBAAgB,QAAQ,KAAK;AAAA,MACvC,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AAAA,EACA,IAAI,oBAAoB;AACtB,WAAO,KAAK,gBAAgB,KAAK,OAAO,eAAe,gBAAgB,aAAa;AAAA,EACtF;AAAA,EACA,wBAAwB;AACtB,SAAK,kBAAkB,CAAC;AACxB,SAAK,eAAe,MAAM,KAAK,YAAY,GAAG,GAAG,IAAI;AAAA,EACvD;AAAA,EACA,eAAe,QAAQ,OAAO,OAAO,SAAS;AAC5C,QAAI,SAAS,MAAM,QAAQ;AACzB,eAAS,QAAQ,OAAO;AACtB,aAAK,SAAS;AACd,cAAM,UAAU;AAAA,UACd;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAS,YAAY,SAAS,OAAO,WAAW;AAAA,QAClD;AACA,aAAK,gBAAgB,KAAK,OAAO;AACjC,YAAI,QAAQ,WAAW,KAAK,UAAU;AACpC,eAAK,eAAe,MAAM,KAAK,UAAU,QAAQ,GAAG,QAAQ,OAAO;AAAA,QACrE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,OAAO,MAAM;AACvB,QAAI,cAAc,MAAM;AACxB,QAAI,SAAS,aAAa,gBAAgB,KAAK,SAAS,aAAa,qBAAqB,GAAG;AAC3F;AAAA,IACF,WAAW,KAAK,eAAe;AAC7B,UAAI,KAAK,eAAe,OAAO;AAC7B,aAAK,QAAQ;AACb;AAAA,MACF,OAAO;AACL,YAAI,CAAC,KAAK,OAAO,SAAS,sBAAsB,GAAG;AACjD,eAAK,QAAQ,KAAK,QAAQ,GAAG,KAAK,KAAK,+CAA+C;AAAA,QACxF;AAAA,MACF;AACA,UAAI,KAAK,iBAAiB,GAAG;AAC3B,eAAO,KAAK,eAAe,KAAK,KAAK,KAAK,aAAa;AACvD,YAAI,CAAC,MAAM;AACT;AAAA,QACF;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,qBAAqB,IAAI;AAC1C,UAAI,WAAW,SAAS;AACxB,UAAI,KAAK,wBAAwB,GAAG;AAClC,YAAI,UAAU;AACZ,cAAI,KAAK,uBAAwB,MAAK,cAAc,MAAM,KAAK;AAAA,cAAO,MAAK,YAAY,KAAK,UAAU,OAAO,CAAC,KAAK,MAAM,KAAK,KAAK;AACnI,cAAI,KAAK,wBAAwB,KAAK,QAAQ;AAC5C,iBAAK,YAAY,KAAK,QAAQ,KAAK;AAAA,UACrC;AACA,eAAK,gBAAgB,KAAK,KAAK,SAAS;AACxC,eAAK,eAAe,KAAK;AAAA,YACvB,eAAe;AAAA,YACf;AAAA,UACF,CAAC;AAAA,QACH,OAAO;AACL,cAAI,KAAK,uBAAwB,MAAK,cAAc,MAAM,IAAI;AAAA,cAAO,MAAK,YAAY,CAAC,GAAI,KAAK,aAAa,CAAC,GAAI,IAAI;AACtH,cAAI,KAAK,wBAAwB,KAAK,QAAQ;AAC5C,iBAAK,YAAY,KAAK,QAAQ,IAAI;AAAA,UACpC;AACA,eAAK,gBAAgB,KAAK,KAAK,SAAS;AACxC,eAAK,aAAa,KAAK;AAAA,YACrB,eAAe;AAAA,YACf;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,OAAO;AACL,YAAI,gBAAgB,KAAK,cAAc,QAAQ,KAAK;AACpD,YAAI,eAAe;AACjB,cAAI,UAAU,MAAM,WAAW,MAAM;AACrC,cAAI,YAAY,SAAS;AACvB,gBAAI,KAAK,sBAAsB,GAAG;AAChC,mBAAK,gBAAgB,KAAK,IAAI;AAAA,YAChC,OAAO;AACL,mBAAK,YAAY,KAAK,UAAU,OAAO,CAAC,KAAK,MAAM,KAAK,KAAK;AAC7D,mBAAK,gBAAgB,KAAK,KAAK,SAAS;AAAA,YAC1C;AACA,iBAAK,eAAe,KAAK;AAAA,cACvB,eAAe;AAAA,cACf;AAAA,YACF,CAAC;AAAA,UACH,OAAO;AACL,gBAAI,KAAK,sBAAsB,GAAG;AAChC,mBAAK,gBAAgB,KAAK,IAAI;AAAA,YAChC,WAAW,KAAK,wBAAwB,GAAG;AACzC,mBAAK,YAAY,CAAC,UAAU,CAAC,IAAI,KAAK,aAAa,CAAC;AACpD,mBAAK,YAAY,CAAC,GAAG,KAAK,WAAW,IAAI;AACzC,mBAAK,gBAAgB,KAAK,KAAK,SAAS;AAAA,YAC1C;AACA,iBAAK,aAAa,KAAK;AAAA,cACrB,eAAe;AAAA,cACf;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF,OAAO;AACL,cAAI,KAAK,sBAAsB,GAAG;AAChC,gBAAI,UAAU;AACZ,mBAAK,YAAY;AACjB,mBAAK,eAAe,KAAK;AAAA,gBACvB,eAAe;AAAA,gBACf;AAAA,cACF,CAAC;AAAA,YACH,OAAO;AACL,mBAAK,YAAY;AACjB,yBAAW,MAAM;AACf,qBAAK,aAAa,KAAK;AAAA,kBACrB,eAAe;AAAA,kBACf;AAAA,gBACF,CAAC;AAAA,cACH,CAAC;AAAA,YACH;AAAA,UACF,OAAO;AACL,gBAAI,UAAU;AACZ,mBAAK,YAAY,KAAK,UAAU,OAAO,CAAC,KAAK,MAAM,KAAK,KAAK;AAC7D,mBAAK,eAAe,KAAK;AAAA,gBACvB,eAAe;AAAA,gBACf;AAAA,cACF,CAAC;AAAA,YACH,OAAO;AACL,mBAAK,YAAY,CAAC,GAAI,KAAK,aAAa,CAAC,GAAI,IAAI;AACjD,yBAAW,MAAM;AACf,qBAAK,aAAa,KAAK;AAAA,kBACrB,eAAe;AAAA,kBACf;AAAA,gBACF,CAAC;AAAA,cACH,CAAC;AAAA,YACH;AAAA,UACF;AACA,eAAK,gBAAgB,KAAK,KAAK,SAAS;AAAA,QAC1C;AAAA,MACF;AAAA,IACF;AACA,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,iBAAiB;AACf,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,iBAAiB,OAAO,MAAM;AAC5B,QAAI,KAAK,aAAa;AACpB,UAAI,cAAc,MAAM;AACxB,UAAI,YAAY,aAAa,YAAY,UAAU,QAAQ,gBAAgB,MAAM,GAAG;AAClF;AAAA,MACF,OAAO;AACL,YAAI,QAAQ,KAAK,qBAAqB,IAAI;AAC1C,YAAI,WAAW,SAAS;AACxB,YAAI,CAAC,UAAU;AACb,cAAI,KAAK,sBAAsB,EAAG,MAAK,gBAAgB,KAAK,IAAI;AAAA,cAAO,MAAK,gBAAgB,KAAK,CAAC,IAAI,CAAC;AAAA,QACzG;AACA,aAAK,YAAY,KAAK,KAAK;AAC3B,aAAK,wBAAwB,KAAK;AAAA,UAChC,eAAe;AAAA,UACf;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,eAAe,OAAO,MAAM;AAC1B,SAAK,kBAAkB,KAAK;AAAA,MAC1B,eAAe;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB,MAAM;AACzB,QAAI,QAAQ;AACZ,QAAI,KAAK,iBAAiB,KAAK,WAAW;AACxC,UAAI,KAAK,sBAAsB,GAAG;AAChC,YAAI,gBAAgB,KAAK,UAAU,OAAO,KAAK,UAAU,QAAQ,KAAK,OAAO,KAAK,aAAa;AAC/F,gBAAQ,gBAAgB,IAAI;AAAA,MAC9B,OAAO;AACL,iBAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,cAAI,eAAe,KAAK,UAAU,CAAC;AACnC,cAAI,gBAAgB,aAAa,OAAO,aAAa,QAAQ,KAAK,OAAO,gBAAgB;AACzF,cAAI,eAAe;AACjB,oBAAQ;AACR;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,eAAe,MAAM,aAAa,QAAQ,OAAO;AAE/C,UAAM,QAAQ,KAAK,iBAAiB,IAAI,KAAK,eAAe,KAAK,KAAK,WAAW,IAAI;AACrF,QAAI,OAAO;AACT,YAAM,MAAM,IAAI,SAAS,KAAK,MAAM;AAAA,IACtC;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,UAAU,KAAK,iBAAiB,KAAK,cAAc;AAAA,EACjE;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,UAAU,KAAK,iBAAiB,eAAe,MAAM,SAAS;AAAA,EAC5E;AAAA,EACA,eAAe,KAAK,OAAO;AACzB,aAAS,QAAQ,OAAO;AACtB,UAAI,KAAK,QAAQ,KAAK;AACpB,eAAO;AAAA,MACT;AACA,UAAI,KAAK,UAAU;AACjB,YAAI,cAAc,KAAK,eAAe,KAAK,KAAK,QAAQ;AACxD,YAAI,aAAa;AACf,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,MAAM,QAAQ;AACxB,QAAI,KAAK,YAAY,KAAK,SAAS,QAAQ;AACzC,UAAI,gBAAgB;AACpB,UAAI,uBAAuB;AAC3B,eAAS,SAAS,KAAK,UAAU;AAC/B,YAAI,KAAK,WAAW,KAAK,GAAG;AAC1B;AAAA,QACF,WAAW,MAAM,iBAAiB;AAChC,iCAAuB;AAAA,QACzB;AAAA,MACF;AACA,UAAI,UAAU,iBAAiB,KAAK,SAAS,QAAQ;AACnD,aAAK,YAAY,CAAC,GAAI,KAAK,aAAa,CAAC,GAAI,IAAI;AACjD,aAAK,kBAAkB;AAAA,MACzB,OAAO;AACL,YAAI,CAAC,QAAQ;AACX,cAAI,QAAQ,KAAK,qBAAqB,IAAI;AAC1C,cAAI,SAAS,GAAG;AACd,iBAAK,YAAY,KAAK,UAAU,OAAO,CAAC,KAAK,MAAM,KAAK,KAAK;AAAA,UAC/D;AAAA,QACF;AACA,YAAI,wBAAwB,gBAAgB,KAAK,iBAAiB,KAAK,SAAS,OAAQ,MAAK,kBAAkB;AAAA,YAAU,MAAK,kBAAkB;AAAA,MAClJ;AACA,WAAK,eAAe,MAAM,KAAK,eAAe,iBAAiB;AAAA,IACjE;AACA,QAAI,SAAS,KAAK;AAClB,QAAI,QAAQ;AACV,WAAK,YAAY,QAAQ,MAAM;AAAA,IACjC;AAAA,EACF;AAAA,EACA,cAAc,MAAM,QAAQ;AAC1B,QAAI,QAAQ,KAAK,qBAAqB,IAAI;AAC1C,QAAI,UAAU,SAAS,IAAI;AACzB,WAAK,YAAY,CAAC,GAAI,KAAK,aAAa,CAAC,GAAI,IAAI;AAAA,IACnD,WAAW,CAAC,UAAU,QAAQ,IAAI;AAChC,WAAK,YAAY,KAAK,UAAU,OAAO,CAAC,KAAK,MAAM,KAAK,KAAK;AAAA,IAC/D;AACA,SAAK,kBAAkB;AACvB,SAAK,eAAe,MAAM,KAAK,eAAe,iBAAiB;AAC/D,QAAI,KAAK,YAAY,KAAK,SAAS,QAAQ;AACzC,eAAS,SAAS,KAAK,UAAU;AAC/B,aAAK,cAAc,OAAO,MAAM;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW,MAAM;AACf,WAAO,KAAK,qBAAqB,IAAI,KAAK;AAAA,EAC5C;AAAA,EACA,wBAAwB;AACtB,WAAO,KAAK,iBAAiB,KAAK,iBAAiB;AAAA,EACrD;AAAA,EACA,0BAA0B;AACxB,WAAO,KAAK,iBAAiB,KAAK,iBAAiB;AAAA,EACrD;AAAA,EACA,0BAA0B;AACxB,WAAO,KAAK,iBAAiB,KAAK,iBAAiB;AAAA,EACrD;AAAA,EACA,WAAW,MAAM;AACf,WAAO,KAAK,QAAQ,QAAQ,QAAQ,EAAE,KAAK,YAAY,KAAK,SAAS;AAAA,EACvE;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,gBAAgB,KAAK,gBAAgB,KAAK;AAAA,EACxD;AAAA,EACA,mBAAmB,MAAM;AACvB,QAAI,KAAK,aAAc,QAAO,KAAK,OAAO,KAAK,aAAa,KAAK,IAAI,IAAI,KAAK,aAAa,SAAS;AAAA,QAAO,QAAO;AAAA,EACpH;AAAA,EACA,WAAW,OAAO;AAChB,QAAI,KAAK,mBAAmB,CAAC,KAAK,SAAS,KAAK,MAAM,WAAW,IAAI;AACnE,YAAM,aAAa,aAAa;AAChC,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA,EACA,OAAO,OAAO;AACZ,QAAI,KAAK,mBAAmB,CAAC,KAAK,SAAS,KAAK,MAAM,WAAW,IAAI;AACnE,YAAM,eAAe;AACrB,UAAI,WAAW,KAAK;AACpB,UAAI,KAAK,UAAU,UAAU,MAAM,KAAK,aAAa,GAAG;AACtD,YAAI,gBAAgB,KAAK;AACzB,aAAK,QAAQ,KAAK,SAAS,CAAC;AAC5B,YAAI,KAAK,cAAc;AACrB,eAAK,WAAW,KAAK;AAAA,YACnB,eAAe;AAAA,YACf;AAAA,YACA,UAAU;AAAA,YACV,OAAO;AAAA,YACP,QAAQ,MAAM;AACZ,mBAAK,gBAAgB,UAAU,aAAa;AAAA,YAC9C;AAAA,UACF,CAAC;AAAA,QACH,OAAO;AACL,eAAK,WAAW,KAAK;AAAA,YACnB,eAAe;AAAA,YACf;AAAA,YACA,UAAU;AAAA,YACV,OAAO;AAAA,UACT,CAAC;AACD,eAAK,gBAAgB,UAAU,aAAa;AAAA,QAC9C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,gBAAgB,UAAU,eAAe;AACvC,SAAK,iBAAiB,OAAO,eAAe,CAAC;AAC7C,SAAK,MAAM,KAAK,QAAQ;AACxB,SAAK,gBAAgB,SAAS;AAAA,MAC5B,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,kBAAkB,KAAK,UAAU,KAAK,UAAU,MAAM,KAAK,aAAa,GAAG;AAClF,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA,EACA,YAAY,OAAO;AACjB,QAAI,KAAK,gBAAgB;AACvB,UAAI,OAAO,MAAM,cAAc,sBAAsB;AACrD,UAAI,MAAM,IAAI,KAAK,OAAO,KAAK,SAAS,MAAM,IAAI,KAAK,QAAQ,MAAM,IAAI,KAAK,MAAM,KAAK,UAAU,MAAM,IAAI,KAAK,KAAK;AACrH,aAAK,YAAY;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,UAAU,UAAU,eAAe;AAC3C,QAAI,CAAC,UAAU;AAEb,aAAO;AAAA,IACT,WAAW,KAAK,iBAAiB,aAAa,GAAG;AAC/C,UAAI,QAAQ;AACZ,UAAI,UAAU;AACZ,YAAI,aAAa,UAAU;AACzB,kBAAQ;AAAA,QACV,OAAO;AACL,cAAI,SAAS,SAAS;AACtB,iBAAO,UAAU,MAAM;AACrB,gBAAI,WAAW,UAAU;AACvB,sBAAQ;AACR;AAAA,YACF;AACA,qBAAS,OAAO;AAAA,UAClB;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,iBAAiB,WAAW;AAC1B,QAAI,YAAY,KAAK;AACrB,QAAI,WAAW;AACb,UAAI,OAAO,cAAc,UAAU;AACjC,YAAI,OAAO,cAAc,SAAU,QAAO,cAAc;AAAA,iBAAmB,MAAM,QAAQ,SAAS,EAAG,QAAO,UAAU,QAAQ,SAAS,KAAK;AAAA,MAC9I,WAAW,MAAM,QAAQ,SAAS,GAAG;AACnC,YAAI,OAAO,cAAc,UAAU;AACjC,iBAAO,UAAU,QAAQ,SAAS,KAAK;AAAA,QACzC,WAAW,MAAM,QAAQ,SAAS,GAAG;AACnC,mBAAS,KAAK,WAAW;AACvB,qBAAS,MAAM,WAAW;AACxB,kBAAI,MAAM,IAAI;AACZ,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ,OAAO;AACb,QAAI,cAAc;AAClB,QAAI,gBAAgB,IAAI;AACtB,WAAK,gBAAgB;AAAA,IACvB,OAAO;AACL,WAAK,gBAAgB,CAAC;AACtB,YAAM,eAAe,KAAK,SAAS,MAAM,GAAG;AAC5C,YAAM,aAAa,cAAc,WAAW,EAAE,kBAAkB,KAAK,YAAY;AACjF,YAAM,eAAe,KAAK,eAAe;AACzC,eAAS,QAAQ,KAAK,OAAO;AAC3B,YAAI,WAAW,mBACV;AAEL,YAAI,oBAAoB;AAAA,UACtB;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,YAAI,iBAAiB,KAAK,kBAAkB,UAAU,iBAAiB,KAAK,KAAK,gBAAgB,UAAU,iBAAiB,MAAM,CAAC,iBAAiB,KAAK,gBAAgB,UAAU,iBAAiB,KAAK,KAAK,kBAAkB,UAAU,iBAAiB,IAAI;AAC7P,eAAK,cAAc,KAAK,QAAQ;AAAA,QAClC;AAAA,MACF;AAAA,IACF;AACA,SAAK,sBAAsB;AAC3B,SAAK,SAAS,KAAK;AAAA,MACjB,QAAQ;AAAA,MACR,eAAe,KAAK;AAAA,IACtB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,SAAK,gBAAgB;AACrB,QAAI,KAAK,mBAAmB,KAAK,gBAAgB,eAAe;AAC9D,WAAK,gBAAgB,cAAc,QAAQ;AAAA,IAC7C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,OAAO;AAC1B,SAAK,iBAAiB,KAAK,UAAU,cAAc,KAAK;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,SAAS;AAChB,QAAI,KAAK,eAAe;AACtB,WAAK,UAAU,SAAS,OAAO;AAAA,IACjC,WAAW,KAAK,oBAAoB,KAAK,iBAAiB,eAAe;AACvE,UAAI,KAAK,iBAAiB,cAAc,UAAU;AAChD,aAAK,iBAAiB,cAAc,SAAS,OAAO;AAAA,MACtD,OAAO;AACL,aAAK,iBAAiB,cAAc,aAAa,QAAQ;AACzD,aAAK,iBAAiB,cAAc,YAAY,QAAQ;AAAA,MAC1D;AAAA,IACF;AAAA,EACF;AAAA,EACA,kBAAkB,MAAM,mBAAmB;AACzC,QAAI,MAAM;AACR,UAAI,UAAU;AACd,UAAI,KAAK,UAAU;AACjB,YAAI,aAAa,CAAC,GAAG,KAAK,QAAQ;AAClC,aAAK,WAAW,CAAC;AACjB,iBAAS,aAAa,YAAY;AAChC,cAAI,gBAAgB,mBACf;AAEL,cAAI,KAAK,gBAAgB,eAAe,iBAAiB,GAAG;AAC1D,sBAAU;AACV,iBAAK,SAAS,KAAK,aAAa;AAAA,UAClC;AAAA,QACF;AAAA,MACF;AACA,UAAI,SAAS;AACX,aAAK,WAAW;AAChB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EACA,gBAAgB,MAAM,QAAQ;AAC5B,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,UAAU;AACd,aAAS,SAAS,cAAc;AAC9B,UAAI,aAAa,cAAc,OAAO,iBAAiB,MAAM,KAAK,CAAC,CAAC,EAAE,kBAAkB,KAAK,YAAY;AACzG,UAAI,WAAW,QAAQ,UAAU,IAAI,IAAI;AACvC,kBAAU;AAAA,MACZ;AAAA,IACF;AACA,QAAI,CAAC,WAAW,gBAAgB,CAAC,KAAK,WAAW,IAAI,GAAG;AACtD,gBAAU,KAAK,kBAAkB,MAAM;AAAA,QACrC;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC,KAAK;AAAA,IACR;AACA,WAAO;AAAA,EACT;AAAA,EACA,SAAS,SAAS,OAAO;AACvB,UAAM,iBAAiB,QAAQ,gBAAgB;AAC/C,WAAO,iBAAiB,eAAe,KAAK,EAAE,QAAQ;AAAA,EACxD;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,GAAG,cAAc,SAAS,CAAC;AAAA,EACzC;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,uBAAuB;AAC9B,WAAK,sBAAsB,YAAY;AAAA,IACzC;AACA,QAAI,KAAK,sBAAsB;AAC7B,WAAK,qBAAqB,YAAY;AAAA,IACxC;AACA,UAAM,YAAY;AAAA,EACpB;AAAA,EACA,OAAO,OAAO,SAAS,aAAa,mBAAmB;AACrD,WAAO,KAAK,qBAAqB,OAAS,kBAAqB,qBAAqB,CAAC,CAAC;AAAA,EACxF;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,QAAQ,CAAC;AAAA,IACtB,gBAAgB,SAAS,oBAAoB,IAAI,KAAK,UAAU;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,MAAM,CAAC;AACnC,QAAG,eAAe,UAAU,MAAM,CAAC;AACnC,QAAG,eAAe,UAAU,MAAM,CAAC;AACnC,QAAG,eAAe,UAAU,MAAM,CAAC;AACnC,QAAG,eAAe,UAAU,MAAM,CAAC;AACnC,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AACrE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AACnE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AACrE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AACrE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AACrE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,uBAAuB,GAAG;AAC3E,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,sBAAsB,GAAG;AAC1E,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,uBAAuB,GAAG;AAC3E,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,sBAAsB,GAAG;AAC1E,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB,GAAG;AACzE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW,SAAS,WAAW,IAAI,KAAK;AACtC,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,MAAM,CAAC;AACtB,QAAG,YAAY,MAAM,CAAC;AAAA,MACxB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,kBAAkB,GAAG;AACtE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAC/D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AAAA,MACzE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,eAAe;AAAA,MACf,aAAa;AAAA,MACb,WAAW;AAAA,MACX,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,gBAAgB,CAAC,GAAG,kBAAkB,kBAAkB,gBAAgB;AAAA,MACxE,gBAAgB,CAAC,GAAG,kBAAkB,kBAAkB,gBAAgB;AAAA,MACxE,kBAAkB,CAAC,GAAG,oBAAoB,oBAAoB,gBAAgB;AAAA,MAC9E,sBAAsB,CAAC,GAAG,wBAAwB,wBAAwB,gBAAgB;AAAA,MAC1F,wBAAwB,CAAC,GAAG,0BAA0B,0BAA0B,gBAAgB;AAAA,MAChG,SAAS,CAAC,GAAG,WAAW,WAAW,gBAAgB;AAAA,MACnD,aAAa;AAAA,MACb,cAAc;AAAA,MACd,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,cAAc,CAAC,GAAG,gBAAgB,gBAAgB,gBAAgB;AAAA,MAClE,QAAQ,CAAC,GAAG,UAAU,UAAU,gBAAgB;AAAA,MAChD,sBAAsB,CAAC,GAAG,wBAAwB,wBAAwB,gBAAgB;AAAA,MAC1F,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,mBAAmB;AAAA,MACnB,eAAe;AAAA,MACf,cAAc;AAAA,MACd,cAAc;AAAA,MACd,MAAM,CAAC,GAAG,QAAQ,QAAQ,gBAAgB;AAAA,MAC1C,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,MACrE,uBAAuB,CAAC,GAAG,yBAAyB,yBAAyB,eAAe;AAAA,MAC5F,sBAAsB;AAAA,MACtB,aAAa,CAAC,GAAG,eAAe,eAAe,eAAe;AAAA,MAC9D,cAAc;AAAA,MACd,SAAS;AAAA,MACT,mBAAmB,CAAC,GAAG,qBAAqB,qBAAqB,gBAAgB;AAAA,MACjF,mBAAmB;AAAA,IACrB;AAAA,IACA,SAAS;AAAA,MACP,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,mBAAmB;AAAA,MACnB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,UAAU;AAAA,IACZ;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,SAAS,CAAC,GAAM,0BAA6B,4BAA+B,oBAAoB;AAAA,IAClI,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,eAAe,EAAE,GAAG,CAAC,GAAG,QAAQ,YAAY,aAAa,aAAa,WAAW,SAAS,GAAG,CAAC,SAAS,8BAA8B,GAAG,MAAM,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,SAAS,wBAAwB,GAAG,MAAM,GAAG,CAAC,GAAG,eAAe,gBAAgB,GAAG,CAAC,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,QAAQ,cAAc,GAAG,MAAM,GAAG,CAAC,SAAS,uBAAuB,GAAG,MAAM,GAAG,CAAC,GAAG,QAAQ,YAAY,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,cAAc,IAAI,QAAQ,UAAU,gBAAgB,OAAO,GAAG,uBAAuB,GAAG,iBAAiB,SAAS,YAAY,GAAG,CAAC,SAAS,sBAAsB,GAAG,MAAM,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,cAAc,eAAe,GAAG,SAAS,YAAY,SAAS,gBAAgB,YAAY,QAAQ,WAAW,YAAY,uBAAuB,cAAc,GAAG,MAAM,GAAG,CAAC,cAAc,eAAe,GAAG,YAAY,uBAAuB,cAAc,SAAS,YAAY,gBAAgB,YAAY,QAAQ,SAAS,GAAG,CAAC,SAAS,wBAAwB,QAAQ,QAAQ,GAAG,WAAW,SAAS,GAAG,MAAM,GAAG,CAAC,QAAQ,QAAQ,GAAG,wBAAwB,GAAG,SAAS,GAAG,CAAC,GAAG,SAAS,WAAW,QAAQ,cAAc,cAAc,aAAa,SAAS,YAAY,eAAe,eAAe,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,GAAG,SAAS,WAAW,QAAQ,cAAc,cAAc,aAAa,SAAS,YAAY,eAAe,aAAa,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC,SAAS,wBAAwB,QAAQ,QAAQ,GAAG,MAAM,GAAG,CAAC,QAAQ,QAAQ,GAAG,sBAAsB,GAAG,CAAC,GAAG,QAAQ,cAAc,aAAa,SAAS,SAAS,eAAe,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,GAAG,QAAQ,cAAc,aAAa,SAAS,SAAS,aAAa,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC,GAAG,QAAQ,UAAU,CAAC;AAAA,IAC13D,UAAU,SAAS,cAAc,IAAI,KAAK;AACxC,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,WAAW,QAAQ,SAAS,kCAAkC,QAAQ;AACvE,iBAAO,IAAI,OAAO,MAAM;AAAA,QAC1B,CAAC,EAAE,YAAY,SAAS,sCAAsC,QAAQ;AACpE,iBAAO,IAAI,WAAW,MAAM;AAAA,QAC9B,CAAC,EAAE,aAAa,SAAS,yCAAyC;AAChE,iBAAO,IAAI,YAAY;AAAA,QACzB,CAAC,EAAE,aAAa,SAAS,uCAAuC,QAAQ;AACtE,iBAAO,IAAI,YAAY,MAAM;AAAA,QAC/B,CAAC;AACD,QAAG,WAAW,GAAG,qBAAqB,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,8BAA8B,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,6BAA6B,GAAG,GAAG,cAAc,EAAE,GAAG,6BAA6B,GAAG,GAAG,aAAa,EAAE,GAAG,8BAA8B,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,qBAAqB,GAAG,GAAG,OAAO,EAAE,EAAE,GAAG,8BAA8B,GAAG,GAAG,gBAAgB,CAAC;AACvX,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,WAAW,IAAI,UAAU;AAC5B,QAAG,WAAW,WAAW,IAAI,cAAc,EAAE,WAAW,IAAI,KAAK;AACjE,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,WAAW,IAAI,gBAAgB,MAAM;AAC/D,QAAG,UAAU;AACb,QAAG,WAAW,oBAAoB,IAAI,kBAAkB,IAAI,eAAe;AAC3E,QAAG,UAAU;AACb,QAAG,cAAc,IAAI,kBAAkB,IAAI,kBAAkB,IAAI,CAAC;AAClE,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,SAAS,UAAU,IAAI,YAAY,MAAM,OAAO,OAAO,QAAQ,MAAM;AACnF,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,CAAC,IAAI,YAAY,IAAI,YAAY,KAAK,QAAQ,IAAI,YAAY,EAAE,WAAW,EAAE;AACnG,QAAG,UAAU;AACb,QAAG,WAAW,oBAAoB,IAAI,kBAAkB,IAAI,eAAe;AAAA,MAC7E;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAiB,SAAY,SAAY,MAAS,kBAAqB,SAAS,UAAU,cAAc,YAAY,aAAa,WAAW,aAAa,WAAW,WAAW,YAAY,iBAAoB,SAAS;AAAA,IACvO,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,MAAM,CAAC;AAAA,IAC7E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,UAAU,cAAc,YAAY,aAAa,WAAW,aAAa,WAAW,WAAW,YAAY,eAAe;AAAA,MAClJ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAsGV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,SAAS;AAAA,IACvB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,QACf,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,QACb,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,QACf,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,QACf,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,QACf,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,QACd,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,QACpB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,QACrB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,QACpB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,QACnB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,OAAO,OAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAY;AAAA,EAC/C;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,MAAM,YAAY;AAAA,IAC5B,SAAS,CAAC,MAAM,YAAY;AAAA,EAC9B,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,MAAM,cAAc,YAAY;AAAA,EAC5C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,MAAM,YAAY;AAAA,MAC5B,SAAS,CAAC,MAAM,YAAY;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["TreeClasses"]}
﻿namespace TRtek_EYS.Domain.Entities;

public class Configuration : BaseEntity
{
    public string Name { get; set; } = null!;
    public string? Description { get; set; }
    public string? WebServerIp { get; set; }
    public string? WebServerUrl { get; set; }
    public string? DatabaseType { get; set; }
    public string? DatabaseVersion { get; set; }
    public string? Username { get; set; }
    public string? EncryptedPassword { get; set; }

    public Guid CustomerId { get; set; }
    public Customer? Customer { get; set; }
}

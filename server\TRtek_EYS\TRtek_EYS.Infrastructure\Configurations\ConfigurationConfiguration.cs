﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TRtek_EYS.Domain.Entities;

namespace TRtek_EYS.Infrastructure.Configurations
{
    public class ConfigurationConfiguration : IEntityTypeConfiguration<Configuration>
    {
        public void Configure(EntityTypeBuilder<Configuration> builder)
        {
            builder.ToTable("Configurations");

            builder.HasKey(x => x.Id);

            builder.Property(x => x.Name)
                   .IsRequired()
                   .HasMaxLength(100);

            builder.Property(x => x.Description)
                   .HasMaxLength(250);

            builder.Property(x => x.WebServerIp)
                   .HasMaxLength(45); // IPv4: 15 karakter, IPv6: 45 karakter

            builder.Property(x => x.WebServerUrl)
                   .HasMaxLength(200);

            builder.Property(x => x.DatabaseType)
                   .HasMaxLength(50);

            builder.Property(x => x.DatabaseVersion)
                   .HasMaxLength(50);

            builder.Property(x => x.Username)
                   .HasMaxLength(100);

            builder.Property(x => x.EncryptedPassword)
                   .HasMaxLength(200);

            builder.Property(x => x.CreatedAt)
                   .IsRequired();

            // İlişki: Configuration -> Customer
            builder.HasOne(x => x.Customer)
                   .WithMany()
                   .HasForeignKey(x => x.CustomerId)
                   .OnDelete(DeleteBehavior.Cascade);
        }
    }
}

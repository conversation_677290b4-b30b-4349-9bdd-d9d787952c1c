import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { Project } from '../models/project.model';

@Injectable({
    providedIn: 'root'
})
export class ProjectService {
    private apiUrl = `${environment.apiUrl}/api/projects`;

    constructor(private http: HttpClient) {}

    // V1 API
    getProjects(): Observable<any[]> {
        return this.http.get<any[]>(this.apiUrl);
    }

    // V2 API - Genişletilmiş veriler
    getProjectsV2(): Observable<any[]> {
        return this.http.get<any[]>(`${this.apiUrl}/v2`);
    }

    getProjectById(id: string): Observable<any> {
        return this.http.get<any>(`${this.apiUrl}/${id}`);
    }

    createProject(project: any): Observable<any> {
        return this.http.post<any>(this.apiUrl, project);
    }

    updateProject(id: string, project: any): Observable<any> {
        return this.http.put<any>(`${this.apiUrl}/${id}`, project);
    }

    deleteProject(id: string): Observable<any> {
        return this.http.delete<any>(`${this.apiUrl}/${id}`);
    }

    // Dashboard için istatistikler
    getProjectStats(): Observable<any> {
        return this.getProjectsV2();
    }
}

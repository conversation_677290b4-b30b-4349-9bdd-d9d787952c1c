import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Table, TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { InputIconModule } from 'primeng/inputicon';
import { IconFieldModule } from 'primeng/iconfield';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToastModule } from 'primeng/toast';
import { ProgressBar } from 'primeng/progressbar';
import { ConfirmationService, MessageService } from 'primeng/api';
import { Technology, TechnologyType, TechnologyCategory, MaturityLevel, LearningCurve } from '../../models/technology.model';
import { TechnologyService } from '../../services/technology.service';

@Component({
    selector: 'app-technologies-list',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        TableModule,
        ButtonModule,
        InputTextModule,
        InputIconModule,
        IconFieldModule,
        TagModule,
        TooltipModule,
        ConfirmDialogModule,
        ToastModule,
        ProgressBar
    ],
    providers: [ConfirmationService, MessageService],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <h5>Teknoloji Yönetimi</h5>
                        <p-button
                            icon="pi pi-plus"
                            (onClick)="createTechnology()"
                            severity="primary"
                            rounded
                            outlined
                            pTooltip="Yeni Teknoloji">
                        </p-button>
                    </div>

                    <p-table 
                        #dt 
                        [value]="technologies" 
                        [rows]="10" 
                        [paginator]="true"
                        [globalFilterFields]="['technologyCode', 'name', 'vendor', 'type', 'category']"
                        [tableStyle]="{ 'min-width': '75rem' }"
                        [rowHover]="true"
                        dataKey="id"
                        currentPageReportTemplate="{first} - {last} / {totalRecords} teknoloji"
                        [showCurrentPageReport]="true">
                        
                        <ng-template pTemplate="caption">
                            <div class="flex align-items-center justify-content-between">
                                <h5 class="m-0">Teknolojiler</h5>
                                <p-iconfield iconPosition="left">
                                    <p-inputicon>
                                        <i class="pi pi-search"></i>
                                    </p-inputicon>
                                    <input 
                                        pInputText 
                                        type="text" 
                                        (input)="dt.filterGlobal($any($event.target).value, 'contains')" 
                                        placeholder="Teknoloji ara..." />
                                </p-iconfield>
                            </div>
                        </ng-template>

                        <ng-template pTemplate="header">
                            <tr>
                                <th pSortableColumn="technologyCode">
                                    Teknoloji Kodu <p-sortIcon field="technologyCode"></p-sortIcon>
                                </th>
                                <th pSortableColumn="name">
                                    Teknoloji Adı <p-sortIcon field="name"></p-sortIcon>
                                </th>
                                <th pSortableColumn="type">
                                    Tip <p-sortIcon field="type"></p-sortIcon>
                                </th>
                                <th pSortableColumn="category">
                                    Kategori <p-sortIcon field="category"></p-sortIcon>
                                </th>
                                <th pSortableColumn="version">
                                    Versiyon <p-sortIcon field="version"></p-sortIcon>
                                </th>
                                <th pSortableColumn="popularityScore">
                                    Popülerlik <p-sortIcon field="popularityScore"></p-sortIcon>
                                </th>
                                <th pSortableColumn="maturityLevel">
                                    Olgunluk <p-sortIcon field="maturityLevel"></p-sortIcon>
                                </th>
                                <th>Proje Sayısı</th>
                                <th>Durum</th>
                                <th>İşlemler</th>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="body" let-technology>
                            <tr [class]="technology.isDeprecated ? 'bg-red-50' : ''">
                                <td>
                                    <span class="font-medium">{{ technology.technologyCode }}</span>
                                </td>
                                <td>
                                    <div>
                                        <span class="font-medium">{{ technology.name }}</span>
                                        <div class="text-sm text-500 mt-1">{{ technology.description | slice:0:50 }}...</div>
                                        <div class="flex gap-1 mt-1" *ngIf="technology.isRecommended || technology.isDeprecated">
                                            <p-tag 
                                                value="Önerilen" 
                                                severity="success" 
                                                class="text-xs"
                                                *ngIf="technology.isRecommended">
                                            </p-tag>
                                            <p-tag 
                                                value="Deprecated" 
                                                severity="danger" 
                                                class="text-xs"
                                                *ngIf="technology.isDeprecated">
                                            </p-tag>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="technology.type" 
                                        severity="info">
                                    </p-tag>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="technology.category" 
                                        severity="secondary">
                                    </p-tag>
                                </td>
                                <td>
                                    <div>
                                        <span class="font-medium">{{ technology.version }}</span>
                                        <div class="text-sm text-500" *ngIf="technology.latestVersion && technology.version !== technology.latestVersion">
                                            Son: {{ technology.latestVersion }}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <div class="mb-1">
                                            <span class="font-medium">{{ technology.popularityScore }}%</span>
                                        </div>
                                        <p-progressBar 
                                            [value]="technology.popularityScore" 
                                            [style]="{'height': '6px'}"
                                            [showValue]="false">
                                        </p-progressBar>
                                    </div>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="technology.maturityLevel" 
                                        [severity]="getMaturitySeverity(technology.maturityLevel)">
                                    </p-tag>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <span class="font-medium" 
                                              [class]="technology.projectCount > 0 ? 'text-green-600' : 'text-500'">
                                            {{ technology.projectCount }}
                                        </span>
                                        <span class="text-500"> proje</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="flex flex-column gap-1">
                                        <p-tag 
                                            [value]="technology.isActive ? 'Aktif' : 'Pasif'" 
                                            [severity]="technology.isActive ? 'success' : 'secondary'"
                                            class="text-xs">
                                        </p-tag>
                                        <p-tag 
                                            [value]="technology.learningCurve" 
                                            [severity]="getLearningCurveSeverity(technology.learningCurve)"
                                            class="text-xs">
                                        </p-tag>
                                    </div>
                                </td>
                                <td>
                                    <div class="flex gap-2">
                                        <p-button 
                                            icon="pi pi-eye" 
                                            class="p-button-rounded p-button-text p-button-info"
                                            pTooltip="Detay"
                                            (onClick)="viewTechnology(technology.id)">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-pencil" 
                                            class="p-button-rounded p-button-text p-button-warning"
                                            pTooltip="Düzenle"
                                            (onClick)="editTechnology(technology.id)">
                                        </p-button>
                                        <p-button 
                                            [icon]="technology.isRecommended ? 'pi pi-star-fill' : 'pi pi-star'" 
                                            class="p-button-rounded p-button-text p-button-help"
                                            [pTooltip]="technology.isRecommended ? 'Öneriden Kaldır' : 'Öner'"
                                            (onClick)="toggleRecommendation(technology)">
                                        </p-button>
                                        <p-button 
                                            [icon]="technology.isActive ? 'pi pi-pause' : 'pi pi-play'" 
                                            class="p-button-rounded p-button-text"
                                            [class.p-button-secondary]="technology.isActive"
                                            [class.p-button-success]="!technology.isActive"
                                            [pTooltip]="technology.isActive ? 'Pasifleştir' : 'Aktifleştir'"
                                            (onClick)="toggleTechnologyStatus(technology)">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-trash" 
                                            class="p-button-rounded p-button-text p-button-danger"
                                            pTooltip="Sil"
                                            (onClick)="deleteTechnology(technology)"
                                            [disabled]="technology.projectCount > 0">
                                        </p-button>
                                    </div>
                                </td>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="emptymessage">
                            <tr>
                                <td colspan="10" class="text-center py-4">
                                    <i class="pi pi-info-circle text-4xl text-500 mb-3"></i>
                                    <div class="text-500 font-medium">Henüz teknoloji bulunmuyor</div>
                                    <div class="text-500">Yeni teknoloji eklemek için "Yeni Teknoloji" butonunu kullanın</div>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                </div>
            </div>
        </div>

        <p-confirmDialog></p-confirmDialog>
        <p-toast></p-toast>
    `
})
export class TechnologiesListComponent implements OnInit {
    @ViewChild('dt') table!: Table;
    
    technologies: Technology[] = [];

    constructor(
        private technologyService: TechnologyService,
        private router: Router,
        private confirmationService: ConfirmationService,
        private messageService: MessageService
    ) {}

    ngOnInit() {
        this.loadTechnologies();
    }

    loadTechnologies() {
        this.technologyService.getTechnologies().subscribe(technologies => {
            this.technologies = technologies;
        });
    }

    createTechnology() {
        this.router.navigate(['/pages/technologies/new']);
    }

    viewTechnology(id: string) {
        this.router.navigate(['/pages/technologies', id]);
    }

    editTechnology(id: string) {
        this.router.navigate(['/pages/technologies', id, 'edit']);
    }

    toggleRecommendation(technology: Technology) {
        const action = technology.isRecommended ? 'öneriden kaldırmak' : 'önermek';
        this.confirmationService.confirm({
            message: `"${technology.name}" teknolojisini ${action} istediğinizden emin misiniz?`,
            header: 'Öneri Durumu Değiştirme Onayı',
            icon: technology.isRecommended ? 'pi pi-star-fill' : 'pi pi-star',
            acceptLabel: 'Evet',
            rejectLabel: 'Hayır',
            accept: () => {
                this.technologyService.toggleRecommendation(technology.id).subscribe(() => {
                    this.loadTechnologies();
                    this.messageService.add({
                        severity: 'info',
                        summary: 'Başarılı',
                        detail: `Teknoloji ${technology.isRecommended ? 'öneriden kaldırıldı' : 'önerildi'}`
                    });
                });
            }
        });
    }

    toggleTechnologyStatus(technology: Technology) {
        const action = technology.isActive ? 'pasifleştirmek' : 'aktifleştirmek';
        this.confirmationService.confirm({
            message: `"${technology.name}" teknolojisini ${action} istediğinizden emin misiniz?`,
            header: 'Teknoloji Durumu Değiştirme Onayı',
            icon: technology.isActive ? 'pi pi-pause' : 'pi pi-play',
            acceptLabel: 'Evet',
            rejectLabel: 'Hayır',
            accept: () => {
                this.technologyService.toggleTechnologyStatus(technology.id).subscribe(() => {
                    this.loadTechnologies();
                    this.messageService.add({
                        severity: 'info',
                        summary: 'Başarılı',
                        detail: `Teknoloji ${technology.isActive ? 'pasifleştirildi' : 'aktifleştirildi'}`
                    });
                });
            }
        });
    }

    deleteTechnology(technology: Technology) {
        this.confirmationService.confirm({
            message: `"${technology.name}" teknolojisini silmek istediğinizden emin misiniz?`,
            header: 'Teknoloji Silme Onayı',
            icon: 'pi pi-exclamation-triangle',
            acceptLabel: 'Evet',
            rejectLabel: 'Hayır',
            accept: () => {
                this.technologyService.deleteTechnology(technology.id).subscribe(success => {
                    if (success) {
                        this.technologies = this.technologies.filter(t => t.id !== technology.id);
                        this.messageService.add({
                            severity: 'success',
                            summary: 'Başarılı',
                            detail: 'Teknoloji başarıyla silindi'
                        });
                    } else {
                        this.messageService.add({
                            severity: 'error',
                            summary: 'Hata',
                            detail: 'Teknoloji silinemedi. Kullanımda olan teknolojiler silinemez.'
                        });
                    }
                });
            }
        });
    }

    getMaturitySeverity(maturityLevel: MaturityLevel): string {
        switch (maturityLevel) {
            case MaturityLevel.EXPERIMENTAL:
                return 'danger';
            case MaturityLevel.ALPHA:
                return 'warning';
            case MaturityLevel.BETA:
                return 'info';
            case MaturityLevel.STABLE:
                return 'success';
            case MaturityLevel.MATURE:
                return 'success';
            case MaturityLevel.LEGACY:
                return 'secondary';
            case MaturityLevel.DEPRECATED:
                return 'danger';
            default:
                return 'info';
        }
    }

    getLearningCurveSeverity(learningCurve: LearningCurve): string {
        switch (learningCurve) {
            case LearningCurve.VERY_EASY:
                return 'success';
            case LearningCurve.EASY:
                return 'success';
            case LearningCurve.MODERATE:
                return 'warning';
            case LearningCurve.HARD:
                return 'danger';
            case LearningCurve.VERY_HARD:
                return 'danger';
            default:
                return 'info';
        }
    }
}

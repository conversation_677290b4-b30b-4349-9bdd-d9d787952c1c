﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.RepositoryInfos.Commands.Delete
{
    public class DeleteRepositoryInfoCommandHandler : IRequestHandler<DeleteRepositoryInfoCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public DeleteRepositoryInfoCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(DeleteRepositoryInfoCommand request, CancellationToken cancellationToken)
        {
            var repo = await _context.RepositoryInfos
                .FirstOrDefaultAsync(r => r.Id == request.Id, cancellationToken);

            if (repo is null)
                throw new Exception("Silinecek repository bulunamadı.");

            _context.RepositoryInfos.Remove(repo);
            await _context.SaveChangesAsync(cancellationToken);

            return repo.Id;
        }
    }
}

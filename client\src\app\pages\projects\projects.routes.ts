import { Routes } from '@angular/router';

export default [
    {
        path: '',
        loadComponent: () => import('./components/projects-list/projects-list.component').then(m => m.ProjectsListComponent)
    },
    {
        path: 'new',
        loadComponent: () => import('./components/project-form/project-form.component').then(m => m.ProjectFormComponent)
    },
    {
        path: ':id',
        loadComponent: () => import('./components/project-detail/project-detail.component').then(m => m.ProjectDetailComponent)
    },
    {
        path: ':id/edit',
        loadComponent: () => import('./components/project-form/project-form.component').then(m => m.ProjectFormComponent)
    }
] as Routes;

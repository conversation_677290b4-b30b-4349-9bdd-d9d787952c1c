import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Router } from '@angular/router';
import { ErrorHandlerService } from '../services/error-handler.service';

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {
    constructor(
        private errorHandler: ErrorHandlerService,
        private router: Router
    ) {}

    intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
        return next.handle(req).pipe(
            catchError((error: HttpErrorResponse) => {
                // Handle 401 Unauthorized - redirect to login
                if (error.status === 401) {
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                    this.router.navigate(['/auth/login']);
                }

                // Handle other errors
                return this.errorHandler.handleError(error);
            })
        );
    }
}

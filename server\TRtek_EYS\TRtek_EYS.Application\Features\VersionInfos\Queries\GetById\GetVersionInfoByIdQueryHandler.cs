﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.VersionInfos.Queries.GetById
{
    public class GetVersionInfoByIdQueryHandler : IRequestHandler<GetVersionInfoByIdQuery, VersionInfoGetByIdDto>
    {
        private readonly IApplicationDbContext _context;

        public GetVersionInfoByIdQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<VersionInfoGetByIdDto> Handle(GetVersionInfoByIdQuery request, CancellationToken cancellationToken)
        {
            var version = await _context.VersionInfos
                .AsNoTracking()
                .FirstOrDefaultAsync(v => v.Id == request.Id, cancellationToken);

            if (version is null)
                throw new Exception("Sürüm bilgisi bulunamadı.");

            return new VersionInfoGetByIdDto
            {
                Id = version.Id,
                VersionNumber = version.VersionNumber,
                ReleaseDate = version.ReleaseDate,
                ReleaseNotes = version.ReleaseNotes,
                Status = version.Status,
                ProjectId = version.ProjectId
            };
        }
    }
}

﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.VersionInfos.Commands.Delete
{
    public class DeleteVersionInfoCommandHandler : IRequestHandler<DeleteVersionInfoCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public DeleteVersionInfoCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(DeleteVersionInfoCommand request, CancellationToken cancellationToken)
        {
            var version = await _context.VersionInfos
                .FirstOrDefaultAsync(v => v.Id == request.Id, cancellationToken);

            if (version is null)
                throw new Exception("Silinecek sürüm bilgisi bulunamadı.");

            _context.VersionInfos.Remove(version);
            await _context.SaveChangesAsync(cancellationToken);

            return version.Id;
        }
    }
}

using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;
using System.Text.Json;

namespace TRtek_EYS.Application.Features.Customers.Queries.GetAll
{
    public class GetAllCustomersV2QueryHandler : IRequestHandler<GetAllCustomersV2Query, List<CustomerGetAllDtoV2>>
    {
        private readonly IApplicationDbContext _context;

        public GetAllCustomersV2QueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<CustomerGetAllDtoV2>> Handle(GetAllCustomersV2Query request, CancellationToken cancellationToken)
        {
            var customers = await _context.Customers
                .AsNoTracking()
                .ToListAsync(cancellationToken);

            var result = customers.Select(customer => new CustomerGetAllDtoV2
            {
                Id = customer.Id,
                Name = customer.Name,
                ContactPerson = customer.ContactPerson,
                Email = customer.Email,
                Phone = customer.Phone,
                Address = customer.Address,
                TaxNumber = customer.TaxNumber,
                IsActive = customer.IsActive,
                
                // Yeni alanlar
                CustomerCode = customer.CustomerCode,
                CustomerType = customer.CustomerType,
                Industry = customer.Industry,
                Status = customer.Status,
                CompanySize = customer.CompanySize,
                Website = customer.Website,
                Description = customer.Description,
                SecondaryEmail = customer.SecondaryEmail,
                SecondaryPhone = customer.SecondaryPhone,
                Fax = customer.Fax,
                City = customer.City,
                Country = customer.Country,
                PostalCode = customer.PostalCode,
                BillingAddress = customer.BillingAddress,
                ShippingAddress = customer.ShippingAddress,
                CreditLimit = customer.CreditLimit,
                Currency = customer.Currency,
                PaymentTerms = customer.PaymentTerms,
                FirstContactDate = customer.FirstContactDate,
                LastContactDate = customer.LastContactDate,
                Notes = customer.Notes,
                Tags = ParseTags(customer.Tags),
                ProjectCount = customer.ProjectCount,
                TotalRevenue = customer.TotalRevenue,
                AssignedSalesRep = customer.AssignedSalesRep,
                Priority = customer.Priority,
                IsVip = customer.IsVip,
                CreatedAt = customer.CreatedAt,
                UpdatedAt = customer.UpdatedAt
            }).ToList();

            return result;
        }

        private string[]? ParseTags(string? tagsJson)
        {
            if (string.IsNullOrEmpty(tagsJson))
                return null;

            try
            {
                return JsonSerializer.Deserialize<string[]>(tagsJson);
            }
            catch
            {
                return null;
            }
        }
    }
}

import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { TabViewModule } from 'primeng/tabview';
import { TagModule } from 'primeng/tag';
import { CardModule } from 'primeng/card';
import { Role } from '../../models/role.model';
import { RoleService } from '../../services/role.service';

@Component({
    selector: 'app-role-detail',
    standalone: true,
    imports: [
        CommonModule,
        ButtonModule,
        TabViewModule,
        TagModule,
        CardModule
    ],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <div>
                            <h5 class="m-0">Rol Detayı</h5>
                            <p class="text-500 mt-1">{{ role?.roleCode }}</p>
                        </div>
                        <div class="flex gap-2">
                            <p-button 
                                label="Düzenle" 
                                icon="pi pi-pencil" 
                                class="p-button-warning"
                                (onClick)="editRole()"
                                [disabled]="role?.isSystemRole">
                            </p-button>
                            <p-button 
                                label="Geri" 
                                icon="pi pi-arrow-left" 
                                class="p-button-secondary"
                                (onClick)="goBack()">
                            </p-button>
                        </div>
                    </div>

                    <p-tabView *ngIf="role">
                        <p-tabPanel header="Rol Bilgileri" leftIcon="pi pi-shield">
                            <div class="grid">
                                <div class="col-12 md:col-6">
                                    <p-card header="Temel Bilgiler">
                                        <div class="field">
                                            <label class="font-medium">Rol Adı:</label>
                                            <div class="mt-1 flex align-items-center gap-2">
                                                <span>{{ role.name }}</span>
                                                <i class="pi pi-shield text-orange-500" 
                                                   *ngIf="role.isSystemRole"
                                                   pTooltip="Sistem Rolü"></i>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Açıklama:</label>
                                            <div class="mt-1">{{ role.description }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Rol Tipi:</label>
                                            <div class="mt-1">
                                                <p-tag 
                                                    [value]="role.isSystemRole ? 'Sistem Rolü' : 'Özel Rol'" 
                                                    [severity]="role.isSystemRole ? 'warning' : 'info'">
                                                </p-tag>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Durum:</label>
                                            <div class="mt-1">
                                                <p-tag 
                                                    [value]="role.isActive ? 'Aktif' : 'Pasif'" 
                                                    [severity]="role.isActive ? 'success' : 'secondary'">
                                                </p-tag>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                                <div class="col-12 md:col-6">
                                    <p-card header="İstatistikler">
                                        <div class="field">
                                            <label class="font-medium">Yetki Sayısı:</label>
                                            <div class="mt-1">
                                                <span class="font-bold text-primary text-xl">{{ role.permissions.length }}</span>
                                                <span class="text-500 ml-1">yetki</span>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Kullanıcı Sayısı:</label>
                                            <div class="mt-1">
                                                <span class="font-bold text-green-600 text-xl">{{ role.userCount }}</span>
                                                <span class="text-500 ml-1">kullanıcı</span>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Oluşturulma:</label>
                                            <div class="mt-1">{{ role.createdAt | date:'dd/MM/yyyy HH:mm' }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Son Güncelleme:</label>
                                            <div class="mt-1">{{ role.updatedAt | date:'dd/MM/yyyy HH:mm' }}</div>
                                        </div>
                                    </p-card>
                                </div>
                            </div>
                        </p-tabPanel>

                        <p-tabPanel header="Yetkiler" leftIcon="pi pi-key">
                            <div class="grid">
                                <div class="col-12">
                                    <p-card>
                                        <div *ngIf="role.permissions.length === 0" class="text-center py-4">
                                            <i class="pi pi-info-circle text-4xl text-500 mb-3"></i>
                                            <div class="text-500 font-medium">Bu role henüz yetki atanmamış</div>
                                        </div>
                                        <div *ngIf="role.permissions.length > 0">
                                            <div *ngFor="let category of getPermissionsByCategory()" class="mb-4">
                                                <h6 class="text-primary mb-3">{{ category.name }}</h6>
                                                <div class="grid">
                                                    <div *ngFor="let permission of category.permissions" class="col-12 md:col-6 lg:col-4">
                                                        <div class="border-1 border-200 border-round p-3 mb-2">
                                                            <div class="flex align-items-start gap-2">
                                                                <i class="pi pi-check text-green-500 mt-1"></i>
                                                                <div class="flex-1">
                                                                    <div class="font-medium">{{ permission.name }}</div>
                                                                    <div class="text-sm text-500 mt-1">{{ permission.description }}</div>
                                                                    <div class="flex gap-2 mt-2">
                                                                        <p-tag [value]="permission.module" severity="secondary" class="text-xs"></p-tag>
                                                                        <p-tag [value]="permission.action" severity="info" class="text-xs"></p-tag>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                            </div>
                        </p-tabPanel>

                        <p-tabPanel header="Notlar" leftIcon="pi pi-file-edit">
                            <p-card>
                                <div *ngIf="!role.notes" class="text-500">
                                    Henüz not eklenmemiş
                                </div>
                                <div *ngIf="role.notes" class="white-space-pre-line">
                                    {{ role.notes }}
                                </div>
                            </p-card>
                        </p-tabPanel>
                    </p-tabView>
                </div>
            </div>
        </div>
    `
})
export class RoleDetailComponent implements OnInit {
    role: Role | null = null;
    roleId: string = '';

    constructor(
        private route: ActivatedRoute,
        private router: Router,
        private roleService: RoleService
    ) {}

    ngOnInit() {
        this.roleId = this.route.snapshot.params['id'];
        this.loadRole();
    }

    loadRole() {
        this.roleService.getRoleById(this.roleId).subscribe(role => {
            this.role = role || null;
        });
    }

    editRole() {
        this.router.navigate(['/pages/roles', this.roleId, 'edit']);
    }

    goBack() {
        this.router.navigate(['/pages/roles']);
    }

    getPermissionsByCategory() {
        if (!this.role) return [];
        
        const categories: { [key: string]: any[] } = {};
        
        this.role.permissions.forEach(permission => {
            const categoryName = permission.category;
            if (!categories[categoryName]) {
                categories[categoryName] = [];
            }
            categories[categoryName].push(permission);
        });

        return Object.keys(categories).map(categoryName => ({
            name: categoryName,
            permissions: categories[categoryName]
        }));
    }
}

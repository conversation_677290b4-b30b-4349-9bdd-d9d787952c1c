export interface Project {
    id: string;
    projectCode: string;
    name: string;
    description: string;
    status: ProjectStatus;
    platforms: Platform[];
    technologies: Technology[];
    customers: Customer[];
    teamMembers: TeamMember[];
    repositoryUrl?: string;
    currentVersion: string;
    createdDate: Date;
    lastUpdated: Date;
    startDate?: Date;
    endDate?: Date;
    estimatedHours?: number;
    actualHours?: number;
    priority: Priority;
    projectManager?: string;
    notes?: string;
}

export interface Customer {
    id: string;
    name: string;
    contactPerson: string;
    email: string;
    phone?: string;
    contractDate: Date;
    licenseType: LicenseType;
    isActive: boolean;
}

export interface TeamMember {
    id: string;
    userId: string;
    userName: string;
    role: string;
    assignedDate: Date;
    isActive: boolean;
}

export interface Technology {
    id: string;
    name: string;
    version: string;
    category: TechnologyCategory;
}

export enum ProjectStatus {
    PLANNING = 'Planlama',
    DEVELOPMENT = 'Geliştirme',
    TESTING = 'Test',
    DEPLOYMENT = 'Dağıtım',
    MAINTENANCE = 'Bakım',
    COMPLETED = 'Tamamlandı',
    CANCELLED = 'İptal Edildi',
    ON_HOLD = 'Beklemede'
}

export enum Platform {
    WEB = 'Web',
    MOBILE = 'Mobile',
    DESKTOP = 'Desktop',
    API = 'API'
}

export enum Priority {
    LOW = 'Düşük',
    MEDIUM = 'Orta',
    HIGH = 'Yüksek',
    CRITICAL = 'Kritik'
}

export enum LicenseType {
    PERPETUAL = 'Kalıcı Lisans',
    SUBSCRIPTION = 'Abonelik',
    TRIAL = 'Deneme',
    CUSTOM = 'Özel'
}

export enum TechnologyCategory {
    FRONTEND = 'Frontend',
    BACKEND = 'Backend',
    DATABASE = 'Veritabanı',
    DEVOPS = 'DevOps',
    MOBILE = 'Mobile',
    TESTING = 'Test'
}

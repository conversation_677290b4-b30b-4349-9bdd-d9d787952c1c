﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TRtek_EYS.Application.Features.Technologies.Commands.Add;
using TRtek_EYS.Application.Features.Technologies.Commands.Delete;
using TRtek_EYS.Application.Features.Technologies.Commands.Update;
using TRtek_EYS.Application.Features.Technologies.Queries.GetAll;
using TRtek_EYS.Application.Features.Technologies.Queries.GetById;

namespace TRtek_EYS.WebAPI.Controllers;

[Route("api/[controller]")]
[ApiController]
public class TechnologiesController : ApiController
{
    public TechnologiesController(IMediator mediator) : base(mediator) { }

    [HttpGet]
    public async Task<IActionResult> GetAll()
    {
        var result = await Mediator.Send(new GetAllTechnologiesQuery());
        return Ok(result); // List<TechnologyGetAllDto>
    }

    [HttpGet("v2")]
    [Authorize]
    public async Task<IActionResult> GetAllV2()
    {
        var result = await Mediator.Send(new GetAllTechnologiesV2Query());
        return Ok(result); // List<TechnologyGetAllDtoV2>
    }

    [HttpGet("{id:guid}")]
    public async Task<IActionResult> GetById(Guid id)
    {
        var result = await Mediator.Send(new GetTechnologyByIdQuery(id));
        return result is null ? NotFound() : Ok(result); 
    }

    [HttpPost]
    [AllowAnonymous]
    public async Task<IActionResult> Add([FromBody] AddTechnologyCommand command)
    {
        try
        {
            Console.WriteLine($"Received command: {System.Text.Json.JsonSerializer.Serialize(command)}");
            var id = await Mediator.Send(command);
            return CreatedAtAction(nameof(GetById), new { id }, null);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in Add Technology: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            throw;
        }
    }

    [HttpPut("{id:guid}")]
    public async Task<IActionResult> Update(Guid id, [FromBody] UpdateTechnologyCommand command)
    {
        if (id != command.Id)
            return BadRequest("ID uyuşmazlığı.");

        await Mediator.Send(command);
        return NoContent();
    }

    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> Delete(Guid id)
    {
        await Mediator.Send(new DeleteTechnologyCommand { Id = id });
        return NoContent();
    }
}

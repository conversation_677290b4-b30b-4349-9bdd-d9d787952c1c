import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Platform, PlatformFormData, PlatformType, PlatformCategory, SupportLevel, LicenseType, RequirementType } from '../models/platform.model';

@Injectable({
    providedIn: 'root'
})
export class PlatformService {
    private mockPlatforms: Platform[] = [
        {
            id: '1',
            platformCode: 'PLT-2025-001',
            name: 'Microsoft Azure',
            description: 'Microsoft\'un bulut bilişim platformu. Uygulama geliştirme, dağıtım ve yönetim için kapsamlı hizmetler sunar.',
            type: PlatformType.CLOUD_PLATFORM,
            category: PlatformCategory.INFRASTRUCTURE,
            version: '2025.1',
            vendor: 'Microsoft',
            website: 'https://azure.microsoft.com',
            documentation: 'https://docs.microsoft.com/azure',
            supportLevel: SupportLevel.FULL_SUPPORT,
            licenseType: LicenseType.SUBSCRIPTION,
            cost: 1500,
            currency: 'USD',
            projectCount: 8,
            isActive: true,
            isSupported: true,
            releaseDate: new Date('2024-01-15'),
            createdAt: new Date('2025-01-01'),
            updatedAt: new Date('2025-07-16'),
            createdBy: 'System Admin',
            tags: ['cloud', 'microsoft', 'paas', 'iaas'],
            features: [
                {
                    id: 'f1',
                    name: 'App Service',
                    description: 'Web uygulamaları için PaaS hizmeti',
                    isAvailable: true,
                    version: '2025.1'
                },
                {
                    id: 'f2',
                    name: 'SQL Database',
                    description: 'Yönetilen SQL veritabanı hizmeti',
                    isAvailable: true,
                    version: '2025.1'
                }
            ],
            requirements: [
                {
                    id: 'r1',
                    type: RequirementType.NETWORK,
                    name: 'Internet Bağlantısı',
                    minimumVersion: '10 Mbps',
                    recommendedVersion: '100 Mbps',
                    description: 'Stabil internet bağlantısı gereklidir'
                }
            ],
            notes: 'Ana bulut platformumuz'
        },
        {
            id: '2',
            platformCode: 'PLT-2025-002',
            name: 'Docker',
            description: 'Konteynerleştirme platformu. Uygulamaları izole edilmiş ortamlarda çalıştırmak için kullanılır.',
            type: PlatformType.CONTAINER_PLATFORM,
            category: PlatformCategory.DEPLOYMENT,
            version: '24.0.5',
            vendor: 'Docker Inc.',
            website: 'https://docker.com',
            documentation: 'https://docs.docker.com',
            supportLevel: SupportLevel.COMMUNITY_SUPPORT,
            licenseType: LicenseType.OPEN_SOURCE,
            projectCount: 12,
            isActive: true,
            isSupported: true,
            releaseDate: new Date('2023-08-24'),
            createdAt: new Date('2025-01-05'),
            updatedAt: new Date('2025-07-10'),
            createdBy: 'DevOps Team',
            tags: ['container', 'docker', 'deployment', 'microservices'],
            features: [
                {
                    id: 'f3',
                    name: 'Container Runtime',
                    description: 'Container çalıştırma ortamı',
                    isAvailable: true,
                    version: '24.0.5'
                },
                {
                    id: 'f4',
                    name: 'Docker Compose',
                    description: 'Multi-container uygulamalar için araç',
                    isAvailable: true,
                    version: '2.20.2'
                }
            ],
            requirements: [
                {
                    id: 'r2',
                    type: RequirementType.OPERATING_SYSTEM,
                    name: 'Linux/Windows',
                    minimumVersion: 'Linux Kernel 3.10+',
                    recommendedVersion: 'Linux Kernel 5.0+',
                    description: 'Modern işletim sistemi gereklidir'
                }
            ],
            notes: 'Tüm projelerde kullanılan konteyner platformu'
        },
        {
            id: '3',
            platformCode: 'PLT-2025-003',
            name: 'PostgreSQL',
            description: 'Açık kaynak nesne-ilişkisel veritabanı sistemi. Güçlü SQL desteği ve genişletilebilirlik sunar.',
            type: PlatformType.DATABASE,
            category: PlatformCategory.DATABASE,
            version: '16.0',
            vendor: 'PostgreSQL Global Development Group',
            website: 'https://postgresql.org',
            documentation: 'https://postgresql.org/docs',
            supportLevel: SupportLevel.COMMUNITY_SUPPORT,
            licenseType: LicenseType.OPEN_SOURCE,
            projectCount: 6,
            isActive: true,
            isSupported: true,
            releaseDate: new Date('2023-09-14'),
            createdAt: new Date('2025-01-10'),
            updatedAt: new Date('2025-06-20'),
            createdBy: 'Database Team',
            tags: ['database', 'postgresql', 'sql', 'open-source'],
            features: [
                {
                    id: 'f5',
                    name: 'ACID Compliance',
                    description: 'Tam ACID uyumluluğu',
                    isAvailable: true,
                    version: '16.0'
                },
                {
                    id: 'f6',
                    name: 'JSON Support',
                    description: 'Native JSON veri tipi desteği',
                    isAvailable: true,
                    version: '16.0'
                }
            ],
            requirements: [
                {
                    id: 'r3',
                    type: RequirementType.HARDWARE,
                    name: 'RAM',
                    minimumVersion: '1 GB',
                    recommendedVersion: '4 GB',
                    description: 'Minimum bellek gereksinimi'
                }
            ],
            notes: 'Ana veritabanı platformumuz'
        },
        {
            id: '4',
            platformCode: 'PLT-2025-004',
            name: 'Kubernetes',
            description: 'Container orkestrasyon platformu. Büyük ölçekli container uygulamalarını yönetmek için kullanılır.',
            type: PlatformType.CONTAINER_PLATFORM,
            category: PlatformCategory.INFRASTRUCTURE,
            version: '1.28.2',
            vendor: 'Cloud Native Computing Foundation',
            website: 'https://kubernetes.io',
            documentation: 'https://kubernetes.io/docs',
            supportLevel: SupportLevel.COMMUNITY_SUPPORT,
            licenseType: LicenseType.OPEN_SOURCE,
            projectCount: 4,
            isActive: true,
            isSupported: true,
            releaseDate: new Date('2023-08-23'),
            createdAt: new Date('2025-02-01'),
            updatedAt: new Date('2025-07-05'),
            createdBy: 'DevOps Team',
            tags: ['kubernetes', 'orchestration', 'container', 'k8s'],
            features: [
                {
                    id: 'f7',
                    name: 'Pod Management',
                    description: 'Container pod yönetimi',
                    isAvailable: true,
                    version: '1.28.2'
                },
                {
                    id: 'f8',
                    name: 'Service Discovery',
                    description: 'Otomatik servis keşfi',
                    isAvailable: true,
                    version: '1.28.2'
                }
            ],
            requirements: [
                {
                    id: 'r4',
                    type: RequirementType.HARDWARE,
                    name: 'CPU',
                    minimumVersion: '2 Core',
                    recommendedVersion: '4 Core',
                    description: 'Minimum işlemci gereksinimi'
                }
            ],
            notes: 'Büyük projeler için orkestrasyon'
        },
        {
            id: '5',
            platformCode: 'PLT-2025-005',
            name: 'Redis',
            description: 'In-memory veri yapısı deposu. Cache, message broker ve veritabanı olarak kullanılır.',
            type: PlatformType.DATABASE,
            category: PlatformCategory.INFRASTRUCTURE,
            version: '7.2.0',
            vendor: 'Redis Ltd.',
            website: 'https://redis.io',
            documentation: 'https://redis.io/docs',
            supportLevel: SupportLevel.COMMERCIAL,
            licenseType: LicenseType.OPEN_SOURCE,
            projectCount: 10,
            isActive: true,
            isSupported: true,
            releaseDate: new Date('2023-08-15'),
            createdAt: new Date('2025-01-20'),
            updatedAt: new Date('2025-07-01'),
            createdBy: 'Backend Team',
            tags: ['redis', 'cache', 'in-memory', 'nosql'],
            features: [
                {
                    id: 'f9',
                    name: 'Caching',
                    description: 'Yüksek performanslı önbellekleme',
                    isAvailable: true,
                    version: '7.2.0'
                },
                {
                    id: 'f10',
                    name: 'Pub/Sub',
                    description: 'Mesajlaşma sistemi',
                    isAvailable: true,
                    version: '7.2.0'
                }
            ],
            requirements: [
                {
                    id: 'r5',
                    type: RequirementType.HARDWARE,
                    name: 'RAM',
                    minimumVersion: '512 MB',
                    recommendedVersion: '2 GB',
                    description: 'In-memory için bellek gereksinimi'
                }
            ],
            notes: 'Cache ve session yönetimi için'
        },
        {
            id: '6',
            platformCode: 'PLT-2025-006',
            name: 'Jenkins',
            description: 'Açık kaynak otomasyon sunucusu. CI/CD pipeline\'ları oluşturmak için kullanılır.',
            type: PlatformType.DEVELOPMENT_PLATFORM,
            category: PlatformCategory.DEVELOPMENT,
            version: '2.414.1',
            vendor: 'Jenkins Community',
            website: 'https://jenkins.io',
            documentation: 'https://jenkins.io/doc',
            supportLevel: SupportLevel.COMMUNITY_SUPPORT,
            licenseType: LicenseType.OPEN_SOURCE,
            projectCount: 15,
            isActive: false,
            isSupported: false,
            releaseDate: new Date('2023-07-26'),
            endOfLifeDate: new Date('2025-12-31'),
            createdAt: new Date('2025-03-01'),
            updatedAt: new Date('2025-07-12'),
            createdBy: 'DevOps Team',
            updatedBy: 'DevOps Lead',
            tags: ['jenkins', 'ci-cd', 'automation', 'deprecated'],
            features: [
                {
                    id: 'f11',
                    name: 'Pipeline',
                    description: 'CI/CD pipeline oluşturma',
                    isAvailable: false,
                    version: '2.414.1'
                }
            ],
            requirements: [
                {
                    id: 'r6',
                    type: RequirementType.SOFTWARE,
                    name: 'Java',
                    minimumVersion: 'Java 11',
                    recommendedVersion: 'Java 17',
                    description: 'Java runtime gereklidir'
                }
            ],
            notes: 'GitHub Actions ile değiştirildi'
        }
    ];

    getPlatforms(): Observable<Platform[]> {
        return of(this.mockPlatforms);
    }

    getPlatformById(id: string): Observable<Platform | undefined> {
        return of(this.mockPlatforms.find(platform => platform.id === id));
    }

    createPlatform(platformData: PlatformFormData): Observable<Platform> {
        const newPlatform: Platform = {
            id: Date.now().toString(),
            ...platformData,
            projectCount: 0,
            createdAt: new Date(),
            updatedAt: new Date(),
            createdBy: 'Current User',
            features: [],
            requirements: []
        };
        this.mockPlatforms.push(newPlatform);
        return of(newPlatform);
    }

    updatePlatform(id: string, platformData: PlatformFormData): Observable<Platform | undefined> {
        const index = this.mockPlatforms.findIndex(platform => platform.id === id);
        if (index === -1) return of(undefined);

        const existingPlatform = this.mockPlatforms[index];
        const updatedPlatform: Platform = {
            ...existingPlatform,
            ...platformData,
            updatedAt: new Date(),
            updatedBy: 'Current User'
        };
        this.mockPlatforms[index] = updatedPlatform;
        return of(updatedPlatform);
    }

    deletePlatform(id: string): Observable<boolean> {
        const index = this.mockPlatforms.findIndex(platform => platform.id === id);
        if (index === -1) return of(false);
        
        const platform = this.mockPlatforms[index];
        if (platform.projectCount > 0) {
            // Kullanımda olan platformlar silinemez
            return of(false);
        }
        
        this.mockPlatforms.splice(index, 1);
        return of(true);
    }

    togglePlatformStatus(id: string): Observable<boolean> {
        const platform = this.mockPlatforms.find(p => p.id === id);
        if (!platform) return of(false);
        
        platform.isActive = !platform.isActive;
        platform.updatedAt = new Date();
        platform.updatedBy = 'Current User';
        return of(true);
    }
}

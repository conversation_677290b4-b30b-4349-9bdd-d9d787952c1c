import { Routes } from '@angular/router';

export default [
    {
        path: '',
        loadComponent: () => import('./components/technologies-list/technologies-list.component').then(m => m.TechnologiesListComponent)
    },
    {
        path: 'new',
        loadComponent: () => import('./components/technology-form/technology-form.component').then(m => m.TechnologyFormComponent)
    },
    {
        path: ':id',
        loadComponent: () => import('./components/technology-detail/technology-detail.component').then(m => m.TechnologyDetailComponent)
    },
    {
        path: ':id/edit',
        loadComponent: () => import('./components/technology-form/technology-form.component').then(m => m.TechnologyFormComponent)
    }
] as Routes;

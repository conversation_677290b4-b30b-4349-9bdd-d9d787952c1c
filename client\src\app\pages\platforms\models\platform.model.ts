export interface Platform {
    id: string;
    platformCode: string;
    name: string;
    description: string;
    type: PlatformType;
    category: PlatformCategory;
    version: string;
    vendor: string;
    website?: string;
    documentation?: string;
    supportLevel: SupportLevel;
    licenseType: LicenseType;
    cost?: number;
    currency?: string;
    projectCount: number;
    isActive: boolean;
    isSupported: boolean;
    releaseDate?: Date;
    endOfLifeDate?: Date;
    createdAt: Date;
    updatedAt: Date;
    createdBy: string;
    updatedBy?: string;
    tags: string[];
    features: PlatformFeature[];
    requirements: SystemRequirement[];
    notes?: string;
}

export interface PlatformFeature {
    id: string;
    name: string;
    description: string;
    isAvailable: boolean;
    version?: string;
}

export interface SystemRequirement {
    id: string;
    type: RequirementType;
    name: string;
    minimumVersion: string;
    recommendedVersion?: string;
    description?: string;
}

export interface PlatformFormData {
    platformCode: string;
    name: string;
    description: string;
    type: PlatformType;
    category: PlatformCategory;
    version: string;
    vendor: string;
    website?: string;
    documentation?: string;
    supportLevel: SupportLevel;
    licenseType: LicenseType;
    cost?: number;
    currency?: string;
    isActive: boolean;
    isSupported: boolean;
    releaseDate?: Date;
    endOfLifeDate?: Date;
    tags: string[];
    notes?: string;
}

export interface PlatformUsage {
    id: string;
    platformId: string;
    platformName: string;
    projectId: string;
    projectName: string;
    projectCode: string;
    usageType: UsageType;
    startDate: Date;
    endDate?: Date;
    isActive: boolean;
    notes?: string;
}

export enum PlatformType {
    OPERATING_SYSTEM = 'İşletim Sistemi',
    DATABASE = 'Veritabanı',
    WEB_SERVER = 'Web Sunucusu',
    APPLICATION_SERVER = 'Uygulama Sunucusu',
    CLOUD_PLATFORM = 'Bulut Platformu',
    CONTAINER_PLATFORM = 'Container Platformu',
    MOBILE_PLATFORM = 'Mobil Platform',
    DEVELOPMENT_PLATFORM = 'Geliştirme Platformu',
    INTEGRATION_PLATFORM = 'Entegrasyon Platformu',
    ANALYTICS_PLATFORM = 'Analitik Platformu'
}

export enum PlatformCategory {
    INFRASTRUCTURE = 'Altyapı',
    DEVELOPMENT = 'Geliştirme',
    DATABASE = 'Veritabanı',
    SECURITY = 'Güvenlik',
    MONITORING = 'İzleme',
    DEPLOYMENT = 'Dağıtım',
    TESTING = 'Test',
    COLLABORATION = 'İşbirliği',
    COMMUNICATION = 'İletişim',
    PRODUCTIVITY = 'Verimlilik'
}

export enum SupportLevel {
    FULL_SUPPORT = 'Tam Destek',
    LIMITED_SUPPORT = 'Sınırlı Destek',
    COMMUNITY_SUPPORT = 'Topluluk Desteği',
    NO_SUPPORT = 'Destek Yok',
    DEPRECATED = 'Kullanımdan Kaldırıldı',
    COMMERCIAL = "COMMERCIAL"
}

export enum LicenseType {
    OPEN_SOURCE = 'Açık Kaynak',
    COMMERCIAL = 'Ticari',
    FREEMIUM = 'Freemium',
    SUBSCRIPTION = 'Abonelik',
    ENTERPRISE = 'Kurumsal',
    CUSTOM = 'Özel'
}

export enum RequirementType {
    OPERATING_SYSTEM = 'İşletim Sistemi',
    HARDWARE = 'Donanım',
    SOFTWARE = 'Yazılım',
    NETWORK = 'Ağ',
    SECURITY = 'Güvenlik',
    PERFORMANCE = 'Performans'
}

export enum UsageType {
    DEVELOPMENT = 'Geliştirme',
    TESTING = 'Test',
    STAGING = 'Hazırlık',
    PRODUCTION = 'Üretim',
    BACKUP = 'Yedek',
    ARCHIVE = 'Arşiv'
}

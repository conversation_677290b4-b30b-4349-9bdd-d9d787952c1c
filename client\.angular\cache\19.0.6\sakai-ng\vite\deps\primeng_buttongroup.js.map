{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-buttongroup.mjs"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"*\"];\nconst theme = ({\n  dt\n}) => `\n.p-buttongroup .p-button {\n    margin: 0;\n}\n\n.p-buttongroup .p-button:not(:last-child),\n.p-buttongroup .p-button:not(:last-child):hover {\n    border-right: 0 none;\n}\n\n.p-buttongroup .p-button:not(:first-of-type):not(:last-of-type) {\n    border-radius: 0;\n}\n\n.p-buttongroup .p-button:first-of-type:not(:only-of-type) {\n    border-start-end-radius: 0;\n    border-end-end-radius: 0;\n}\n\n.p-buttongroup .p-button:last-of-type:not(:only-of-type) {\n    border-start-start-radius: 0;\n    border-end-start-radius: 0;\n}\n\n.p-buttongroup .p-button:focus {\n    position: relative;\n    z-index: 1;\n}\n\n.p-buttongroup {\n    display:flex;\n}\n\n.p-buttongroup > .p-button {\n    flex: 1;\n}\n\n/* For PrimeNG */\n\n.p-buttongroup .p-button:focus,\n.p-buttongroup p-button:focus .p-button,\n.p-buttonset .p-button:focus,\n.p-buttonset .p-button:focus,\n.p-buttonset p-button:focus .p-button,\n.p-buttonset .p-button:focus {\n    position: relative;\n    z-index: 1;\n}\n\n.p-buttongroup .p-button:not(:last-child),\n.p-buttongroup .p-button:not(:last-child):hover,\n.p-buttongroup p-button:not(:last-child) .p-button,\n.p-buttongroup p-button:not(:last-child) .p-button:hover,\n.p-buttonset .p-button:not(:last-child),\n.p-buttonset .p-button:not(:last-child):hover,\n.p-buttonset p-button:not(:last-child) .p-button,\n.p-buttonset p-button:not(:last-child) .p-button:hover {\n    border-right: 0 none;\n}\n\n.p-buttongroup .p-button:not(:first-of-type):not(:last-of-type),\n.p-buttongroup p-button:not(:first-of-type):not(:last-of-type) .p-button,\n.p-buttonset .p-button:not(:first-of-type):not(:last-of-type),\n.p-buttonset p-button:not(:first-of-type):not(:last-of-type) .p-button {\n    border-radius: 0;\n}\n\n.p-buttongroup .p-button:first-of-type:not(:only-of-type),\n.p-buttongroup p-button:first-of-type:not(:only-of-type) .p-button,\n.p-buttonset .p-button:first-of-type:not(:only-of-type),\n.p-buttonset p-button:first-of-type:not(:only-of-type) .p-button {\n    border-start-end-radius: 0;\n    border-end-end-radius: 0;\n}\n\n.p-buttongroup .p-button:last-of-type:not(:only-of-type),\n.p-buttongroup p-button:last-of-type:not(:only-of-type) .p-button,\n.p-buttonset .p-button:last-of-type:not(:only-of-type),\n.p-buttonset p-button:last-of-type:not(:only-of-type) .p-button {\n    border-start-start-radius: 0;\n    border-end-start-radius: 0;\n}\n\np-button[iconpos='right'] spinnericon {\n    order: 1;\n}\n`;\nconst classes = {\n  root: 'p-buttongroup p-component'\n};\nclass ButtonGroupStyle extends BaseStyle {\n  name = 'buttongroup';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵButtonGroupStyle_BaseFactory;\n    return function ButtonGroupStyle_Factory(__ngFactoryType__) {\n      return (ɵButtonGroupStyle_BaseFactory || (ɵButtonGroupStyle_BaseFactory = i0.ɵɵgetInheritedFactory(ButtonGroupStyle)))(__ngFactoryType__ || ButtonGroupStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ButtonGroupStyle,\n    factory: ButtonGroupStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ButtonGroupStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * A set of Buttons can be displayed together using the ButtonGroup component.\n *\n * [Live Demo](https://www.primeng.org/button/)\n *\n * @module buttongroupstyle\n *\n */\nvar ButtonGroupClasses;\n(function (ButtonGroupClasses) {\n  /**\n   * Class name of the root element\n   */\n  ButtonGroupClasses[\"root\"] = \"p-buttongroup\";\n})(ButtonGroupClasses || (ButtonGroupClasses = {}));\nclass ButtonGroup extends BaseComponent {\n  _componentStyle = inject(ButtonGroupStyle);\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵButtonGroup_BaseFactory;\n    return function ButtonGroup_Factory(__ngFactoryType__) {\n      return (ɵButtonGroup_BaseFactory || (ɵButtonGroup_BaseFactory = i0.ɵɵgetInheritedFactory(ButtonGroup)))(__ngFactoryType__ || ButtonGroup);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: ButtonGroup,\n    selectors: [[\"p-buttonGroup\"], [\"p-buttongroup\"], [\"p-button-group\"]],\n    features: [i0.ɵɵProvidersFeature([ButtonGroupStyle]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 2,\n    vars: 0,\n    consts: [[\"role\", \"group\", 1, \"p-buttongroup\", \"p-component\"]],\n    template: function ButtonGroup_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"span\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementEnd();\n      }\n    },\n    dependencies: [CommonModule],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ButtonGroup, [{\n    type: Component,\n    args: [{\n      selector: 'p-buttonGroup, p-buttongroup, p-button-group',\n      standalone: true,\n      imports: [CommonModule],\n      template: `\n        <span class=\"p-buttongroup p-component\" role=\"group\">\n            <ng-content></ng-content>\n        </span>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [ButtonGroupStyle]\n    }]\n  }], null, null);\n})();\nclass ButtonGroupModule {\n  static ɵfac = function ButtonGroupModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ButtonGroupModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ButtonGroupModule,\n    imports: [ButtonGroup],\n    exports: [ButtonGroup]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [ButtonGroup]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ButtonGroupModule, [{\n    type: NgModule,\n    args: [{\n      imports: [ButtonGroup],\n      exports: [ButtonGroup]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ButtonGroup, ButtonGroupClasses, ButtonGroupModule, ButtonGroupStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuFN,IAAM,UAAU;AAAA,EACd,MAAM;AACR;AACA,IAAM,mBAAN,MAAM,0BAAyB,UAAU;AAAA,EACvC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,yBAAyB,mBAAmB;AAC1D,cAAQ,kCAAkC,gCAAmC,sBAAsB,iBAAgB,IAAI,qBAAqB,iBAAgB;AAAA,IAC9J;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,kBAAiB;AAAA,EAC5B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,qBAAoB;AAI7B,EAAAA,oBAAmB,MAAM,IAAI;AAC/B,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAClD,IAAM,cAAN,MAAM,qBAAoB,cAAc;AAAA,EACtC,kBAAkB,OAAO,gBAAgB;AAAA,EACzC,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,oBAAoB,mBAAmB;AACrD,cAAQ,6BAA6B,2BAA8B,sBAAsB,YAAW,IAAI,qBAAqB,YAAW;AAAA,IAC1I;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,GAAG,CAAC,eAAe,GAAG,CAAC,gBAAgB,CAAC;AAAA,IACpE,UAAU,CAAI,mBAAmB,CAAC,gBAAgB,CAAC,GAAM,0BAA0B;AAAA,IACnF,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,SAAS,GAAG,iBAAiB,aAAa,CAAC;AAAA,IAC7D,UAAU,SAAS,qBAAqB,IAAI,KAAK;AAC/C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa;AAAA,MAClB;AAAA,IACF;AAAA,IACA,cAAc,CAAC,YAAY;AAAA,IAC3B,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,YAAY;AAAA,MACtB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,gBAAgB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,WAAW;AAAA,IACrB,SAAS,CAAC,WAAW;AAAA,EACvB,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,WAAW;AAAA,EACvB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,WAAW;AAAA,MACrB,SAAS,CAAC,WAAW;AAAA,IACvB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["ButtonGroupClasses"]}
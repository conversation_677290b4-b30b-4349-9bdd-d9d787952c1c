﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Platforms.Queries.GetById
{
    public class GetPlatformByIdQueryHandler : IRequestHandler<GetPlatformByIdQuery, PlatformGetByIdDto>
    {
        private readonly IApplicationDbContext _context;

        public GetPlatformByIdQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<PlatformGetByIdDto> Handle(GetPlatformByIdQuery request, CancellationToken cancellationToken)
        {
            var platform = await _context.Platforms
                .AsNoTracking()
                .FirstOrDefaultAsync(p => p.Id == request.Id, cancellationToken);

            if (platform is null)
                throw new Exception("Platform bulunamadı.");

            return new PlatformGetByIdDto
            {
                Id = platform.Id,
                Type = platform.Type.ToString(),
                ProjectId = platform.ProjectId
            };
        }
    }
}

export interface Technology {
    id: string;
    technologyCode: string;
    name: string;
    description: string;
    type: TechnologyType;
    category: TechnologyCategory;
    version: string;
    latestVersion?: string;
    vendor?: string;
    website?: string;
    documentation?: string;
    repository?: string;
    licenseType: LicenseType;
    maturityLevel: MaturityLevel;
    popularityScore: number;
    learningCurve: LearningCurve;
    projectCount: number;
    teamCount: number;
    isActive: boolean;
    isRecommended: boolean;
    isDeprecated: boolean;
    releaseDate?: Date;
    lastUpdateDate?: Date;
    endOfLifeDate?: Date;
    createdAt: Date;
    updatedAt: Date;
    createdBy: string;
    updatedBy?: string;
    tags: string[];
    features: TechnologyFeature[];
    alternatives: string[];
    dependencies: TechnologyDependency[];
    pros: string[];
    cons: string[];
    useCases: string[];
    notes?: string;
}

export interface TechnologyFeature {
    id: string;
    name: string;
    description: string;
    isAvailable: boolean;
    version?: string;
    importance: FeatureImportance;
}

export interface TechnologyDependency {
    id: string;
    name: string;
    type: DependencyType;
    version: string;
    isRequired: boolean;
    description?: string;
}

export interface TechnologyFormData {
    technologyCode: string;
    name: string;
    description: string;
    type: TechnologyType;
    category: TechnologyCategory;
    version: string;
    latestVersion?: string;
    vendor?: string;
    website?: string;
    documentation?: string;
    repository?: string;
    licenseType: LicenseType;
    maturityLevel: MaturityLevel;
    popularityScore: number;
    learningCurve: LearningCurve;
    isActive: boolean;
    isRecommended: boolean;
    isDeprecated: boolean;
    releaseDate?: Date;
    endOfLifeDate?: Date;
    tags: string[];
    alternatives: string[];
    pros: string[];
    cons: string[];
    useCases: string[];
    notes?: string;
}

export interface TechnologyUsage {
    id: string;
    technologyId: string;
    technologyName: string;
    projectId: string;
    projectName: string;
    projectCode: string;
    usageType: UsageType;
    startDate: Date;
    endDate?: Date;
    isActive: boolean;
    notes?: string;
}

export enum TechnologyType {
    PROGRAMMING_LANGUAGE = 'Programlama Dili',
    FRAMEWORK = 'Framework',
    LIBRARY = 'Kütüphane',
    DATABASE = 'Veritabanı',
    TOOL = 'Araç',
    PLATFORM = 'Platform',
    RUNTIME = 'Runtime',
    COMPILER = 'Derleyici',
    IDE = 'IDE',
    VERSION_CONTROL = 'Versiyon Kontrol',
    BUILD_TOOL = 'Build Aracı',
    TESTING_TOOL = 'Test Aracı',
    DEPLOYMENT_TOOL = 'Deployment Aracı',
    MONITORING_TOOL = 'İzleme Aracı',
    SECURITY_TOOL = 'Güvenlik Aracı'
}

export enum TechnologyCategory {
    FRONTEND = 'Frontend',
    BACKEND = 'Backend',
    MOBILE = 'Mobil',
    DESKTOP = 'Masaüstü',
    WEB = 'Web',
    DATABASE = 'Veritabanı',
    DEVOPS = 'DevOps',
    TESTING = 'Test',
    SECURITY = 'Güvenlik',
    AI_ML = 'AI/ML',
    BLOCKCHAIN = 'Blockchain',
    IOT = 'IoT',
    GAME_DEVELOPMENT = 'Oyun Geliştirme',
    DATA_SCIENCE = 'Veri Bilimi',
    CLOUD = 'Bulut'
}

export enum LicenseType {
    OPEN_SOURCE = 'Açık Kaynak',
    COMMERCIAL = 'Ticari',
    FREEMIUM = 'Freemium',
    PROPRIETARY = 'Özel Mülkiyet',
    DUAL_LICENSE = 'Çift Lisans',
    CUSTOM = 'Özel'
}

export enum MaturityLevel {
    EXPERIMENTAL = 'Deneysel',
    ALPHA = 'Alpha',
    BETA = 'Beta',
    STABLE = 'Kararlı',
    MATURE = 'Olgun',
    LEGACY = 'Eski',
    DEPRECATED = 'Kullanımdan Kaldırıldı'
}

export enum LearningCurve {
    VERY_EASY = 'Çok Kolay',
    EASY = 'Kolay',
    MODERATE = 'Orta',
    HARD = 'Zor',
    VERY_HARD = 'Çok Zor'
}

export enum FeatureImportance {
    LOW = 'Düşük',
    MEDIUM = 'Orta',
    HIGH = 'Yüksek',
    CRITICAL = 'Kritik'
}

export enum DependencyType {
    RUNTIME = 'Runtime',
    BUILD_TIME = 'Build Time',
    DEVELOPMENT = 'Geliştirme',
    OPTIONAL = 'Opsiyonel',
    PEER = 'Peer'
}

export enum UsageType {
    PRIMARY = 'Ana',
    SECONDARY = 'İkincil',
    EXPERIMENTAL = 'Deneysel',
    LEGACY = 'Eski',
    MIGRATION = 'Geçiş'
}

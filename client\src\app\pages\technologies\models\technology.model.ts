export interface Technology {
    id: string;
    technologyCode: string;
    name: string;
    description: string;
    type: TechnologyType;
    category: TechnologyCategory;
    version: string;
    latestVersion?: string;
    vendor?: string;
    website?: string;
    documentation?: string;
    repository?: string;
    licenseType: LicenseType;
    maturityLevel: MaturityLevel;
    popularityScore: number;
    learningCurve: LearningCurve;
    projectCount: number;
    teamCount: number;
    isActive: boolean;
    isRecommended: boolean;
    isDeprecated: boolean;
    releaseDate?: Date;
    lastUpdateDate?: Date;
    endOfLifeDate?: Date;
    createdAt: Date;
    updatedAt: Date;
    createdBy: string;
    updatedBy?: string;
    tags: string[];
    features: TechnologyFeature[];
    alternatives: string[];
    dependencies: TechnologyDependency[];
    pros: string[];
    cons: string[];
    useCases: string[];
    notes?: string;
}

export interface TechnologyFeature {
    id: string;
    name: string;
    description: string;
    isAvailable: boolean;
    version?: string;
    importance: FeatureImportance;
}

export interface TechnologyDependency {
    id: string;
    name: string;
    type: DependencyType;
    version: string;
    isRequired: boolean;
    description?: string;
}

export interface TechnologyFormData {
    technologyCode: string;
    name: string;
    description: string;
    type: TechnologyType;
    category: TechnologyCategory;
    version: string;
    latestVersion?: string;
    vendor?: string;
    website?: string;
    documentation?: string;
    repository?: string;
    licenseType: LicenseType;
    maturityLevel: MaturityLevel;
    popularityScore: number;
    learningCurve: LearningCurve;
    isActive: boolean;
    isRecommended: boolean;
    isDeprecated: boolean;
    releaseDate?: Date;
    endOfLifeDate?: Date;
    tags: string[];
    alternatives: string[];
    pros: string[];
    cons: string[];
    useCases: string[];
    notes?: string;
}

export interface TechnologyUsage {
    id: string;
    technologyId: string;
    technologyName: string;
    projectId: string;
    projectName: string;
    projectCode: string;
    usageType: UsageType;
    startDate: Date;
    endDate?: Date;
    isActive: boolean;
    notes?: string;
}

export enum TechnologyType {
    PROGRAMMING_LANGUAGE = 'PROGRAMMING_LANGUAGE',
    FRAMEWORK = 'FRAMEWORK',
    LIBRARY = 'LIBRARY',
    DATABASE = 'DATABASE',
    TOOL = 'TOOL',
    PLATFORM = 'PLATFORM',
    RUNTIME = 'RUNTIME',
    TESTING = 'TESTING',
    DEVOPS = 'DEVOPS',
    CLOUD_SERVICE = 'CLOUD_SERVICE',
    API = 'API',
    MOBILE = 'MOBILE',
    WEB = 'WEB',
    DESKTOP = 'DESKTOP',
    GAME_ENGINE = 'GAME_ENGINE',
    AI_ML = 'AI_ML',
    BLOCKCHAIN = 'BLOCKCHAIN',
    IOT = 'IOT',
    SECURITY = 'SECURITY',
    MONITORING = 'MONITORING',
    OTHER = 'OTHER'
}

export enum TechnologyCategory {
    FRONTEND = 'FRONTEND',
    BACKEND = 'BACKEND',
    DATABASE = 'DATABASE',
    MOBILE = 'MOBILE',
    DEVOPS = 'DEVOPS',
    TESTING = 'TESTING',
    DESIGN = 'DESIGN',
    ANALYTICS = 'ANALYTICS',
    SECURITY = 'SECURITY',
    INFRASTRUCTURE = 'INFRASTRUCTURE',
    COMMUNICATION = 'COMMUNICATION',
    PRODUCTIVITY = 'PRODUCTIVITY',
    DEVELOPMENT_TOOLS = 'DEVELOPMENT_TOOLS',
    MONITORING = 'MONITORING',
    DEPLOYMENT = 'DEPLOYMENT',
    VERSION_CONTROL = 'VERSION_CONTROL',
    PROJECT_MANAGEMENT = 'PROJECT_MANAGEMENT',
    DOCUMENTATION = 'DOCUMENTATION',
    PERFORMANCE = 'PERFORMANCE',
    INTEGRATION = 'INTEGRATION',
    OTHER = 'OTHER'
}

export enum LicenseType {
    OPEN_SOURCE = 'OPEN_SOURCE',
    MIT = 'MIT',
    APACHE = 'APACHE',
    GPL = 'GPL',
    BSD = 'BSD',
    COMMERCIAL = 'COMMERCIAL',
    PROPRIETARY = 'PROPRIETARY',
    FREEMIUM = 'FREEMIUM',
    SUBSCRIPTION = 'SUBSCRIPTION',
    PERPETUAL = 'PERPETUAL',
    TRIAL = 'TRIAL',
    CUSTOM = 'CUSTOM',
    SAAS = 'SAAS',
    FREE = 'FREE',
    ENTERPRISE = 'ENTERPRISE',
    COMMUNITY = 'COMMUNITY',
    ACADEMIC = 'ACADEMIC',
    OTHER = 'OTHER'
}

export enum MaturityLevel {
    EXPERIMENTAL = 'Deneysel',
    ALPHA = 'Alpha',
    BETA = 'Beta',
    STABLE = 'Kararlı',
    MATURE = 'Olgun',
    LEGACY = 'Eski',
    DEPRECATED = 'Kullanımdan Kaldırıldı'
}

export enum LearningCurve {
    VERY_EASY = 'Çok Kolay',
    EASY = 'Kolay',
    MODERATE = 'Orta',
    HARD = 'Zor',
    VERY_HARD = 'Çok Zor'
}

export enum FeatureImportance {
    LOW = 'Düşük',
    MEDIUM = 'Orta',
    HIGH = 'Yüksek',
    CRITICAL = 'Kritik'
}

export enum DependencyType {
    RUNTIME = 'Runtime',
    BUILD_TIME = 'Build Time',
    DEVELOPMENT = 'Geliştirme',
    OPTIONAL = 'Opsiyonel',
    PEER = 'Peer'
}

export enum UsageType {
    PRIMARY = 'Ana',
    SECONDARY = 'İkincil',
    EXPERIMENTAL = 'Deneysel',
    LEGACY = 'Eski',
    MIGRATION = 'Geçiş'
}

{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-breadcrumb.mjs"], "sourcesContent": ["import * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, inject, ContentChildren, ContentChild, Output, Input, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport * as i1 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule, PrimeTemplate } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { ChevronRightIcon, HomeIcon } from 'primeng/icons';\nimport * as i3 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"item\"];\nconst _c1 = [\"separator\"];\nconst _c2 = a0 => ({\n  \"p-breadcrumb-home-item\": true,\n  \"p-disabled\": a0\n});\nconst _c3 = () => ({\n  exact: false\n});\nconst _c4 = a0 => ({\n  \"p-breadcrumb-item\": true,\n  \"p-disabled\": a0\n});\nconst _c5 = a0 => ({\n  $implicit: a0\n});\nfunction Breadcrumb_li_2_a_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 16);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.home.icon)(\"ngStyle\", ctx_r1.home == null ? null : ctx_r1.home.style);\n  }\n}\nfunction Breadcrumb_li_2_a_1_HomeIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"HomeIcon\", 17);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-breadcrumb-item-icon\");\n  }\n}\nfunction Breadcrumb_li_2_a_1_ng_container_3_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.home.label);\n  }\n}\nfunction Breadcrumb_li_2_a_1_ng_container_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 20);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.home.label, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction Breadcrumb_li_2_a_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Breadcrumb_li_2_a_1_ng_container_3_span_1_Template, 2, 1, \"span\", 18)(2, Breadcrumb_li_2_a_1_ng_container_3_ng_template_2_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const htmlHomeLabel_r3 = i0.ɵɵreference(3);\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.home.escape !== false)(\"ngIfElse\", htmlHomeLabel_r3);\n  }\n}\nfunction Breadcrumb_li_2_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 12);\n    i0.ɵɵlistener(\"click\", function Breadcrumb_li_2_a_1_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onClick($event, ctx_r1.home));\n    });\n    i0.ɵɵtemplate(1, Breadcrumb_li_2_a_1_span_1_Template, 1, 2, \"span\", 13)(2, Breadcrumb_li_2_a_1_HomeIcon_2_Template, 1, 1, \"HomeIcon\", 14)(3, Breadcrumb_li_2_a_1_ng_container_3_Template, 4, 2, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"href\", ctx_r1.home.url ? ctx_r1.home.url : null, i0.ɵɵsanitizeUrl)(\"target\", ctx_r1.home.target);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.homeAriaLabel)(\"title\", ctx_r1.home.title)(\"tabindex\", ctx_r1.home.disabled ? null : \"0\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.home.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.home.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.home.label);\n  }\n}\nfunction Breadcrumb_li_2_a_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 16);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.home.icon)(\"ngStyle\", ctx_r1.home.iconStyle);\n  }\n}\nfunction Breadcrumb_li_2_a_2_HomeIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"HomeIcon\", 17);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-breadcrumb-item-icon\");\n  }\n}\nfunction Breadcrumb_li_2_a_2_ng_container_3_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.home.label);\n  }\n}\nfunction Breadcrumb_li_2_a_2_ng_container_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 20);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.home.label, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction Breadcrumb_li_2_a_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Breadcrumb_li_2_a_2_ng_container_3_span_1_Template, 2, 1, \"span\", 18)(2, Breadcrumb_li_2_a_2_ng_container_3_ng_template_2_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const htmlHomeRouteLabel_r5 = i0.ɵɵreference(3);\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.home.escape !== false)(\"ngIfElse\", htmlHomeRouteLabel_r5);\n  }\n}\nfunction Breadcrumb_li_2_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 21);\n    i0.ɵɵlistener(\"click\", function Breadcrumb_li_2_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onClick($event, ctx_r1.home));\n    });\n    i0.ɵɵtemplate(1, Breadcrumb_li_2_a_2_span_1_Template, 1, 2, \"span\", 13)(2, Breadcrumb_li_2_a_2_HomeIcon_2_Template, 1, 1, \"HomeIcon\", 14)(3, Breadcrumb_li_2_a_2_ng_container_3_Template, 4, 2, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"routerLink\", ctx_r1.home.routerLink)(\"queryParams\", ctx_r1.home.queryParams)(\"routerLinkActiveOptions\", ctx_r1.home.routerLinkActiveOptions || i0.ɵɵpureFunction0(16, _c3))(\"target\", ctx_r1.home.target)(\"fragment\", ctx_r1.home.fragment)(\"queryParamsHandling\", ctx_r1.home.queryParamsHandling)(\"preserveFragment\", ctx_r1.home.preserveFragment)(\"skipLocationChange\", ctx_r1.home.skipLocationChange)(\"replaceUrl\", ctx_r1.home.replaceUrl)(\"state\", ctx_r1.home.state);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.homeAriaLabel)(\"title\", ctx_r1.home.title)(\"tabindex\", ctx_r1.home.disabled ? null : \"0\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.home.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.home.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.home.label);\n  }\n}\nfunction Breadcrumb_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 9);\n    i0.ɵɵtemplate(1, Breadcrumb_li_2_a_1_Template, 4, 8, \"a\", 10)(2, Breadcrumb_li_2_a_2_Template, 4, 17, \"a\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.home.styleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c2, ctx_r1.home.disabled))(\"ngStyle\", ctx_r1.home.style)(\"tooltipOptions\", ctx_r1.home.tooltipOptions);\n    i0.ɵɵattribute(\"id\", ctx_r1.home.id)(\"data-pc-section\", \"home\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.home.routerLink);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.home.routerLink);\n  }\n}\nfunction Breadcrumb_li_3_ChevronRightIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\");\n  }\n}\nfunction Breadcrumb_li_3_2_ng_template_0_Template(rf, ctx) {}\nfunction Breadcrumb_li_3_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Breadcrumb_li_3_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Breadcrumb_li_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 22);\n    i0.ɵɵtemplate(1, Breadcrumb_li_3_ChevronRightIcon_1_Template, 1, 0, \"ChevronRightIcon\", 15)(2, Breadcrumb_li_3_2_Template, 1, 0, null, 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"data-pc-section\", \"separator\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.separatorTemplate && !ctx_r1._separatorTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.separatorTemplate || ctx_r1._separatorTemplate);\n  }\n}\nfunction Breadcrumb_ng_template_4_li_0_Conditional_1_0_ng_template_0_Template(rf, ctx) {}\nfunction Breadcrumb_ng_template_4_li_0_Conditional_1_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Breadcrumb_ng_template_4_li_0_Conditional_1_0_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Breadcrumb_ng_template_4_li_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Breadcrumb_ng_template_4_li_0_Conditional_1_0_Template, 1, 0, null, 26);\n  }\n  if (rf & 2) {\n    const menuitem_r6 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.itemTemplate || ctx_r1._itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c5, menuitem_r6));\n  }\n}\nfunction Breadcrumb_ng_template_4_li_0_Conditional_2_a_0_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 16);\n  }\n  if (rf & 2) {\n    const menuitem_r6 = i0.ɵɵnextContext(5).$implicit;\n    i0.ɵɵproperty(\"ngClass\", menuitem_r6 == null ? null : menuitem_r6.icon)(\"ngStyle\", menuitem_r6 == null ? null : menuitem_r6.iconStyle);\n  }\n}\nfunction Breadcrumb_ng_template_4_li_0_Conditional_2_a_0_ng_container_1_ng_container_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const menuitem_r6 = i0.ɵɵnextContext(6).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(menuitem_r6 == null ? null : menuitem_r6.label);\n  }\n}\nfunction Breadcrumb_ng_template_4_li_0_Conditional_2_a_0_ng_container_1_ng_container_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 20);\n  }\n  if (rf & 2) {\n    const menuitem_r6 = i0.ɵɵnextContext(6).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", menuitem_r6 == null ? null : menuitem_r6.label, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction Breadcrumb_ng_template_4_li_0_Conditional_2_a_0_ng_container_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Breadcrumb_ng_template_4_li_0_Conditional_2_a_0_ng_container_1_ng_container_2_span_1_Template, 2, 1, \"span\", 18)(2, Breadcrumb_ng_template_4_li_0_Conditional_2_a_0_ng_container_1_ng_container_2_ng_template_2_Template, 1, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const htmlLabel_r8 = i0.ɵɵreference(3);\n    const menuitem_r6 = i0.ɵɵnextContext(5).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (menuitem_r6 == null ? null : menuitem_r6.escape) !== false)(\"ngIfElse\", htmlLabel_r8);\n  }\n}\nfunction Breadcrumb_ng_template_4_li_0_Conditional_2_a_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Breadcrumb_ng_template_4_li_0_Conditional_2_a_0_ng_container_1_span_1_Template, 1, 2, \"span\", 13)(2, Breadcrumb_ng_template_4_li_0_Conditional_2_a_0_ng_container_1_ng_container_2_Template, 4, 2, \"ng-container\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const menuitem_r6 = i0.ɵɵnextContext(4).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", menuitem_r6 == null ? null : menuitem_r6.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", menuitem_r6 == null ? null : menuitem_r6.label);\n  }\n}\nfunction Breadcrumb_ng_template_4_li_0_Conditional_2_a_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 28);\n    i0.ɵɵlistener(\"click\", function Breadcrumb_ng_template_4_li_0_Conditional_2_a_0_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const menuitem_r6 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClick($event, menuitem_r6));\n    });\n    i0.ɵɵtemplate(1, Breadcrumb_ng_template_4_li_0_Conditional_2_a_0_ng_container_1_Template, 3, 2, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const menuitem_r6 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"target\", menuitem_r6 == null ? null : menuitem_r6.target);\n    i0.ɵɵattribute(\"href\", (menuitem_r6 == null ? null : menuitem_r6.url) ? menuitem_r6 == null ? null : menuitem_r6.url : null, i0.ɵɵsanitizeUrl)(\"title\", menuitem_r6 == null ? null : menuitem_r6.title)(\"tabindex\", (menuitem_r6 == null ? null : menuitem_r6.disabled) ? null : \"0\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.itemTemplate && !ctx_r1._itemTemplate);\n  }\n}\nfunction Breadcrumb_ng_template_4_li_0_Conditional_2_a_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 16);\n  }\n  if (rf & 2) {\n    const menuitem_r6 = i0.ɵɵnextContext(4).$implicit;\n    i0.ɵɵproperty(\"ngClass\", menuitem_r6 == null ? null : menuitem_r6.icon)(\"ngStyle\", menuitem_r6 == null ? null : menuitem_r6.iconStyle);\n  }\n}\nfunction Breadcrumb_ng_template_4_li_0_Conditional_2_a_1_ng_container_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const menuitem_r6 = i0.ɵɵnextContext(5).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(menuitem_r6 == null ? null : menuitem_r6.label);\n  }\n}\nfunction Breadcrumb_ng_template_4_li_0_Conditional_2_a_1_ng_container_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 20);\n  }\n  if (rf & 2) {\n    const menuitem_r6 = i0.ɵɵnextContext(5).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", menuitem_r6 == null ? null : menuitem_r6.label, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction Breadcrumb_ng_template_4_li_0_Conditional_2_a_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Breadcrumb_ng_template_4_li_0_Conditional_2_a_1_ng_container_2_span_1_Template, 2, 1, \"span\", 18)(2, Breadcrumb_ng_template_4_li_0_Conditional_2_a_1_ng_container_2_ng_template_2_Template, 1, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const htmlRouteLabel_r10 = i0.ɵɵreference(3);\n    const menuitem_r6 = i0.ɵɵnextContext(4).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (menuitem_r6 == null ? null : menuitem_r6.escape) !== false)(\"ngIfElse\", htmlRouteLabel_r10);\n  }\n}\nfunction Breadcrumb_ng_template_4_li_0_Conditional_2_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 21);\n    i0.ɵɵlistener(\"click\", function Breadcrumb_ng_template_4_li_0_Conditional_2_a_1_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const menuitem_r6 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClick($event, menuitem_r6));\n    });\n    i0.ɵɵtemplate(1, Breadcrumb_ng_template_4_li_0_Conditional_2_a_1_span_1_Template, 1, 2, \"span\", 13)(2, Breadcrumb_ng_template_4_li_0_Conditional_2_a_1_ng_container_2_Template, 4, 2, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const menuitem_r6 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"routerLink\", menuitem_r6 == null ? null : menuitem_r6.routerLink)(\"queryParams\", menuitem_r6 == null ? null : menuitem_r6.queryParams)(\"routerLinkActiveOptions\", (menuitem_r6 == null ? null : menuitem_r6.routerLinkActiveOptions) || i0.ɵɵpureFunction0(14, _c3))(\"target\", menuitem_r6 == null ? null : menuitem_r6.target)(\"fragment\", menuitem_r6 == null ? null : menuitem_r6.fragment)(\"queryParamsHandling\", menuitem_r6 == null ? null : menuitem_r6.queryParamsHandling)(\"preserveFragment\", menuitem_r6 == null ? null : menuitem_r6.preserveFragment)(\"skipLocationChange\", menuitem_r6 == null ? null : menuitem_r6.skipLocationChange)(\"replaceUrl\", menuitem_r6 == null ? null : menuitem_r6.replaceUrl)(\"state\", menuitem_r6 == null ? null : menuitem_r6.state);\n    i0.ɵɵattribute(\"title\", menuitem_r6 == null ? null : menuitem_r6.title)(\"tabindex\", (menuitem_r6 == null ? null : menuitem_r6.disabled) ? null : \"0\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", menuitem_r6 == null ? null : menuitem_r6.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", menuitem_r6 == null ? null : menuitem_r6.label);\n  }\n}\nfunction Breadcrumb_ng_template_4_li_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Breadcrumb_ng_template_4_li_0_Conditional_2_a_0_Template, 2, 5, \"a\", 27)(1, Breadcrumb_ng_template_4_li_0_Conditional_2_a_1_Template, 3, 15, \"a\", 11);\n  }\n  if (rf & 2) {\n    const menuitem_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"ngIf\", !(menuitem_r6 == null ? null : menuitem_r6.routerLink));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", menuitem_r6 == null ? null : menuitem_r6.routerLink);\n  }\n}\nfunction Breadcrumb_ng_template_4_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 25);\n    i0.ɵɵtemplate(1, Breadcrumb_ng_template_4_li_0_Conditional_1_Template, 1, 4)(2, Breadcrumb_ng_template_4_li_0_Conditional_2_Template, 2, 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const menuitem_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(menuitem_r6.styleClass);\n    i0.ɵɵproperty(\"ngStyle\", menuitem_r6.style)(\"ngClass\", i0.ɵɵpureFunction1(8, _c4, menuitem_r6.disabled))(\"tooltipOptions\", menuitem_r6.tooltipOptions);\n    i0.ɵɵattribute(\"id\", menuitem_r6.id)(\"data-pc-section\", \"menuitem\");\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.itemTemplate || ctx_r1._itemTemplate ? 1 : 2);\n  }\n}\nfunction Breadcrumb_ng_template_4_li_1_ChevronRightIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\");\n  }\n}\nfunction Breadcrumb_ng_template_4_li_1_2_ng_template_0_Template(rf, ctx) {}\nfunction Breadcrumb_ng_template_4_li_1_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Breadcrumb_ng_template_4_li_1_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Breadcrumb_ng_template_4_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 22);\n    i0.ɵɵtemplate(1, Breadcrumb_ng_template_4_li_1_ChevronRightIcon_1_Template, 1, 0, \"ChevronRightIcon\", 15)(2, Breadcrumb_ng_template_4_li_1_2_Template, 1, 0, null, 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"separator\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.separatorTemplate && !ctx_r1._separatorTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.separatorTemplate || ctx_r1._separatorTemplate);\n  }\n}\nfunction Breadcrumb_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Breadcrumb_ng_template_4_li_0_Template, 3, 10, \"li\", 24)(1, Breadcrumb_ng_template_4_li_1_Template, 3, 3, \"li\", 7);\n  }\n  if (rf & 2) {\n    const menuitem_r6 = ctx.$implicit;\n    const end_r11 = ctx.last;\n    i0.ɵɵproperty(\"ngIf\", menuitem_r6.visible !== false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !end_r11 && menuitem_r6.visible !== false);\n  }\n}\nconst theme = ({\n  dt\n}) => `\n.p-breadcrumb {\n    background: ${dt('breadcrumb.background')};\n    padding: ${dt('breadcrumb.padding')};\n    overflow-x: auto;\n}\n\n.p-breadcrumb-list {\n    margin: 0;\n    padding: 0;\n    list-style-type: none;\n    display: flex;\n    align-items: center;\n    flex-wrap: nowrap;\n    gap: ${dt('breadcrumb.gap')};\n}\n\n.p-breadcrumb-separator {\n    display: flex;\n    align-items: center;\n    color: ${dt('breadcrumb.separator.color')};\n}\n\n.p-breadcrumb-separator .p-icon:dir(rtl) {\n    transform: rotate(180deg);\n}\n\n.p-breadcrumb::-webkit-scrollbar {\n    display: none;\n}\n\n.p-breadcrumb-item-link {\n    text-decoration: none;\n    display: flex;\n    align-items: center;\n    gap: ${dt('breadcrumb.item.gap')};\n    transition: background ${dt('breadcrumb.transition.duration')}, color ${dt('breadcrumb.transition.duration')}, outline-color ${dt('breadcrumb.transition.duration')}, box-shadow ${dt('breadcrumb.transition.duration')};\n    border-radius: ${dt('breadcrumb.item.border.radius')};\n    outline-color: transparent;\n    color: ${dt('breadcrumb.item.color')};\n}\n\n.p-breadcrumb-item-link:focus-visible {\n    box-shadow: ${dt('breadcrumb.item.focus.ring.shadow')};\n    outline: ${dt('breadcrumb.item.focus.ring.width')} ${dt('breadcrumb.item.focus.ring.style')} ${dt('breadcrumb.item.focus.ring.color')};\n    outline-offset: ${dt('breadcrumb.item.focus.ring.offset')};\n}\n\n.p-breadcrumb-item-link:hover .p-breadcrumb-item-label {\n    color: ${dt('breadcrumb.item.hover.color')};\n}\n\n.p-breadcrumb-item-label {\n    transition: inherit;\n}\n\n.p-breadcrumb-item-icon {\n    color: ${dt('breadcrumb.item.icon.color')};\n    transition: inherit;\n}\n\n.p-breadcrumb-item-link:hover .p-breadcrumb-item-icon {\n    color: ${dt('breadcrumb.item.icon.hover.color')};\n}\n`;\nconst classes = {\n  root: 'p-breadcrumb p-component',\n  list: 'p-breadcrumb-list',\n  homeItem: 'p-breadcrumb-home-item',\n  separator: 'p-breadcrumb-separator',\n  item: ({\n    instance\n  }) => ['p-breadcrumb-item', {\n    'p-disabled': instance.disabled()\n  }],\n  itemLink: 'p-breadcrumb-item-link',\n  itemIcon: 'p-breadcrumb-item-icon',\n  itemLabel: 'p-breadcrumb-item-label'\n};\nclass BreadCrumbStyle extends BaseStyle {\n  name = 'breadcrumb';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵBreadCrumbStyle_BaseFactory;\n    return function BreadCrumbStyle_Factory(__ngFactoryType__) {\n      return (ɵBreadCrumbStyle_BaseFactory || (ɵBreadCrumbStyle_BaseFactory = i0.ɵɵgetInheritedFactory(BreadCrumbStyle)))(__ngFactoryType__ || BreadCrumbStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BreadCrumbStyle,\n    factory: BreadCrumbStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BreadCrumbStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * Breadcrumb provides contextual information about page hierarchy.\n *\n * [Live Demo](https://www.primeng.org/breadcrumb/)\n *\n * @module breadcrumbstyle\n *\n */\nvar BreadcrumbClasses;\n(function (BreadcrumbClasses) {\n  /**\n   * Class name of the root element\n   */\n  BreadcrumbClasses[\"root\"] = \"p-breadcrumb\";\n  /**\n   * Class name of the list element\n   */\n  BreadcrumbClasses[\"list\"] = \"p-breadcrumb-list\";\n  /**\n   * Class name of the home item element\n   */\n  BreadcrumbClasses[\"homeItem\"] = \"p-breadcrumb-home-item\";\n  /**\n   * Class name of the separator element\n   */\n  BreadcrumbClasses[\"separator\"] = \"p-breadcrumb-separator\";\n  /**\n   * Class name of the item element\n   */\n  BreadcrumbClasses[\"item\"] = \"p-breadcrumb-item\";\n  /**\n   * Class name of the item link element\n   */\n  BreadcrumbClasses[\"itemLink\"] = \"p-breadcrumb-item-link\";\n  /**\n   * Class name of the item icon element\n   */\n  BreadcrumbClasses[\"itemIcon\"] = \"p-breadcrumb-item-icon\";\n  /**\n   * Class name of the item label element\n   */\n  BreadcrumbClasses[\"itemLabel\"] = \"p-breadcrumb-item-label\";\n})(BreadcrumbClasses || (BreadcrumbClasses = {}));\n\n/**\n * Breadcrumb provides contextual information about page hierarchy.\n * @group Components\n */\nclass Breadcrumb extends BaseComponent {\n  router;\n  /**\n   * An array of menuitems.\n   * @group Props\n   */\n  model;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * MenuItem configuration for the home icon.\n   * @group Props\n   */\n  home;\n  /**\n   * Defines a string that labels the home icon for accessibility.\n   * @group Props\n   */\n  homeAriaLabel;\n  /**\n   * Fired when an item is selected.\n   * @param {BreadcrumbItemClickEvent} event - custom click event.\n   * @group Emits\n   */\n  onItemClick = new EventEmitter();\n  _componentStyle = inject(BreadCrumbStyle);\n  constructor(router) {\n    super();\n    this.router = router;\n  }\n  onClick(event, item) {\n    if (item.disabled) {\n      event.preventDefault();\n      return;\n    }\n    if (!item.url && !item.routerLink) {\n      event.preventDefault();\n    }\n    if (item.command) {\n      item.command({\n        originalEvent: event,\n        item: item\n      });\n    }\n    this.onItemClick.emit({\n      originalEvent: event,\n      item: item\n    });\n  }\n  /**\n   * Defines template option for item.\n   * @group Templates\n   */\n  itemTemplate;\n  /**\n   * Defines template option for separator.\n   * @group Templates\n   */\n  separatorTemplate;\n  templates;\n  _separatorTemplate;\n  _itemTemplate;\n  ngAfterContentInit() {\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'separator':\n          this._separatorTemplate = item.template;\n          break;\n        case 'item':\n          this._itemTemplate = item.template;\n          break;\n        default:\n          this._itemTemplate = item.template;\n          break;\n      }\n    });\n  }\n  static ɵfac = function Breadcrumb_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Breadcrumb)(i0.ɵɵdirectiveInject(i1.Router));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Breadcrumb,\n    selectors: [[\"p-breadcrumb\"]],\n    contentQueries: function Breadcrumb_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, _c0, 5);\n        i0.ɵɵcontentQuery(dirIndex, _c1, 5);\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.itemTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.separatorTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    inputs: {\n      model: \"model\",\n      style: \"style\",\n      styleClass: \"styleClass\",\n      home: \"home\",\n      homeAriaLabel: \"homeAriaLabel\"\n    },\n    outputs: {\n      onItemClick: \"onItemClick\"\n    },\n    features: [i0.ɵɵProvidersFeature([BreadCrumbStyle]), i0.ɵɵInheritDefinitionFeature],\n    decls: 5,\n    vars: 10,\n    consts: [[\"htmlHomeLabel\", \"\"], [\"htmlHomeRouteLabel\", \"\"], [\"htmlLabel\", \"\"], [\"htmlRouteLabel\", \"\"], [3, \"ngStyle\", \"ngClass\"], [1, \"p-breadcrumb-list\"], [\"pTooltip\", \"\", 3, \"class\", \"ngClass\", \"ngStyle\", \"tooltipOptions\", 4, \"ngIf\"], [\"class\", \"p-breadcrumb-separator\", 4, \"ngIf\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"pTooltip\", \"\", 3, \"ngClass\", \"ngStyle\", \"tooltipOptions\"], [\"class\", \"p-breadcrumb-item-link\", 3, \"href\", \"target\", \"click\", 4, \"ngIf\"], [\"class\", \"p-breadcrumb-item-link\", 3, \"routerLink\", \"queryParams\", \"routerLinkActiveOptions\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"click\", 4, \"ngIf\"], [1, \"p-breadcrumb-item-link\", 3, \"click\", \"href\", \"target\"], [\"class\", \"p-breadcrumb-item-icon\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [3, \"styleClass\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"p-breadcrumb-item-icon\", 3, \"ngClass\", \"ngStyle\"], [3, \"styleClass\"], [\"class\", \"p-breadcrumb-item-label\", 4, \"ngIf\", \"ngIfElse\"], [1, \"p-breadcrumb-item-label\"], [1, \"p-breadcrumb-item-label\", 3, \"innerHTML\"], [1, \"p-breadcrumb-item-link\", 3, \"click\", \"routerLink\", \"queryParams\", \"routerLinkActiveOptions\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\"], [1, \"p-breadcrumb-separator\"], [4, \"ngTemplateOutlet\"], [\"pTooltip\", \"\", 3, \"class\", \"ngStyle\", \"ngClass\", \"tooltipOptions\", 4, \"ngIf\"], [\"pTooltip\", \"\", 3, \"ngStyle\", \"ngClass\", \"tooltipOptions\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"class\", \"p-breadcrumb-item-link\", 3, \"target\", \"click\", 4, \"ngIf\"], [1, \"p-breadcrumb-item-link\", 3, \"click\", \"target\"]],\n    template: function Breadcrumb_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"nav\", 4)(1, \"ol\", 5);\n        i0.ɵɵtemplate(2, Breadcrumb_li_2_Template, 3, 11, \"li\", 6)(3, Breadcrumb_li_3_Template, 3, 3, \"li\", 7)(4, Breadcrumb_ng_template_4_Template, 2, 2, \"ng-template\", 8);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"ngClass\", \"p-breadcrumb p-component\");\n        i0.ɵɵattribute(\"data-pc-name\", \"breadcrumb\")(\"data-pc-section\", \"root\");\n        i0.ɵɵadvance();\n        i0.ɵɵattribute(\"data-pc-section\", \"menu\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.home && ctx.home.visible !== false);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.model && ctx.home);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngForOf\", ctx.model);\n      }\n    },\n    dependencies: [CommonModule, i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, RouterModule, i1.RouterLink, TooltipModule, i3.Tooltip, ChevronRightIcon, HomeIcon, SharedModule],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Breadcrumb, [{\n    type: Component,\n    args: [{\n      selector: 'p-breadcrumb',\n      standalone: true,\n      imports: [CommonModule, RouterModule, TooltipModule, ChevronRightIcon, HomeIcon, SharedModule],\n      template: `\n        <nav [class]=\"styleClass\" [ngStyle]=\"style\" [ngClass]=\"'p-breadcrumb p-component'\" [attr.data-pc-name]=\"'breadcrumb'\" [attr.data-pc-section]=\"'root'\">\n            <ol [attr.data-pc-section]=\"'menu'\" class=\"p-breadcrumb-list\">\n                <li\n                    [class]=\"home.styleClass\"\n                    [attr.id]=\"home.id\"\n                    [ngClass]=\"{ 'p-breadcrumb-home-item': true, 'p-disabled': home.disabled }\"\n                    [ngStyle]=\"home.style\"\n                    *ngIf=\"home && home.visible !== false\"\n                    pTooltip\n                    [tooltipOptions]=\"home.tooltipOptions\"\n                    [attr.data-pc-section]=\"'home'\"\n                >\n                    <a\n                        [href]=\"home.url ? home.url : null\"\n                        *ngIf=\"!home.routerLink\"\n                        [attr.aria-label]=\"homeAriaLabel\"\n                        class=\"p-breadcrumb-item-link\"\n                        (click)=\"onClick($event, home)\"\n                        [target]=\"home.target\"\n                        [attr.title]=\"home.title\"\n                        [attr.tabindex]=\"home.disabled ? null : '0'\"\n                    >\n                        <span *ngIf=\"home.icon\" class=\"p-breadcrumb-item-icon\" [ngClass]=\"home.icon\" [ngStyle]=\"home?.style\"></span>\n                        <HomeIcon *ngIf=\"!home.icon\" [styleClass]=\"'p-breadcrumb-item-icon'\" />\n                        <ng-container *ngIf=\"home.label\">\n                            <span *ngIf=\"home.escape !== false; else htmlHomeLabel\" class=\"p-breadcrumb-item-label\">{{ home.label }}</span>\n                            <ng-template #htmlHomeLabel><span class=\"p-breadcrumb-item-label\" [innerHTML]=\"home.label\"></span></ng-template>\n                        </ng-container>\n                    </a>\n                    <a\n                        *ngIf=\"home.routerLink\"\n                        [routerLink]=\"home.routerLink\"\n                        [attr.aria-label]=\"homeAriaLabel\"\n                        [queryParams]=\"home.queryParams\"\n                        [routerLinkActiveOptions]=\"home.routerLinkActiveOptions || { exact: false }\"\n                        class=\"p-breadcrumb-item-link\"\n                        (click)=\"onClick($event, home)\"\n                        [target]=\"home.target\"\n                        [attr.title]=\"home.title\"\n                        [attr.tabindex]=\"home.disabled ? null : '0'\"\n                        [fragment]=\"home.fragment\"\n                        [queryParamsHandling]=\"home.queryParamsHandling\"\n                        [preserveFragment]=\"home.preserveFragment\"\n                        [skipLocationChange]=\"home.skipLocationChange\"\n                        [replaceUrl]=\"home.replaceUrl\"\n                        [state]=\"home.state\"\n                    >\n                        <span *ngIf=\"home.icon\" class=\"p-breadcrumb-item-icon\" [ngClass]=\"home.icon\" [ngStyle]=\"home.iconStyle\"></span>\n                        <HomeIcon *ngIf=\"!home.icon\" [styleClass]=\"'p-breadcrumb-item-icon'\" />\n                        <ng-container *ngIf=\"home.label\">\n                            <span *ngIf=\"home.escape !== false; else htmlHomeRouteLabel\" class=\"p-breadcrumb-item-label\">{{ home.label }}</span>\n                            <ng-template #htmlHomeRouteLabel><span class=\"p-breadcrumb-item-label\" [innerHTML]=\"home.label\"></span></ng-template>\n                        </ng-container>\n                    </a>\n                </li>\n                <li *ngIf=\"model && home\" class=\"p-breadcrumb-separator\" [attr.data-pc-section]=\"'separator'\">\n                    <ChevronRightIcon *ngIf=\"!separatorTemplate && !_separatorTemplate\" />\n                    <ng-template *ngTemplateOutlet=\"separatorTemplate || _separatorTemplate\"></ng-template>\n                </li>\n                <ng-template ngFor let-menuitem let-end=\"last\" [ngForOf]=\"model\">\n                    <li\n                        *ngIf=\"menuitem.visible !== false\"\n                        [class]=\"menuitem.styleClass\"\n                        [attr.id]=\"menuitem.id\"\n                        [ngStyle]=\"menuitem.style\"\n                        [ngClass]=\"{ 'p-breadcrumb-item': true, 'p-disabled': menuitem.disabled }\"\n                        pTooltip\n                        [tooltipOptions]=\"menuitem.tooltipOptions\"\n                        [attr.data-pc-section]=\"'menuitem'\"\n                    >\n                        @if (itemTemplate || _itemTemplate) {\n                            <ng-template *ngTemplateOutlet=\"itemTemplate || _itemTemplate; context: { $implicit: menuitem }\"></ng-template>\n                        } @else {\n                            <a\n                                *ngIf=\"!menuitem?.routerLink\"\n                                [attr.href]=\"menuitem?.url ? menuitem?.url : null\"\n                                class=\"p-breadcrumb-item-link\"\n                                (click)=\"onClick($event, menuitem)\"\n                                [target]=\"menuitem?.target\"\n                                [attr.title]=\"menuitem?.title\"\n                                [attr.tabindex]=\"menuitem?.disabled ? null : '0'\"\n                            >\n                                <ng-container *ngIf=\"!itemTemplate && !_itemTemplate\">\n                                    <span *ngIf=\"menuitem?.icon\" class=\"p-breadcrumb-item-icon\" [ngClass]=\"menuitem?.icon\" [ngStyle]=\"menuitem?.iconStyle\"></span>\n                                    <ng-container *ngIf=\"menuitem?.label\">\n                                        <span *ngIf=\"menuitem?.escape !== false; else htmlLabel\" class=\"p-breadcrumb-item-label\">{{ menuitem?.label }}</span>\n                                        <ng-template #htmlLabel><span class=\"p-breadcrumb-item-label\" [innerHTML]=\"menuitem?.label\"></span></ng-template>\n                                    </ng-container>\n                                </ng-container>\n                            </a>\n                            <a\n                                *ngIf=\"menuitem?.routerLink\"\n                                [routerLink]=\"menuitem?.routerLink\"\n                                [queryParams]=\"menuitem?.queryParams\"\n                                [routerLinkActiveOptions]=\"menuitem?.routerLinkActiveOptions || { exact: false }\"\n                                class=\"p-breadcrumb-item-link\"\n                                (click)=\"onClick($event, menuitem)\"\n                                [target]=\"menuitem?.target\"\n                                [attr.title]=\"menuitem?.title\"\n                                [attr.tabindex]=\"menuitem?.disabled ? null : '0'\"\n                                [fragment]=\"menuitem?.fragment\"\n                                [queryParamsHandling]=\"menuitem?.queryParamsHandling\"\n                                [preserveFragment]=\"menuitem?.preserveFragment\"\n                                [skipLocationChange]=\"menuitem?.skipLocationChange\"\n                                [replaceUrl]=\"menuitem?.replaceUrl\"\n                                [state]=\"menuitem?.state\"\n                            >\n                                <span *ngIf=\"menuitem?.icon\" class=\"p-breadcrumb-item-icon\" [ngClass]=\"menuitem?.icon\" [ngStyle]=\"menuitem?.iconStyle\"></span>\n                                <ng-container *ngIf=\"menuitem?.label\">\n                                    <span *ngIf=\"menuitem?.escape !== false; else htmlRouteLabel\" class=\"p-breadcrumb-item-label\">{{ menuitem?.label }}</span>\n                                    <ng-template #htmlRouteLabel><span class=\"p-breadcrumb-item-label\" [innerHTML]=\"menuitem?.label\"></span></ng-template>\n                                </ng-container>\n                            </a>\n                        }\n                    </li>\n                    <li *ngIf=\"!end && menuitem.visible !== false\" class=\"p-breadcrumb-separator\" [attr.data-pc-section]=\"'separator'\">\n                        <ChevronRightIcon *ngIf=\"!separatorTemplate && !_separatorTemplate\" />\n                        <ng-template *ngTemplateOutlet=\"separatorTemplate || _separatorTemplate\"></ng-template>\n                    </li>\n                </ng-template>\n            </ol>\n        </nav>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [BreadCrumbStyle]\n    }]\n  }], () => [{\n    type: i1.Router\n  }], {\n    model: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    home: [{\n      type: Input\n    }],\n    homeAriaLabel: [{\n      type: Input\n    }],\n    onItemClick: [{\n      type: Output\n    }],\n    itemTemplate: [{\n      type: ContentChild,\n      args: ['item']\n    }],\n    separatorTemplate: [{\n      type: ContentChild,\n      args: ['separator']\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass BreadcrumbModule {\n  static ɵfac = function BreadcrumbModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BreadcrumbModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: BreadcrumbModule,\n    imports: [Breadcrumb, SharedModule],\n    exports: [Breadcrumb, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [Breadcrumb, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BreadcrumbModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Breadcrumb, SharedModule],\n      exports: [Breadcrumb, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BreadCrumbStyle, Breadcrumb, BreadcrumbClasses, BreadcrumbModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,IAAM,MAAM,CAAC,MAAM;AACnB,IAAM,MAAM,CAAC,WAAW;AACxB,IAAM,MAAM,SAAO;AAAA,EACjB,0BAA0B;AAAA,EAC1B,cAAc;AAChB;AACA,IAAM,MAAM,OAAO;AAAA,EACjB,OAAO;AACT;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,qBAAqB;AAAA,EACrB,cAAc;AAChB;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,KAAK,IAAI,EAAE,WAAW,OAAO,QAAQ,OAAO,OAAO,OAAO,KAAK,KAAK;AAAA,EACtG;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,YAAY,EAAE;AAAA,EAChC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,wBAAwB;AAAA,EACtD;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,KAAK,KAAK;AAAA,EACxC;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,aAAa,OAAO,KAAK,OAAU,cAAc;AAAA,EACjE;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,2DAA2D,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAC5M,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,YAAY,CAAC;AACzC,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,KAAK,WAAW,KAAK,EAAE,YAAY,gBAAgB;AAAA,EAClF;AACF;AACA,SAAS,6BAA6B,IAAI,KAAK;AAC7C,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,SAAS,SAAS,gDAAgD,QAAQ;AACtF,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,QAAQ,QAAQ,OAAO,IAAI,CAAC;AAAA,IAC3D,CAAC;AACD,IAAG,WAAW,GAAG,qCAAqC,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,yCAAyC,GAAG,GAAG,YAAY,EAAE,EAAE,GAAG,6CAA6C,GAAG,GAAG,gBAAgB,EAAE;AAClN,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,QAAQ,OAAO,KAAK,MAAM,OAAO,KAAK,MAAM,MAAS,aAAa,EAAE,UAAU,OAAO,KAAK,MAAM;AAC9G,IAAG,YAAY,cAAc,OAAO,aAAa,EAAE,SAAS,OAAO,KAAK,KAAK,EAAE,YAAY,OAAO,KAAK,WAAW,OAAO,GAAG;AAC5H,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,KAAK,IAAI;AACtC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,KAAK,IAAI;AACvC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,KAAK,KAAK;AAAA,EACzC;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,KAAK,IAAI,EAAE,WAAW,OAAO,KAAK,SAAS;AAAA,EAC7E;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,YAAY,EAAE;AAAA,EAChC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,wBAAwB;AAAA,EACtD;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,KAAK,KAAK;AAAA,EACxC;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,aAAa,OAAO,KAAK,OAAU,cAAc;AAAA,EACjE;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,2DAA2D,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAC5M,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,wBAA2B,YAAY,CAAC;AAC9C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,KAAK,WAAW,KAAK,EAAE,YAAY,qBAAqB;AAAA,EACvF;AACF;AACA,SAAS,6BAA6B,IAAI,KAAK;AAC7C,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,SAAS,SAAS,gDAAgD,QAAQ;AACtF,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,QAAQ,QAAQ,OAAO,IAAI,CAAC;AAAA,IAC3D,CAAC;AACD,IAAG,WAAW,GAAG,qCAAqC,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,yCAAyC,GAAG,GAAG,YAAY,EAAE,EAAE,GAAG,6CAA6C,GAAG,GAAG,gBAAgB,EAAE;AAClN,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,cAAc,OAAO,KAAK,UAAU,EAAE,eAAe,OAAO,KAAK,WAAW,EAAE,2BAA2B,OAAO,KAAK,2BAA8B,gBAAgB,IAAI,GAAG,CAAC,EAAE,UAAU,OAAO,KAAK,MAAM,EAAE,YAAY,OAAO,KAAK,QAAQ,EAAE,uBAAuB,OAAO,KAAK,mBAAmB,EAAE,oBAAoB,OAAO,KAAK,gBAAgB,EAAE,sBAAsB,OAAO,KAAK,kBAAkB,EAAE,cAAc,OAAO,KAAK,UAAU,EAAE,SAAS,OAAO,KAAK,KAAK;AAC3d,IAAG,YAAY,cAAc,OAAO,aAAa,EAAE,SAAS,OAAO,KAAK,KAAK,EAAE,YAAY,OAAO,KAAK,WAAW,OAAO,GAAG;AAC5H,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,KAAK,IAAI;AACtC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,KAAK,IAAI;AACvC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,KAAK,KAAK;AAAA,EACzC;AACF;AACA,SAAS,yBAAyB,IAAI,KAAK;AACzC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,WAAW,GAAG,8BAA8B,GAAG,GAAG,KAAK,EAAE,EAAE,GAAG,8BAA8B,GAAG,IAAI,KAAK,EAAE;AAC7G,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,KAAK,UAAU;AACpC,IAAG,WAAW,WAAc,gBAAgB,GAAG,KAAK,OAAO,KAAK,QAAQ,CAAC,EAAE,WAAW,OAAO,KAAK,KAAK,EAAE,kBAAkB,OAAO,KAAK,cAAc;AACrJ,IAAG,YAAY,MAAM,OAAO,KAAK,EAAE,EAAE,mBAAmB,MAAM;AAC9D,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,KAAK,UAAU;AAC7C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,KAAK,UAAU;AAAA,EAC9C;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,kBAAkB;AAAA,EACpC;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AAAC;AAC5D,SAAS,2BAA2B,IAAI,KAAK;AAC3C,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,aAAa;AAAA,EAChF;AACF;AACA,SAAS,yBAAyB,IAAI,KAAK;AACzC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,EAAE;AAC7B,IAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,oBAAoB,EAAE,EAAE,GAAG,4BAA4B,GAAG,GAAG,MAAM,EAAE;AACzI,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,mBAAmB,WAAW;AAC7C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,qBAAqB,CAAC,OAAO,kBAAkB;AAC7E,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,qBAAqB,OAAO,kBAAkB;AAAA,EACzF;AACF;AACA,SAAS,qEAAqE,IAAI,KAAK;AAAC;AACxF,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,sEAAsE,GAAG,GAAG,aAAa;AAAA,EAC5G;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,MAAM,EAAE;AAAA,EACzF;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAiB,cAAc,CAAC,EAAE;AACxC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,oBAAoB,OAAO,gBAAgB,OAAO,aAAa,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,WAAW,CAAC;AAAA,EACnJ;AACF;AACA,SAAS,+EAA+E,IAAI,KAAK;AAC/F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAiB,cAAc,CAAC,EAAE;AACxC,IAAG,WAAW,WAAW,eAAe,OAAO,OAAO,YAAY,IAAI,EAAE,WAAW,eAAe,OAAO,OAAO,YAAY,SAAS;AAAA,EACvI;AACF;AACA,SAAS,8FAA8F,IAAI,KAAK;AAC9G,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAiB,cAAc,CAAC,EAAE;AACxC,IAAG,UAAU;AACb,IAAG,kBAAkB,eAAe,OAAO,OAAO,YAAY,KAAK;AAAA,EACrE;AACF;AACA,SAAS,qGAAqG,IAAI,KAAK;AACrH,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAiB,cAAc,CAAC,EAAE;AACxC,IAAG,WAAW,aAAa,eAAe,OAAO,OAAO,YAAY,OAAU,cAAc;AAAA,EAC9F;AACF;AACA,SAAS,uFAAuF,IAAI,KAAK;AACvG,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,+FAA+F,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,sGAAsG,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAClS,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAkB,YAAY,CAAC;AACrC,UAAM,cAAiB,cAAc,CAAC,EAAE;AACxC,IAAG,UAAU;AACb,IAAG,WAAW,SAAS,eAAe,OAAO,OAAO,YAAY,YAAY,KAAK,EAAE,YAAY,YAAY;AAAA,EAC7G;AACF;AACA,SAAS,wEAAwE,IAAI,KAAK;AACxF,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,gFAAgF,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,wFAAwF,GAAG,GAAG,gBAAgB,EAAE;AACtO,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAiB,cAAc,CAAC,EAAE;AACxC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,eAAe,OAAO,OAAO,YAAY,IAAI;AACnE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,eAAe,OAAO,OAAO,YAAY,KAAK;AAAA,EACtE;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,SAAS,SAAS,4EAA4E,QAAQ;AAClH,MAAG,cAAc,GAAG;AACpB,YAAM,cAAiB,cAAc,CAAC,EAAE;AACxC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,QAAQ,QAAQ,WAAW,CAAC;AAAA,IAC3D,CAAC;AACD,IAAG,WAAW,GAAG,yEAAyE,GAAG,GAAG,gBAAgB,EAAE;AAClH,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAiB,cAAc,CAAC,EAAE;AACxC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,UAAU,eAAe,OAAO,OAAO,YAAY,MAAM;AACvE,IAAG,YAAY,SAAS,eAAe,OAAO,OAAO,YAAY,OAAO,eAAe,OAAO,OAAO,YAAY,MAAM,MAAS,aAAa,EAAE,SAAS,eAAe,OAAO,OAAO,YAAY,KAAK,EAAE,aAAa,eAAe,OAAO,OAAO,YAAY,YAAY,OAAO,GAAG;AACpR,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,gBAAgB,CAAC,OAAO,aAAa;AAAA,EACrE;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAiB,cAAc,CAAC,EAAE;AACxC,IAAG,WAAW,WAAW,eAAe,OAAO,OAAO,YAAY,IAAI,EAAE,WAAW,eAAe,OAAO,OAAO,YAAY,SAAS;AAAA,EACvI;AACF;AACA,SAAS,+EAA+E,IAAI,KAAK;AAC/F,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAiB,cAAc,CAAC,EAAE;AACxC,IAAG,UAAU;AACb,IAAG,kBAAkB,eAAe,OAAO,OAAO,YAAY,KAAK;AAAA,EACrE;AACF;AACA,SAAS,sFAAsF,IAAI,KAAK;AACtG,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAiB,cAAc,CAAC,EAAE;AACxC,IAAG,WAAW,aAAa,eAAe,OAAO,OAAO,YAAY,OAAU,cAAc;AAAA,EAC9F;AACF;AACA,SAAS,wEAAwE,IAAI,KAAK;AACxF,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,gFAAgF,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,uFAAuF,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AACpQ,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,qBAAwB,YAAY,CAAC;AAC3C,UAAM,cAAiB,cAAc,CAAC,EAAE;AACxC,IAAG,UAAU;AACb,IAAG,WAAW,SAAS,eAAe,OAAO,OAAO,YAAY,YAAY,KAAK,EAAE,YAAY,kBAAkB;AAAA,EACnH;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,SAAS,SAAS,4EAA4E,QAAQ;AAClH,MAAG,cAAc,GAAG;AACpB,YAAM,cAAiB,cAAc,CAAC,EAAE;AACxC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,QAAQ,QAAQ,WAAW,CAAC;AAAA,IAC3D,CAAC;AACD,IAAG,WAAW,GAAG,iEAAiE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,yEAAyE,GAAG,GAAG,gBAAgB,EAAE;AACxM,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAiB,cAAc,CAAC,EAAE;AACxC,IAAG,WAAW,cAAc,eAAe,OAAO,OAAO,YAAY,UAAU,EAAE,eAAe,eAAe,OAAO,OAAO,YAAY,WAAW,EAAE,4BAA4B,eAAe,OAAO,OAAO,YAAY,4BAA+B,gBAAgB,IAAI,GAAG,CAAC,EAAE,UAAU,eAAe,OAAO,OAAO,YAAY,MAAM,EAAE,YAAY,eAAe,OAAO,OAAO,YAAY,QAAQ,EAAE,uBAAuB,eAAe,OAAO,OAAO,YAAY,mBAAmB,EAAE,oBAAoB,eAAe,OAAO,OAAO,YAAY,gBAAgB,EAAE,sBAAsB,eAAe,OAAO,OAAO,YAAY,kBAAkB,EAAE,cAAc,eAAe,OAAO,OAAO,YAAY,UAAU,EAAE,SAAS,eAAe,OAAO,OAAO,YAAY,KAAK;AAC/vB,IAAG,YAAY,SAAS,eAAe,OAAO,OAAO,YAAY,KAAK,EAAE,aAAa,eAAe,OAAO,OAAO,YAAY,YAAY,OAAO,GAAG;AACpJ,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,eAAe,OAAO,OAAO,YAAY,IAAI;AACnE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,eAAe,OAAO,OAAO,YAAY,KAAK;AAAA,EACtE;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,KAAK,EAAE,EAAE,GAAG,0DAA0D,GAAG,IAAI,KAAK,EAAE;AAAA,EACvK;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAiB,cAAc,CAAC,EAAE;AACxC,IAAG,WAAW,QAAQ,EAAE,eAAe,OAAO,OAAO,YAAY,WAAW;AAC5E,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,eAAe,OAAO,OAAO,YAAY,UAAU;AAAA,EAC3E;AACF;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,EAAE;AAC7B,IAAG,WAAW,GAAG,sDAAsD,GAAG,CAAC,EAAE,GAAG,sDAAsD,GAAG,CAAC;AAC1I,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAiB,cAAc,EAAE;AACvC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,YAAY,UAAU;AACpC,IAAG,WAAW,WAAW,YAAY,KAAK,EAAE,WAAc,gBAAgB,GAAG,KAAK,YAAY,QAAQ,CAAC,EAAE,kBAAkB,YAAY,cAAc;AACrJ,IAAG,YAAY,MAAM,YAAY,EAAE,EAAE,mBAAmB,UAAU;AAClE,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,gBAAgB,OAAO,gBAAgB,IAAI,CAAC;AAAA,EACtE;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,kBAAkB;AAAA,EACpC;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AAAC;AAC1E,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,aAAa;AAAA,EAC9F;AACF;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,EAAE;AAC7B,IAAG,WAAW,GAAG,2DAA2D,GAAG,GAAG,oBAAoB,EAAE,EAAE,GAAG,0CAA0C,GAAG,GAAG,MAAM,EAAE;AACrK,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,mBAAmB,WAAW;AAC7C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,qBAAqB,CAAC,OAAO,kBAAkB;AAC7E,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,qBAAqB,OAAO,kBAAkB;AAAA,EACzF;AACF;AACA,SAAS,kCAAkC,IAAI,KAAK;AAClD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,wCAAwC,GAAG,IAAI,MAAM,EAAE,EAAE,GAAG,wCAAwC,GAAG,GAAG,MAAM,CAAC;AAAA,EACpI;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAc,IAAI;AACxB,UAAM,UAAU,IAAI;AACpB,IAAG,WAAW,QAAQ,YAAY,YAAY,KAAK;AACnD,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,WAAW,YAAY,YAAY,KAAK;AAAA,EACjE;AACF;AACA,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA,kBAEY,GAAG,uBAAuB,CAAC;AAAA,eAC9B,GAAG,oBAAoB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WAW5B,GAAG,gBAAgB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAMlB,GAAG,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WAelC,GAAG,qBAAqB,CAAC;AAAA,6BACP,GAAG,gCAAgC,CAAC,WAAW,GAAG,gCAAgC,CAAC,mBAAmB,GAAG,gCAAgC,CAAC,gBAAgB,GAAG,gCAAgC,CAAC;AAAA,qBACtM,GAAG,+BAA+B,CAAC;AAAA;AAAA,aAE3C,GAAG,uBAAuB,CAAC;AAAA;AAAA;AAAA;AAAA,kBAItB,GAAG,mCAAmC,CAAC;AAAA,eAC1C,GAAG,kCAAkC,CAAC,IAAI,GAAG,kCAAkC,CAAC,IAAI,GAAG,kCAAkC,CAAC;AAAA,sBACnH,GAAG,mCAAmC,CAAC;AAAA;AAAA;AAAA;AAAA,aAIhD,GAAG,6BAA6B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAQjC,GAAG,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,aAKhC,GAAG,kCAAkC,CAAC;AAAA;AAAA;AAGnD,IAAM,UAAU;AAAA,EACd,MAAM;AAAA,EACN,MAAM;AAAA,EACN,UAAU;AAAA,EACV,WAAW;AAAA,EACX,MAAM,CAAC;AAAA,IACL;AAAA,EACF,MAAM,CAAC,qBAAqB;AAAA,IAC1B,cAAc,SAAS,SAAS;AAAA,EAClC,CAAC;AAAA,EACD,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AACb;AACA,IAAM,kBAAN,MAAM,yBAAwB,UAAU;AAAA,EACtC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,wBAAwB,mBAAmB;AACzD,cAAQ,iCAAiC,+BAAkC,sBAAsB,gBAAe,IAAI,qBAAqB,gBAAe;AAAA,IAC1J;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,iBAAgB;AAAA,EAC3B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,oBAAmB;AAI5B,EAAAA,mBAAkB,MAAM,IAAI;AAI5B,EAAAA,mBAAkB,MAAM,IAAI;AAI5B,EAAAA,mBAAkB,UAAU,IAAI;AAIhC,EAAAA,mBAAkB,WAAW,IAAI;AAIjC,EAAAA,mBAAkB,MAAM,IAAI;AAI5B,EAAAA,mBAAkB,UAAU,IAAI;AAIhC,EAAAA,mBAAkB,UAAU,IAAI;AAIhC,EAAAA,mBAAkB,WAAW,IAAI;AACnC,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAMhD,IAAM,aAAN,MAAM,oBAAmB,cAAc;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,IAAI,aAAa;AAAA,EAC/B,kBAAkB,OAAO,eAAe;AAAA,EACxC,YAAY,QAAQ;AAClB,UAAM;AACN,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,QAAQ,OAAO,MAAM;AACnB,QAAI,KAAK,UAAU;AACjB,YAAM,eAAe;AACrB;AAAA,IACF;AACA,QAAI,CAAC,KAAK,OAAO,CAAC,KAAK,YAAY;AACjC,YAAM,eAAe;AAAA,IACvB;AACA,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ;AAAA,QACX,eAAe;AAAA,QACf;AAAA,MACF,CAAC;AAAA,IACH;AACA,SAAK,YAAY,KAAK;AAAA,MACpB,eAAe;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,qBAAqB;AACnB,SAAK,WAAW,QAAQ,UAAQ;AAC9B,cAAQ,KAAK,QAAQ,GAAG;AAAA,QACtB,KAAK;AACH,eAAK,qBAAqB,KAAK;AAC/B;AAAA,QACF,KAAK;AACH,eAAK,gBAAgB,KAAK;AAC1B;AAAA,QACF;AACE,eAAK,gBAAgB,KAAK;AAC1B;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAe,kBAAqB,MAAM,CAAC;AAAA,EAC9E;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,gBAAgB,SAAS,0BAA0B,IAAI,KAAK,UAAU;AACpE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AACnE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,oBAAoB,GAAG;AACxE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,eAAe,CAAC,GAAM,0BAA0B;AAAA,IAClF,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,iBAAiB,EAAE,GAAG,CAAC,sBAAsB,EAAE,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC,kBAAkB,EAAE,GAAG,CAAC,GAAG,WAAW,SAAS,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,YAAY,IAAI,GAAG,SAAS,WAAW,WAAW,kBAAkB,GAAG,MAAM,GAAG,CAAC,SAAS,0BAA0B,GAAG,MAAM,GAAG,CAAC,SAAS,IAAI,GAAG,SAAS,GAAG,CAAC,YAAY,IAAI,GAAG,WAAW,WAAW,gBAAgB,GAAG,CAAC,SAAS,0BAA0B,GAAG,QAAQ,UAAU,SAAS,GAAG,MAAM,GAAG,CAAC,SAAS,0BAA0B,GAAG,cAAc,eAAe,2BAA2B,UAAU,YAAY,uBAAuB,oBAAoB,sBAAsB,cAAc,SAAS,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,0BAA0B,GAAG,SAAS,QAAQ,QAAQ,GAAG,CAAC,SAAS,0BAA0B,GAAG,WAAW,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,cAAc,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,0BAA0B,GAAG,WAAW,SAAS,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,SAAS,2BAA2B,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG,2BAA2B,GAAG,WAAW,GAAG,CAAC,GAAG,0BAA0B,GAAG,SAAS,cAAc,eAAe,2BAA2B,UAAU,YAAY,uBAAuB,oBAAoB,sBAAsB,cAAc,OAAO,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,YAAY,IAAI,GAAG,SAAS,WAAW,WAAW,kBAAkB,GAAG,MAAM,GAAG,CAAC,YAAY,IAAI,GAAG,WAAW,WAAW,gBAAgB,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,SAAS,0BAA0B,GAAG,UAAU,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,0BAA0B,GAAG,SAAS,QAAQ,CAAC;AAAA,IACpnD,UAAU,SAAS,oBAAoB,IAAI,KAAK;AAC9C,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,MAAM,CAAC;AACzC,QAAG,WAAW,GAAG,0BAA0B,GAAG,IAAI,MAAM,CAAC,EAAE,GAAG,0BAA0B,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,mCAAmC,GAAG,GAAG,eAAe,CAAC;AACnK,QAAG,aAAa,EAAE;AAAA,MACpB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,UAAU;AAC5B,QAAG,WAAW,WAAW,IAAI,KAAK,EAAE,WAAW,0BAA0B;AACzE,QAAG,YAAY,gBAAgB,YAAY,EAAE,mBAAmB,MAAM;AACtE,QAAG,UAAU;AACb,QAAG,YAAY,mBAAmB,MAAM;AACxC,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,QAAQ,IAAI,KAAK,YAAY,KAAK;AAC5D,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,SAAS,IAAI,IAAI;AAC3C,QAAG,UAAU;AACb,QAAG,WAAW,WAAW,IAAI,KAAK;AAAA,MACpC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAiB,SAAY,SAAY,MAAS,kBAAqB,SAAS,cAAiB,YAAY,eAAkB,SAAS,kBAAkB,UAAU,YAAY;AAAA,IAC/L,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,cAAc,eAAe,kBAAkB,UAAU,YAAY;AAAA,MAC7F,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA4HV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,eAAe;AAAA,IAC7B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,YAAY,YAAY;AAAA,IAClC,SAAS,CAAC,YAAY,YAAY;AAAA,EACpC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,YAAY,cAAc,YAAY;AAAA,EAClD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY,YAAY;AAAA,MAClC,SAAS,CAAC,YAAY,YAAY;AAAA,IACpC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["BreadcrumbClasses"]}
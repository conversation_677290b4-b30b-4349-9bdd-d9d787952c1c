﻿using MediatR;

namespace TRtek_EYS.Application.Features.Projects.Commands.Update
{
    public class UpdateProjectCommand : IRequest<Guid>
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = null!;
        public string Code { get; set; } = null!;
        public string? Description { get; set; }
        public int Status { get; set; } // ProjectStatus enum
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public Guid CreatedByUserId { get; set; }
    }
}

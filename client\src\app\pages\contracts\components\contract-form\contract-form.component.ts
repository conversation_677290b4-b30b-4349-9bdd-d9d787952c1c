import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { Textarea } from 'primeng/inputtextarea';
import { Select } from 'primeng/select';
import { InputNumber } from 'primeng/inputnumber';
import { ToastModule } from 'primeng/toast';
import { MessageService } from 'primeng/api';
import { ContractStatus, LicenseType, PaymentTerms, SupportLevel } from '../../models/contract.model';
import { TooltipModule } from 'primeng/tooltip';

@Component({
    selector: 'app-contract-form',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        ButtonModule,
        InputTextModule,
        Textarea,
        Select,
        InputNumber,
        ToastModule,
        TooltipModule
    ],
    providers: [MessageService],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <h5>{{ isEditMode ? 'Sözleşme Düzenle' : 'Yeni Sözleşme' }}</h5>
                        <p-button 
                            label="Geri" 
                            icon="pi pi-arrow-left" 
                            class="p-button-secondary"
                            (onClick)="goBack()">
                        </p-button>
                    </div>

                    <form [formGroup]="contractForm" (ngSubmit)="onSubmit()">
                        <div class="grid">
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="contractNumber" class="font-medium">Sözleşme No *</label>
                                    <input 
                                        id="contractNumber"
                                        type="text" 
                                        pInputText 
                                        formControlName="contractNumber"
                                        [readonly]="isEditMode"
                                        class="w-full"
                                        placeholder="Otomatik oluşturulacak" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="customerName" class="font-medium">Müşteri *</label>
                                    <input 
                                        id="customerName"
                                        type="text" 
                                        pInputText 
                                        formControlName="customerName"
                                        class="w-full"
                                        placeholder="Müşteri adı" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="projectName" class="font-medium">Proje *</label>
                                    <input 
                                        id="projectName"
                                        type="text" 
                                        pInputText 
                                        formControlName="projectName"
                                        class="w-full"
                                        placeholder="Proje adı" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="licenseType" class="font-medium">Lisans Türü *</label>
                                    <p-select 
                                        id="licenseType"
                                        formControlName="licenseType"
                                        [options]="licenseTypeOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Lisans türü seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="contractValue" class="font-medium">Sözleşme Değeri *</label>
                                    <p-inputnumber 
                                        id="contractValue"
                                        formControlName="contractValue"
                                        mode="currency"
                                        currency="TRY"
                                        locale="tr-TR"
                                        class="w-full">
                                    </p-inputnumber>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="status" class="font-medium">Durum *</label>
                                    <p-select 
                                        id="status"
                                        formControlName="status"
                                        [options]="statusOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Durum seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="supportLevel" class="font-medium">Destek Seviyesi</label>
                                    <p-select 
                                        id="supportLevel"
                                        formControlName="supportLevel"
                                        [options]="supportLevelOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Destek seviyesi seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="maxUsers" class="font-medium">Maksimum Kullanıcı</label>
                                    <p-inputnumber 
                                        id="maxUsers"
                                        formControlName="maxUsers"
                                        [min]="1"
                                        placeholder="Kullanıcı sayısı"
                                        class="w-full">
                                    </p-inputnumber>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field">
                                    <label for="notes" class="font-medium">Notlar</label>
                                    <p-textarea 
                                        id="notes"
                                        formControlName="notes"
                                        rows="3"
                                        class="w-full"
                                        placeholder="Sözleşme notları...">
                                    </p-textarea>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-content-end gap-2 mt-4">
                            <p-button 
                                icon="pi pi-times"
                                severity="secondary"
                                rounded
                                outlined
                                type="button"
                                pTooltip="İptal"
                                (onClick)="goBack()">
                            </p-button>
                            <p-button 
                                icon="pi pi-check"
                                severity="success"
                                rounded
                                outlined
                                type="submit"
                                [pTooltip]="isEditMode ? 'Güncelle' : 'Kaydet'"
                                [disabled]="contractForm.invalid">
                            </p-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <p-toast></p-toast>
    `
})
export class ContractFormComponent implements OnInit {
    contractForm!: FormGroup;
    isEditMode = false;
    contractId: string = '';

    licenseTypeOptions = [
        { label: 'Kalıcı Lisans', value: LicenseType.PERPETUAL },
        { label: 'Abonelik', value: LicenseType.SUBSCRIPTION },
        { label: 'Deneme', value: LicenseType.TRIAL },
        { label: 'SaaS', value: LicenseType.SAAS },
        { label: 'Kurumsal', value: LicenseType.ENTERPRISE },
        { label: 'Özel', value: LicenseType.CUSTOM }
    ];

    statusOptions = [
        { label: 'Taslak', value: ContractStatus.DRAFT },
        { label: 'Aktif', value: ContractStatus.ACTIVE },
        { label: 'Beklemede', value: ContractStatus.PENDING },
        { label: 'Askıya Alınmış', value: ContractStatus.SUSPENDED }
    ];

    supportLevelOptions = [
        { label: 'Temel', value: SupportLevel.BASIC },
        { label: 'Standart', value: SupportLevel.STANDARD },
        { label: 'Premium', value: SupportLevel.PREMIUM },
        { label: 'Kurumsal', value: SupportLevel.ENTERPRISE },
        { label: 'Destek Yok', value: SupportLevel.NONE }
    ];

    constructor(
        private fb: FormBuilder,
        private route: ActivatedRoute,
        private router: Router,
        private messageService: MessageService
    ) {}

    ngOnInit() {
        this.initForm();
        this.checkEditMode();
    }

    initForm() {
        this.contractForm = this.fb.group({
            contractNumber: ['', Validators.required],
            customerName: ['', Validators.required],
            projectName: ['', Validators.required],
            licenseType: [LicenseType.SUBSCRIPTION, Validators.required],
            contractValue: [0, [Validators.required, Validators.min(0)]],
            status: [ContractStatus.DRAFT, Validators.required],
            supportLevel: [SupportLevel.STANDARD],
            maxUsers: [null],
            notes: ['']
        });

        if (!this.isEditMode) {
            this.generateContractNumber();
        }
    }

    checkEditMode() {
        this.contractId = this.route.snapshot.params['id'];
        this.isEditMode = !!this.contractId && this.router.url.includes('edit');
        
        if (this.isEditMode) {
            this.loadContract();
        }
    }

    generateContractNumber() {
        const year = new Date().getFullYear();
        const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        const contractNumber = `CNT-${year}-${randomNum}`;
        this.contractForm.patchValue({ contractNumber });
    }

    loadContract() {
        // Dummy data for edit mode
        const dummyContract = {
            contractNumber: 'CNT-2024-001',
            customerName: 'ABC Teknoloji A.Ş.',
            projectName: 'E-Ticaret Platformu',
            licenseType: LicenseType.SUBSCRIPTION,
            contractValue: 500000,
            status: ContractStatus.ACTIVE,
            supportLevel: SupportLevel.PREMIUM,
            maxUsers: 100,
            notes: 'Yıllık abonelik sözleşmesi'
        };

        this.contractForm.patchValue(dummyContract);
    }

    onSubmit() {
        if (this.contractForm.valid) {
            this.messageService.add({
                severity: 'success',
                summary: 'Başarılı',
                detail: this.isEditMode ? 'Sözleşme başarıyla güncellendi' : 'Sözleşme başarıyla oluşturuldu'
            });

            setTimeout(() => {
                this.router.navigate(['/pages/contracts']);
            }, 1500);
        }
    }

    goBack() {
        this.router.navigate(['/pages/contracts']);
    }
}

﻿using MediatR;
using TRtek_EYS.Application.Common.Interfaces;
using TRtek_EYS.Domain.Entities;

namespace TRtek_EYS.Application.Features.VersionInfos.Commands.Add
{
    public class AddVersionInfoCommandHandler : IRequestHandler<AddVersionInfoCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public AddVersionInfoCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(AddVersionInfoCommand request, CancellationToken cancellationToken)
        {
            var version = new VersionInfo
            {
                VersionNumber = request.VersionNumber,
                ReleaseDate = request.ReleaseDate,
                ReleaseNotes = request.ReleaseNotes,
                Status = request.Status,
                ProjectId = request.ProjectId,
                CreatedAt = DateTime.UtcNow
            };

            await _context.VersionInfos.AddAsync(version, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);

            return version.Id;
        }
    }
}

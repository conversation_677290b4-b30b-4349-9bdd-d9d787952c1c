﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TRtek_EYS.Domain.Entities;

namespace TRtek_EYS.Infrastructure.Configurations
{
    public class ProjectTeamConfiguration : IEntityTypeConfiguration<ProjectTeam>
    {
        public void Configure(EntityTypeBuilder<ProjectTeam> builder)
        {
            builder.ToTable("ProjectTeams");

            // Composite Primary Key
            builder.HasKey(pt => new { pt.ProjectId, pt.TeamId });

            // Project ilişkisi
            builder.HasOne(pt => pt.Project)
                   .WithMany(p => p.ProjectTeams)
                   .HasForeignKey(pt => pt.ProjectId)
                   .OnDelete(DeleteBehavior.Cascade);

            // Team ilişkisi
            builder.HasOne(pt => pt.Team)
                   .WithMany(t => t.ProjectTeams)
                   .HasForeignKey(pt => pt.TeamId)
                   .OnDelete(DeleteBehavior.Cascade);
        }
    }
}

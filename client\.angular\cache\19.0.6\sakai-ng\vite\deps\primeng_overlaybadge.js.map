{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-overlaybadge.mjs"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, booleanAttribute, Input, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { SharedModule } from 'primeng/api';\nimport * as i1 from 'primeng/badge';\nimport { BadgeModule } from 'primeng/badge';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"*\"];\nconst theme = ({\n  dt\n}) => `\n.p-overlaybadge {\n    position: relative;\n}\n\n.p-overlaybadge .p-badge {\n    position: absolute;\n    top: 0;\n    right: 0;\n    transform: translate(50%, -50%);\n    transform-origin: 100% 0;\n    margin: 0;\n    outline-width: ${dt('overlaybadge.outline.width')};\n    outline-style: solid;\n    outline-color: ${dt('overlaybadge.outline.color')};\n}\n`;\nconst classes = {\n  root: 'p-overlaybadge'\n};\nclass OverlayBadgeStyle extends BaseStyle {\n  name = 'overlaybadge';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵOverlayBadgeStyle_BaseFactory;\n    return function OverlayBadgeStyle_Factory(__ngFactoryType__) {\n      return (ɵOverlayBadgeStyle_BaseFactory || (ɵOverlayBadgeStyle_BaseFactory = i0.ɵɵgetInheritedFactory(OverlayBadgeStyle)))(__ngFactoryType__ || OverlayBadgeStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: OverlayBadgeStyle,\n    factory: OverlayBadgeStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayBadgeStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * OverlayPanel is a container component positioned as connected to its target.\n * @group Components\n */\nclass OverlayBadge extends BaseComponent {\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Inline style of the element.\n   * @group Props\n   */\n  style;\n  /**\n   * Size of the badge, valid options are \"large\" and \"xlarge\".\n   * @group Props\n   */\n  badgeSize;\n  /**\n   * Severity type of the badge.\n   * @group Props\n   */\n  severity;\n  /**\n   * Value to display inside the badge.\n   * @group Props\n   */\n  value;\n  /**\n   * When specified, disables the component.\n   * @group Props\n   */\n  badgeDisabled = false;\n  /**\n   * Size of the badge, valid options are \"large\" and \"xlarge\".\n   * @group Props\n   * @deprecated use badgeSize instead.\n   */\n  set size(value) {\n    this._size = value;\n    !this.badgeSize && this.size && console.log('size property is deprecated and will removed in v18, use badgeSize instead.');\n  }\n  get size() {\n    return this._size;\n  }\n  _size;\n  _componentStyle = inject(OverlayBadgeStyle);\n  constructor() {\n    super();\n  }\n  static ɵfac = function OverlayBadge_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || OverlayBadge)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: OverlayBadge,\n    selectors: [[\"p-overlayBadge\"], [\"p-overlay-badge\"], [\"p-overlaybadge\"]],\n    inputs: {\n      styleClass: \"styleClass\",\n      style: \"style\",\n      badgeSize: \"badgeSize\",\n      severity: \"severity\",\n      value: \"value\",\n      badgeDisabled: [2, \"badgeDisabled\", \"badgeDisabled\", booleanAttribute],\n      size: \"size\"\n    },\n    features: [i0.ɵɵProvidersFeature([OverlayBadgeStyle]), i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 3,\n    vars: 7,\n    consts: [[1, \"p-overlaybadge\"], [3, \"styleClass\", \"badgeSize\", \"severity\", \"value\", \"badgeDisabled\"]],\n    template: function OverlayBadge_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelement(2, \"p-badge\", 1);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵstyleMap(ctx.style);\n        i0.ɵɵproperty(\"styleClass\", ctx.styleClass)(\"badgeSize\", ctx.badgeSize)(\"severity\", ctx.severity)(\"value\", ctx.value)(\"badgeDisabled\", ctx.badgeDisabled);\n      }\n    },\n    dependencies: [CommonModule, BadgeModule, i1.Badge, SharedModule],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayBadge, [{\n    type: Component,\n    args: [{\n      selector: 'p-overlayBadge, p-overlay-badge, p-overlaybadge',\n      standalone: true,\n      imports: [CommonModule, BadgeModule, SharedModule],\n      template: `\n        <div class=\"p-overlaybadge\">\n            <ng-content></ng-content>\n            <p-badge [styleClass]=\"styleClass\" [style]=\"style\" [badgeSize]=\"badgeSize\" [severity]=\"severity\" [value]=\"value\" [badgeDisabled]=\"badgeDisabled\" />\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [OverlayBadgeStyle]\n    }]\n  }], () => [], {\n    styleClass: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    badgeSize: [{\n      type: Input\n    }],\n    severity: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    badgeDisabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    size: [{\n      type: Input\n    }]\n  });\n})();\nclass OverlayBadgeModule {\n  static ɵfac = function OverlayBadgeModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || OverlayBadgeModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: OverlayBadgeModule,\n    imports: [OverlayBadge, SharedModule],\n    exports: [OverlayBadge, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [OverlayBadge, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayBadgeModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayBadge, SharedModule],\n      exports: [OverlayBadge, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { OverlayBadge, OverlayBadgeModule, OverlayBadgeStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAYe,GAAG,4BAA4B,CAAC;AAAA;AAAA,qBAEhC,GAAG,4BAA4B,CAAC;AAAA;AAAA;AAGrD,IAAM,UAAU;AAAA,EACd,MAAM;AACR;AACA,IAAM,oBAAN,MAAM,2BAA0B,UAAU;AAAA,EACxC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,0BAA0B,mBAAmB;AAC3D,cAAQ,mCAAmC,iCAAoC,sBAAsB,kBAAiB,IAAI,qBAAqB,kBAAiB;AAAA,IAClK;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,mBAAkB;AAAA,EAC7B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,eAAN,MAAM,sBAAqB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhB,IAAI,KAAK,OAAO;AACd,SAAK,QAAQ;AACb,KAAC,KAAK,aAAa,KAAK,QAAQ,QAAQ,IAAI,6EAA6E;AAAA,EAC3H;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA;AAAA,EACA,kBAAkB,OAAO,iBAAiB;AAAA,EAC1C,cAAc;AACZ,UAAM;AAAA,EACR;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,gBAAgB,GAAG,CAAC,iBAAiB,GAAG,CAAC,gBAAgB,CAAC;AAAA,IACvE,QAAQ;AAAA,MACN,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,MACP,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,MACrE,MAAM;AAAA,IACR;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,iBAAiB,CAAC,GAAM,0BAA6B,0BAA0B;AAAA,IACjH,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,gBAAgB,GAAG,CAAC,GAAG,cAAc,aAAa,YAAY,SAAS,eAAe,CAAC;AAAA,IACpG,UAAU,SAAS,sBAAsB,IAAI,KAAK;AAChD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,aAAa,CAAC;AACjB,QAAG,UAAU,GAAG,WAAW,CAAC;AAC5B,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,IAAI,KAAK;AACvB,QAAG,WAAW,cAAc,IAAI,UAAU,EAAE,aAAa,IAAI,SAAS,EAAE,YAAY,IAAI,QAAQ,EAAE,SAAS,IAAI,KAAK,EAAE,iBAAiB,IAAI,aAAa;AAAA,MAC1J;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAc,aAAgB,OAAO,YAAY;AAAA,IAChE,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,aAAa,YAAY;AAAA,MACjD,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,iBAAiB;AAAA,IAC/B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,cAAc,YAAY;AAAA,IACpC,SAAS,CAAC,cAAc,YAAY;AAAA,EACtC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,cAAc,cAAc,YAAY;AAAA,EACpD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,YAAY;AAAA,MACpC,SAAS,CAAC,cAAc,YAAY;AAAA,IACtC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}
﻿using TRtek_EYS.Domain.Entities;

public class Role : BaseEntity
{
    public string Name { get; set; } = null!;
    public string? Description { get; set; }

    public Guid TeamId { get; set; }
    public Team? Team { get; set; }

    public ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
    public ICollection<ProjectUserRole> ProjectUserRoles { get; set; } = new List<ProjectUserRole>();

}

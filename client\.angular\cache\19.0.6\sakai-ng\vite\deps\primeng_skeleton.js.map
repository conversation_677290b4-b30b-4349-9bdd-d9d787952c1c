{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-skeleton.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, Input, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { SharedModule } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { BaseStyle } from 'primeng/base';\nconst theme = ({\n  dt\n}) => `\n.p-skeleton {\n    overflow: hidden;\n    background: ${dt('skeleton.background')};\n    border-radius: ${dt('skeleton.border.radius')};\n}\n\n.p-skeleton::after {\n    content: \"\";\n    animation: p-skeleton-animation 1.2s infinite;\n    height: 100%;\n    left: 0;\n    position: absolute;\n    right: 0;\n    top: 0;\n    transform: translateX(-100%);\n    z-index: 1;\n    background: linear-gradient(90deg, rgba(255, 255, 255, 0), ${dt('skeleton.animation.background')}, rgba(255, 255, 255, 0));\n}\n\n[dir='rtl'] .p-skeleton::after {\n    animation-name: p-skeleton-animation-rtl;\n}\n\n.p-skeleton-circle {\n    border-radius: 50%;\n}\n\n.p-skeleton-animation-none::after {\n    animation: none;\n}\n\n@keyframes p-skeleton-animation {\n    from {\n        transform: translateX(-100%);\n    }\n    to {\n        transform: translateX(100%);\n    }\n}\n\n@keyframes p-skeleton-animation-rtl {\n    from {\n        transform: translateX(100%);\n    }\n    to {\n        transform: translateX(-100%);\n    }\n}\n`;\nconst inlineStyles = {\n  root: {\n    position: 'relative'\n  }\n};\nconst classes = {\n  root: ({\n    props\n  }) => ['p-skeleton p-component', {\n    'p-skeleton-circle': props.shape === 'circle',\n    'p-skeleton-animation-none': props.animation === 'none'\n  }]\n};\nclass SkeletonStyle extends BaseStyle {\n  name = 'skeleton';\n  theme = theme;\n  classes = classes;\n  inlineStyles = inlineStyles;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵSkeletonStyle_BaseFactory;\n    return function SkeletonStyle_Factory(__ngFactoryType__) {\n      return (ɵSkeletonStyle_BaseFactory || (ɵSkeletonStyle_BaseFactory = i0.ɵɵgetInheritedFactory(SkeletonStyle)))(__ngFactoryType__ || SkeletonStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: SkeletonStyle,\n    factory: SkeletonStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SkeletonStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * Skeleton is a placeholder to display instead of the actual content.\n *\n * [Live Demo](https://www.primeng.org/skeleton/)\n *\n * @module skeletonstyle\n *\n */\nvar SkeletonClasses;\n(function (SkeletonClasses) {\n  /**\n   * Class name of the root element\n   */\n  SkeletonClasses[\"root\"] = \"p-skeleton\";\n})(SkeletonClasses || (SkeletonClasses = {}));\n\n/**\n * Skeleton is a placeholder to display instead of the actual content.\n * @group Components\n */\nclass Skeleton extends BaseComponent {\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Inline style of the element.\n   * @group Props\n   */\n  style;\n  /**\n   * Shape of the element.\n   * @group Props\n   */\n  shape = 'rectangle';\n  /**\n   * Type of the animation.\n   * @gruop Props\n   */\n  animation = 'wave';\n  /**\n   * Border radius of the element, defaults to value from theme.\n   * @group Props\n   */\n  borderRadius;\n  /**\n   * Size of the skeleton.\n   * @group Props\n   */\n  size;\n  /**\n   * Width of the element.\n   * @group Props\n   */\n  width = '100%';\n  /**\n   * Height of the element.\n   * @group Props\n   */\n  height = '1rem';\n  _componentStyle = inject(SkeletonStyle);\n  containerClass() {\n    return {\n      'p-skeleton p-component': true,\n      'p-skeleton-circle': this.shape === 'circle',\n      'p-skeleton-animation-none': this.animation === 'none'\n    };\n  }\n  get containerStyle() {\n    const inlineStyles = this._componentStyle?.inlineStyles['root'];\n    let style;\n    if (this.size) style = {\n      ...this.style,\n      ...inlineStyles,\n      width: this.size,\n      height: this.size,\n      borderRadius: this.borderRadius\n    };else style = {\n      ...inlineStyles,\n      width: this.width,\n      height: this.height,\n      borderRadius: this.borderRadius,\n      ...this.style\n    };\n    return style;\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵSkeleton_BaseFactory;\n    return function Skeleton_Factory(__ngFactoryType__) {\n      return (ɵSkeleton_BaseFactory || (ɵSkeleton_BaseFactory = i0.ɵɵgetInheritedFactory(Skeleton)))(__ngFactoryType__ || Skeleton);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Skeleton,\n    selectors: [[\"p-skeleton\"]],\n    inputs: {\n      styleClass: \"styleClass\",\n      style: \"style\",\n      shape: \"shape\",\n      animation: \"animation\",\n      borderRadius: \"borderRadius\",\n      size: \"size\",\n      width: \"width\",\n      height: \"height\"\n    },\n    features: [i0.ɵɵProvidersFeature([SkeletonStyle]), i0.ɵɵInheritDefinitionFeature],\n    decls: 1,\n    vars: 7,\n    consts: [[3, \"ngClass\", \"ngStyle\"]],\n    template: function Skeleton_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", ctx.containerClass())(\"ngStyle\", ctx.containerStyle);\n        i0.ɵɵattribute(\"data-pc-name\", \"skeleton\")(\"aria-hidden\", true)(\"data-pc-section\", \"root\");\n      }\n    },\n    dependencies: [CommonModule, i1.NgClass, i1.NgStyle, SharedModule],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Skeleton, [{\n    type: Component,\n    args: [{\n      selector: 'p-skeleton',\n      standalone: true,\n      imports: [CommonModule, SharedModule],\n      template: ` <div [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"containerStyle\" [attr.data-pc-name]=\"'skeleton'\" [attr.aria-hidden]=\"true\" [attr.data-pc-section]=\"'root'\"></div> `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [SkeletonStyle]\n    }]\n  }], null, {\n    styleClass: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    shape: [{\n      type: Input\n    }],\n    animation: [{\n      type: Input\n    }],\n    borderRadius: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    width: [{\n      type: Input\n    }],\n    height: [{\n      type: Input\n    }]\n  });\n})();\nclass SkeletonModule {\n  static ɵfac = function SkeletonModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || SkeletonModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: SkeletonModule,\n    imports: [Skeleton, SharedModule],\n    exports: [Skeleton, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [Skeleton, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SkeletonModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Skeleton, SharedModule],\n      exports: [Skeleton, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Skeleton, SkeletonClasses, SkeletonModule, SkeletonStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA;AAAA,kBAGY,GAAG,qBAAqB,CAAC;AAAA,qBACtB,GAAG,wBAAwB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iEAagB,GAAG,+BAA+B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiCpG,IAAM,eAAe;AAAA,EACnB,MAAM;AAAA,IACJ,UAAU;AAAA,EACZ;AACF;AACA,IAAM,UAAU;AAAA,EACd,MAAM,CAAC;AAAA,IACL;AAAA,EACF,MAAM,CAAC,0BAA0B;AAAA,IAC/B,qBAAqB,MAAM,UAAU;AAAA,IACrC,6BAA6B,MAAM,cAAc;AAAA,EACnD,CAAC;AACH;AACA,IAAM,gBAAN,MAAM,uBAAsB,UAAU;AAAA,EACpC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,eAAe;AAAA,EACf,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,sBAAsB,mBAAmB;AACvD,cAAQ,+BAA+B,6BAAgC,sBAAsB,cAAa,IAAI,qBAAqB,cAAa;AAAA,IAClJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,eAAc;AAAA,EACzB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,kBAAiB;AAI1B,EAAAA,iBAAgB,MAAM,IAAI;AAC5B,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAM5C,IAAM,WAAN,MAAM,kBAAiB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKR,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKZ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKR,SAAS;AAAA,EACT,kBAAkB,OAAO,aAAa;AAAA,EACtC,iBAAiB;AACf,WAAO;AAAA,MACL,0BAA0B;AAAA,MAC1B,qBAAqB,KAAK,UAAU;AAAA,MACpC,6BAA6B,KAAK,cAAc;AAAA,IAClD;AAAA,EACF;AAAA,EACA,IAAI,iBAAiB;AACnB,UAAMC,gBAAe,KAAK,iBAAiB,aAAa,MAAM;AAC9D,QAAI;AACJ,QAAI,KAAK,KAAM,SAAQ,gDAClB,KAAK,QACLA,gBAFkB;AAAA,MAGrB,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,MACb,cAAc,KAAK;AAAA,IACrB;AAAA,QAAO,SAAQ,gDACVA,gBADU;AAAA,MAEb,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,MACb,cAAc,KAAK;AAAA,QAChB,KAAK;AAEV,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,iBAAiB,mBAAmB;AAClD,cAAQ,0BAA0B,wBAA2B,sBAAsB,SAAQ,IAAI,qBAAqB,SAAQ;AAAA,IAC9H;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,IAC1B,QAAQ;AAAA,MACN,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,WAAW;AAAA,MACX,cAAc;AAAA,MACd,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,aAAa,CAAC,GAAM,0BAA0B;AAAA,IAChF,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,WAAW,SAAS,CAAC;AAAA,IAClC,UAAU,SAAS,kBAAkB,IAAI,KAAK;AAC5C,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,GAAG,OAAO,CAAC;AAAA,MAC1B;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,UAAU;AAC5B,QAAG,WAAW,WAAW,IAAI,eAAe,CAAC,EAAE,WAAW,IAAI,cAAc;AAC5E,QAAG,YAAY,gBAAgB,UAAU,EAAE,eAAe,IAAI,EAAE,mBAAmB,MAAM;AAAA,MAC3F;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAiB,SAAY,SAAS,YAAY;AAAA,IACjE,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,YAAY;AAAA,MACpC,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,aAAa;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,UAAU,YAAY;AAAA,IAChC,SAAS,CAAC,UAAU,YAAY;AAAA,EAClC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,UAAU,cAAc,YAAY;AAAA,EAChD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,UAAU,YAAY;AAAA,MAChC,SAAS,CAAC,UAAU,YAAY;AAAA,IAClC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["SkeletonClasses", "inlineStyles"]}
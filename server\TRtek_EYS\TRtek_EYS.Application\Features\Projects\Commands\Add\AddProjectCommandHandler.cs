using MediatR;
using TRtek_EYS.Application.Common.Interfaces;
using TRtek_EYS.Domain.Entities;
using TRtek_EYS.Domain.Enums;

namespace TRtek_EYS.Application.Features.Projects.Commands.Add
{
    public class AddProjectCommandHandler : IRequestHandler<AddProjectCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public AddProjectCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(AddProjectCommand request, CancellationToken cancellationToken)
        {
            var project = new Project
            {
                Name = request.Name,
                Code = request.Code,
                Description = request.Description,
                StartDate = request.StartDate,
                EndDate = request.EndDate,
                Status = ProjectStatus.PLANNING,
                CreatedAt = DateTime.UtcNow,
                CreatedByUserId = request.CreatedByUserId 
            };


            await _context.Projects.AddAsync(project, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);

            return project.Id;
        }
    }
}

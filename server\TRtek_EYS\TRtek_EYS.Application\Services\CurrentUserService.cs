﻿using Microsoft.AspNetCore.Http;
using System.Security.Claims;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Services
{
    public class CurrentUserService : ICurrentUserService
    {
        public Guid? UserId { get; }
        public string? Username { get; }
        public string? Role { get; }

        public CurrentUserService(IHttpContextAccessor httpContextAccessor)
        {
            var user = httpContextAccessor.HttpContext?.User;

            if (user?.Identity?.IsAuthenticated == true)
            {
                var userIdClaim = user.FindFirst(ClaimTypes.NameIdentifier); // genelde "sub" olarak gelir
                var usernameClaim = user.FindFirst(ClaimTypes.Name);
                var roleClaim = user.FindFirst(ClaimTypes.Role);

                if (Guid.TryParse(userIdClaim?.Value, out var parsedUserId))
                {
                    UserId = parsedUserId;
                }

                Username = usernameClaim?.Value;
                Role = roleClaim?.Value;
            }
        }
    }
}

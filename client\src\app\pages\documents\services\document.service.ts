import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Document, DocumentFormData, DocumentType, DocumentCategory, DocumentFormat, DocumentStatus, DocumentVisibility, DifficultyLevel } from '../models/document.model';

@Injectable({
    providedIn: 'root'
})
export class DocumentService {
    private mockDocuments: Document[] = [
        {
            id: '1',
            documentCode: 'DOC-2025-001',
            title: '<PERSON>je <PERSON>eli<PERSON>rme Standardı',
            description: 'Şirket içi yazılım geliştirme standartları ve best practice\'ler. Tüm geliştirme ekiplerinin uyması gereken kuralları içerir.',
            type: DocumentType.POLICY,
            category: DocumentCategory.DEVELOPMENT,
            format: DocumentFormat.PDF,
            version: '2.3',
            status: DocumentStatus.PUBLISHED,
            visibility: DocumentVisibility.INTERNAL,
            filePath: '/documents/standards/dev-standard-v2.3.pdf',
            fileSize: 1250000,
            fileName: 'dev-standard-v2.3.pdf',
            authorId: '101',
            authorName: 'Ahmet Yılmaz',
            reviewerId: '102',
            reviewerName: 'Mehmet Öztürk',
            approvedById: '103',
            approvedByName: 'Zeynep Kaya',
            isTemplate: false,
            isArchived: false,
            downloadCount: 45,
            viewCount: 120,
            createdAt: new Date('2025-01-15'),
            updatedAt: new Date('2025-03-20'),
            publishedAt: new Date('2025-03-25'),
            reviewedAt: new Date('2025-03-22'),
            approvedAt: new Date('2025-03-23'),
            tags: ['standard', 'development', 'best-practice', 'coding'],
            attachments: [
                {
                    id: 'a1',
                    name: 'Code Review Checklist',
                    fileName: 'code-review-checklist.xlsx',
                    filePath: '/documents/standards/code-review-checklist.xlsx',
                    fileSize: 45000,
                    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    uploadedAt: new Date('2025-03-20'),
                    uploadedBy: 'Ahmet Yılmaz'
                }
            ],
            revisions: [
                {
                    id: 'r1',
                    version: '2.2',
                    description: 'Önceki versiyon',
                    changes: ['CI/CD bölümü güncellendi', 'Kod standartları eklendi'],
                    createdAt: new Date('2023-11-10'),
                    createdBy: 'Ahmet Yılmaz',
                    filePath: '/documents/standards/dev-standard-v2.2.pdf'
                }
            ],
            comments: [
                {
                    id: 'c1',
                    content: 'Docker bölümü güncellenebilir',
                    authorId: '104',
                    authorName: 'Ali Demir',
                    createdAt: new Date('2025-03-21'),
                    isResolved: true,
                    replies: []
                }
            ],
            metadata: {
                keywords: ['development', 'standard', 'coding', 'best-practice'],
                language: 'tr',
                audience: ['developers', 'team-leads'],
                difficulty: DifficultyLevel.INTERMEDIATE,
                estimatedReadTime: 45,
                lastReviewDate: new Date('2025-03-22'),
                nextReviewDate: new Date('2025-09-22'),
                relatedDocuments: ['DOC-2025-002', 'DOC-2025-005']
            },
            notes: 'Her 6 ayda bir gözden geçirilmeli'
        },
        {
            id: '2',
            documentCode: 'DOC-2025-002',
            title: 'API Tasarım Kılavuzu',
            description: 'RESTful API tasarım prensipleri ve standartları. Endpoint isimlendirme, HTTP metodları, status kodları ve versiyonlama konularını içerir.',
            type: DocumentType.TECHNICAL,
            category: DocumentCategory.DEVELOPMENT,
            format: DocumentFormat.MARKDOWN,
            version: '1.5',
            status: DocumentStatus.PUBLISHED,
            visibility: DocumentVisibility.TEAM,
            filePath: '/documents/guides/api-design-guide-v1.5.md',
            fileSize: 350000,
            fileName: 'api-design-guide-v1.5.md',
            authorId: '105',
            authorName: 'Ayşe Yıldız',
            reviewerId: '106',
            reviewerName: 'Can Şahin',
            approvedById: '103',
            approvedByName: 'Zeynep Kaya',
            isTemplate: false,
            isArchived: false,
            downloadCount: 32,
            viewCount: 87,
            createdAt: new Date('2025-02-10'),
            updatedAt: new Date('2025-04-15'),
            publishedAt: new Date('2025-04-18'),
            reviewedAt: new Date('2025-04-16'),
            approvedAt: new Date('2025-04-17'),
            tags: ['api', 'rest', 'design', 'backend'],
            attachments: [],
            revisions: [
                {
                    id: 'r2',
                    version: '1.4',
                    description: 'Önceki versiyon',
                    changes: ['Authentication bölümü eklendi', 'Örnek kodlar güncellendi'],
                    createdAt: new Date('2023-12-05'),
                    createdBy: 'Ayşe Yıldız',
                    filePath: '/documents/guides/api-design-guide-v1.4.md'
                }
            ],
            comments: [],
            metadata: {
                keywords: ['api', 'rest', 'design', 'backend', 'http'],
                language: 'tr',
                audience: ['backend-developers', 'architects'],
                difficulty: DifficultyLevel.ADVANCED,
                estimatedReadTime: 30,
                lastReviewDate: new Date('2025-04-16'),
                nextReviewDate: new Date('2025-10-16'),
                relatedDocuments: ['DOC-2025-001', 'DOC-2025-003']
            },
            notes: 'Yeni API projeleri için referans doküman'
        },
        {
            id: '3',
            documentCode: 'DOC-2025-003',
            title: 'Angular Uygulama Mimarisi',
            description: 'Angular uygulamaları için mimari tasarım, folder yapısı, state management ve modüler yapı hakkında bilgiler.',
            type: DocumentType.DESIGN,
            category: DocumentCategory.ARCHITECTURE,
            format: DocumentFormat.HTML,
            version: '1.0',
            status: DocumentStatus.PUBLISHED,
            visibility: DocumentVisibility.TEAM,
            url: 'https://wiki.company.com/docs/angular-architecture',
            authorId: '107',
            authorName: 'Emre Kılıç',
            reviewerId: '101',
            reviewerName: 'Ahmet Yılmaz',
            approvedById: '108',
            approvedByName: 'Deniz Arslan',
            isTemplate: false,
            isArchived: false,
            downloadCount: 0,
            viewCount: 56,
            createdAt: new Date('2025-03-05'),
            updatedAt: new Date('2025-05-10'),
            publishedAt: new Date('2025-05-12'),
            reviewedAt: new Date('2025-05-11'),
            approvedAt: new Date('2025-05-11'),
            tags: ['angular', 'architecture', 'frontend', 'design'],
            attachments: [
                {
                    id: 'a2',
                    name: 'Architecture Diagram',
                    fileName: 'angular-arch-diagram.png',
                    filePath: '/documents/diagrams/angular-arch-diagram.png',
                    fileSize: 250000,
                    mimeType: 'image/png',
                    uploadedAt: new Date('2025-05-10'),
                    uploadedBy: 'Emre Kılıç'
                }
            ],
            revisions: [],
            comments: [
                {
                    id: 'c2',
                    content: 'State management bölümü detaylandırılabilir',
                    authorId: '109',
                    authorName: 'Selin Yılmaz',
                    createdAt: new Date('2025-05-15'),
                    isResolved: false,
                    replies: [
                        {
                            id: 'c3',
                            content: 'Bir sonraki versiyonda ekleyeceğim',
                            authorId: '107',
                            authorName: 'Emre Kılıç',
                            createdAt: new Date('2025-05-16'),
                            isResolved: false,
                            replies: []
                        }
                    ]
                }
            ],
            metadata: {
                keywords: ['angular', 'architecture', 'frontend', 'design', 'state-management'],
                language: 'tr',
                audience: ['frontend-developers'],
                difficulty: DifficultyLevel.INTERMEDIATE,
                estimatedReadTime: 25,
                lastReviewDate: new Date('2025-05-11'),
                nextReviewDate: new Date('2025-11-11'),
                relatedDocuments: ['DOC-2025-001']
            },
            notes: 'Yeni frontend geliştiricileri için önemli'
        },
        {
            id: '4',
            documentCode: 'DOC-2025-004',
            title: 'Proje Teklif Şablonu',
            description: 'Müşterilere sunulacak proje teklifleri için standart şablon. Kapsam, zaman, maliyet ve risk bölümlerini içerir.',
            type: DocumentType.TEMPLATE,
            category: DocumentCategory.BUSINESS,
            format: DocumentFormat.WORD,
            version: '3.1',
            status: DocumentStatus.PUBLISHED,
            visibility: DocumentVisibility.INTERNAL,
            filePath: '/documents/templates/project-proposal-template-v3.1.docx',
            fileSize: 520000,
            fileName: 'project-proposal-template-v3.1.docx',
            authorId: '110',
            authorName: 'Murat Özkan',
            reviewerId: '111',
            reviewerName: 'Canan Yılmaz',
            approvedById: '112',
            approvedByName: 'Serkan Demir',
            isTemplate: true,
            isArchived: false,
            downloadCount: 78,
            viewCount: 95,
            createdAt: new Date('2023-09-15'),
            updatedAt: new Date('2025-02-20'),
            publishedAt: new Date('2025-02-25'),
            reviewedAt: new Date('2025-02-22'),
            approvedAt: new Date('2025-02-23'),
            tags: ['template', 'proposal', 'business', 'project'],
            attachments: [],
            revisions: [
                {
                    id: 'r3',
                    version: '3.0',
                    description: 'Önceki versiyon',
                    changes: ['Maliyet tablosu güncellendi', 'Risk yönetimi bölümü eklendi'],
                    createdAt: new Date('2023-06-10'),
                    createdBy: 'Murat Özkan',
                    filePath: '/documents/templates/project-proposal-template-v3.0.docx'
                }
            ],
            comments: [],
            metadata: {
                keywords: ['template', 'proposal', 'business', 'project', 'client'],
                language: 'tr',
                audience: ['project-managers', 'business-analysts'],
                difficulty: DifficultyLevel.BEGINNER,
                estimatedReadTime: 15,
                lastReviewDate: new Date('2025-02-22'),
                nextReviewDate: new Date('2025-08-22'),
                relatedDocuments: []
            },
            notes: 'Tüm proje tekliflerinde bu şablon kullanılmalı'
        },
        {
            id: '5',
            documentCode: 'DOC-2025-005',
            title: 'Veritabanı Tasarım Standardı',
            description: 'Veritabanı tasarım prensipleri, isimlendirme kuralları, indeksleme stratejileri ve performans optimizasyonu.',
            type: DocumentType.TECHNICAL,
            category: DocumentCategory.DATABASE,
            format: DocumentFormat.PDF,
            version: '2.0',
            status: DocumentStatus.DRAFT,
            visibility: DocumentVisibility.TEAM,
            filePath: '/documents/standards/db-design-standard-v2.0-draft.pdf',
            fileSize: 980000,
            fileName: 'db-design-standard-v2.0-draft.pdf',
            authorId: '113',
            authorName: 'Kemal Yıldırım',
            isTemplate: false,
            isArchived: false,
            downloadCount: 5,
            viewCount: 12,
            createdAt: new Date('2025-06-01'),
            updatedAt: new Date('2025-07-10'),
            tags: ['database', 'design', 'sql', 'performance'],
            attachments: [
                {
                    id: 'a3',
                    name: 'Performance Benchmark Results',
                    fileName: 'db-benchmark-results.xlsx',
                    filePath: '/documents/data/db-benchmark-results.xlsx',
                    fileSize: 320000,
                    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    uploadedAt: new Date('2025-07-05'),
                    uploadedBy: 'Kemal Yıldırım'
                }
            ],
            revisions: [
                {
                    id: 'r4',
                    version: '1.5',
                    description: 'Önceki yayınlanan versiyon',
                    changes: ['NoSQL bölümü eklendi', 'Performans optimizasyonu güncellendi'],
                    createdAt: new Date('2023-10-15'),
                    createdBy: 'Kemal Yıldırım',
                    filePath: '/documents/standards/db-design-standard-v1.5.pdf'
                }
            ],
            comments: [
                {
                    id: 'c4',
                    content: 'MongoDB bölümü daha detaylı olabilir',
                    authorId: '114',
                    authorName: 'Burak Şahin',
                    createdAt: new Date('2025-07-12'),
                    isResolved: false,
                    replies: []
                }
            ],
            metadata: {
                keywords: ['database', 'design', 'sql', 'nosql', 'performance', 'indexing'],
                language: 'tr',
                audience: ['backend-developers', 'database-admins'],
                difficulty: DifficultyLevel.ADVANCED,
                estimatedReadTime: 60,
                relatedDocuments: ['DOC-2025-001', 'DOC-2025-002']
            },
            notes: 'Taslak - henüz yayınlanmadı'
        },
        {
            id: '6',
            documentCode: 'DOC-2023-015',
            title: 'Legacy Sistem Dokümantasyonu',
            description: 'Eski ERP sisteminin teknik dokümantasyonu. Sistem mimarisi, veritabanı şeması ve API referanslarını içerir.',
            type: DocumentType.TECHNICAL,
            category: DocumentCategory.LEGACY,
            format: DocumentFormat.PDF,
            version: '1.2',
            status: DocumentStatus.ARCHIVED,
            visibility: DocumentVisibility.RESTRICTED,
            filePath: '/documents/legacy/erp-system-doc-v1.2.pdf',
            fileSize: 4500000,
            fileName: 'erp-system-doc-v1.2.pdf',
            authorId: '115',
            authorName: 'Hakan Koç',
            reviewerId: '116',
            reviewerName: 'Leyla Demir',
            approvedById: '117',
            approvedByName: 'Osman Kaya',
            isTemplate: false,
            isArchived: true,
            downloadCount: 23,
            viewCount: 45,
            createdAt: new Date('2023-03-10'),
            updatedAt: new Date('2023-05-15'),
            publishedAt: new Date('2023-05-20'),
            reviewedAt: new Date('2023-05-18'),
            approvedAt: new Date('2023-05-19'),
            expiryDate: new Date('2025-05-20'),
            tags: ['legacy', 'erp', 'system', 'archived'],
            attachments: [],
            revisions: [],
            comments: [],
            metadata: {
                keywords: ['legacy', 'erp', 'system', 'documentation'],
                language: 'tr',
                audience: ['maintenance-team'],
                difficulty: DifficultyLevel.EXPERT,
                estimatedReadTime: 120,
                lastReviewDate: new Date('2023-05-18'),
                relatedDocuments: []
            },
            notes: 'Sistem kullanımdan kaldırıldı, sadece referans amaçlı saklanıyor'
        }
    ];

    getDocuments(): Observable<Document[]> {
        return of(this.mockDocuments);
    }

    getDocumentById(id: string): Observable<Document | undefined> {
        return of(this.mockDocuments.find(doc => doc.id === id));
    }

    createDocument(documentData: DocumentFormData): Observable<Document> {
        const newDocument: Document = {
            id: Date.now().toString(),
            ...documentData,
            authorId: '101', // Current user ID
            authorName: 'Ahmet Yılmaz', // Current user name
            isArchived: false,
            downloadCount: 0,
            viewCount: 0,
            createdAt: new Date(),
            updatedAt: new Date(),
            attachments: [],
            revisions: [],
            comments: [],
            metadata: {
                keywords: documentData.tags,
                language: 'tr',
                audience: [],
                difficulty: DifficultyLevel.INTERMEDIATE,
                estimatedReadTime: 0,
                relatedDocuments: []
            }
        };
        this.mockDocuments.push(newDocument);
        return of(newDocument);
    }

    updateDocument(id: string, documentData: DocumentFormData): Observable<Document | undefined> {
        const index = this.mockDocuments.findIndex(doc => doc.id === id);
        if (index === -1) return of(undefined);

        const existingDocument = this.mockDocuments[index];
        const updatedDocument: Document = {
            ...existingDocument,
            ...documentData,
            updatedAt: new Date()
        };
        this.mockDocuments[index] = updatedDocument;
        return of(updatedDocument);
    }

    deleteDocument(id: string): Observable<boolean> {
        const index = this.mockDocuments.findIndex(doc => doc.id === id);
        if (index === -1) return of(false);
        
        this.mockDocuments.splice(index, 1);
        return of(true);
    }

    archiveDocument(id: string): Observable<boolean> {
        const document = this.mockDocuments.find(d => d.id === id);
        if (!document) return of(false);
        
        document.isArchived = true;
        document.status = DocumentStatus.ARCHIVED;
        document.updatedAt = new Date();
        return of(true);
    }

    publishDocument(id: string): Observable<boolean> {
        const document = this.mockDocuments.find(d => d.id === id);
        if (!document) return of(false);
        
        document.status = DocumentStatus.PUBLISHED;
        document.publishedAt = new Date();
        document.updatedAt = new Date();
        return of(true);
    }

    incrementViewCount(id: string): Observable<boolean> {
        const document = this.mockDocuments.find(d => d.id === id);
        if (!document) return of(false);
        
        document.viewCount++;
        return of(true);
    }

    incrementDownloadCount(id: string): Observable<boolean> {
        const document = this.mockDocuments.find(d => d.id === id);
        if (!document) return of(false);
        
        document.downloadCount++;
        return of(true);
    }
}

import { Routes } from '@angular/router';

export default [
    {
        path: '',
        loadComponent: () => import('./components/roles-list/roles-list.component').then(m => m.RolesListComponent)
    },
    {
        path: 'new',
        loadComponent: () => import('./components/role-form/role-form.component').then(m => m.RoleFormComponent)
    },
    {
        path: ':id',
        loadComponent: () => import('./components/role-detail/role-detail.component').then(m => m.RoleDetailComponent)
    },
    {
        path: ':id/edit',
        loadComponent: () => import('./components/role-form/role-form.component').then(m => m.RoleFormComponent)
    }
] as Routes;

﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.VersionInfos.Commands.Update
{
    public class UpdateVersionInfoCommandHandler : IRequestHandler<UpdateVersionInfoCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public UpdateVersionInfoCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(UpdateVersionInfoCommand request, CancellationToken cancellationToken)
        {
            var version = await _context.VersionInfos
                .FirstOrDefaultAsync(v => v.Id == request.Id, cancellationToken);

            if (version is null)
                throw new Exception("Sürüm bilgisi bulunamadı.");

            version.VersionNumber = request.VersionNumber;
            version.ReleaseDate = request.ReleaseDate;
            version.ReleaseNotes = request.ReleaseNotes;
            version.Status = request.Status;
            version.ProjectId = request.ProjectId;

            await _context.SaveChangesAsync(cancellationToken);

            return version.Id;
        }
    }
}

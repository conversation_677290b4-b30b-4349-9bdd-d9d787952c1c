import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Role, RoleFormData, Permission, SYSTEM_PERMISSIONS, PermissionCategory } from '../models/role.model';

@Injectable({
    providedIn: 'root'
})
export class RoleService {
    private mockRoles: Role[] = [
        {
            id: '1',
            roleCode: 'ADMIN',
            name: 'Sistem Yöneticisi',
            description: 'Tüm sistem yetkilerine sahip yönetici rolü',
            permissions: SYSTEM_PERMISSIONS,
            userCount: 2,
            isSystemRole: true,
            isActive: true,
            createdAt: new Date('2024-01-01'),
            updatedAt: new Date('2024-07-16'),
            createdBy: 'System',
            notes: 'Sistem yöneticisi rolü - değiştirilemez'
        },
        {
            id: '2',
            roleCode: 'PM',
            name: '<PERSON><PERSON>',
            description: '<PERSON>je yönetimi ve takım koordinasyonu yetkilerine sahip rol',
            permissions: SYSTEM_PERMISSIONS.filter(p => 
                p.category === PermissionCategory.PROJECT_MANAGEMENT ||
                p.category === PermissionCategory.TEAM_MANAGEMENT ||
                p.category === PermissionCategory.REPORTING
            ),
            userCount: 5,
            isSystemRole: false,
            isActive: true,
            createdAt: new Date('2024-01-15'),
            updatedAt: new Date('2024-07-10'),
            createdBy: 'Admin',
            notes: 'Proje yöneticileri için özel rol'
        },
        {
            id: '3',
            roleCode: 'DEV',
            name: 'Geliştirici',
            description: 'Geliştirme süreçlerine katılım yetkilerine sahip rol',
            permissions: SYSTEM_PERMISSIONS.filter(p => 
                p.code === 'PROJECT_READ' ||
                p.code === 'PROJECT_UPDATE' ||
                p.code === 'TEAM_READ'
            ),
            userCount: 15,
            isSystemRole: false,
            isActive: true,
            createdAt: new Date('2024-01-20'),
            updatedAt: new Date('2024-06-15'),
            createdBy: 'Admin',
            notes: 'Geliştirici ekibi için standart rol'
        },
        {
            id: '4',
            roleCode: 'QA',
            name: 'Kalite Güvence',
            description: 'Test ve kalite süreçlerine katılım yetkilerine sahip rol',
            permissions: SYSTEM_PERMISSIONS.filter(p => 
                p.code === 'PROJECT_READ' ||
                p.code === 'REPORT_READ'
            ),
            userCount: 8,
            isSystemRole: false,
            isActive: true,
            createdAt: new Date('2024-02-01'),
            updatedAt: new Date('2024-05-20'),
            createdBy: 'Admin',
            notes: 'QA ekibi için özel rol'
        },
        {
            id: '5',
            roleCode: 'VIEWER',
            name: 'Görüntüleyici',
            description: 'Sadece görüntüleme yetkilerine sahip rol',
            permissions: SYSTEM_PERMISSIONS.filter(p => 
                p.action.toString() === 'Oku'
            ),
            userCount: 3,
            isSystemRole: false,
            isActive: true,
            createdAt: new Date('2024-03-01'),
            updatedAt: new Date('2024-04-10'),
            createdBy: 'Admin',
            notes: 'Sadece görüntüleme yetkisi olan kullanıcılar için'
        },
        {
            id: '6',
            roleCode: 'TEMP',
            name: 'Geçici Rol',
            description: 'Test amaçlı oluşturulmuş geçici rol',
            permissions: [],
            userCount: 0,
            isSystemRole: false,
            isActive: false,
            createdAt: new Date('2024-06-01'),
            updatedAt: new Date('2024-06-15'),
            createdBy: 'Admin',
            notes: 'Test amaçlı - silinebilir'
        }
    ];

    getRoles(): Observable<Role[]> {
        return of(this.mockRoles);
    }

    getRoleById(id: string): Observable<Role | undefined> {
        return of(this.mockRoles.find(role => role.id === id));
    }

    getPermissions(): Observable<Permission[]> {
        return of(SYSTEM_PERMISSIONS);
    }

    getPermissionsByCategory(): Observable<{ [key: string]: Permission[] }> {
        const grouped = SYSTEM_PERMISSIONS.reduce((acc, permission) => {
            const category = permission.category;
            if (!acc[category]) {
                acc[category] = [];
            }
            acc[category].push(permission);
            return acc;
        }, {} as { [key: string]: Permission[] });
        
        return of(grouped);
    }

    createRole(roleData: RoleFormData): Observable<Role> {
        const selectedPermissions = SYSTEM_PERMISSIONS.filter(p => 
            roleData.permissions.includes(p.id)
        );

        const newRole: Role = {
            id: Date.now().toString(),
            ...roleData,
            permissions: selectedPermissions,
            userCount: 0,
            isSystemRole: false,
            createdAt: new Date(),
            updatedAt: new Date(),
            createdBy: 'Current User'
        };
        
        this.mockRoles.push(newRole);
        return of(newRole);
    }

    updateRole(id: string, roleData: RoleFormData): Observable<Role | undefined> {
        const index = this.mockRoles.findIndex(role => role.id === id);
        if (index === -1) return of(undefined);

        const role = this.mockRoles[index];
        if (role.isSystemRole) {
            // Sistem rolleri güncellenemez
            return of(undefined);
        }

        const selectedPermissions = SYSTEM_PERMISSIONS.filter(p => 
            roleData.permissions.includes(p.id)
        );

        const updatedRole: Role = {
            ...role,
            ...roleData,
            permissions: selectedPermissions,
            updatedAt: new Date(),
            updatedBy: 'Current User'
        };
        
        this.mockRoles[index] = updatedRole;
        return of(updatedRole);
    }

    deleteRole(id: string): Observable<boolean> {
        const index = this.mockRoles.findIndex(role => role.id === id);
        if (index === -1) return of(false);

        const role = this.mockRoles[index];
        if (role.isSystemRole || role.userCount > 0) {
            // Sistem rolleri veya kullanıcısı olan roller silinemez
            return of(false);
        }
        
        this.mockRoles.splice(index, 1);
        return of(true);
    }

    toggleRoleStatus(id: string): Observable<boolean> {
        const role = this.mockRoles.find(r => r.id === id);
        if (!role || role.isSystemRole) return of(false);
        
        role.isActive = !role.isActive;
        role.updatedAt = new Date();
        role.updatedBy = 'Current User';
        return of(true);
    }

    cloneRole(id: string, newName: string): Observable<Role | undefined> {
        const originalRole = this.mockRoles.find(r => r.id === id);
        if (!originalRole) return of(undefined);

        const clonedRole: Role = {
            ...originalRole,
            id: Date.now().toString(),
            roleCode: `${originalRole.roleCode}_COPY`,
            name: newName,
            description: `${originalRole.description} (Kopya)`,
            userCount: 0,
            isSystemRole: false,
            createdAt: new Date(),
            updatedAt: new Date(),
            createdBy: 'Current User',
            notes: `${originalRole.name} rolünden kopyalandı`
        };

        this.mockRoles.push(clonedRole);
        return of(clonedRole);
    }
}

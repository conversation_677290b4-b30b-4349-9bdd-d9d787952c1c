﻿using TRtek_EYS.Domain.Enums;

namespace TRtek_EYS.Application.Features.Platforms.Queries.GetAll
{
    // V1 DTO
    public class PlatformGetAllDto
    {
        public Guid Id { get; set; }
        public string Type { get; set; } = null!;
        public Guid ProjectId { get; set; }
    }

    // V2 DTO - Genişletilmiş
    public class PlatformGetAllDtoV2
    {
        public Guid Id { get; set; }
        public PlatformType Type { get; set; }
        public Guid ProjectId { get; set; }

        // Yeni alanlar
        public string? Name { get; set; }
        public string? PlatformCode { get; set; }
        public string? Description { get; set; }
        public PlatformCategory? Category { get; set; }
        public string? Version { get; set; }
        public string? Vendor { get; set; }
        public string? Website { get; set; }
        public string? Documentation { get; set; }
        public SupportLevel? SupportLevel { get; set; }
        public LicenseType? LicenseType { get; set; }
        public decimal? Cost { get; set; }
        public string? Currency { get; set; }
        public bool IsActive { get; set; }
        public bool IsSupported { get; set; }
        public DateTime? ReleaseDate { get; set; }
        public DateTime? EndOfLifeDate { get; set; }
        public string[]? Tags { get; set; }
        public string? Notes { get; set; }
        public int ProjectCount { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}

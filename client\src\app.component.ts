import { Component } from '@angular/core';
import { RouterModule } from '@angular/router';
import { LoadingComponent } from './app/shared/components/loading/loading.component';
import { ToastModule } from 'primeng/toast';

@Component({
    selector: 'app-root',
    standalone: true,
    imports: [RouterModule, LoadingComponent, ToastModule],
    template: `
        <router-outlet></router-outlet>
        <app-loading></app-loading>
        <p-toast position="top-right"></p-toast>
    `
})
export class AppComponent {}

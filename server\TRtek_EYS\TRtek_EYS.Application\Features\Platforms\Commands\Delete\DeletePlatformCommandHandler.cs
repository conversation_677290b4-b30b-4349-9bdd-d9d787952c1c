﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Platforms.Commands.Delete
{
    public class DeletePlatformCommandHandler : IRequestHandler<DeletePlatformCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public DeletePlatformCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(DeletePlatformCommand request, CancellationToken cancellationToken)
        {
            var platform = await _context.Platforms
                .FirstOrDefaultAsync(p => p.Id == request.Id, cancellationToken);

            if (platform is null)
                throw new Exception("Silinecek platform bulunamadı.");

            _context.Platforms.Remove(platform);
            await _context.SaveChangesAsync(cancellationToken);

            return platform.Id;
        }
    }
}

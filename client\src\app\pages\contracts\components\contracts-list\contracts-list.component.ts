import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Table, TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { InputIconModule } from 'primeng/inputicon';
import { IconFieldModule } from 'primeng/iconfield';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToastModule } from 'primeng/toast';
import { ConfirmationService, MessageService } from 'primeng/api';
import { Contract, ContractStatus, LicenseType, SupportLevel } from '../../models/contract.model';

@Component({
    selector: 'app-contracts-list',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        TableModule,
        ButtonModule,
        InputTextModule,
        InputIconModule,
        IconFieldModule,
        TagModule,
        TooltipModule,
        ConfirmDialogModule,
        ToastModule
    ],
    providers: [ConfirmationService, MessageService],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <h5>Sözleşme Yönetimi</h5>
                        <p-button
                            icon="pi pi-plus"
                            (onClick)="createContract()"
                            severity="primary"
                            rounded
                            outlined
                            pTooltip="Yeni Sözleşme">
                        </p-button>
                    </div>

                    <p-table 
                        #dt 
                        [value]="contracts" 
                        [rows]="10" 
                        [paginator]="true"
                        [globalFilterFields]="['contractNumber', 'customerName', 'projectName', 'licenseType', 'status']"
                        [tableStyle]="{ 'min-width': '75rem' }"
                        [rowHover]="true"
                        dataKey="id"
                        currentPageReportTemplate="{first} - {last} / {totalRecords} sözleşme"
                        [showCurrentPageReport]="true">
                        
                        <ng-template pTemplate="caption">
                            <div class="flex align-items-center justify-content-between">
                                <h5 class="m-0">Sözleşmeler</h5>
                                <p-iconfield iconPosition="left">
                                    <p-inputicon>
                                        <i class="pi pi-search"></i>
                                    </p-inputicon>
                                    <input 
                                        pInputText 
                                        type="text" 
                                        (input)="dt.filterGlobal($any($event.target).value, 'contains')" 
                                        placeholder="Sözleşme ara..." />
                                </p-iconfield>
                            </div>
                        </ng-template>

                        <ng-template pTemplate="header">
                            <tr>
                                <th pSortableColumn="contractNumber">
                                    Sözleşme No <p-sortIcon field="contractNumber"></p-sortIcon>
                                </th>
                                <th pSortableColumn="customerName">
                                    Müşteri <p-sortIcon field="customerName"></p-sortIcon>
                                </th>
                                <th pSortableColumn="projectName">
                                    Proje <p-sortIcon field="projectName"></p-sortIcon>
                                </th>
                                <th pSortableColumn="licenseType">
                                    Lisans Türü <p-sortIcon field="licenseType"></p-sortIcon>
                                </th>
                                <th pSortableColumn="contractDate">
                                    Sözleşme Tarihi <p-sortIcon field="contractDate"></p-sortIcon>
                                </th>
                                <th pSortableColumn="endDate">
                                    Bitiş Tarihi <p-sortIcon field="endDate"></p-sortIcon>
                                </th>
                                <th pSortableColumn="contractValue">
                                    Değer <p-sortIcon field="contractValue"></p-sortIcon>
                                </th>
                                <th pSortableColumn="status">
                                    Durum <p-sortIcon field="status"></p-sortIcon>
                                </th>
                                <th>Destek</th>
                                <th>İşlemler</th>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="body" let-contract>
                            <tr>
                                <td>
                                    <span class="font-medium">{{ contract.contractNumber }}</span>
                                </td>
                                <td>
                                    <div>
                                        <span class="font-medium">{{ contract.customerName }}</span>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <span class="font-medium">{{ contract.projectName }}</span>
                                        <div class="text-sm text-500 mt-1">{{ contract.projectCode }}</div>
                                    </div>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="contract.licenseType" 
                                        [severity]="getLicenseTypeSeverity(contract.licenseType)">
                                    </p-tag>
                                </td>
                                <td>
                                    <span>{{ contract.contractDate | date:'dd/MM/yyyy' }}</span>
                                </td>
                                <td>
                                    <span *ngIf="contract.endDate">{{ contract.endDate | date:'dd/MM/yyyy' }}</span>
                                    <span *ngIf="!contract.endDate" class="text-500">-</span>
                                </td>
                                <td>
                                    <div class="text-right">
                                        <span class="font-medium">{{ contract.contractValue | currency:contract.currency:'symbol':'1.0-0' }}</span>
                                    </div>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="contract.status" 
                                        [severity]="getStatusSeverity(contract.status)">
                                    </p-tag>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="contract.supportLevel" 
                                        severity="info">
                                    </p-tag>
                                </td>
                                <td>
                                    <div class="flex gap-2">
                                        <p-button 
                                            icon="pi pi-eye" 
                                            class="p-button-rounded p-button-text p-button-info"
                                            pTooltip="Detay"
                                            (onClick)="viewContract(contract.id)">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-pencil" 
                                            class="p-button-rounded p-button-text p-button-warning"
                                            pTooltip="Düzenle"
                                            (onClick)="editContract(contract.id)">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-copy" 
                                            class="p-button-rounded p-button-text p-button-success"
                                            pTooltip="Yenile"
                                            (onClick)="renewContract(contract)">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-trash" 
                                            class="p-button-rounded p-button-text p-button-danger"
                                            pTooltip="Sil"
                                            (onClick)="deleteContract(contract)">
                                        </p-button>
                                    </div>
                                </td>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="emptymessage">
                            <tr>
                                <td colspan="10" class="text-center py-4">
                                    <i class="pi pi-info-circle text-4xl text-500 mb-3"></i>
                                    <div class="text-500 font-medium">Henüz sözleşme bulunmuyor</div>
                                    <div class="text-500">Yeni sözleşme eklemek için "Yeni Sözleşme" butonunu kullanın</div>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                </div>
            </div>
        </div>

        <p-confirmDialog></p-confirmDialog>
        <p-toast></p-toast>
    `
})
export class ContractsListComponent implements OnInit {
    @ViewChild('dt') table!: Table;
    
    contracts: Contract[] = [];

    constructor(
        private router: Router,
        private confirmationService: ConfirmationService,
        private messageService: MessageService
    ) {}

    ngOnInit() {
        this.loadContracts();
    }

    loadContracts() {
        this.contracts = [
            {
                id: '1',
                contractNumber: 'CNT-2025-001',
                customerId: '1',
                customerName: 'ABC Teknoloji A.Ş.',
                projectId: '1',
                projectName: 'E-Ticaret Platformu',
                projectCode: 'PRJ-2025-001',
                contractDate: new Date('2025-01-15'),
                startDate: new Date('2025-02-01'),
                endDate: new Date('2025-01-31'),
                licenseType: LicenseType.SUBSCRIPTION,
                contractValue: 500000,
                currency: 'TRY',
                status: ContractStatus.ACTIVE,
                renewalDate: new Date('2025-12-01'),
                isActive: true,
                paymentTerms: 'Yıllık' as any,
                supportLevel: SupportLevel.PREMIUM,
                maxUsers: 100,
                features: [],
                documents: [],
                notes: 'Yıllık abonelik sözleşmesi',
                createdBy: 'Admin',
                createdDate: new Date('2025-01-15')
            },
            {
                id: '2',
                contractNumber: 'CNT-2025-002',
                customerId: '2',
                customerName: 'XYZ Finans Ltd.',
                projectId: '2',
                projectName: 'CRM Sistemi',
                projectCode: 'PRJ-2025-002',
                contractDate: new Date('2025-02-01'),
                startDate: new Date('2025-02-15'),
                endDate: new Date('2027-02-14'),
                licenseType: LicenseType.PERPETUAL,
                contractValue: 750000,
                currency: 'TRY',
                status: ContractStatus.ACTIVE,
                isActive: true,
                paymentTerms: 'Tek Seferlik' as any,
                supportLevel: SupportLevel.ENTERPRISE,
                maxUsers: 50,
                features: [],
                documents: [],
                notes: 'Kalıcı lisans sözleşmesi',
                createdBy: 'Admin',
                createdDate: new Date('2025-02-01')
            },
            {
                id: '3',
                contractNumber: 'CNT-2025-003',
                customerId: '3',
                customerName: 'DEF Sağlık Hizmetleri',
                projectId: '3',
                projectName: 'Mobil Bankacılık',
                projectCode: 'PRJ-2025-003',
                contractDate: new Date('2025-01-05'),
                startDate: new Date('2025-01-15'),
                endDate: new Date('2025-07-14'),
                licenseType: LicenseType.TRIAL,
                contractValue: 0,
                currency: 'TRY',
                status: ContractStatus.EXPIRED,
                isActive: false,
                paymentTerms: 'Ücretsiz' as any,
                supportLevel: SupportLevel.BASIC,
                maxUsers: 10,
                features: [],
                documents: [],
                notes: 'Deneme sürümü sözleşmesi',
                createdBy: 'Admin',
                createdDate: new Date('2025-01-05')
            }
        ];
    }

    createContract() {
        this.router.navigate(['/pages/contracts/new']);
    }

    viewContract(id: string) {
        this.router.navigate(['/pages/contracts', id]);
    }

    editContract(id: string) {
        this.router.navigate(['/pages/contracts', id, 'edit']);
    }

    renewContract(contract: Contract) {
        this.confirmationService.confirm({
            message: `"${contract.contractNumber}" sözleşmesini yenilemek istediğinizden emin misiniz?`,
            header: 'Sözleşme Yenileme Onayı',
            icon: 'pi pi-question-circle',
            acceptLabel: 'Evet',
            rejectLabel: 'Hayır',
            accept: () => {
                this.messageService.add({
                    severity: 'success',
                    summary: 'Başarılı',
                    detail: 'Sözleşme yenileme işlemi başlatıldı'
                });
            }
        });
    }

    deleteContract(contract: Contract) {
        this.confirmationService.confirm({
            message: `"${contract.contractNumber}" sözleşmesini silmek istediğinizden emin misiniz?`,
            header: 'Sözleşme Silme Onayı',
            icon: 'pi pi-exclamation-triangle',
            acceptLabel: 'Evet',
            rejectLabel: 'Hayır',
            accept: () => {
                this.contracts = this.contracts.filter(c => c.id !== contract.id);
                this.messageService.add({
                    severity: 'success',
                    summary: 'Başarılı',
                    detail: 'Sözleşme başarıyla silindi'
                });
            }
        });
    }

    getStatusSeverity(status: ContractStatus): string {
        switch (status) {
            case ContractStatus.ACTIVE:
                return 'success';
            case ContractStatus.PENDING:
                return 'warning';
            case ContractStatus.EXPIRED:
                return 'danger';
            case ContractStatus.DRAFT:
                return 'info';
            case ContractStatus.CANCELLED:
                return 'secondary';
            case ContractStatus.RENEWED:
                return 'success';
            case ContractStatus.SUSPENDED:
                return 'warning';
            default:
                return 'info';
        }
    }

    getLicenseTypeSeverity(licenseType: LicenseType): string {
        switch (licenseType) {
            case LicenseType.PERPETUAL:
                return 'success';
            case LicenseType.SUBSCRIPTION:
                return 'info';
            case LicenseType.TRIAL:
                return 'warning';
            case LicenseType.ENTERPRISE:
                return 'success';
            case LicenseType.SAAS:
                return 'info';
            case LicenseType.CUSTOM:
                return 'secondary';
            default:
                return 'info';
        }
    }
}

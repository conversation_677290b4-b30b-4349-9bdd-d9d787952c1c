using System.Net;
using System.Text.Json;
using TRtek_EYS.Application.Common.Models;

namespace TRtek_EYS.WebAPI.Middlewares;

public class GlobalExceptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<GlobalExceptionMiddleware> _logger;

    public GlobalExceptionMiddleware(RequestDelegate next, ILogger<GlobalExceptionMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An unhandled exception occurred");
            await HandleExceptionAsync(context, ex);
        }
    }

    private static async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        context.Response.ContentType = "application/json";
        
        var response = new ApiResponse<object>();

        switch (exception)
        {
            case ArgumentException:
                response = ApiResponse<object>.ErrorResult("Geçersiz parametre", new List<string> { exception.Message });
                context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                break;
            
            case UnauthorizedAccessException:
                response = ApiResponse<object>.ErrorResult("Yetkisiz erişim", new List<string> { exception.Message });
                context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                break;
            
            case KeyNotFoundException:
                response = ApiResponse<object>.ErrorResult("Kayıt bulunamadı", new List<string> { exception.Message });
                context.Response.StatusCode = (int)HttpStatusCode.NotFound;
                break;
            
            default:
                response = ApiResponse<object>.ErrorResult("Sunucu hatası", new List<string> { "Beklenmeyen bir hata oluştu" });
                context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                break;
        }

        var jsonResponse = JsonSerializer.Serialize(response, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        await context.Response.WriteAsync(jsonResponse);
    }
}

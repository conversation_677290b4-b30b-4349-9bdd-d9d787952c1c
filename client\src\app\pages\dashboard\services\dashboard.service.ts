import { Injectable } from '@angular/core';
import { Observable, forkJoin, map } from 'rxjs';
import { UserService } from '../../users/services/user.service';
import { RoleService } from '../../roles/services/role.service';
import { PlatformService } from '../../platforms/services/platform.service';
import { TechnologyService } from '../../technologies/services/technology.service';
import { DocumentService } from '../../documents/services/document.service';
import { CustomerService } from '../../customers/services/customer.service';
import { ProjectService } from '../../projects/services/project.service';

export interface DashboardStats {
    totalUsers: number;
    activeUsers: number;
    totalRoles: number;
    activeRoles: number;
    totalPlatforms: number;
    activePlatforms: number;
    totalTechnologies: number;
    activeTechnologies: number;
    recommendedTechnologies: number;
    totalDocuments: number;
    publishedDocuments: number;
    draftDocuments: number;
    // Yeni alanlar
    totalCustomers: number;
    activeCustomers: number;
    vipCustomers: number;
    totalProjects: number;
    activeProjects: number;
    completedProjects: number;
    totalRevenue: number;
}

export interface RecentActivity {
    id: string;
    type: 'user' | 'role' | 'platform' | 'technology' | 'document';
    title: string;
    description: string;
    date: Date;
    icon: string;
    severity: 'success' | 'info' | 'warning' | 'danger';
}

export interface ChartData {
    labels: string[];
    datasets: any[];
}

export interface PlatformUsage {
    name: string;
    projectCount: number;
    percentage: number;
}

export interface TechnologyPopularity {
    name: string;
    popularityScore: number;
    isRecommended: boolean;
    category: string;
}

export interface DocumentStats {
    type: string;
    count: number;
    percentage: number;
}

@Injectable({
    providedIn: 'root'
})
export class DashboardService {

    constructor(
        private userService: UserService,
        private roleService: RoleService,
        private platformService: PlatformService,
        private technologyService: TechnologyService,
        private documentService: DocumentService,
        private customerService: CustomerService,
        private projectService: ProjectService
    ) {}

    getDashboardStats(): Observable<DashboardStats> {
        return forkJoin({
            users: this.userService.getUsers(),
            roles: this.roleService.getRoles(),
            platforms: this.platformService.getPlatformsV2(),
            technologies: this.technologyService.getTechnologiesV2(),
            documents: this.documentService.getDocuments(),
            customers: this.customerService.getCustomersV2(),
            projects: this.projectService.getProjectsV2()
        }).pipe(
            map(({ users, roles, platforms, technologies, documents, customers, projects }) => ({
                totalUsers: users.length,
                activeUsers: users.filter((u: any) => u.isActive).length,
                totalRoles: roles.length,
                activeRoles: roles.filter((r: any) => r.isActive).length,
                totalPlatforms: platforms.length,
                activePlatforms: platforms.filter((p: any) => p.isActive).length,
                totalTechnologies: technologies.length,
                activeTechnologies: technologies.filter((t: any) => t.isActive).length,
                recommendedTechnologies: technologies.filter((t: any) => t.isRecommended).length,
                totalDocuments: documents.length,
                publishedDocuments: documents.filter((d: any) => d.status === 'Yayınlandı').length,
                draftDocuments: documents.filter((d: any) => d.status === 'Taslak').length,
                // Yeni alanlar
                totalCustomers: customers.length,
                activeCustomers: customers.filter((c: any) => c.isActive).length,
                vipCustomers: customers.filter((c: any) => c.isVip).length,
                totalProjects: projects.length,
                activeProjects: projects.filter((p: any) => p.isActive).length,
                completedProjects: projects.filter((p: any) => p.status === 'COMPLETED').length,
                totalRevenue: customers.reduce((sum: number, c: any) => sum + (c.totalRevenue || 0), 0)
            }))
        );
    }

    getRecentActivities(): Observable<RecentActivity[]> {
        return forkJoin({
            users: this.userService.getUsers(),
            roles: this.roleService.getRoles(),
            platforms: this.platformService.getPlatforms(),
            technologies: this.technologyService.getTechnologies(),
            documents: this.documentService.getDocuments()
        }).pipe(
            map(({ users, roles, platforms, technologies, documents }) => {
                const activities: RecentActivity[] = [];

                // Son kullanıcılar
                users.slice(0, 2).forEach(user => {
                    activities.push({
                        id: `user-${user.id}`,
                        type: 'user',
                        title: 'Yeni Kullanıcı',
                        description: `${user.firstName} ${user.lastName} sisteme eklendi`,
                        date: user.createdAt,
                        icon: 'pi pi-user-plus',
                        severity: 'success'
                    });
                });

                // Son platformlar
                platforms.slice(0, 2).forEach(platform => {
                    activities.push({
                        id: `platform-${platform.id}`,
                        type: 'platform',
                        title: 'Platform Güncellendi',
                        description: `${platform.name} platformu güncellendi`,
                        date: platform.updatedAt,
                        icon: 'pi pi-server',
                        severity: 'info'
                    });
                });

                // Son teknolojiler
                technologies.slice(0, 2).forEach(tech => {
                    activities.push({
                        id: `tech-${tech.id}`,
                        type: 'technology',
                        title: tech.isRecommended ? 'Teknoloji Önerildi' : 'Teknoloji Eklendi',
                        description: `${tech.name} teknolojisi ${tech.isRecommended ? 'önerildi' : 'eklendi'}`,
                        date: tech.updatedAt,
                        icon: 'pi pi-code',
                        severity: tech.isRecommended ? 'success' : 'info'
                    });
                });

                // Son dokümanlar
                documents.slice(0, 2).forEach(doc => {
                    activities.push({
                        id: `doc-${doc.id}`,
                        type: 'document',
                        title: 'Doküman Yayınlandı',
                        description: `${doc.title} dokümanı yayınlandı`,
                        date: doc.publishedAt || doc.updatedAt,
                        icon: 'pi pi-file',
                        severity: doc.status === 'Yayınlandı' ? 'success' : 'warning'
                    });
                });

                // Tarihe göre sırala ve son 8'i al
                return activities
                    .sort((a, b) => b.date.getTime() - a.date.getTime())
                    .slice(0, 8);
            })
        );
    }

    getPlatformUsageChart(): Observable<ChartData> {
        return this.platformService.getPlatforms().pipe(
            map(platforms => {
                const sortedPlatforms = platforms
                    .filter(p => p.projectCount > 0)
                    .sort((a, b) => b.projectCount - a.projectCount)
                    .slice(0, 6);

                return {
                    labels: sortedPlatforms.map(p => p.name),
                    datasets: [{
                        data: sortedPlatforms.map(p => p.projectCount),
                        backgroundColor: [
                            '#FF6384',
                            '#36A2EB',
                            '#FFCE56',
                            '#4BC0C0',
                            '#9966FF',
                            '#FF9F40'
                        ],
                        hoverBackgroundColor: [
                            '#FF6384',
                            '#36A2EB',
                            '#FFCE56',
                            '#4BC0C0',
                            '#9966FF',
                            '#FF9F40'
                        ]
                    }]
                };
            })
        );
    }

    getTechnologyPopularityChart(): Observable<ChartData> {
        return this.technologyService.getTechnologies().pipe(
            map(technologies => {
                const sortedTechs = technologies
                    .filter(t => t.isActive)
                    .sort((a, b) => b.popularityScore - a.popularityScore)
                    .slice(0, 8);

                return {
                    labels: sortedTechs.map(t => t.name),
                    datasets: [{
                        label: 'Popülerlik Skoru',
                        data: sortedTechs.map(t => t.popularityScore),
                        backgroundColor: sortedTechs.map(t => 
                            t.isRecommended ? '#10B981' : '#3B82F6'
                        ),
                        borderColor: sortedTechs.map(t => 
                            t.isRecommended ? '#059669' : '#2563EB'
                        ),
                        borderWidth: 1
                    }]
                };
            })
        );
    }

    getDocumentTypeChart(): Observable<ChartData> {
        return this.documentService.getDocuments().pipe(
            map(documents => {
                const typeCount = documents.reduce((acc, doc) => {
                    acc[doc.type] = (acc[doc.type] || 0) + 1;
                    return acc;
                }, {} as Record<string, number>);

                const sortedTypes = Object.entries(typeCount)
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 6);

                return {
                    labels: sortedTypes.map(([type]) => type),
                    datasets: [{
                        data: sortedTypes.map(([, count]) => count),
                        backgroundColor: [
                            '#8B5CF6',
                            '#06B6D4',
                            '#10B981',
                            '#F59E0B',
                            '#EF4444',
                            '#6B7280'
                        ]
                    }]
                };
            })
        );
    }

    getUserStatusChart(): Observable<ChartData> {
        return this.userService.getUsers().pipe(
            map(users => {
                const statusCount = users.reduce((acc, user) => {
                    const status = user.isActive ? 'Aktif' : 'Pasif';
                    acc[status] = (acc[status] || 0) + 1;
                    return acc;
                }, {} as Record<string, number>);

                return {
                    labels: Object.keys(statusCount),
                    datasets: [{
                        data: Object.values(statusCount),
                        backgroundColor: ['#10B981', '#EF4444'],
                        hoverBackgroundColor: ['#059669', '#DC2626']
                    }]
                };
            })
        );
    }

    getTopPlatforms(): Observable<PlatformUsage[]> {
        return this.platformService.getPlatforms().pipe(
            map(platforms => {
                const totalProjects = platforms.reduce((sum, p) => sum + p.projectCount, 0);
                
                return platforms
                    .filter(p => p.projectCount > 0)
                    .map(p => ({
                        name: p.name,
                        projectCount: p.projectCount,
                        percentage: Math.round((p.projectCount / totalProjects) * 100)
                    }))
                    .sort((a, b) => b.projectCount - a.projectCount)
                    .slice(0, 5);
            })
        );
    }

    getTopTechnologies(): Observable<TechnologyPopularity[]> {
        return this.technologyService.getTechnologies().pipe(
            map(technologies => {
                return technologies
                    .filter(t => t.isActive)
                    .map(t => ({
                        name: t.name,
                        popularityScore: t.popularityScore,
                        isRecommended: t.isRecommended,
                        category: t.category
                    }))
                    .sort((a, b) => b.popularityScore - a.popularityScore)
                    .slice(0, 5);
            })
        );
    }

    getDocumentStatsByType(): Observable<DocumentStats[]> {
        return this.documentService.getDocuments().pipe(
            map(documents => {
                const typeCount = documents.reduce((acc, doc) => {
                    acc[doc.type] = (acc[doc.type] || 0) + 1;
                    return acc;
                }, {} as Record<string, number>);

                const total = documents.length;

                return Object.entries(typeCount)
                    .map(([type, count]) => ({
                        type,
                        count,
                        percentage: Math.round((count / total) * 100)
                    }))
                    .sort((a, b) => b.count - a.count)
                    .slice(0, 5);
            })
        );
    }
}

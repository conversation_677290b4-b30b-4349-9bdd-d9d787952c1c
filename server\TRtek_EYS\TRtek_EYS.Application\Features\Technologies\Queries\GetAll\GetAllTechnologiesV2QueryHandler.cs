using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;
using System.Text.Json;

namespace TRtek_EYS.Application.Features.Technologies.Queries.GetAll
{
    public class GetAllTechnologiesV2QueryHandler : IRequestHandler<GetAllTechnologiesV2Query, List<TechnologyGetAllDtoV2>>
    {
        private readonly IApplicationDbContext _context;
        private readonly IMapper _mapper;

        public GetAllTechnologiesV2QueryHandler(IApplicationDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        public async Task<List<TechnologyGetAllDtoV2>> Handle(GetAllTechnologiesV2Query request, CancellationToken cancellationToken)
        {
            var technologies = await _context.Technologies
                .AsNoTracking()
                .ToListAsync(cancellationToken);

            var result = technologies.Select(tech => new TechnologyGetAllDtoV2
            {
                Id = tech.Id,
                Language = tech.Language ?? string.Empty,
                Framework = tech.Framework,
                Version = tech.Version,
                // ProjectId kaldırıldı - artık Many-to-Many ilişki
                
                // Yeni alanlar
                Name = tech.Name,
                TechnologyCode = tech.TechnologyCode,
                Description = tech.Description,
                Type = tech.Type,
                Category = tech.Category,
                Vendor = tech.Vendor,
                Website = tech.Website,
                Documentation = tech.Documentation,
                Repository = tech.Repository,
                LicenseType = tech.LicenseType,
                IsActive = tech.IsActive,
                IsRecommended = tech.IsRecommended,
                IsDeprecated = tech.IsDeprecated,
                ReleaseDate = tech.ReleaseDate,
                LastUpdateDate = tech.LastUpdateDate,
                EndOfLifeDate = tech.EndOfLifeDate,
                Tags = ParseTags(tech.Tags),
                Notes = tech.Notes,
                ProjectCount = tech.ProjectCount,
                TeamCount = tech.TeamCount,
                PopularityScore = tech.PopularityScore,
                CreatedAt = tech.CreatedAt,
                UpdatedAt = tech.UpdatedAt
            }).ToList();

            return result;
        }

        private string[]? ParseTags(string? tagsJson)
        {
            if (string.IsNullOrEmpty(tagsJson))
                return null;

            try
            {
                return JsonSerializer.Deserialize<string[]>(tagsJson);
            }
            catch
            {
                return null;
            }
        }
    }
}

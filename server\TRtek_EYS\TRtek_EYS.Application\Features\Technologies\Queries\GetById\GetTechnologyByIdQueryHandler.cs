﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Technologies.Queries.GetById
{
    public class GetTechnologyByIdQueryHandler : IRequestHandler<GetTechnologyByIdQuery, TechnologyGetByIdDto>
    {
        private readonly IApplicationDbContext _context;

        public GetTechnologyByIdQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<TechnologyGetByIdDto> Handle(GetTechnologyByIdQuery request, CancellationToken cancellationToken)
        {
            var tech = await _context.Technologies
                .AsNoTracking()
                .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);

            if (tech is null)
                throw new Exception("Teknoloji bulunamadı.");

            return new TechnologyGetByIdDto
            {
                Id = tech.Id,
                Language = tech.Language ?? string.Empty,
                Framework = tech.Framework,
                Version = tech.Version,
                // ProjectId kaldırıldı - artık Many-to-Many ilişki
            };
        }
    }
}

import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';
import { TimelineModule } from 'primeng/timeline';
import { ButtonModule } from 'primeng/button';
import { TagModule } from 'primeng/tag';
import { SkeletonModule } from 'primeng/skeleton';
import { DocumentService } from '../../documents/services/document.service';

interface DocumentActivity {
    id: string;
    document: string;
    documentCode: string;
    type: string;
    user: string;
    date: Date;
    action: string;
    status: string;
}

@Component({
    standalone: true,
    selector: 'app-document-activity-widget',
    imports: [CommonModule, TimelineModule, ButtonModule, TagModule, SkeletonModule],
    template: `<div class="card">
        <div class="flex justify-between align-items-center mb-5">
            <div class="font-semibold text-xl"><PERSON> Aktiviteler<PERSON></div>
            <p-button
                label="Tümün<PERSON>"
                size="small"
                [outlined]="true"
                routerLink="/pages/documents">
            </p-button>
        </div>

        <div *ngIf="isLoading" class="space-y-4">
            <div *ngFor="let item of [1,2,3,4,5]" class="flex gap-3">
                <p-skeleton shape="circle" size="2rem"></p-skeleton>
                <div class="flex-1">
                    <p-skeleton width="60%" height="1rem" class="mb-2"></p-skeleton>
                    <p-skeleton width="40%" height="0.8rem" class="mb-2"></p-skeleton>
                    <p-skeleton width="30%" height="0.6rem"></p-skeleton>
                </div>
            </div>
        </div>

        <p-timeline
            *ngIf="!isLoading && activities.length > 0"
            [value]="activities"
            align="left"
            styleClass="customized-timeline">
            <ng-template pTemplate="marker" let-activity>
                <span class="flex w-2rem h-2rem align-items-center justify-content-center text-white border-circle z-1"
                      [style.background-color]="getActivityColor(activity.status)">
                    <i [class]="getActivityIcon(activity.action)"></i>
                </span>
            </ng-template>
            <ng-template pTemplate="content" let-activity>
                <div class="flex flex-column gap-2">
                    <div class="flex gap-2">
                        <span class="font-bold">{{ activity.document }}</span>
                        <p-tag [value]="activity.type" severity="info" [rounded]="true"></p-tag>
                    </div>
                    <div class="flex gap-2 text-sm">
                        <span class="text-muted-color">{{ activity.documentCode }}</span>
                        <p-tag [value]="activity.status" [severity]="getStatusSeverity(activity.status)" [rounded]="true"></p-tag>
                    </div>
                    <div class="flex gap-2 text-sm">
                        <span class="text-primary font-medium">{{ activity.user }}</span>
                        <small class="text-muted-color">{{ activity.date | date:'dd/MM/yyyy HH:mm' }}</small>
                    </div>
                </div>
            </ng-template>
        </p-timeline>

        <div *ngIf="!isLoading && activities.length === 0" class="text-center py-8">
            <i class="pi pi-info-circle text-4xl text-muted-color mb-4"></i>
            <div class="text-muted-color font-medium">Henüz doküman aktivitesi bulunmuyor</div>
        </div>
    </div>`,
    styles: [`
        .customized-timeline .p-timeline-event-content {
            line-height: 1;
        }
        .customized-timeline .p-timeline-event-marker {
            border: 2px solid var(--surface-ground);
        }
        .space-y-4 > * + * {
            margin-top: 1rem;
        }
    `]
})
export class DocumentActivityWidget implements OnInit, OnDestroy {
    private destroy$ = new Subject<void>();

    activities: DocumentActivity[] = [];
    isLoading = true;

    constructor(private documentService: DocumentService) {}

    ngOnInit() {
        this.loadRecentActivities();
    }

    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
    }

    loadRecentActivities() {
        this.isLoading = true;

        this.documentService.getDocuments()
            .pipe(takeUntil(this.destroy$))
            .subscribe(documents => {
                // Son 5 dokümanı aktivite olarak göster
                this.activities = documents
                    .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
                    .slice(0, 5)
                    .map(doc => ({
                        id: doc.id,
                        document: doc.title,
                        documentCode: doc.documentCode,
                        type: doc.type,
                        user: doc.authorName,
                        date: doc.updatedAt,
                        action: this.getActionFromStatus(doc.status),
                        status: doc.status
                    }));

                this.isLoading = false;
            });
    }

    getActionFromStatus(status: string): string {
        switch (status) {
            case 'Yayınlandı':
                return 'publish';
            case 'Taslak':
                return 'draft';
            case 'İnceleme':
                return 'review';
            default:
                return 'update';
        }
    }

    getActivityColor(status: string): string {
        switch (status) {
            case 'Yayınlandı':
                return '#10B981';
            case 'Taslak':
                return '#F59E0B';
            case 'İnceleme':
                return '#6366F1';
            case 'Arşivlendi':
                return '#64748B';
            default:
                return '#8B5CF6';
        }
    }

    getActivityIcon(action: string): string {
        switch (action) {
            case 'publish':
                return 'pi pi-check-circle';
            case 'draft':
                return 'pi pi-file-edit';
            case 'review':
                return 'pi pi-eye';
            case 'update':
                return 'pi pi-refresh';
            default:
                return 'pi pi-file';
        }
    }

    getStatusSeverity(status: string): string {
        switch (status) {
            case 'Yayınlandı':
                return 'success';
            case 'Taslak':
                return 'warning';
            case 'İnceleme':
                return 'info';
            case 'Arşivlendi':
                return 'secondary';
            default:
                return 'primary';
        }
    }
}

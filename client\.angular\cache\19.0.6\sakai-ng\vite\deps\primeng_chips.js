import {
  Chip,
  ChipModule
} from "./chunk-E7AREI7L.js";
import {
  InputTextModule
} from "./chunk-JS7WXATS.js";
import {
  AutoFocus,
  AutoFocusModule
} from "./chunk-3K2U6TC6.js";
import "./chunk-5G7WYC4N.js";
import {
  UniqueComponentId
} from "./chunk-5H5CNZCN.js";
import {
  TimesCircleIcon,
  TimesIcon
} from "./chunk-NJMPBOMR.js";
import {
  BaseComponent
} from "./chunk-HLLZRONR.js";
import {
  PrimeNG
} from "./chunk-3SHSWDEC.js";
import {
  BaseStyle
} from "./chunk-BCSD4NG6.js";
import {
  PrimeTemplate,
  SharedModule
} from "./chunk-RYQGQIW4.js";
import "./chunk-GFVF2TMO.js";
import {
  NG_VALUE_ACCESSOR
} from "./chunk-P73PIM3P.js";
import "./chunk-US7LRVFB.js";
import "./chunk-PXYLXCRT.js";
import {
  CommonModule,
  NgClass,
  NgForOf,
  Ng<PERSON>f,
  NgStyle,
  NgTemplateOutlet
} from "./chunk-UMAXZX7C.js";
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ContentChildren,
  ElementRef,
  EventEmitter,
  Injectable,
  Input,
  NgModule,
  Output,
  ViewChild,
  ViewEncapsulation,
  booleanAttribute,
  forwardRef,
  inject,
  numberAttribute,
  setClassMetadata,
  ɵɵInheritDefinitionFeature,
  ɵɵInputTransformsFeature,
  ɵɵProvidersFeature,
  ɵɵadvance,
  ɵɵattribute,
  ɵɵclassMap,
  ɵɵclassProp,
  ɵɵcontentQuery,
  ɵɵdefineComponent,
  ɵɵdefineInjectable,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject,
  ɵɵelementContainer,
  ɵɵelementContainerEnd,
  ɵɵelementContainerStart,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵgetInheritedFactory,
  ɵɵlistener,
  ɵɵloadQuery,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵpureFunction1,
  ɵɵpureFunction4,
  ɵɵqueryRefresh,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtemplateRefExtractor,
  ɵɵviewQuery
} from "./chunk-SAS3ZIMR.js";
import "./chunk-4N4GOYJH.js";
import "./chunk-5OPE3T2R.js";
import "./chunk-FHTVLBLO.js";
import "./chunk-WDMUDEB6.js";

// node_modules/primeng/fesm2022/primeng-chips.mjs
var _c0 = ["inputtext"];
var _c1 = ["container"];
var _c2 = (a0, a1, a2, a3) => ({
  "p-inputchips p-component p-input-wrapper": true,
  "p-disabled": a0,
  "p-focus": a1,
  "p-inputwrapper-filled": a2,
  "p-inputwrapper-focus": a3
});
var _c3 = (a0) => ({
  "p-chips-clearable": a0
});
var _c4 = (a0) => ({
  "p-inputchips-chip-item": true,
  "p-focus": a0
});
var _c5 = (a0) => ({
  $implicit: a0
});
var _c6 = (a0) => ({
  removeItem: a0
});
function Chips_li_3_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0);
  }
}
function Chips_li_3_p_chip_3_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0);
  }
}
function Chips_li_3_p_chip_3_ng_template_2_ng_container_0_TimesCircleIcon_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "TimesCircleIcon", 16);
    ɵɵlistener("click", function Chips_li_3_p_chip_3_ng_template_2_ng_container_0_TimesCircleIcon_1_Template_TimesCircleIcon_click_0_listener($event) {
      ɵɵrestoreView(_r7);
      const i_r6 = ɵɵnextContext(4).index;
      const ctx_r3 = ɵɵnextContext();
      return ɵɵresetView(ctx_r3.removeItem($event, i_r6));
    });
    ɵɵelementEnd();
  }
  if (rf & 2) {
    ɵɵproperty("styleClass", "p-chips-token-icon");
    ɵɵattribute("data-pc-section", "removeTokenIcon")("aria-hidden", true);
  }
}
function Chips_li_3_p_chip_3_ng_template_2_ng_container_0_span_2_1_ng_template_0_Template(rf, ctx) {
}
function Chips_li_3_p_chip_3_ng_template_2_ng_container_0_span_2_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, Chips_li_3_p_chip_3_ng_template_2_ng_container_0_span_2_1_ng_template_0_Template, 0, 0, "ng-template");
  }
}
function Chips_li_3_p_chip_3_ng_template_2_ng_container_0_span_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "span", 17);
    ɵɵlistener("click", function Chips_li_3_p_chip_3_ng_template_2_ng_container_0_span_2_Template_span_click_0_listener($event) {
      ɵɵrestoreView(_r8);
      const i_r6 = ɵɵnextContext(4).index;
      const ctx_r3 = ɵɵnextContext();
      return ɵɵresetView(ctx_r3.removeItem($event, i_r6));
    });
    ɵɵtemplate(1, Chips_li_3_p_chip_3_ng_template_2_ng_container_0_span_2_1_Template, 1, 0, null, 11);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r3 = ɵɵnextContext(5);
    ɵɵattribute("data-pc-section", "removeTokenIcon")("aria-hidden", true);
    ɵɵadvance();
    ɵɵproperty("ngTemplateOutlet", ctx_r3.removeTokenIconTemplate)("ngTemplateOutletContext", ɵɵpureFunction1(4, _c6, ctx_r3.removeItem.bind(ctx_r3)));
  }
}
function Chips_li_3_p_chip_3_ng_template_2_ng_container_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtemplate(1, Chips_li_3_p_chip_3_ng_template_2_ng_container_0_TimesCircleIcon_1_Template, 1, 3, "TimesCircleIcon", 14)(2, Chips_li_3_p_chip_3_ng_template_2_ng_container_0_span_2_Template, 2, 6, "span", 15);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r3 = ɵɵnextContext(4);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r3.removeTokenIconTemplate);
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r3.removeTokenIconTemplate);
  }
}
function Chips_li_3_p_chip_3_ng_template_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, Chips_li_3_p_chip_3_ng_template_2_ng_container_0_Template, 3, 2, "ng-container", 9);
  }
  if (rf & 2) {
    const ctx_r3 = ɵɵnextContext(3);
    ɵɵproperty("ngIf", !ctx_r3.disabled);
  }
}
function Chips_li_3_p_chip_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "p-chip", 13);
    ɵɵlistener("onRemove", function Chips_li_3_p_chip_3_Template_p_chip_onRemove_0_listener($event) {
      ɵɵrestoreView(_r5);
      const i_r6 = ɵɵnextContext().index;
      const ctx_r3 = ɵɵnextContext();
      return ɵɵresetView(ctx_r3.removeItem($event, i_r6));
    });
    ɵɵtemplate(1, Chips_li_3_p_chip_3_ng_container_1_Template, 1, 0, "ng-container", 11)(2, Chips_li_3_p_chip_3_ng_template_2_Template, 1, 1, "ng-template", null, 3, ɵɵtemplateRefExtractor);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const item_r3 = ɵɵnextContext().$implicit;
    const ctx_r3 = ɵɵnextContext();
    ɵɵproperty("label", ctx_r3.field ? ctx_r3.resolveFieldData(item_r3, ctx_r3.field) : item_r3)("removeIcon", ctx_r3.chipIcon);
    ɵɵadvance();
    ɵɵproperty("ngTemplateOutlet", ctx_r3.itemTemplate)("ngTemplateOutletContext", ɵɵpureFunction1(4, _c5, item_r3));
  }
}
function Chips_li_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "li", 10, 2);
    ɵɵlistener("click", function Chips_li_3_Template_li_click_0_listener($event) {
      const item_r3 = ɵɵrestoreView(_r2).$implicit;
      const ctx_r3 = ɵɵnextContext();
      return ɵɵresetView(ctx_r3.onItemClick($event, item_r3));
    })("contextmenu", function Chips_li_3_Template_li_contextmenu_0_listener($event) {
      const item_r3 = ɵɵrestoreView(_r2).$implicit;
      const ctx_r3 = ɵɵnextContext();
      return ɵɵresetView(ctx_r3.onItemContextMenu($event, item_r3));
    });
    ɵɵtemplate(2, Chips_li_3_ng_container_2_Template, 1, 0, "ng-container", 11)(3, Chips_li_3_p_chip_3_Template, 4, 6, "p-chip", 12);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const item_r3 = ctx.$implicit;
    const i_r6 = ctx.index;
    const ctx_r3 = ɵɵnextContext();
    ɵɵproperty("ngClass", ɵɵpureFunction1(11, _c4, ctx_r3.focusedIndex === i_r6));
    ɵɵattribute("id", ctx_r3.id + "_chips_item_" + i_r6)("ariaLabel", item_r3)("aria-selected", true)("aria-setsize", ctx_r3.value.length)("aria-posinset", i_r6 + 1)("data-p-focused", ctx_r3.focusedIndex === i_r6)("data-pc-section", "token");
    ɵɵadvance(2);
    ɵɵproperty("ngTemplateOutlet", ctx_r3.itemTemplate)("ngTemplateOutletContext", ɵɵpureFunction1(13, _c5, item_r3));
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r3.itemTemplate);
  }
}
function Chips_li_7_TimesIcon_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r9 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "TimesIcon", 16);
    ɵɵlistener("click", function Chips_li_7_TimesIcon_1_Template_TimesIcon_click_0_listener() {
      ɵɵrestoreView(_r9);
      const ctx_r3 = ɵɵnextContext(2);
      return ɵɵresetView(ctx_r3.clear());
    });
    ɵɵelementEnd();
  }
  if (rf & 2) {
    ɵɵproperty("styleClass", "p-chips-clear-icon");
  }
}
function Chips_li_7_span_2_1_ng_template_0_Template(rf, ctx) {
}
function Chips_li_7_span_2_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, Chips_li_7_span_2_1_ng_template_0_Template, 0, 0, "ng-template");
  }
}
function Chips_li_7_span_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r10 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "span", 19);
    ɵɵlistener("click", function Chips_li_7_span_2_Template_span_click_0_listener() {
      ɵɵrestoreView(_r10);
      const ctx_r3 = ɵɵnextContext(2);
      return ɵɵresetView(ctx_r3.clear());
    });
    ɵɵtemplate(1, Chips_li_7_span_2_1_Template, 1, 0, null, 20);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r3 = ɵɵnextContext(2);
    ɵɵadvance();
    ɵɵproperty("ngTemplateOutlet", ctx_r3.clearIconTemplate);
  }
}
function Chips_li_7_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "li");
    ɵɵtemplate(1, Chips_li_7_TimesIcon_1_Template, 1, 1, "TimesIcon", 14)(2, Chips_li_7_span_2_Template, 2, 1, "span", 18);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r3 = ɵɵnextContext();
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r3.clearIconTemplate);
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r3.clearIconTemplate);
  }
}
var theme = ({
  dt
}) => `
.p-inputchips {
    display: inline-flex;
}

.p-inputchips-input {
    margin: 0;
    list-style-type: none;
    cursor: text;
    overflow: hidden;
    display: flex;
    position: relative;
    align-items: center;
    flex-wrap: wrap;
    padding: calc(${dt("inputchips.padding.y")} / 2) ${dt("inputchips.padding.x")};
    gap: calc(${dt("inputchips.padding.y")} / 2);
    color: ${dt("inputchips.color")};
    background: ${dt("inputchips.background")};
    border: 1px solid ${dt("inputchips.border.color")};
    border-radius: ${dt("inputchips.border.radius")};
    width: 100%;
    transition: background ${dt("inputchips.transition.duration")}, color ${dt("inputchips.transition.duration")}, border-color ${dt("inputchips.transition.duration")}, outline-color ${dt("inputchips.transition.duration")}, box-shadow ${dt("inputchips.transition.duration")};
    outline-color: transparent;
    box-shadow: ${dt("inputchips.shadow")};
}

.p-inputchips:not(.p-disabled):hover .p-inputchips-input {
    border-color: ${dt("inputchips.hover.border.color")};
}

.p-inputchips:not(.p-disabled).p-focus .p-inputchips-input {
    border-color: ${dt("inputchips.focus.border.color")};
    box-shadow: ${dt("inputchips.focus.ring.shadow")};
    outline: ${dt("inputchips.focus.ring.width")} ${dt("inputchips.focus.ring.style")} ${dt("inputchips.focus.ring.color")};
    outline-offset: ${dt("inputchips.focus.ring.offset")};
}

.p-inputchips.p-invalid .p-inputchips-input {
    border-color: ${dt("inputchips.invalid.border.color")};
}

.p-variant-filled.p-inputchips-input {
    background: ${dt("inputchips.filled.background")};
}

.p-inputchips:not(.p-disabled).p-focus .p-variant-filled.p-inputchips-input  {
    background: ${dt("inputchips.filled.focus.background")};
}

.p-inputchips.p-disabled .p-inputchips-input {
    opacity: 1;
    background: ${dt("inputchips.disabled.background")};
    color: ${dt("inputchips.disabled.color")};
}

.p-inputchips-chip.p-chip {
    padding-top: calc(${dt("inputchips.padding.y")} / 2);
    padding-bottom: calc(${dt("inputchips.padding.y")} / 2);
    border-radius: ${dt("inputchips.chip.border.radius")};
    transition: background ${dt("inputchips.transition.duration")}, color ${dt("inputchips.transition.duration")};
}

.p-inputchips-chip-item.p-focus .p-inputchips-chip {
    background: ${dt("inputchips.chip.focus.background")};
    color: ${dt("inputchips.chip.focus.color")};
}

.p-inputchips-input:has(.p-inputchips-chip) {
    padding-left: calc(${dt("inputchips.padding.y")} / 2);
    padding-right: calc(${dt("inputchips.padding.y")} / 2);
}

.p-inputchips-input-item {
    flex: 1 1 auto;
    display: inline-flex;
    padding-top: calc(${dt("inputchips.padding.y")} / 2);
    padding-bottom: calc(${dt("inputchips.padding.y")} / 2);
}

.p-inputchips-input-item input {
    border: 0 none;
    outline: 0 none;
    background: transparent;
    margin: 0;
    padding: 0;
    box-shadow: none;
    border-radius: 0;
    width: 100%;
    font-family: inherit;
    font-feature-settings: inherit;
    font-size: 1rem;
    color: inherit;
}

.p-inputchips-input-item input::placeholder {
    color: ${dt("inputchips.placeholder.color")};
}

/* For PrimeNG */

.p-chips-clear-icon {
    position: absolute;
    top: 50%;
    margin-top: -0.5rem;
    cursor: pointer;
    right: ${dt("inputchips.padding.x")};
}
`;
var classes = {
  root: ({
    instance,
    props
  }) => ["p-inputchips p-component p-inputwrapper", {
    "p-disabled": props.disabled,
    "p-invalid": props.invalid,
    "p-focus": instance.focused,
    "p-inputwrapper-filled": props.modelValue && props.modelValue.length || instance.inputValue && instance.inputValue.length,
    "p-inputwrapper-focus": instance.focused
  }],
  input: ({
    props,
    instance
  }) => ["p-inputchips-input", {
    "p-variant-filled": props.variant ? props.variant === "filled" : instance.config.inputStyle === "filled" || instance.config.inputVariant === "filled"
  }],
  chipItem: ({
    state,
    index
  }) => ["p-inputchips-chip-item", {
    "p-focus": state.focusedIndex === index
  }],
  pcChip: "p-inputchips-chip",
  chipIcon: "p-inputchips-chip-icon",
  inputItem: "p-inputchips-input-item"
};
var ChipsStyle = class _ChipsStyle extends BaseStyle {
  name = "inputchips";
  theme = theme;
  classes = classes;
  static ɵfac = /* @__PURE__ */ (() => {
    let ɵChipsStyle_BaseFactory;
    return function ChipsStyle_Factory(__ngFactoryType__) {
      return (ɵChipsStyle_BaseFactory || (ɵChipsStyle_BaseFactory = ɵɵgetInheritedFactory(_ChipsStyle)))(__ngFactoryType__ || _ChipsStyle);
    };
  })();
  static ɵprov = ɵɵdefineInjectable({
    token: _ChipsStyle,
    factory: _ChipsStyle.ɵfac
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ChipsStyle, [{
    type: Injectable
  }], null, null);
})();
var ChipsClasses;
(function(ChipsClasses2) {
  ChipsClasses2["root"] = "p-chip";
  ChipsClasses2["image"] = "p-chip-image";
  ChipsClasses2["icon"] = "p-chip-icon";
  ChipsClasses2["label"] = "p-chip-label";
  ChipsClasses2["removeIcon"] = "p-chip-remove-icon";
})(ChipsClasses || (ChipsClasses = {}));
var CHIPS_VALUE_ACCESSOR = {
  provide: NG_VALUE_ACCESSOR,
  useExisting: forwardRef(() => Chips),
  multi: true
};
var Chips = class _Chips extends BaseComponent {
  el;
  cd;
  config;
  /**
   * Inline style of the element.
   * @group Props
   */
  style;
  /**
   * Style class of the element.
   * @group Props
   */
  styleClass;
  /**
   * When present, it specifies that the element should be disabled.
   * @group Props
   */
  disabled;
  /**
   * Name of the property to display on a chip.
   * @group Props
   */
  field;
  /**
   * Advisory information to display on input.
   * @group Props
   */
  placeholder;
  /**
   * Maximum number of entries allowed.
   * @group Props
   */
  max;
  /**
   * Maximum length of a chip.
   * @group Props
   */
  maxLength;
  /**
   * Defines a string that labels the input for accessibility.
   * @group Props
   */
  ariaLabel;
  /**
   * Establishes relationships between the component and label(s) where its value should be one or more element IDs.
   * @group Props
   */
  ariaLabelledBy;
  /**
   * Index of the element in tabbing order.
   * @group Props
   */
  tabindex;
  /**
   * Identifier of the focus input to match a label defined for the component.
   * @group Props
   */
  inputId;
  /**
   * Whether to allow duplicate values or not.
   * @group Props
   */
  allowDuplicate = true;
  /**
   * Defines whether duplication check should be case-sensitive
   * @group Props
   */
  caseSensitiveDuplication = true;
  /**
   * Inline style of the input field.
   * @group Props
   */
  inputStyle;
  /**
   * Style class of the input field.
   * @group Props
   */
  inputStyleClass;
  /**
   * Icon to display in chip remove action..
   * @group Props
   */
  chipIcon;
  /**
   * Whether to add an item on tab key press.
   * @group Props
   */
  addOnTab;
  /**
   * Whether to add an item when the input loses focus.
   * @group Props
   */
  addOnBlur;
  /**
   * Separator char to add an item when pressed in addition to the enter key.
   * @group Props
   */
  separator;
  /**
   * When enabled, a clear icon is displayed to clear the value.
   * @group Props
   */
  showClear = false;
  /**
   * When present, it specifies that the component should automatically get focus on load.
   * @group Props
   */
  autofocus;
  /**
   * Specifies the input variant of the component.
   * @group Props
   */
  variant = "outlined";
  /**
   * Callback to invoke on chip add.
   * @param {ChipsAddEvent} event - Custom chip add event.
   * @group Emits
   */
  onAdd = new EventEmitter();
  /**
   * Callback to invoke on chip remove.
   * @param {ChipsRemoveEvent} event - Custom chip remove event.
   * @group Emits
   */
  onRemove = new EventEmitter();
  /**
   * Callback to invoke on focus of input field.
   * @param {Event} event - Browser event.
   * @group Emits
   */
  onFocus = new EventEmitter();
  /**
   * Callback to invoke on blur of input field.
   * @param {Event} event - Browser event.
   * @group Emits
   */
  onBlur = new EventEmitter();
  /**
   * Callback to invoke on chip clicked.
   * @param {ChipsClickEvent} event - Custom chip click event.
   * @group Emits
   */
  onChipClick = new EventEmitter();
  /**
   * Callback to invoke on chip contextmenu.
   * @param {ChipsClickEvent} event - Custom chip contextmenu event.
   * @group Emits
   */
  onChipContextMenu = new EventEmitter();
  /**
   * Callback to invoke on clear token clicked.
   * @group Emits
   */
  onClear = new EventEmitter();
  inputViewChild;
  containerViewChild;
  templates;
  itemTemplate;
  removeTokenIconTemplate;
  clearIconTemplate;
  value;
  onModelChange = () => {
  };
  onModelTouched = () => {
  };
  valueChanged;
  id = UniqueComponentId();
  focused;
  focusedIndex;
  filled;
  _componentStyle = inject(ChipsStyle);
  get focusedOptionId() {
    return this.focusedIndex !== null ? `${this.id}_chips_item_${this.focusedIndex}` : null;
  }
  get isMaxedOut() {
    return this.max && this.value && this.max === this.value.length;
  }
  constructor(el, cd, config) {
    super();
    this.el = el;
    this.cd = cd;
    this.config = config;
    console.log("Deprecated since v18. Use AutoComplete component instead with its typeahead property.");
  }
  ngAfterContentInit() {
    this.templates.forEach((item) => {
      switch (item.getType()) {
        case "item":
          this.itemTemplate = item.template;
          break;
        case "removetokenicon":
          this.removeTokenIconTemplate = item.template;
          break;
        case "clearicon":
          this.clearIconTemplate = item.template;
          break;
        default:
          this.itemTemplate = item.template;
          break;
      }
    });
    this.updateFilledState();
  }
  onWrapperClick() {
    this.inputViewChild?.nativeElement.focus();
  }
  onContainerFocus() {
    this.focused = true;
  }
  onContainerBlur() {
    this.focusedIndex = -1;
    this.focused = false;
  }
  onContainerKeyDown(event) {
    switch (event.code) {
      case "ArrowLeft":
        this.onArrowLeftKeyOn();
        break;
      case "ArrowRight":
        this.onArrowRightKeyOn();
        break;
      case "Backspace":
        this.onBackspaceKeyOn(event);
        break;
      case "Space":
        if (this.focusedIndex !== null && this.value && this.value.length > 0) {
          this.onItemClick(event, this.value[this.focusedIndex]);
        }
        break;
      default:
        break;
    }
  }
  onArrowLeftKeyOn() {
    if (this.inputViewChild.nativeElement.value.length === 0 && this.value && this.value.length > 0) {
      this.focusedIndex = this.focusedIndex === null ? this.value.length - 1 : this.focusedIndex - 1;
      if (this.focusedIndex < 0) this.focusedIndex = 0;
    }
  }
  onArrowRightKeyOn() {
    if (this.inputViewChild.nativeElement.value.length === 0 && this.value && this.value.length > 0) {
      if (this.focusedIndex === this.value.length - 1) {
        this.focusedIndex = null;
        this.inputViewChild?.nativeElement.focus();
      } else {
        this.focusedIndex++;
      }
    }
  }
  onBackspaceKeyOn(event) {
    if (this.focusedIndex !== null) {
      this.removeItem(event, this.focusedIndex);
    }
  }
  onInput() {
    this.updateFilledState();
    this.focusedIndex = null;
  }
  onPaste(event) {
    if (!this.disabled) {
      if (this.separator) {
        const pastedData = (event.clipboardData || this.document.defaultView["clipboardData"]).getData("Text");
        pastedData.split(this.separator).forEach((val) => {
          this.addItem(event, val, true);
        });
        this.inputViewChild.nativeElement.value = "";
      }
      this.updateFilledState();
    }
  }
  updateFilledState() {
    if (!this.value || this.value.length === 0) {
      this.filled = this.inputViewChild && this.inputViewChild.nativeElement && this.inputViewChild.nativeElement.value != "";
    } else {
      this.filled = true;
    }
  }
  onItemClick(event, item) {
    this.onChipClick.emit({
      originalEvent: event,
      value: item
    });
  }
  onItemContextMenu(event, item) {
    this.onChipContextMenu.emit({
      originalEvent: event,
      value: item
    });
  }
  writeValue(value) {
    this.value = value;
    this.updateMaxedOut();
    this.updateFilledState();
    this.cd.markForCheck();
  }
  registerOnChange(fn) {
    this.onModelChange = fn;
  }
  registerOnTouched(fn) {
    this.onModelTouched = fn;
  }
  setDisabledState(val) {
    this.disabled = val;
    this.cd.markForCheck();
  }
  resolveFieldData(data, field) {
    if (data && field) {
      if (field.indexOf(".") == -1) {
        return data[field];
      } else {
        let fields = field.split(".");
        let value = data;
        for (var i = 0, len = fields.length; i < len; ++i) {
          value = value[fields[i]];
        }
        return value;
      }
    } else {
      return null;
    }
  }
  onInputFocus(event) {
    this.focused = true;
    this.focusedIndex = null;
    this.onFocus.emit(event);
  }
  onInputBlur(event) {
    this.focused = false;
    this.focusedIndex = null;
    if (this.addOnBlur && this.inputViewChild.nativeElement.value) {
      this.addItem(event, this.inputViewChild.nativeElement.value, false);
    }
    this.onModelTouched();
    this.onBlur.emit(event);
  }
  removeItem(event, index) {
    if (this.disabled) {
      return;
    }
    let removedItem = this.value[index];
    this.value = this.value.filter((val, i) => i != index);
    this.focusedIndex = null;
    this.inputViewChild.nativeElement.focus();
    this.onModelChange(this.value);
    this.onRemove.emit({
      originalEvent: event,
      value: removedItem
    });
    this.updateFilledState();
    this.updateMaxedOut();
  }
  addItem(event, item, preventDefault) {
    this.value = this.value || [];
    if (item && item.trim().length) {
      const newItemIsDuplicate = this.caseSensitiveDuplication ? this.value.includes(item) : this.value.some((val) => val.toLowerCase() === item.toLowerCase());
      if ((this.allowDuplicate || !newItemIsDuplicate) && !this.isMaxedOut) {
        this.value = [...this.value, item];
        this.onModelChange(this.value);
        this.onAdd.emit({
          originalEvent: event,
          value: item
        });
      }
    }
    this.updateFilledState();
    this.updateMaxedOut();
    this.inputViewChild.nativeElement.value = "";
    if (preventDefault) {
      event.preventDefault();
    }
  }
  /**
   * Callback to invoke on filter reset.
   * @group Method
   */
  clear() {
    this.value = null;
    this.updateFilledState();
    this.onModelChange(this.value);
    this.updateMaxedOut();
    this.onClear.emit();
  }
  onKeyDown(event) {
    const inputValue = event.target.value;
    switch (event.code) {
      case "Backspace":
        if (inputValue.length === 0 && this.value && this.value.length > 0) {
          if (this.focusedIndex !== null) {
            this.removeItem(event, this.focusedIndex);
          } else this.removeItem(event, this.value.length - 1);
        }
        break;
      case "Enter":
      case "NumpadEnter":
        if (inputValue && inputValue.trim().length && !this.isMaxedOut) {
          this.addItem(event, inputValue, true);
        }
        break;
      case "Tab":
        if (this.addOnTab && inputValue && inputValue.trim().length && !this.isMaxedOut) {
          this.addItem(event, inputValue, true);
          event.preventDefault();
        }
        break;
      case "ArrowLeft":
        if (inputValue.length === 0 && this.value && this.value.length > 0) {
          this.containerViewChild?.nativeElement.focus();
        }
        break;
      case "ArrowRight":
        event.stopPropagation();
        break;
      default:
        if (this.separator) {
          if (this.separator === event.key || event.key.match(this.separator)) {
            this.addItem(event, inputValue, true);
          }
        }
        break;
    }
  }
  updateMaxedOut() {
    if (this.inputViewChild && this.inputViewChild.nativeElement) {
      if (this.isMaxedOut) {
        this.inputViewChild.nativeElement.blur();
        this.inputViewChild.nativeElement.disabled = true;
      } else {
        if (this.disabled) {
          this.inputViewChild.nativeElement.blur();
        }
        this.inputViewChild.nativeElement.disabled = this.disabled || false;
      }
    }
  }
  static ɵfac = function Chips_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _Chips)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(PrimeNG));
  };
  static ɵcmp = ɵɵdefineComponent({
    type: _Chips,
    selectors: [["p-chips"]],
    contentQueries: function Chips_ContentQueries(rf, ctx, dirIndex) {
      if (rf & 1) {
        ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);
      }
      if (rf & 2) {
        let _t;
        ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.templates = _t);
      }
    },
    viewQuery: function Chips_Query(rf, ctx) {
      if (rf & 1) {
        ɵɵviewQuery(_c0, 5);
        ɵɵviewQuery(_c1, 5);
      }
      if (rf & 2) {
        let _t;
        ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.inputViewChild = _t.first);
        ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);
      }
    },
    hostAttrs: [1, "p-element", "p-inputwrapper"],
    hostVars: 6,
    hostBindings: function Chips_HostBindings(rf, ctx) {
      if (rf & 2) {
        ɵɵclassProp("p-inputwrapper-filled", ctx.filled)("p-inputwrapper-focus", ctx.focused)("p-chips-clearable", ctx.showClear);
      }
    },
    inputs: {
      style: "style",
      styleClass: "styleClass",
      disabled: [2, "disabled", "disabled", booleanAttribute],
      field: "field",
      placeholder: "placeholder",
      max: [2, "max", "max", numberAttribute],
      maxLength: "maxLength",
      ariaLabel: "ariaLabel",
      ariaLabelledBy: "ariaLabelledBy",
      tabindex: [2, "tabindex", "tabindex", numberAttribute],
      inputId: "inputId",
      allowDuplicate: [2, "allowDuplicate", "allowDuplicate", booleanAttribute],
      caseSensitiveDuplication: [2, "caseSensitiveDuplication", "caseSensitiveDuplication", booleanAttribute],
      inputStyle: "inputStyle",
      inputStyleClass: "inputStyleClass",
      chipIcon: "chipIcon",
      addOnTab: [2, "addOnTab", "addOnTab", booleanAttribute],
      addOnBlur: [2, "addOnBlur", "addOnBlur", booleanAttribute],
      separator: "separator",
      showClear: [2, "showClear", "showClear", booleanAttribute],
      autofocus: [2, "autofocus", "autofocus", booleanAttribute],
      variant: "variant"
    },
    outputs: {
      onAdd: "onAdd",
      onRemove: "onRemove",
      onFocus: "onFocus",
      onBlur: "onBlur",
      onChipClick: "onChipClick",
      onChipContextMenu: "onChipContextMenu",
      onClear: "onClear"
    },
    features: [ɵɵProvidersFeature([CHIPS_VALUE_ACCESSOR, ChipsStyle]), ɵɵInputTransformsFeature, ɵɵInheritDefinitionFeature],
    decls: 8,
    vars: 31,
    consts: [["container", ""], ["inputtext", ""], ["token", ""], ["removeicon", ""], [3, "ngClass", "ngStyle"], ["tabindex", "-1", "role", "listbox", 1, "p-inputchips-input", 3, "click", "focus", "blur", "keydown"], ["role", "option", 3, "ngClass", "click", "contextmenu", 4, "ngFor", "ngForOf"], ["role", "option", 1, "p-inputchips-input-item", 3, "ngClass"], ["type", "text", "pAutoFocus", "", 1, "test", 3, "keydown", "input", "paste", "focus", "blur", "disabled", "ngStyle", "autofocus"], [4, "ngIf"], ["role", "option", 3, "click", "contextmenu", "ngClass"], [4, "ngTemplateOutlet", "ngTemplateOutletContext"], ["class", "p-inputchips-chip", "removable", "", 3, "label", "removeIcon", "onRemove", 4, "ngIf"], ["removable", "", 1, "p-inputchips-chip", 3, "onRemove", "label", "removeIcon"], [3, "styleClass", "click", 4, "ngIf"], ["class", "p-chips-token-icon", 3, "click", 4, "ngIf"], [3, "click", "styleClass"], [1, "p-chips-token-icon", 3, "click"], ["class", "p-chips-clear-icon", 3, "click", 4, "ngIf"], [1, "p-chips-clear-icon", 3, "click"], [4, "ngTemplateOutlet"]],
    template: function Chips_Template(rf, ctx) {
      if (rf & 1) {
        const _r1 = ɵɵgetCurrentView();
        ɵɵelementStart(0, "div", 4)(1, "ul", 5, 0);
        ɵɵlistener("click", function Chips_Template_ul_click_1_listener() {
          ɵɵrestoreView(_r1);
          return ɵɵresetView(ctx.onWrapperClick());
        })("focus", function Chips_Template_ul_focus_1_listener() {
          ɵɵrestoreView(_r1);
          return ɵɵresetView(ctx.onContainerFocus());
        })("blur", function Chips_Template_ul_blur_1_listener() {
          ɵɵrestoreView(_r1);
          return ɵɵresetView(ctx.onContainerBlur());
        })("keydown", function Chips_Template_ul_keydown_1_listener($event) {
          ɵɵrestoreView(_r1);
          return ɵɵresetView(ctx.onContainerKeyDown($event));
        });
        ɵɵtemplate(3, Chips_li_3_Template, 4, 15, "li", 6);
        ɵɵelementStart(4, "li", 7)(5, "input", 8, 1);
        ɵɵlistener("keydown", function Chips_Template_input_keydown_5_listener($event) {
          ɵɵrestoreView(_r1);
          return ɵɵresetView(ctx.onKeyDown($event));
        })("input", function Chips_Template_input_input_5_listener() {
          ɵɵrestoreView(_r1);
          return ɵɵresetView(ctx.onInput());
        })("paste", function Chips_Template_input_paste_5_listener($event) {
          ɵɵrestoreView(_r1);
          return ɵɵresetView(ctx.onPaste($event));
        })("focus", function Chips_Template_input_focus_5_listener($event) {
          ɵɵrestoreView(_r1);
          return ɵɵresetView(ctx.onInputFocus($event));
        })("blur", function Chips_Template_input_blur_5_listener($event) {
          ɵɵrestoreView(_r1);
          return ɵɵresetView(ctx.onInputBlur($event));
        });
        ɵɵelementEnd()();
        ɵɵtemplate(7, Chips_li_7_Template, 3, 2, "li", 9);
        ɵɵelementEnd()();
      }
      if (rf & 2) {
        ɵɵclassMap(ctx.styleClass);
        ɵɵproperty("ngClass", ɵɵpureFunction4(24, _c2, ctx.disabled, ctx.focused, ctx.value && ctx.value.length || (ctx.inputViewChild == null ? null : ctx.inputViewChild.nativeElement.value) && (ctx.inputViewChild == null ? null : ctx.inputViewChild.nativeElement.value.length), ctx.focused))("ngStyle", ctx.style);
        ɵɵattribute("data-pc-name", "chips")("data-pc-section", "root");
        ɵɵadvance();
        ɵɵattribute("aria-labelledby", ctx.ariaLabelledBy)("aria-label", ctx.ariaLabel)("aria-activedescendant", ctx.focused ? ctx.focusedOptionId : void 0)("aria-orientation", "horizontal")("data-pc-section", "container");
        ɵɵadvance(2);
        ɵɵproperty("ngForOf", ctx.value);
        ɵɵadvance();
        ɵɵproperty("ngClass", ɵɵpureFunction1(29, _c3, ctx.showClear && !ctx.disabled));
        ɵɵattribute("data-pc-section", "inputToken");
        ɵɵadvance();
        ɵɵclassMap(ctx.inputStyleClass);
        ɵɵproperty("disabled", ctx.disabled || ctx.isMaxedOut)("ngStyle", ctx.inputStyle)("autofocus", ctx.autofocus);
        ɵɵattribute("id", ctx.inputId)("maxlength", ctx.maxLength)("placeholder", ctx.value && ctx.value.length ? null : ctx.placeholder)("tabindex", ctx.tabindex);
        ɵɵadvance(2);
        ɵɵproperty("ngIf", ctx.value != null && ctx.filled && !ctx.disabled && ctx.showClear);
      }
    },
    dependencies: [CommonModule, NgClass, NgForOf, NgIf, NgTemplateOutlet, NgStyle, InputTextModule, SharedModule, AutoFocusModule, AutoFocus, TimesCircleIcon, TimesIcon, ChipModule, Chip],
    encapsulation: 2,
    changeDetection: 0
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(Chips, [{
    type: Component,
    args: [{
      selector: "p-chips",
      standalone: true,
      imports: [CommonModule, InputTextModule, SharedModule, AutoFocusModule, TimesCircleIcon, TimesIcon, ChipModule],
      template: `
        <div
            [ngClass]="{
                'p-inputchips p-component p-input-wrapper': true,
                'p-disabled': disabled,
                'p-focus': focused,
                'p-inputwrapper-filled': (value && value.length) || (this.inputViewChild?.nativeElement.value && this.inputViewChild?.nativeElement.value.length),
                'p-inputwrapper-focus': focused
            }"
            [ngStyle]="style"
            [class]="styleClass"
            [attr.data-pc-name]="'chips'"
            [attr.data-pc-section]="'root'"
        >
            <ul
                #container
                class="p-inputchips-input"
                tabindex="-1"
                role="listbox"
                [attr.aria-labelledby]="ariaLabelledBy"
                [attr.aria-label]="ariaLabel"
                [attr.aria-activedescendant]="focused ? focusedOptionId : undefined"
                [attr.aria-orientation]="'horizontal'"
                (click)="onWrapperClick()"
                (focus)="onContainerFocus()"
                (blur)="onContainerBlur()"
                (keydown)="onContainerKeyDown($event)"
                [attr.data-pc-section]="'container'"
            >
                <li
                    #token
                    *ngFor="let item of value; let i = index"
                    [attr.id]="id + '_chips_item_' + i"
                    role="option"
                    [attr.ariaLabel]="item"
                    [attr.aria-selected]="true"
                    [attr.aria-setsize]="value.length"
                    [attr.aria-posinset]="i + 1"
                    [attr.data-p-focused]="focusedIndex === i"
                    [ngClass]="{ 'p-inputchips-chip-item': true, 'p-focus': focusedIndex === i }"
                    (click)="onItemClick($event, item)"
                    (contextmenu)="onItemContextMenu($event, item)"
                    [attr.data-pc-section]="'token'"
                >
                    <ng-container *ngTemplateOutlet="itemTemplate; context: { $implicit: item }"></ng-container>
                    <p-chip *ngIf="!itemTemplate" class="p-inputchips-chip" [label]="field ? resolveFieldData(item, field) : item" [removeIcon]="chipIcon" removable (onRemove)="removeItem($event, i)">
                        <ng-container *ngTemplateOutlet="itemTemplate; context: { $implicit: item }"></ng-container>
                        <ng-template #removeicon>
                            <ng-container *ngIf="!disabled">
                                <TimesCircleIcon [styleClass]="'p-chips-token-icon'" *ngIf="!removeTokenIconTemplate" (click)="removeItem($event, i)" [attr.data-pc-section]="'removeTokenIcon'" [attr.aria-hidden]="true" />
                                <span *ngIf="removeTokenIconTemplate" class="p-chips-token-icon" (click)="removeItem($event, i)" [attr.data-pc-section]="'removeTokenIcon'" [attr.aria-hidden]="true">
                                    <ng-template *ngTemplateOutlet="removeTokenIconTemplate; context: { removeItem: removeItem.bind(this) }"></ng-template>
                                </span>
                            </ng-container>
                        </ng-template>
                    </p-chip>
                </li>
                <li class="p-inputchips-input-item" [ngClass]="{ 'p-chips-clearable': showClear && !disabled }" [attr.data-pc-section]="'inputToken'" role="option">
                    <input
                        #inputtext
                        type="text"
                        [attr.id]="inputId"
                        [attr.maxlength]="maxLength"
                        [attr.placeholder]="value && value.length ? null : placeholder"
                        [attr.tabindex]="tabindex"
                        (keydown)="onKeyDown($event)"
                        (input)="onInput()"
                        (paste)="onPaste($event)"
                        (focus)="onInputFocus($event)"
                        (blur)="onInputBlur($event)"
                        [disabled]="disabled || isMaxedOut"
                        [ngStyle]="inputStyle"
                        [class]="inputStyleClass"
                        pAutoFocus
                        [autofocus]="autofocus"
                        class="test"
                    />
                </li>
                <li *ngIf="value != null && filled && !disabled && showClear">
                    <TimesIcon *ngIf="!clearIconTemplate" [styleClass]="'p-chips-clear-icon'" (click)="clear()" />
                    <span *ngIf="clearIconTemplate" class="p-chips-clear-icon" (click)="clear()">
                        <ng-template *ngTemplateOutlet="clearIconTemplate"></ng-template>
                    </span>
                </li>
            </ul>
        </div>
    `,
      host: {
        class: "p-element p-inputwrapper",
        "[class.p-inputwrapper-filled]": "filled",
        "[class.p-inputwrapper-focus]": "focused",
        "[class.p-chips-clearable]": "showClear"
      },
      providers: [CHIPS_VALUE_ACCESSOR, ChipsStyle],
      changeDetection: ChangeDetectionStrategy.OnPush,
      encapsulation: ViewEncapsulation.None
    }]
  }], () => [{
    type: ElementRef
  }, {
    type: ChangeDetectorRef
  }, {
    type: PrimeNG
  }], {
    style: [{
      type: Input
    }],
    styleClass: [{
      type: Input
    }],
    disabled: [{
      type: Input,
      args: [{
        transform: booleanAttribute
      }]
    }],
    field: [{
      type: Input
    }],
    placeholder: [{
      type: Input
    }],
    max: [{
      type: Input,
      args: [{
        transform: numberAttribute
      }]
    }],
    maxLength: [{
      type: Input
    }],
    ariaLabel: [{
      type: Input
    }],
    ariaLabelledBy: [{
      type: Input
    }],
    tabindex: [{
      type: Input,
      args: [{
        transform: numberAttribute
      }]
    }],
    inputId: [{
      type: Input
    }],
    allowDuplicate: [{
      type: Input,
      args: [{
        transform: booleanAttribute
      }]
    }],
    caseSensitiveDuplication: [{
      type: Input,
      args: [{
        transform: booleanAttribute
      }]
    }],
    inputStyle: [{
      type: Input
    }],
    inputStyleClass: [{
      type: Input
    }],
    chipIcon: [{
      type: Input
    }],
    addOnTab: [{
      type: Input,
      args: [{
        transform: booleanAttribute
      }]
    }],
    addOnBlur: [{
      type: Input,
      args: [{
        transform: booleanAttribute
      }]
    }],
    separator: [{
      type: Input
    }],
    showClear: [{
      type: Input,
      args: [{
        transform: booleanAttribute
      }]
    }],
    autofocus: [{
      type: Input,
      args: [{
        transform: booleanAttribute
      }]
    }],
    variant: [{
      type: Input
    }],
    onAdd: [{
      type: Output
    }],
    onRemove: [{
      type: Output
    }],
    onFocus: [{
      type: Output
    }],
    onBlur: [{
      type: Output
    }],
    onChipClick: [{
      type: Output
    }],
    onChipContextMenu: [{
      type: Output
    }],
    onClear: [{
      type: Output
    }],
    inputViewChild: [{
      type: ViewChild,
      args: ["inputtext"]
    }],
    containerViewChild: [{
      type: ViewChild,
      args: ["container"]
    }],
    templates: [{
      type: ContentChildren,
      args: [PrimeTemplate]
    }]
  });
})();
var ChipsModule = class _ChipsModule {
  static ɵfac = function ChipsModule_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ChipsModule)();
  };
  static ɵmod = ɵɵdefineNgModule({
    type: _ChipsModule,
    imports: [Chips, SharedModule],
    exports: [Chips, SharedModule]
  });
  static ɵinj = ɵɵdefineInjector({
    imports: [Chips, SharedModule, SharedModule]
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ChipsModule, [{
    type: NgModule,
    args: [{
      imports: [Chips, SharedModule],
      exports: [Chips, SharedModule]
    }]
  }], null, null);
})();
export {
  CHIPS_VALUE_ACCESSOR,
  Chips,
  ChipsModule
};
//# sourceMappingURL=primeng_chips.js.map

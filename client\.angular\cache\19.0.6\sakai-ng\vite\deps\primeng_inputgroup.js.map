{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-inputgroup.mjs"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, Input, Component, NgModule } from '@angular/core';\nimport { SharedModule } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"*\"];\nconst theme = ({\n  dt\n}) => `\n.p-inputgroup,\n.p-inputgroup .p-floatlabel,\n.p-inputgroup .p-iftalabel {\n    display: flex;\n    align-items: stretch;\n    width: 100%;\n}\n\n.p-inputgroup .p-inputtext,\n.p-inputgroup .p-inputwrapper {\n    flex: 1 1 auto;\n    width: 1%;\n}\n\n.p-inputgroupaddon {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    padding: ${dt('inputgroup.addon.padding')};\n    background: ${dt('inputgroup.addon.background')};\n    color: ${dt('inputgroup.addon.color')};\n    border-block-start: 1px solid ${dt('inputgroup.addon.border.color')};\n    border-block-end: 1px solid ${dt('inputgroup.addon.border.color')};\n    min-width: ${dt('inputgroup.addon.min.width')};\n}\n\n.p-inputgroupaddon:first-child,\n.p-inputgroupaddon + .p-inputgroupaddon {\n    border-inline-start: 1px solid ${dt('inputgroup.addon.border.color')};\n}\n\n.p-inputgroupaddon:last-child {\n    border-inline-end: 1px solid ${dt('inputgroup.addon.border.color')};\n}\n\n.p-inputgroupaddon:has(.p-button) {\n    padding: 0;\n    overflow: hidden;\n}\n\n.p-inputgroupaddon .p-button {\n    border-radius: 0;\n}\n\n.p-inputgroup > .p-component,\n.p-inputgroup > .p-inputwrapper > .p-component,\n.p-inputgroup:first-child > p-button > .p-button,\n.p-inputgroup > .p-floatlabel > .p-component,\n.p-inputgroup > .p-floatlabel > .p-inputwrapper > .p-component,\n.p-inputgroup > .p-iftalabel > .p-component,\n.p-inputgroup > .p-iftalabel > .p-inputwrapper > .p-component {\n    border-radius: 0;\n    margin: 0;\n}\n\n.p-inputgroupaddon:first-child,\n.p-inputgroup > .p-component:first-child,\n.p-inputgroup > .p-inputwrapper:first-child > .p-component,\n.p-inputgroup > .p-floatlabel:first-child > .p-component,\n.p-inputgroup > .p-floatlabel:first-child > .p-inputwrapper > .p-component,\n.p-inputgroup > .p-iftalabel:first-child > .p-component,\n.p-inputgroup > .p-iftalabel:first-child > .p-inputwrapper > .p-component {\n    border-start-start-radius: ${dt('inputgroup.addon.border.radius')};\n    border-end-start-radius: ${dt('inputgroup.addon.border.radius')};\n}\n\n.p-inputgroupaddon:last-child,\n.p-inputgroup > .p-component:last-child,\n.p-inputgroup > .p-inputwrapper:last-child > .p-component,\n.p-inputgroup > .p-floatlabel:last-child > .p-component,\n.p-inputgroup > .p-floatlabel:last-child > .p-inputwrapper > .p-component,\n.p-inputgroup > .p-iftalabel:last-child > .p-component,\n.p-inputgroup > .p-iftalabel:last-child > .p-inputwrapper > .p-component {\n    border-start-end-radius: ${dt('inputgroup.addon.border.radius')};\n    border-end-end-radius: ${dt('inputgroup.addon.border.radius')};\n}\n\n.p-inputgroup .p-component:focus,\n.p-inputgroup .p-component.p-focus,\n.p-inputgroup .p-inputwrapper-focus,\n.p-inputgroup .p-component:focus ~ label,\n.p-inputgroup .p-component.p-focus ~ label,\n.p-inputgroup .p-inputwrapper-focus ~ label {\n    z-index: 1;\n}\n\n.p-inputgroup > .p-button:not(.p-button-icon-only) {\n    width: auto;\n}\n\n/*For PrimeNG*/\n\n.p-inputgroup p-button:first-child, .p-inputgroup p-button:last-child {\n    display: inline-flex;\n}\n\n.p-inputgroup:has(> p-button:first-child) .p-button{\n    border-start-start-radius: ${dt('inputgroup.addon.border.radius')};\n    border-end-start-radius: ${dt('inputgroup.addon.border.radius')};\n}\n\n.p-inputgroup:has(> p-button:last-child) .p-button {\n    border-start-end-radius: ${dt('inputgroup.addon.border.radius')};\n    border-end-end-radius: ${dt('inputgroup.addon.border.radius')};\n}\n`;\nconst classes = {\n  root: ({\n    props\n  }) => ['p-inputgroup', {\n    'p-inputgroup-fluid': props.fluid\n  }]\n};\nclass InputGroupStyle extends BaseStyle {\n  name = 'inputgroup';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵInputGroupStyle_BaseFactory;\n    return function InputGroupStyle_Factory(__ngFactoryType__) {\n      return (ɵInputGroupStyle_BaseFactory || (ɵInputGroupStyle_BaseFactory = i0.ɵɵgetInheritedFactory(InputGroupStyle)))(__ngFactoryType__ || InputGroupStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: InputGroupStyle,\n    factory: InputGroupStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputGroupStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * InputGroup displays text, icon, buttons and other content can be grouped next to an input.\n *\n * [Live Demo](https://www.primeng.org/inputgroup/)\n *\n * @module inputgroupstyle\n *\n */\nvar InputGroupClasses;\n(function (InputGroupClasses) {\n  /**\n   * Class name of the root element\n   */\n  InputGroupClasses[\"root\"] = \"p-inputgroup\";\n})(InputGroupClasses || (InputGroupClasses = {}));\n\n/**\n * InputGroup displays text, icon, buttons and other content can be grouped next to an input.\n * @group Components\n */\nclass InputGroup extends BaseComponent {\n  /**\n   * Inline style of the element.\n   * @group Props\n   */\n  style;\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  styleClass;\n  _componentStyle = inject(InputGroupStyle);\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵInputGroup_BaseFactory;\n    return function InputGroup_Factory(__ngFactoryType__) {\n      return (ɵInputGroup_BaseFactory || (ɵInputGroup_BaseFactory = i0.ɵɵgetInheritedFactory(InputGroup)))(__ngFactoryType__ || InputGroup);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: InputGroup,\n    selectors: [[\"p-inputgroup\"], [\"p-inputGroup\"], [\"p-input-group\"]],\n    hostAttrs: [1, \"p-inputgroup\"],\n    hostVars: 5,\n    hostBindings: function InputGroup_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"data-pc-name\", \"inputgroup\");\n        i0.ɵɵstyleMap(ctx.style);\n        i0.ɵɵclassMap(ctx.styleClass);\n      }\n    },\n    inputs: {\n      style: \"style\",\n      styleClass: \"styleClass\"\n    },\n    features: [i0.ɵɵProvidersFeature([InputGroupStyle]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function InputGroup_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    dependencies: [CommonModule, SharedModule],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputGroup, [{\n    type: Component,\n    args: [{\n      selector: 'p-inputgroup, p-inputGroup, p-input-group',\n      standalone: true,\n      imports: [CommonModule, SharedModule],\n      template: ` <ng-content></ng-content> `,\n      providers: [InputGroupStyle],\n      host: {\n        class: 'p-inputgroup',\n        '[attr.data-pc-name]': '\"inputgroup\"',\n        '[class]': 'styleClass',\n        '[style]': 'style'\n      }\n    }]\n  }], null, {\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }]\n  });\n})();\nclass InputGroupModule {\n  static ɵfac = function InputGroupModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || InputGroupModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: InputGroupModule,\n    imports: [InputGroup, SharedModule],\n    exports: [InputGroup, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [InputGroup, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputGroupModule, [{\n    type: NgModule,\n    args: [{\n      imports: [InputGroup, SharedModule],\n      exports: [InputGroup, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { InputGroup, InputGroupClasses, InputGroupModule, InputGroupStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAmBS,GAAG,0BAA0B,CAAC;AAAA,kBAC3B,GAAG,6BAA6B,CAAC;AAAA,aACtC,GAAG,wBAAwB,CAAC;AAAA,oCACL,GAAG,+BAA+B,CAAC;AAAA,kCACrC,GAAG,+BAA+B,CAAC;AAAA,iBACpD,GAAG,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,qCAKZ,GAAG,+BAA+B,CAAC;AAAA;AAAA;AAAA;AAAA,mCAIrC,GAAG,+BAA+B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iCA8BrC,GAAG,gCAAgC,CAAC;AAAA,+BACtC,GAAG,gCAAgC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+BAUpC,GAAG,gCAAgC,CAAC;AAAA,6BACtC,GAAG,gCAAgC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iCAuBhC,GAAG,gCAAgC,CAAC;AAAA,+BACtC,GAAG,gCAAgC,CAAC;AAAA;AAAA;AAAA;AAAA,+BAIpC,GAAG,gCAAgC,CAAC;AAAA,6BACtC,GAAG,gCAAgC,CAAC;AAAA;AAAA;AAGjE,IAAM,UAAU;AAAA,EACd,MAAM,CAAC;AAAA,IACL;AAAA,EACF,MAAM,CAAC,gBAAgB;AAAA,IACrB,sBAAsB,MAAM;AAAA,EAC9B,CAAC;AACH;AACA,IAAM,kBAAN,MAAM,yBAAwB,UAAU;AAAA,EACtC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,wBAAwB,mBAAmB;AACzD,cAAQ,iCAAiC,+BAAkC,sBAAsB,gBAAe,IAAI,qBAAqB,gBAAe;AAAA,IAC1J;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,iBAAgB;AAAA,EAC3B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,oBAAmB;AAI5B,EAAAA,mBAAkB,MAAM,IAAI;AAC9B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAMhD,IAAM,aAAN,MAAM,oBAAmB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA,kBAAkB,OAAO,eAAe;AAAA,EACxC,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,mBAAmB,mBAAmB;AACpD,cAAQ,4BAA4B,0BAA6B,sBAAsB,WAAU,IAAI,qBAAqB,WAAU;AAAA,IACtI;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,GAAG,CAAC,cAAc,GAAG,CAAC,eAAe,CAAC;AAAA,IACjE,WAAW,CAAC,GAAG,cAAc;AAAA,IAC7B,UAAU;AAAA,IACV,cAAc,SAAS,wBAAwB,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,gBAAgB,YAAY;AAC3C,QAAG,WAAW,IAAI,KAAK;AACvB,QAAG,WAAW,IAAI,UAAU;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,eAAe,CAAC,GAAM,0BAA0B;AAAA,IAClF,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,oBAAoB,IAAI,KAAK;AAC9C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAc,YAAY;AAAA,IACzC,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,YAAY;AAAA,MACpC,UAAU;AAAA,MACV,WAAW,CAAC,eAAe;AAAA,MAC3B,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,uBAAuB;AAAA,QACvB,WAAW;AAAA,QACX,WAAW;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,YAAY,YAAY;AAAA,IAClC,SAAS,CAAC,YAAY,YAAY;AAAA,EACpC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,YAAY,cAAc,YAAY;AAAA,EAClD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY,YAAY;AAAA,MAClC,SAAS,CAAC,YAAY,YAAY;AAAA,IACpC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["InputGroupClasses"]}
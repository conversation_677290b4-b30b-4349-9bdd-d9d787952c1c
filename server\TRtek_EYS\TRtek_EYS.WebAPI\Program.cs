using System.Text;
using DefaultCorsPolicyNugetPackage;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using TRtek_EYS.Application;
using TRtek_EYS.Infrastructure;
using TRtek_EYS.WebAPI.Middlewares;

var builder = WebApplication.CreateBuilder(args);

// 1. CORS
builder.Services.AddDefaultCors();

// 2. HttpContext accessor
builder.Services.AddHttpContextAccessor();

// 3. Katmanlar
builder.Services.AddApplication();
builder.Services.AddInfrastructure(builder.Configuration);

// 4. Authentication
builder.Services.AddAuthentication();

// HttpContext erişimi için
builder.Services.AddHttpContextAccessor();

// 5. Exception middleware
builder.Services.AddExceptionHandler<ExceptionHandler>();
builder.Services.AddProblemDetails();

// 6. Controllers ve Swagger
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();

builder.Services.AddSwaggerGen(setup =>
{
    var jwtSecurityScheme = new OpenApiSecurityScheme
    {
        BearerFormat = "JWT",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.Http,
        Scheme = JwtBearerDefaults.AuthenticationScheme,
        Description = "Enter your JWT token below (without 'Bearer ' prefix)",
        Reference = new OpenApiReference
        {
            Id = JwtBearerDefaults.AuthenticationScheme,
            Type = ReferenceType.SecurityScheme
        }
    };

    setup.AddSecurityDefinition(jwtSecurityScheme.Reference.Id, jwtSecurityScheme);

    setup.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        { jwtSecurityScheme, Array.Empty<string>() }
    });
});

// 7. App build ve middleware
var app = builder.Build();

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors();
app.UseAuthentication();      // ⬅ Token doğrulama
app.UseAuthorization();       // ⬅ Yetki kontrolü
app.UseExceptionHandler();    // ⬅ Global hata yönetimi
app.MapControllers();

// 8. Opsiyonel ilk kullanıcı oluşturma
ExtensionsMiddleware.CreateFirstUser(app);

app.Run();

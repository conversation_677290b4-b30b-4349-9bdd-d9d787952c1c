import { Permission } from './permission.model';

export interface Role {
    id: string;
    name: string;
    description?: string;
    permissions: Permission[];
    isSystem: boolean;
    createdAt: Date;
    updatedAt: Date;
}

export interface SystemRole extends Role {
    isSystem: true;
    code: string;
}

export const SYSTEM_ROLES: { [key: string]: SystemRole } = {
    SUPER_ADMIN: {
        id: 'super-admin',
        name: 'Super Administrator',
        code: 'SUPER_ADMIN',
        description: '<PERSON><PERSON><PERSON> yö<PERSON>ici rolü, tüm sistem yetkilerine sahiptir',
        permissions: Object.values(Permission), // Tüm izinlere sahip
        isSystem: true,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    SYSTEM_ADMIN: {
        id: 'system-admin',
        name: 'System Administrator',
        code: 'SYSTEM_ADMIN',
        description: 'Full system access with all permissions',
        permissions: [
            // Teams permissions
            Permission.TEAMS_CREATE,
            Permission.TEAMS_EDIT,
            Permission.TEAMS_DELETE,
            Permission.TEAMS_VIEW,
            
            // Roles permissions
            Permission.MANAGE_ROLES,
            Permission.VIEW_ROLES,
            
            // Projects permissions
            Permission.MANAGE_PROJECTS,
            Permission.VIEW_PROJECTS,
            Permission.MANAGE_PLATFORMS,
            Permission.VIEW_PLATFORMS,
            Permission.MANAGE_TECHNOLOGIES,
            Permission.VIEW_TECHNOLOGIES,
            Permission.MANAGE_DOCUMENTS,
            Permission.VIEW_DOCUMENTS,
            
            // Customer permissions
            Permission.MANAGE_CUSTOMERS,
            Permission.VIEW_CUSTOMERS,
            Permission.MANAGE_CUSTOMER_PROJECT_RELATIONS,
            Permission.MANAGE_CONTRACTS,
            Permission.VIEW_CONTRACTS,
            Permission.MANAGE_CUSTOMER_CONFIGS,
            Permission.VIEW_CUSTOMER_CONFIGS,
            
            // Team Management
            Permission.ASSIGN_TEAM_MEMBERS,
            Permission.MANAGE_TEAM_ROLES,
            
            // Repository
            Permission.MANAGE_REPOSITORY,
            Permission.VIEW_REPOSITORY,
            
            // Versions
            Permission.MANAGE_PROJECT_VERSIONS,
            Permission.VIEW_PROJECT_VERSIONS
        ],
        isSystem: true,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    ANALYST: {
        id: 'analyst',
        name: 'Analyst',
        code: 'ANALYST',
        description: 'Manages customer relations and contracts',
        permissions: [
            Permission.MANAGE_CUSTOMERS,
            Permission.VIEW_CUSTOMERS,
            Permission.MANAGE_CUSTOMER_PROJECT_RELATIONS,
            Permission.MANAGE_CONTRACTS,
            Permission.VIEW_CONTRACTS,
            Permission.MANAGE_CUSTOMER_CONFIGS,
            Permission.VIEW_CUSTOMER_CONFIGS
        ],
        isSystem: true,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    SOFTWARE_ANALYST: {
        id: 'software-analyst',
        name: 'Software Analyst',
        code: 'SOFTWARE_ANALYST',
        description: 'Manages projects, platforms, and technologies',
        permissions: [
            Permission.MANAGE_PROJECTS,
            Permission.VIEW_PROJECTS,
            Permission.MANAGE_PLATFORMS,
            Permission.VIEW_PLATFORMS,
            Permission.MANAGE_TECHNOLOGIES,
            Permission.VIEW_TECHNOLOGIES,
            Permission.MANAGE_DOCUMENTS,
            Permission.VIEW_DOCUMENTS
        ],
        isSystem: true,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    PROJECT_MANAGER: {
        id: 'project-manager',
        name: 'Project Manager',
        code: 'PROJECT_MANAGER',
        description: 'Manages team assignments and roles in projects',
        permissions: [
            Permission.ASSIGN_TEAM_MEMBERS,
            Permission.MANAGE_TEAM_ROLES,
            Permission.TEAMS_VIEW,
            Permission.VIEW_PROJECTS
        ],
        isSystem: true,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    SUPPORT_SPECIALIST: {
        id: 'support-specialist',
        name: 'Support Specialist',
        code: 'SUPPORT_SPECIALIST',
        description: 'Manages customer configurations',
        permissions: [
            Permission.MANAGE_CUSTOMER_CONFIGS,
            Permission.VIEW_CUSTOMER_CONFIGS,
            Permission.VIEW_CUSTOMERS
        ],
        isSystem: true,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    DEVELOPER: {
        id: 'developer',
        name: 'Developer',
        code: 'DEVELOPER',
        description: 'Manages repository information',
        permissions: [
            Permission.MANAGE_REPOSITORY,
            Permission.VIEW_REPOSITORY,
            Permission.VIEW_PROJECTS,
            Permission.VIEW_TECHNOLOGIES
        ],
        isSystem: true,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    RELEASE_MANAGER: {
        id: 'release-manager',
        name: 'Release Manager',
        code: 'RELEASE_MANAGER',
        description: 'Manages project versions',
        permissions: [
            Permission.MANAGE_PROJECT_VERSIONS,
            Permission.VIEW_PROJECT_VERSIONS,
            Permission.VIEW_PROJECTS,
            Permission.VIEW_REPOSITORY
        ],
        isSystem: true,
        createdAt: new Date(),
        updatedAt: new Date()
    }
};

# === BACKEND (.NET / ASP.NET Core) ===
**/bin/
**/obj/
**/.vs/
**/*.user
**/*.suo
**/*.userosscache
**/*.sln.docstates

# Build results
*.dll
*.exe
*.app
*.pdb
*.cache
*.log

# Publish results
**/publish/
**/out/

# Rider / JetBrains
.idea/

# Secret config
appsettings.*.json
secrets.json

# === FRONTEND (Angular) ===
node_modules/
dist/
tmp/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json

# Environment files (optional)
src/environments/environment*.ts

# IDE / Editor
.vscode/
*.code-workspace

# OS-specific
.DS_Store
Thumbs.db
.sakai-ng/
using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Technologies.Queries.GetAll
{
    public class GetAllTechnologiesQueryHandler : IRequestHandler<GetAllTechnologiesQuery, List<TechnologyGetAllDto>>
    {
        private readonly IApplicationDbContext _context;

        public GetAllTechnologiesQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<TechnologyGetAllDto>> Handle(GetAllTechnologiesQuery request, CancellationToken cancellationToken)
        {
            return await _context.Technologies
                .AsNoTracking()
                .Select(t => new TechnologyGetAllDto
                {
                    Id = t.Id,
                    Language = t.Language ?? string.Empty,
                    Framework = t.Framework,
                    Version = t.Version,
                    // ProjectId kaldırıldı - artık Many-to-Many ilişki
                })
                .ToListAsync(cancellationToken);
        }
    }
}

import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { Permission } from '../models/permission.model';
import { AuthService } from '../services/auth.service';

export const permissionGuard = (requiredPermissions: Permission[]): CanActivateFn => {
    return () => {
        const authService = inject(AuthService);
        const router = inject(Router);

        if (authService.hasAllPermissions(requiredPermissions)) {
            return true;
        }

        router.navigate(['/auth/access-denied']);
        return false;
    };
};

import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { Permission } from '../models/permission.model';
import { AuthService } from '../services/auth.service';

export const permissionGuard = (requiredPermissions: Permission[]): CanActivateFn => {
    return () => {
        const authService = inject(AuthService);
        const router = inject(Router);

        // Geçici olarak tüm izinleri ver (development için)
        const currentUser = authService.getCurrentUser();
        if (currentUser) {
            console.log('Permission guard - User found, allowing access');
            return true;
        }

        // Kullanıcı yoksa login'e yönlendir
        router.navigate(['/auth/login']);
        return false;
    };
};

﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TRtek_EYS.Domain.Entities;

namespace TRtek_EYS.Infrastructure.Configurations
{
    public class CustomerConfiguration : IEntityTypeConfiguration<Customer>
    {
        public void Configure(EntityTypeBuilder<Customer> builder)
        {
            builder.ToTable("Customers");

            builder.HasKey(c => c.Id);

            builder.Property(c => c.Name)
                   .IsRequired()
                   .HasMaxLength(150);

            builder.Property(c => c.ContactPerson)
                   .HasMaxLength(100);

            builder.Property(c => c.Email)
                   .HasMaxLength(100);

            builder.Property(c => c.Phone)
                   .HasMaxLength(20);

            builder.Property(c => c.Address)
                   .HasMaxLength(250);

            builder.Property(c => c.TaxNumber)
                   .HasMaxLength(20);

            builder.Property(c => c.IsActive)
                   .IsRequired();

            // YENİ ALANLAR İÇİN CONFIGURATION
            builder.Property(c => c.CustomerCode)
                   .HasMaxLength(50);

            builder.Property(c => c.CompanySize)
                   .HasMaxLength(50);

            builder.Property(c => c.Website)
                   .HasMaxLength(200);

            builder.Property(c => c.Description)
                   .HasMaxLength(1000);

            builder.Property(c => c.SecondaryEmail)
                   .HasMaxLength(100);

            builder.Property(c => c.SecondaryPhone)
                   .HasMaxLength(20);

            builder.Property(c => c.Fax)
                   .HasMaxLength(20);

            builder.Property(c => c.City)
                   .HasMaxLength(100);

            builder.Property(c => c.Country)
                   .HasMaxLength(100);

            builder.Property(c => c.PostalCode)
                   .HasMaxLength(20);

            builder.Property(c => c.BillingAddress)
                   .HasMaxLength(500);

            builder.Property(c => c.ShippingAddress)
                   .HasMaxLength(500);

            builder.Property(c => c.CreditLimit)
                   .HasPrecision(15, 2);

            builder.Property(c => c.Currency)
                   .HasMaxLength(10);

            builder.Property(c => c.PaymentTerms)
                   .HasMaxLength(200);

            builder.Property(c => c.Notes)
                   .HasMaxLength(2000);

            builder.Property(c => c.Tags)
                   .HasMaxLength(1000);

            builder.Property(c => c.AssignedSalesRep)
                   .HasMaxLength(100);

            builder.Property(c => c.TotalRevenue)
                   .HasPrecision(15, 2);

            builder.Property(c => c.CreatedAt)
                   .IsRequired();

        }
    }
}

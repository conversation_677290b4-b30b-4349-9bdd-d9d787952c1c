﻿using TRtek_EYS.Domain.Enums;

namespace TRtek_EYS.Domain.Entities;

public class Customer : BaseEntity
{
    // MEVCUT ALANLAR
    public string Name { get; set; } = null!;
    public string? ContactPerson { get; set; }
    public string? Email { get; set; }
    public string? Phone { get; set; }
    public string? Address { get; set; }
    public string? TaxNumber { get; set; }
    public bool IsActive { get; set; }

    // YENİ ALANLAR
    public string? CustomerCode { get; set; }
    public CustomerType? CustomerType { get; set; }
    public IndustryType? Industry { get; set; }
    public CustomerStatus? Status { get; set; }
    public string? CompanySize { get; set; }
    public string? Website { get; set; }
    public string? Description { get; set; }
    public string? SecondaryEmail { get; set; }
    public string? SecondaryPhone { get; set; }
    public string? Fax { get; set; }
    public string? City { get; set; }
    public string? Country { get; set; }
    public string? PostalCode { get; set; }
    public string? BillingAddress { get; set; }
    public string? ShippingAddress { get; set; }
    public decimal? CreditLimit { get; set; }
    public string? Currency { get; set; }
    public string? PaymentTerms { get; set; }
    public DateTime? FirstContactDate { get; set; }
    public DateTime? LastContactDate { get; set; }
    public string? Notes { get; set; }
    public string? Tags { get; set; }
    public int ProjectCount { get; set; } = 0;
    public decimal TotalRevenue { get; set; } = 0;
    public string? AssignedSalesRep { get; set; }
    public int Priority { get; set; } = 1; // 1-5 scale
    public bool IsVip { get; set; } = false;

    // İLİŞKİLER
    public ICollection<CustomerProject> CustomerProjects { get; set; } = new List<CustomerProject>();
}

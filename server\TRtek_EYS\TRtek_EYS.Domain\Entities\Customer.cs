﻿namespace TRtek_EYS.Domain.Entities;

public class Customer : BaseEntity
{
    public string Name { get; set; } = null!;
    public string? ContactPerson { get; set; }
    public string? Email { get; set; }
    public string? Phone { get; set; }
    public string? Address { get; set; }
    public string? TaxNumber { get; set; }
    public bool IsActive { get; set; }

    public ICollection<Project> Projects { get; set; } = new List<Project>();
}

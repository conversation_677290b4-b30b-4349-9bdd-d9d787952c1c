﻿namespace TRtek_EYS.Domain.Entities;

public class Customer : BaseEntity
{
    public string Name { get; set; } = null!;
    public string? ContactPerson { get; set; }
    public string? Email { get; set; }
    public string? Phone { get; set; }
    public string? Address { get; set; }
    public string? TaxNumber { get; set; }
    public bool IsActive { get; set; }

    public ICollection<CustomerProject> CustomerProjects { get; set; } = new List<CustomerProject>(); 

}

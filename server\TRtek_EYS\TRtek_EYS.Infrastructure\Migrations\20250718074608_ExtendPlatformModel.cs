﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TRtek_EYS.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class ExtendPlatformModel : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "Category",
                table: "Platforms",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "Cost",
                table: "Platforms",
                type: "numeric(10,2)",
                precision: 10,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Currency",
                table: "Platforms",
                type: "character varying(10)",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "Platforms",
                type: "character varying(1000)",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Documentation",
                table: "Platforms",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "EndOfLifeDate",
                table: "Platforms",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "Platforms",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsSupported",
                table: "Platforms",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "LicenseType",
                table: "Platforms",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Name",
                table: "Platforms",
                type: "character varying(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Notes",
                table: "Platforms",
                type: "character varying(2000)",
                maxLength: 2000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PlatformCode",
                table: "Platforms",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ProjectCount",
                table: "Platforms",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<DateTime>(
                name: "ReleaseDate",
                table: "Platforms",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "SupportLevel",
                table: "Platforms",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Tags",
                table: "Platforms",
                type: "character varying(2000)",
                maxLength: 2000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Vendor",
                table: "Platforms",
                type: "character varying(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Version",
                table: "Platforms",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Website",
                table: "Platforms",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Category",
                table: "Platforms");

            migrationBuilder.DropColumn(
                name: "Cost",
                table: "Platforms");

            migrationBuilder.DropColumn(
                name: "Currency",
                table: "Platforms");

            migrationBuilder.DropColumn(
                name: "Description",
                table: "Platforms");

            migrationBuilder.DropColumn(
                name: "Documentation",
                table: "Platforms");

            migrationBuilder.DropColumn(
                name: "EndOfLifeDate",
                table: "Platforms");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "Platforms");

            migrationBuilder.DropColumn(
                name: "IsSupported",
                table: "Platforms");

            migrationBuilder.DropColumn(
                name: "LicenseType",
                table: "Platforms");

            migrationBuilder.DropColumn(
                name: "Name",
                table: "Platforms");

            migrationBuilder.DropColumn(
                name: "Notes",
                table: "Platforms");

            migrationBuilder.DropColumn(
                name: "PlatformCode",
                table: "Platforms");

            migrationBuilder.DropColumn(
                name: "ProjectCount",
                table: "Platforms");

            migrationBuilder.DropColumn(
                name: "ReleaseDate",
                table: "Platforms");

            migrationBuilder.DropColumn(
                name: "SupportLevel",
                table: "Platforms");

            migrationBuilder.DropColumn(
                name: "Tags",
                table: "Platforms");

            migrationBuilder.DropColumn(
                name: "Vendor",
                table: "Platforms");

            migrationBuilder.DropColumn(
                name: "Version",
                table: "Platforms");

            migrationBuilder.DropColumn(
                name: "Website",
                table: "Platforms");
        }
    }
}

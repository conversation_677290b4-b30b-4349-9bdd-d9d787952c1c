{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-chip.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, inject, booleanAttribute, ContentChildren, ContentChild, Input, Output, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { TranslationKeys, SharedModule, PrimeTemplate } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { TimesCircleIcon } from 'primeng/icons';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"removeicon\"];\nconst _c1 = [\"*\"];\nfunction Chip_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 4);\n    i0.ɵɵlistener(\"error\", function Chip_img_1_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.imageError($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r1.image, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.alt);\n  }\n}\nfunction Chip_ng_template_2_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 6);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r1.icon);\n    i0.ɵɵproperty(\"ngClass\", \"p-chip-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\");\n  }\n}\nfunction Chip_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Chip_ng_template_2_span_0_Template, 1, 4, \"span\", 5);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.icon);\n  }\n}\nfunction Chip_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.label);\n  }\n}\nfunction Chip_ng_container_5_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 11);\n    i0.ɵɵlistener(\"click\", function Chip_ng_container_5_ng_container_1_span_1_Template_span_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.close($event));\n    })(\"keydown\", function Chip_ng_container_5_ng_container_1_span_1_Template_span_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onKeydown($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(ctx_r1.removeIcon);\n    i0.ɵɵproperty(\"ngClass\", \"p-chip-remove-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"removeicon\")(\"aria-label\", ctx_r1.removeAriaLabel);\n  }\n}\nfunction Chip_ng_container_5_ng_container_1_TimesCircleIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"TimesCircleIcon\", 12);\n    i0.ɵɵlistener(\"click\", function Chip_ng_container_5_ng_container_1_TimesCircleIcon_2_Template_TimesCircleIcon_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.close($event));\n    })(\"keydown\", function Chip_ng_container_5_ng_container_1_TimesCircleIcon_2_Template_TimesCircleIcon_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onKeydown($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(\"p-chip-remove-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"removeicon\")(\"aria-label\", ctx_r1.removeAriaLabel);\n  }\n}\nfunction Chip_ng_container_5_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Chip_ng_container_5_ng_container_1_span_1_Template, 1, 5, \"span\", 9)(2, Chip_ng_container_5_ng_container_1_TimesCircleIcon_2_Template, 1, 4, \"TimesCircleIcon\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.removeIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.removeIcon);\n  }\n}\nfunction Chip_ng_container_5_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Chip_ng_container_5_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Chip_ng_container_5_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Chip_ng_container_5_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵlistener(\"click\", function Chip_ng_container_5_span_2_Template_span_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.close($event));\n    })(\"keydown\", function Chip_ng_container_5_span_2_Template_span_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onKeydown($event));\n    });\n    i0.ɵɵtemplate(1, Chip_ng_container_5_span_2_1_Template, 1, 0, null, 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"removeicon\")(\"aria-label\", ctx_r1.removeAriaLabel);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.removeIconTemplate || ctx_r1._removeIconTemplate);\n  }\n}\nfunction Chip_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Chip_ng_container_5_ng_container_1_Template, 3, 2, \"ng-container\", 3)(2, Chip_ng_container_5_span_2_Template, 2, 3, \"span\", 8);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.removeIconTemplate && !ctx_r1._removeIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.removeIconTemplate || ctx_r1._removeIconTemplate);\n  }\n}\nconst theme = ({\n  dt\n}) => `\n.p-chip {\n    display: inline-flex;\n    align-items: center;\n    background: ${dt('chip.background')};\n    color: ${dt('chip.color')};\n    border-radius: ${dt('chip.border.radius')};\n    padding: ${dt('chip.padding.y')} ${dt('chip.padding.x')};\n    gap: ${dt('chip.gap')};\n}\n\n.p-chip-icon {\n    color: ${dt('chip.icon.color')};\n    font-size: ${dt('chip.icon.font.size')};\n    width: ${dt('chip.icon.size')};\n    height: ${dt('chip.icon.size')};\n}\n\n.p-chip-image {\n    border-radius: 50%;\n    width: ${dt('chip.image.width')};\n    height: ${dt('chip.image.height')};\n    margin-left: calc(-1 * ${dt('chip.padding.y')});\n}\n\n.p-chip:has(.p-chip-remove-icon) {\n    padding-inline-end: ${dt('chip.padding.y')};\n}\n\n.p-chip:has(.p-chip-image) {\n    padding-top: calc(${dt('chip.padding.y')} / 2);\n    padding-bottom: calc(${dt('chip.padding.y')} / 2);\n}\n\n.p-chip-remove-icon {\n    cursor: pointer;\n    font-size: ${dt('chip.remove.icon.font.size')};\n    width: ${dt('chip.remove.icon.size')};\n    height: ${dt('chip.remove.icon.size')};\n    color: ${dt('chip.remove.icon.color')};\n    border-radius: 50%;\n    transition: outline-color ${dt('chip.transition.duration')}, box-shadow ${dt('chip.transition.duration')};\n    outline-color: transparent;\n}\n\n.p-chip-remove-icon:focus-visible {\n    box-shadow: ${dt('chip.remove.icon.focus.ring.shadow')};\n    outline: ${dt('chip.remove.icon.focus.ring.width')} ${dt('chip.remove.icon.focus.ring.style')} ${dt('chip.remove.icon.focus.ring.color')};\n    outline-offset: ${dt('chip.remove.icon.focus.ring.offset')};\n}\n`;\nconst classes = {\n  root: 'p-chip p-component',\n  image: 'p-chip-image',\n  icon: 'p-chip-icon',\n  label: 'p-chip-label',\n  removeIcon: 'p-chip-remove-icon'\n};\nclass ChipStyle extends BaseStyle {\n  name = 'chip';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵChipStyle_BaseFactory;\n    return function ChipStyle_Factory(__ngFactoryType__) {\n      return (ɵChipStyle_BaseFactory || (ɵChipStyle_BaseFactory = i0.ɵɵgetInheritedFactory(ChipStyle)))(__ngFactoryType__ || ChipStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ChipStyle,\n    factory: ChipStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ChipStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * Chip represents people using icons, labels and images.\n *\n * [Live Demo](https://www.primeng.org/chip)\n *\n * @module chipstyle\n *\n */\nvar ChipClasses;\n(function (ChipClasses) {\n  /**\n   * Class name of the root element\n   */\n  ChipClasses[\"root\"] = \"p-chip\";\n  /**\n   * Class name of the image element\n   */\n  ChipClasses[\"image\"] = \"p-chip-image\";\n  /**\n   * Class name of the icon element\n   */\n  ChipClasses[\"icon\"] = \"p-chip-icon\";\n  /**\n   * Class name of the label element\n   */\n  ChipClasses[\"label\"] = \"p-chip-label\";\n  /**\n   * Class name of the remove icon element\n   */\n  ChipClasses[\"removeIcon\"] = \"p-chip-remove-icon\";\n})(ChipClasses || (ChipClasses = {}));\n\n/**\n * Chip represents people using icons, labels and images.\n * @group Components\n */\nclass Chip extends BaseComponent {\n  /**\n   * Defines the text to display.\n   * @group Props\n   */\n  label;\n  /**\n   * Defines the icon to display.\n   * @group Props\n   */\n  icon;\n  /**\n   * Defines the image to display.\n   * @group Props\n   */\n  image;\n  /**\n   * Alt attribute of the image.\n   * @group Props\n   */\n  alt;\n  /**\n   * Inline style of the element.\n   * @group Props\n   */\n  style;\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Whether to display a remove icon.\n   * @group Props\n   */\n  removable = false;\n  /**\n   * Icon of the remove element.\n   * @group Props\n   */\n  removeIcon;\n  /**\n   * Callback to invoke when a chip is removed.\n   * @param {MouseEvent} event - Mouse event.\n   * @group Emits\n   */\n  onRemove = new EventEmitter();\n  /**\n   * This event is triggered if an error occurs while loading an image file.\n   * @param {Event} event - Browser event.\n   * @group Emits\n   */\n  onImageError = new EventEmitter();\n  visible = true;\n  get removeAriaLabel() {\n    return this.config.getTranslation(TranslationKeys.ARIA)['removeLabel'];\n  }\n  /**\n   * Used to pass all properties of the chipProps to the Chip component.\n   * @group Props\n   */\n  get chipProps() {\n    return this._chipProps;\n  }\n  set chipProps(val) {\n    this._chipProps = val;\n    if (val && typeof val === 'object') {\n      //@ts-ignore\n      Object.entries(val).forEach(([k, v]) => this[`_${k}`] !== v && (this[`_${k}`] = v));\n    }\n  }\n  _chipProps;\n  _componentStyle = inject(ChipStyle);\n  removeIconTemplate;\n  templates;\n  _removeIconTemplate;\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'removeicon':\n          this._removeIconTemplate = item.template;\n          break;\n        default:\n          this._removeIconTemplate = item.template;\n          break;\n      }\n    });\n  }\n  ngOnChanges(simpleChanges) {\n    super.ngOnChanges(simpleChanges);\n    if (simpleChanges.chipProps && simpleChanges.chipProps.currentValue) {\n      const {\n        currentValue\n      } = simpleChanges.chipProps;\n      if (currentValue.label !== undefined) {\n        this.label = currentValue.label;\n      }\n      if (currentValue.icon !== undefined) {\n        this.icon = currentValue.icon;\n      }\n      if (currentValue.image !== undefined) {\n        this.image = currentValue.image;\n      }\n      if (currentValue.alt !== undefined) {\n        this.alt = currentValue.alt;\n      }\n      if (currentValue.style !== undefined) {\n        this.style = currentValue.style;\n      }\n      if (currentValue.styleClass !== undefined) {\n        this.styleClass = currentValue.styleClass;\n      }\n      if (currentValue.removable !== undefined) {\n        this.removable = currentValue.removable;\n      }\n      if (currentValue.removeIcon !== undefined) {\n        this.removeIcon = currentValue.removeIcon;\n      }\n    }\n  }\n  containerClass() {\n    let classes = 'p-chip p-component';\n    if (this.styleClass) {\n      classes += ` ${this.styleClass}`;\n    }\n    return classes;\n  }\n  close(event) {\n    this.visible = false;\n    this.onRemove.emit(event);\n  }\n  onKeydown(event) {\n    if (event.key === 'Enter' || event.key === 'Backspace') {\n      this.close(event);\n    }\n  }\n  imageError(event) {\n    this.onImageError.emit(event);\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵChip_BaseFactory;\n    return function Chip_Factory(__ngFactoryType__) {\n      return (ɵChip_BaseFactory || (ɵChip_BaseFactory = i0.ɵɵgetInheritedFactory(Chip)))(__ngFactoryType__ || Chip);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Chip,\n    selectors: [[\"p-chip\"]],\n    contentQueries: function Chip_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, _c0, 4);\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.removeIconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    hostVars: 9,\n    hostBindings: function Chip_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"data-pc-name\", \"chip\")(\"aria-label\", ctx.label)(\"data-pc-section\", \"root\");\n        i0.ɵɵstyleMap(ctx.style);\n        i0.ɵɵclassMap(ctx.containerClass());\n        i0.ɵɵstyleProp(\"display\", !ctx.visible && \"none\");\n      }\n    },\n    inputs: {\n      label: \"label\",\n      icon: \"icon\",\n      image: \"image\",\n      alt: \"alt\",\n      style: \"style\",\n      styleClass: \"styleClass\",\n      removable: [2, \"removable\", \"removable\", booleanAttribute],\n      removeIcon: \"removeIcon\",\n      chipProps: \"chipProps\"\n    },\n    outputs: {\n      onRemove: \"onRemove\",\n      onImageError: \"onImageError\"\n    },\n    features: [i0.ɵɵProvidersFeature([ChipStyle]), i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c1,\n    decls: 6,\n    vars: 4,\n    consts: [[\"iconTemplate\", \"\"], [\"class\", \"p-chip-image\", 3, \"src\", \"alt\", \"error\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"p-chip-label\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"p-chip-image\", 3, \"error\", \"src\", \"alt\"], [3, \"class\", \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\"], [1, \"p-chip-label\"], [\"tabindex\", \"0\", \"class\", \"p-chip-remove-icon\", \"role\", \"button\", 3, \"click\", \"keydown\", 4, \"ngIf\"], [\"tabindex\", \"0\", \"role\", \"button\", 3, \"class\", \"ngClass\", \"click\", \"keydown\", 4, \"ngIf\"], [\"tabindex\", \"0\", \"role\", \"button\", 3, \"class\", \"click\", \"keydown\", 4, \"ngIf\"], [\"tabindex\", \"0\", \"role\", \"button\", 3, \"click\", \"keydown\", \"ngClass\"], [\"tabindex\", \"0\", \"role\", \"button\", 3, \"click\", \"keydown\"], [\"tabindex\", \"0\", \"role\", \"button\", 1, \"p-chip-remove-icon\", 3, \"click\", \"keydown\"], [4, \"ngTemplateOutlet\"]],\n    template: function Chip_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n        i0.ɵɵtemplate(1, Chip_img_1_Template, 1, 2, \"img\", 1)(2, Chip_ng_template_2_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(4, Chip_div_4_Template, 2, 2, \"div\", 2)(5, Chip_ng_container_5_Template, 3, 2, \"ng-container\", 3);\n      }\n      if (rf & 2) {\n        const iconTemplate_r6 = i0.ɵɵreference(3);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.image)(\"ngIfElse\", iconTemplate_r6);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.label);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.removable);\n      }\n    },\n    dependencies: [CommonModule, i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, TimesCircleIcon, SharedModule],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Chip, [{\n    type: Component,\n    args: [{\n      selector: 'p-chip',\n      standalone: true,\n      imports: [CommonModule, TimesCircleIcon, SharedModule],\n      template: `\n        <ng-content></ng-content>\n        <img class=\"p-chip-image\" [src]=\"image\" *ngIf=\"image; else iconTemplate\" (error)=\"imageError($event)\" [alt]=\"alt\" />\n        <ng-template #iconTemplate><span *ngIf=\"icon\" [class]=\"icon\" [ngClass]=\"'p-chip-icon'\" [attr.data-pc-section]=\"'icon'\"></span></ng-template>\n        <div class=\"p-chip-label\" *ngIf=\"label\" [attr.data-pc-section]=\"'label'\">{{ label }}</div>\n        <ng-container *ngIf=\"removable\">\n            <ng-container *ngIf=\"!removeIconTemplate && !_removeIconTemplate\">\n                <span\n                    tabindex=\"0\"\n                    *ngIf=\"removeIcon\"\n                    [class]=\"removeIcon\"\n                    [ngClass]=\"'p-chip-remove-icon'\"\n                    [attr.data-pc-section]=\"'removeicon'\"\n                    (click)=\"close($event)\"\n                    (keydown)=\"onKeydown($event)\"\n                    [attr.aria-label]=\"removeAriaLabel\"\n                    role=\"button\"\n                ></span>\n                <TimesCircleIcon tabindex=\"0\" *ngIf=\"!removeIcon\" [class]=\"'p-chip-remove-icon'\" [attr.data-pc-section]=\"'removeicon'\" (click)=\"close($event)\" (keydown)=\"onKeydown($event)\" [attr.aria-label]=\"removeAriaLabel\" role=\"button\" />\n            </ng-container>\n            <span *ngIf=\"removeIconTemplate || _removeIconTemplate\" tabindex=\"0\" [attr.data-pc-section]=\"'removeicon'\" class=\"p-chip-remove-icon\" (click)=\"close($event)\" (keydown)=\"onKeydown($event)\" [attr.aria-label]=\"removeAriaLabel\" role=\"button\">\n                <ng-template *ngTemplateOutlet=\"removeIconTemplate || _removeIconTemplate\"></ng-template>\n            </span>\n        </ng-container>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [ChipStyle],\n      host: {\n        '[class]': 'containerClass()',\n        '[style]': 'style',\n        '[style.display]': '!visible && \"none\"',\n        '[attr.data-pc-name]': \"'chip'\",\n        '[attr.aria-label]': 'label',\n        '[attr.data-pc-section]': \"'root'\"\n      }\n    }]\n  }], null, {\n    label: [{\n      type: Input\n    }],\n    icon: [{\n      type: Input\n    }],\n    image: [{\n      type: Input\n    }],\n    alt: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    removable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    removeIcon: [{\n      type: Input\n    }],\n    onRemove: [{\n      type: Output\n    }],\n    onImageError: [{\n      type: Output\n    }],\n    chipProps: [{\n      type: Input\n    }],\n    removeIconTemplate: [{\n      type: ContentChild,\n      args: ['removeicon', {\n        descendants: false\n      }]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass ChipModule {\n  static ɵfac = function ChipModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ChipModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ChipModule,\n    imports: [Chip, SharedModule],\n    exports: [Chip, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [Chip, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ChipModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Chip, SharedModule],\n      exports: [Chip, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Chip, ChipClasses, ChipModule, ChipStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,IAAM,MAAM,CAAC,YAAY;AACzB,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,oBAAoB,IAAI,KAAK;AACpC,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,SAAS,SAAS,yCAAyC,QAAQ;AAC/E,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,WAAW,MAAM,CAAC;AAAA,IACjD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,OAAO,OAAU,aAAa,EAAE,OAAO,OAAO,GAAG;AAAA,EACxE;AACF;AACA,SAAS,mCAAmC,IAAI,KAAK;AACnD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,IAAI;AACzB,IAAG,WAAW,WAAW,aAAa;AACtC,IAAG,YAAY,mBAAmB,MAAM;AAAA,EAC1C;AACF;AACA,SAAS,4BAA4B,IAAI,KAAK;AAC5C,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,oCAAoC,GAAG,GAAG,QAAQ,CAAC;AAAA,EACtE;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,QAAQ,OAAO,IAAI;AAAA,EACnC;AACF;AACA,SAAS,oBAAoB,IAAI,KAAK;AACpC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,mBAAmB,OAAO;AACzC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,KAAK;AAAA,EACnC;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,WAAW,SAAS,SAAS,yEAAyE,QAAQ;AAC/G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,MAAM,MAAM,CAAC;AAAA,IAC5C,CAAC,EAAE,WAAW,SAAS,2EAA2E,QAAQ;AACxG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,UAAU,MAAM,CAAC;AAAA,IAChD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,UAAU;AAC/B,IAAG,WAAW,WAAW,oBAAoB;AAC7C,IAAG,YAAY,mBAAmB,YAAY,EAAE,cAAc,OAAO,eAAe;AAAA,EACtF;AACF;AACA,SAAS,8DAA8D,IAAI,KAAK;AAC9E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,mBAAmB,EAAE;AAC1C,IAAG,WAAW,SAAS,SAAS,+FAA+F,QAAQ;AACrI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,MAAM,MAAM,CAAC;AAAA,IAC5C,CAAC,EAAE,WAAW,SAAS,iGAAiG,QAAQ;AAC9H,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,UAAU,MAAM,CAAC;AAAA,IAChD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,oBAAoB;AAClC,IAAG,YAAY,mBAAmB,YAAY,EAAE,cAAc,OAAO,eAAe;AAAA,EACtF;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,+DAA+D,GAAG,GAAG,mBAAmB,EAAE;AACnL,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,UAAU;AACvC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,UAAU;AAAA,EAC1C;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AAAC;AACvE,SAAS,sCAAsC,IAAI,KAAK;AACtD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,aAAa;AAAA,EAC3F;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,WAAW,SAAS,SAAS,0DAA0D,QAAQ;AAChG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,MAAM,MAAM,CAAC;AAAA,IAC5C,CAAC,EAAE,WAAW,SAAS,4DAA4D,QAAQ;AACzF,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,UAAU,MAAM,CAAC;AAAA,IAChD,CAAC;AACD,IAAG,WAAW,GAAG,uCAAuC,GAAG,GAAG,MAAM,EAAE;AACtE,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,mBAAmB,YAAY,EAAE,cAAc,OAAO,eAAe;AACpF,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,sBAAsB,OAAO,mBAAmB;AAAA,EAC3F;AACF;AACA,SAAS,6BAA6B,IAAI,KAAK;AAC7C,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,qCAAqC,GAAG,GAAG,QAAQ,CAAC;AAC9I,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,sBAAsB,CAAC,OAAO,mBAAmB;AAC/E,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,sBAAsB,OAAO,mBAAmB;AAAA,EAC/E;AACF;AACA,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA;AAAA;AAAA,kBAIY,GAAG,iBAAiB,CAAC;AAAA,aAC1B,GAAG,YAAY,CAAC;AAAA,qBACR,GAAG,oBAAoB,CAAC;AAAA,eAC9B,GAAG,gBAAgB,CAAC,IAAI,GAAG,gBAAgB,CAAC;AAAA,WAChD,GAAG,UAAU,CAAC;AAAA;AAAA;AAAA;AAAA,aAIZ,GAAG,iBAAiB,CAAC;AAAA,iBACjB,GAAG,qBAAqB,CAAC;AAAA,aAC7B,GAAG,gBAAgB,CAAC;AAAA,cACnB,GAAG,gBAAgB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,aAKrB,GAAG,kBAAkB,CAAC;AAAA,cACrB,GAAG,mBAAmB,CAAC;AAAA,6BACR,GAAG,gBAAgB,CAAC;AAAA;AAAA;AAAA;AAAA,0BAIvB,GAAG,gBAAgB,CAAC;AAAA;AAAA;AAAA;AAAA,wBAItB,GAAG,gBAAgB,CAAC;AAAA,2BACjB,GAAG,gBAAgB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,iBAK9B,GAAG,4BAA4B,CAAC;AAAA,aACpC,GAAG,uBAAuB,CAAC;AAAA,cAC1B,GAAG,uBAAuB,CAAC;AAAA,aAC5B,GAAG,wBAAwB,CAAC;AAAA;AAAA,gCAET,GAAG,0BAA0B,CAAC,gBAAgB,GAAG,0BAA0B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,kBAK1F,GAAG,oCAAoC,CAAC;AAAA,eAC3C,GAAG,mCAAmC,CAAC,IAAI,GAAG,mCAAmC,CAAC,IAAI,GAAG,mCAAmC,CAAC;AAAA,sBACtH,GAAG,oCAAoC,CAAC;AAAA;AAAA;AAG9D,IAAM,UAAU;AAAA,EACd,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,OAAO;AAAA,EACP,YAAY;AACd;AACA,IAAM,YAAN,MAAM,mBAAkB,UAAU;AAAA,EAChC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,kBAAkB,mBAAmB;AACnD,cAAQ,2BAA2B,yBAA4B,sBAAsB,UAAS,IAAI,qBAAqB,UAAS;AAAA,IAClI;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,WAAU;AAAA,EACrB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,cAAa;AAItB,EAAAA,aAAY,MAAM,IAAI;AAItB,EAAAA,aAAY,OAAO,IAAI;AAIvB,EAAAA,aAAY,MAAM,IAAI;AAItB,EAAAA,aAAY,OAAO,IAAI;AAIvB,EAAAA,aAAY,YAAY,IAAI;AAC9B,GAAG,gBAAgB,cAAc,CAAC,EAAE;AAMpC,IAAM,OAAN,MAAM,cAAa,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM5B,eAAe,IAAI,aAAa;AAAA,EAChC,UAAU;AAAA,EACV,IAAI,kBAAkB;AACpB,WAAO,KAAK,OAAO,eAAe,gBAAgB,IAAI,EAAE,aAAa;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,KAAK;AACjB,SAAK,aAAa;AAClB,QAAI,OAAO,OAAO,QAAQ,UAAU;AAElC,aAAO,QAAQ,GAAG,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,MAAM,MAAM,KAAK,IAAI,CAAC,EAAE,IAAI,EAAE;AAAA,IACpF;AAAA,EACF;AAAA,EACA;AAAA,EACA,kBAAkB,OAAO,SAAS;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA,qBAAqB;AACnB,SAAK,UAAU,QAAQ,UAAQ;AAC7B,cAAQ,KAAK,QAAQ,GAAG;AAAA,QACtB,KAAK;AACH,eAAK,sBAAsB,KAAK;AAChC;AAAA,QACF;AACE,eAAK,sBAAsB,KAAK;AAChC;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,YAAY,eAAe;AACzB,UAAM,YAAY,aAAa;AAC/B,QAAI,cAAc,aAAa,cAAc,UAAU,cAAc;AACnE,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,cAAc;AAClB,UAAI,aAAa,UAAU,QAAW;AACpC,aAAK,QAAQ,aAAa;AAAA,MAC5B;AACA,UAAI,aAAa,SAAS,QAAW;AACnC,aAAK,OAAO,aAAa;AAAA,MAC3B;AACA,UAAI,aAAa,UAAU,QAAW;AACpC,aAAK,QAAQ,aAAa;AAAA,MAC5B;AACA,UAAI,aAAa,QAAQ,QAAW;AAClC,aAAK,MAAM,aAAa;AAAA,MAC1B;AACA,UAAI,aAAa,UAAU,QAAW;AACpC,aAAK,QAAQ,aAAa;AAAA,MAC5B;AACA,UAAI,aAAa,eAAe,QAAW;AACzC,aAAK,aAAa,aAAa;AAAA,MACjC;AACA,UAAI,aAAa,cAAc,QAAW;AACxC,aAAK,YAAY,aAAa;AAAA,MAChC;AACA,UAAI,aAAa,eAAe,QAAW;AACzC,aAAK,aAAa,aAAa;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,QAAIC,WAAU;AACd,QAAI,KAAK,YAAY;AACnB,MAAAA,YAAW,IAAI,KAAK,UAAU;AAAA,IAChC;AACA,WAAOA;AAAA,EACT;AAAA,EACA,MAAM,OAAO;AACX,SAAK,UAAU;AACf,SAAK,SAAS,KAAK,KAAK;AAAA,EAC1B;AAAA,EACA,UAAU,OAAO;AACf,QAAI,MAAM,QAAQ,WAAW,MAAM,QAAQ,aAAa;AACtD,WAAK,MAAM,KAAK;AAAA,IAClB;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,aAAa,KAAK,KAAK;AAAA,EAC9B;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,aAAa,mBAAmB;AAC9C,cAAQ,sBAAsB,oBAAuB,sBAAsB,KAAI,IAAI,qBAAqB,KAAI;AAAA,IAC9G;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,QAAQ,CAAC;AAAA,IACtB,gBAAgB,SAAS,oBAAoB,IAAI,KAAK,UAAU;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB,GAAG;AACzE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,UAAU;AAAA,IACV,cAAc,SAAS,kBAAkB,IAAI,KAAK;AAChD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,gBAAgB,MAAM,EAAE,cAAc,IAAI,KAAK,EAAE,mBAAmB,MAAM;AACzF,QAAG,WAAW,IAAI,KAAK;AACvB,QAAG,WAAW,IAAI,eAAe,CAAC;AAClC,QAAG,YAAY,WAAW,CAAC,IAAI,WAAW,MAAM;AAAA,MAClD;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,MACL,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,WAAW,CAAC,GAAG,aAAa,aAAa,gBAAgB;AAAA,MACzD,YAAY;AAAA,MACZ,WAAW;AAAA,IACb;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,SAAS,CAAC,GAAM,0BAA6B,4BAA+B,oBAAoB;AAAA,IAClI,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,gBAAgB,EAAE,GAAG,CAAC,SAAS,gBAAgB,GAAG,OAAO,OAAO,SAAS,GAAG,QAAQ,UAAU,GAAG,CAAC,SAAS,gBAAgB,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,gBAAgB,GAAG,SAAS,OAAO,KAAK,GAAG,CAAC,GAAG,SAAS,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,YAAY,KAAK,SAAS,sBAAsB,QAAQ,UAAU,GAAG,SAAS,WAAW,GAAG,MAAM,GAAG,CAAC,YAAY,KAAK,QAAQ,UAAU,GAAG,SAAS,WAAW,SAAS,WAAW,GAAG,MAAM,GAAG,CAAC,YAAY,KAAK,QAAQ,UAAU,GAAG,SAAS,SAAS,WAAW,GAAG,MAAM,GAAG,CAAC,YAAY,KAAK,QAAQ,UAAU,GAAG,SAAS,WAAW,SAAS,GAAG,CAAC,YAAY,KAAK,QAAQ,UAAU,GAAG,SAAS,SAAS,GAAG,CAAC,YAAY,KAAK,QAAQ,UAAU,GAAG,sBAAsB,GAAG,SAAS,SAAS,GAAG,CAAC,GAAG,kBAAkB,CAAC;AAAA,IACtxB,UAAU,SAAS,cAAc,IAAI,KAAK;AACxC,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AACjB,QAAG,WAAW,GAAG,qBAAqB,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,6BAA6B,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,qBAAqB,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,8BAA8B,GAAG,GAAG,gBAAgB,CAAC;AAAA,MACjP;AACA,UAAI,KAAK,GAAG;AACV,cAAM,kBAAqB,YAAY,CAAC;AACxC,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,KAAK,EAAE,YAAY,eAAe;AAC5D,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,QAAQ,IAAI,KAAK;AAC/B,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,SAAS;AAAA,MACrC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAiB,SAAY,MAAS,kBAAkB,iBAAiB,YAAY;AAAA,IACpG,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,MAAM,CAAC;AAAA,IAC7E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,iBAAiB,YAAY;AAAA,MACrD,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAyBV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,SAAS;AAAA,MACrB,MAAM;AAAA,QACJ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,mBAAmB;AAAA,QACnB,uBAAuB;AAAA,QACvB,qBAAqB;AAAA,QACrB,0BAA0B;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,QACnB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,OAAO,OAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAY;AAAA,EAC/C;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,MAAM,YAAY;AAAA,IAC5B,SAAS,CAAC,MAAM,YAAY;AAAA,EAC9B,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,MAAM,cAAc,YAAY;AAAA,EAC5C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,MAAM,YAAY;AAAA,MAC5B,SAAS,CAAC,MAAM,YAAY;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["ChipClasses", "classes"]}
﻿using MediatR;
using TRtek_EYS.Application.Features.Auth.Login;

namespace TRtek_EYS.Application.Features.Auth.Register
{
    public class RegisterCommand : IRequest<LoginCommandResponse>
    {
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string? Email { get; set; }
    }
}

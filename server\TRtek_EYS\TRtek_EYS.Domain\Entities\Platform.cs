﻿using TRtek_EYS.Domain.Enums;

namespace TRtek_EYS.Domain.Entities;

public class Platform : BaseEntity
{
    // MEVCUT ALANLAR
    public PlatformType Type { get; set; }
    public Guid ProjectId { get; set; }
    public Project? Project { get; set; }

    // YENİ ALANLAR
    public string? Name { get; set; }
    public string? PlatformCode { get; set; }
    public string? Description { get; set; }
    public PlatformCategory? Category { get; set; }
    public string? Version { get; set; }
    public string? Vendor { get; set; }
    public string? Website { get; set; }
    public string? Documentation { get; set; }
    public SupportLevel? SupportLevel { get; set; }
    public LicenseType? LicenseType { get; set; }
    public decimal? Cost { get; set; }
    public string? Currency { get; set; }
    public bool IsActive { get; set; } = true;
    public bool IsSupported { get; set; } = true;
    public DateTime? ReleaseDate { get; set; }
    public DateTime? EndOfLifeDate { get; set; }
    public string? Tags { get; set; }
    public string? Notes { get; set; }
    public int ProjectCount { get; set; } = 0;
}

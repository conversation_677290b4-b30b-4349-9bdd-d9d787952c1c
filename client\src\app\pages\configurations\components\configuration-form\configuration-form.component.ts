import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { Textarea } from 'primeng/inputtextarea';
import { Select } from 'primeng/select';
import { ToastModule } from 'primeng/toast';
import { MessageService } from 'primeng/api';
import { Environment, ConfigurationStatus } from '../../models/configuration.model';

@Component({
    selector: 'app-configuration-form',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        ButtonModule,
        InputTextModule,
        Textarea,
        Select,
        ToastModule
    ],
    providers: [MessageService],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <h5>{{ isEditMode ? 'Konfigürasyon Düzenle' : 'Yeni Konfigürasyon' }}</h5>
                        <p-button 
                            label="Geri" 
                            icon="pi pi-arrow-left" 
                            class="p-button-secondary"
                            (onClick)="goBack()">
                        </p-button>
                    </div>

                    <form [formGroup]="configForm" (ngSubmit)="onSubmit()">
                        <div class="grid">
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="configurationCode" class="font-medium">Konfigürasyon Kodu *</label>
                                    <input 
                                        id="configurationCode"
                                        type="text" 
                                        pInputText 
                                        formControlName="configurationCode"
                                        [readonly]="isEditMode"
                                        class="w-full"
                                        placeholder="Otomatik oluşturulacak" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="configurationName" class="font-medium">Konfigürasyon Adı *</label>
                                    <input 
                                        id="configurationName"
                                        type="text" 
                                        pInputText 
                                        formControlName="configurationName"
                                        class="w-full"
                                        placeholder="Konfigürasyon adı" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="customerName" class="font-medium">Müşteri *</label>
                                    <input 
                                        id="customerName"
                                        type="text" 
                                        pInputText 
                                        formControlName="customerName"
                                        class="w-full"
                                        placeholder="Müşteri adı" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="projectName" class="font-medium">Proje *</label>
                                    <input 
                                        id="projectName"
                                        type="text" 
                                        pInputText 
                                        formControlName="projectName"
                                        class="w-full"
                                        placeholder="Proje adı" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="environment" class="font-medium">Ortam *</label>
                                    <p-select 
                                        id="environment"
                                        formControlName="environment"
                                        [options]="environmentOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Ortam seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="status" class="font-medium">Durum *</label>
                                    <p-select 
                                        id="status"
                                        formControlName="status"
                                        [options]="statusOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Durum seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field">
                                    <label for="description" class="font-medium">Açıklama</label>
                                    <p-textarea 
                                        id="description"
                                        formControlName="description"
                                        rows="3"
                                        class="w-full"
                                        placeholder="Konfigürasyon açıklaması...">
                                    </p-textarea>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-content-end gap-2 mt-4">
                            <p-button 
                                label="İptal" 
                                icon="pi pi-times" 
                                class="p-button-secondary"
                                type="button"
                                (onClick)="goBack()">
                            </p-button>
                            <p-button 
                                [label]="isEditMode ? 'Güncelle' : 'Kaydet'" 
                                icon="pi pi-check" 
                                class="p-button-success"
                                type="submit"
                                [disabled]="configForm.invalid">
                            </p-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <p-toast></p-toast>
    `
})
export class ConfigurationFormComponent implements OnInit {
    configForm!: FormGroup;
    isEditMode = false;
    configurationId: string = '';

    environmentOptions = [
        { label: 'Geliştirme', value: Environment.DEVELOPMENT },
        { label: 'Test', value: Environment.TESTING },
        { label: 'Hazırlık', value: Environment.STAGING },
        { label: 'Üretim', value: Environment.PRODUCTION },
        { label: 'Demo', value: Environment.DEMO }
    ];

    statusOptions = [
        { label: 'Taslak', value: ConfigurationStatus.DRAFT },
        { label: 'Aktif', value: ConfigurationStatus.ACTIVE },
        { label: 'Pasif', value: ConfigurationStatus.INACTIVE },
        { label: 'Beklemede', value: ConfigurationStatus.PENDING }
    ];

    constructor(
        private fb: FormBuilder,
        private route: ActivatedRoute,
        private router: Router,
        private messageService: MessageService
    ) {}

    ngOnInit() {
        this.initForm();
        this.checkEditMode();
    }

    initForm() {
        this.configForm = this.fb.group({
            configurationCode: ['', Validators.required],
            configurationName: ['', Validators.required],
            customerName: ['', Validators.required],
            projectName: ['', Validators.required],
            environment: [Environment.DEVELOPMENT, Validators.required],
            status: [ConfigurationStatus.DRAFT, Validators.required],
            description: ['']
        });

        if (!this.isEditMode) {
            this.generateConfigurationCode();
        }
    }

    checkEditMode() {
        this.configurationId = this.route.snapshot.params['id'];
        this.isEditMode = !!this.configurationId && this.router.url.includes('edit');
        
        if (this.isEditMode) {
            this.loadConfiguration();
        }
    }

    generateConfigurationCode() {
        const year = new Date().getFullYear();
        const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        const configCode = `CFG-${year}-${randomNum}`;
        this.configForm.patchValue({ configurationCode: configCode });
    }

    loadConfiguration() {
        // Dummy data for edit mode
        const dummyConfig = {
            configurationCode: 'CFG-2024-001',
            configurationName: 'Üretim Ortamı Konfigürasyonu',
            customerName: 'ABC Teknoloji A.Ş.',
            projectName: 'E-Ticaret Platformu',
            environment: Environment.PRODUCTION,
            status: ConfigurationStatus.ACTIVE,
            description: 'E-ticaret platformu için üretim ortamı ayarları'
        };

        this.configForm.patchValue(dummyConfig);
    }

    onSubmit() {
        if (this.configForm.valid) {
            this.messageService.add({
                severity: 'success',
                summary: 'Başarılı',
                detail: this.isEditMode ? 'Konfigürasyon başarıyla güncellendi' : 'Konfigürasyon başarıyla oluşturuldu'
            });

            setTimeout(() => {
                this.router.navigate(['/pages/configurations']);
            }, 1500);
        }
    }

    goBack() {
        this.router.navigate(['/pages/configurations']);
    }
}

import { Routes } from '@angular/router';

export default [
    {
        path: '',
        loadComponent: () => import('./components/platforms-list/platforms-list.component').then(m => m.PlatformsListComponent)
    },
    {
        path: 'new',
        loadComponent: () => import('./components/platform-form/platform-form.component').then(m => m.PlatformFormComponent)
    },
    {
        path: ':id',
        loadComponent: () => import('./components/platform-detail/platform-detail.component').then(m => m.PlatformDetailComponent)
    },
    {
        path: ':id/edit',
        loadComponent: () => import('./components/platform-form/platform-form.component').then(m => m.PlatformFormComponent)
    }
] as Routes;

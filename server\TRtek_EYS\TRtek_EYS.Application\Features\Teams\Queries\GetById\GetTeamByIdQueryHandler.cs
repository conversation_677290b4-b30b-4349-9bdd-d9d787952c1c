﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Teams.Queries.GetById
{
    public class GetTeamByIdQueryHandler : IRequestHandler<GetTeamByIdQuery, TeamGetByIdDto>
    {
        private readonly IApplicationDbContext _context;

        public GetTeamByIdQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<TeamGetByIdDto> Handle(GetTeamByIdQuery request, CancellationToken cancellationToken)
        {
            var team = await _context.Teams
                .AsNoTracking()
                .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);

            if (team is null)
                throw new Exception("Takım bulunamadı.");

            return new TeamGetByIdDto
            {
                Id = team.Id,
                Name = team.Name,
                Description = team.Description
            };
        }
    }
}

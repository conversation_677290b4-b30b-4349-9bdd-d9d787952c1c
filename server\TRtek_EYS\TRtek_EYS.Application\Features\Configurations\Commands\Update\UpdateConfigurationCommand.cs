﻿using MediatR;
using System;

namespace TRtek_EYS.Application.Features.Configurations.Commands.Update
{
    public class UpdateConfigurationCommand : IRequest<Guid>
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = null!;
        public string? Description { get; set; }
        public string? WebServerIp { get; set; }
        public string? WebServerUrl { get; set; }
        public string? DatabaseType { get; set; }
        public string? DatabaseVersion { get; set; }
        public string? Username { get; set; }
        public string? EncryptedPassword { get; set; }
        public Guid CustomerId { get; set; }
    }
}

import { Routes } from '@angular/router';
import { Permission } from '../../core/models/permission.model';
import { permissionGuard } from '../../core/guards/permission.guard';

export const TEAMS_ROUTES: Routes = [
    {
        path: '',
        data: { breadcrumb: 'Teams' },
        children: [
            {
                path: '',
                loadComponent: () => 
                    import('./components/teams-list/teams-list.component')
                    .then(m => m.TeamsListComponent),
                data: { breadcrumb: null }
            },
            {
                path: 'create',
                loadComponent: () => 
                    import('./components/team-form/team-form.component')
                    .then(m => m.TeamFormComponent),
                data: { breadcrumb: 'Create Team' },
                canActivate: [permissionGuard([Permission.TEAMS_CREATE])]
            },
            {
                path: ':id',
                loadComponent: () => 
                    import('./components/team-detail/team-detail.component')
                    .then(m => m.TeamDetailComponent),
                data: { breadcrumb: 'Team Details' },
                canActivate: [permissionGuard([Permission.TEAMS_VIEW])]
            },
            {
                path: ':id/edit',
                loadComponent: () => 
                    import('./components/team-form/team-form.component')
                    .then(m => m.TeamFormComponent),
                data: { breadcrumb: 'Edit Team' },
                canActivate: [permissionGuard([Permission.TEAMS_EDIT])]
            }
        ]
    }
];

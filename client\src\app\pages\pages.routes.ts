import { Routes } from '@angular/router';
import { Documentation } from './documentation/documentation';
import { Crud } from './crud/crud';
import { Empty } from './empty/empty';
import { TEAMS_ROUTES } from './teams/teams.routes';

export default [
    { path: 'teams', children: TEAMS_ROUTES },
    {
        path: 'projects',
        loadChildren: () => import('./projects/projects.routes')
    },
    {
        path: 'customers',
        loadChildren: () => import('./customers/customers.routes')
    },
    {
        path: 'contracts',
        loadChildren: () => import('./contracts/contracts.routes')
    },
    {
        path: 'configurations',
        loadChildren: () => import('./configurations/configurations.routes')
    },
    {
        path: 'assignments',
        loadChildren: () => import('./assignments/assignments.routes')
    },
    {
        path: 'users',
        loadChildren: () => import('./users/users.routes')
    },
    {
        path: 'roles',
        loadChildren: () => import('./roles/roles.routes')
    },
    {
        path: 'platforms',
        loadChildren: () => import('./platforms/platforms.routes')
    },
    {
        path: 'technologies',
        loadChildren: () => import('./technologies/technologies.routes')
    },
    {
        path: 'documents',
        loadChildren: () => import('./documents/documents.routes')
    },
    { path: 'documentation', component: Documentation },
    { path: 'crud', component: Crud },
    { path: 'empty', component: Empty },
    { path: '**', redirectTo: '/notfound' }
] as Routes;

﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TRtek_EYS.Domain.Entities;

namespace TRtek_EYS.Infrastructure.Persistence.Configurations
{
    public class ProjectUserRoleConfiguration : IEntityTypeConfiguration<ProjectUserRole>
    {
        public void Configure(EntityTypeBuilder<ProjectUserRole> builder)
        {
            builder.<PERSON><PERSON><PERSON>(pur => new { pur.UserId, pur.ProjectId, pur.RoleId });

            builder
                .HasOne(pur => pur.User)
                .WithMany(u => u.ProjectUserRoles)
                .HasForeignKey(pur => pur.UserId);

            builder
                .HasOne(pur => pur.Project)
                .WithMany(p => p.ProjectUserRoles)
                .HasForeignKey(pur => pur.ProjectId);

            builder
                .HasOne(pur => pur.Role)
                .WithMany(r => r.ProjectUserRoles)
                .HasForeignKey(pur => pur.RoleId);

            builder
                .Property(pur => pur.AssignedAt)
                .HasDefaultValueSql("GETUTCDATE()");
        }
    }
}

import { Routes } from '@angular/router';

export default [
    {
        path: '',
        loadComponent: () => import('./components/assignments-list/assignments-list.component').then(m => m.AssignmentsListComponent)
    },
    {
        path: 'new',
        loadComponent: () => import('./components/assignment-form/assignment-form.component').then(m => m.AssignmentFormComponent)
    },
    {
        path: ':id',
        loadComponent: () => import('./components/assignment-detail/assignment-detail.component').then(m => m.AssignmentDetailComponent)
    },
    {
        path: ':id/edit',
        loadComponent: () => import('./components/assignment-form/assignment-form.component').then(m => m.AssignmentFormComponent)
    }
] as Routes;

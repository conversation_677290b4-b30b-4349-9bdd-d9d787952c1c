﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Platforms.Queries.GetAll
{
    public class GetAllPlatformsQueryHandler : IRequestHandler<GetAllPlatformsQuery, List<PlatformGetAllDto>>
    {
        private readonly IApplicationDbContext _context;

        public GetAllPlatformsQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<PlatformGetAllDto>> Handle(GetAllPlatformsQuery request, CancellationToken cancellationToken)
        {
            return await _context.Platforms
                .AsNoTracking()
                .Select(p => new PlatformGetAllDto
                {
                    Id = p.Id,
                    Type = p.Type.ToString(),
                    ProjectId = p.ProjectId
                })
                .ToListAsync(cancellationToken);
        }
    }
}

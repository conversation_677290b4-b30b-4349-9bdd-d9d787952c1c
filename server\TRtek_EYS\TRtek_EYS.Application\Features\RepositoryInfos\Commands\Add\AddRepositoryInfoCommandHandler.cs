﻿using MediatR;
using TRtek_EYS.Application.Common.Interfaces;
using TRtek_EYS.Domain.Entities;

namespace TRtek_EYS.Application.Features.RepositoryInfos.Commands.Add
{
    public class AddRepositoryInfoCommandHandler : IRe<PERSON>Handler<AddRepositoryInfoCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public AddRepositoryInfoCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(AddRepositoryInfoCommand request, CancellationToken cancellationToken)
        {
            var repo = new RepositoryInfo
            {
                Url = request.Url,
                Provider = request.Provider,
                IsPrivate = request.IsPrivate,
                ProjectId = request.ProjectId,
                CreatedAt = DateTime.UtcNow
            };

            await _context.RepositoryInfos.AddAsync(repo, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);

            return repo.Id;
        }
    }
}

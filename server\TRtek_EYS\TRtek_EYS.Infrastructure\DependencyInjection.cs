﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;
using TRtek_EYS.Application.Common.Interfaces;
using TRtek_EYS.Application.Services;
using TRtek_EYS.Infrastructure.Options;
using TRtek_EYS.Infrastructure.Persistence;
using TRtek_EYS.Infrastructure.Services;
using Npgsql.EntityFrameworkCore.PostgreSQL;

namespace TRtek_EYS.Infrastructure
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
        {
            // 1. DbContext ve Interface Mapping
            services.AddDbContext<ApplicationDbContext>(options =>
            {
                options.UseNpgsql(configuration.GetConnectionString("DefaultConnection"));
            });

            services.AddScoped<IApplicationDbContext>(provider => provider.GetRequiredService<ApplicationDbContext>());

            // 2. JWT Servisi ve Ayarları
            services.Configure<JwtOptions>(configuration.GetSection("JwtSettings"));
            services.AddScoped<IJwtProvider, JwtProvider>(); 


            // 3. Aktif Kullanıcı Servisi
            services.AddScoped<ICurrentUserService, CurrentUserService>();

            // 4. Assembly'den otomatik servis kaydı (opsiyonel ama önerilir)
            services.Scan(scan => scan
                .FromAssemblies(Assembly.GetExecutingAssembly())
                .AddClasses()
                .AsImplementedInterfaces()
                .WithScopedLifetime());

            return services;
        }
    }
}

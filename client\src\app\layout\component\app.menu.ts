import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MenuItem } from 'primeng/api';
import { AppMenuitem } from './app.menuitem';

@Component({
    selector: 'app-menu',
    standalone: true,
    imports: [CommonModule, AppMenuitem, RouterModule],
    template: `<ul class="layout-menu">
        <ng-container *ngFor="let item of model; let i = index">
            <li app-menuitem *ngIf="!item.separator" [item]="item" [index]="i" [root]="true"></li>
            <li *ngIf="item.separator" class="menu-separator"></li>
        </ng-container>
    </ul>`
})
export class AppMenu implements OnInit {
    model: MenuItem[] = [];

    ngOnInit() {
        this.model = [
            {
                label: 'Ana Sayfa',
                items: [{ label: 'Dashboard', icon: 'pi pi-fw pi-home', routerLink: ['/'] }]
            },
            {
                label: '<PERSON><PERSON>',
                items: [
                    { label: 'Projeler', icon: 'pi pi-fw pi-briefcase', routerLink: ['/pages/projects'] },
                    { label: 'Platformlar', icon: 'pi pi-fw pi-server', routerLink: ['/pages/platforms'] },
                    { label: 'Teknolojiler', icon: 'pi pi-fw pi-code', routerLink: ['/pages/technologies'] },
                    { label: 'Dokümanlar', icon: 'pi pi-fw pi-file', routerLink: ['/pages/documents'] }
                ]
            },
            {
                label: 'Müşteri Yönetimi',
                items: [
                    { label: 'Müşteriler', icon: 'pi pi-fw pi-building', routerLink: ['/pages/customers'] },
                    { label: 'Sözleşmeler', icon: 'pi pi-fw pi-file-edit', routerLink: ['/pages/contracts'] },
                    { label: 'Konfigürasyonlar', icon: 'pi pi-fw pi-cog', routerLink: ['/pages/configurations'] }
                ]
            },
            {
                label: 'Takım Yönetimi',
                items: [
                    { label: 'Takımlar', icon: 'pi pi-fw pi-users', routerLink: ['/pages/teams'] },
                    { label: 'Proje Atamaları', icon: 'pi pi-fw pi-sitemap', routerLink: ['/pages/assignments'] }
                ]
            },
            {
                label: 'Sistem Yönetimi',
                items: [
                    { label: 'Kullanıcılar', icon: 'pi pi-fw pi-user', routerLink: ['/pages/users'] },
                    { label: 'Roller', icon: 'pi pi-fw pi-shield', routerLink: ['/pages/roles'] }
                ]
            },
            { separator: true },
            {
                label: 'Geliştirici Araçları',
                items: [
                    {
                        label: 'UI Bileşenleri',
                        icon: 'pi pi-fw pi-palette',
                        items: [
                            { label: 'Form Layout', icon: 'pi pi-fw pi-id-card', routerLink: ['/uikit/formlayout'] },
                            { label: 'Input', icon: 'pi pi-fw pi-check-square', routerLink: ['/uikit/input'] },
                            { label: 'Button', icon: 'pi pi-fw pi-mobile', routerLink: ['/uikit/button'] },
                            { label: 'Table', icon: 'pi pi-fw pi-table', routerLink: ['/uikit/table'] },
                            { label: 'List', icon: 'pi pi-fw pi-list', routerLink: ['/uikit/list'] },
                            { label: 'Tree', icon: 'pi pi-fw pi-share-alt', routerLink: ['/uikit/tree'] },
                            { label: 'Panel', icon: 'pi pi-fw pi-tablet', routerLink: ['/uikit/panel'] },
                            { label: 'Overlay', icon: 'pi pi-fw pi-clone', routerLink: ['/uikit/overlay'] },
                            { label: 'Media', icon: 'pi pi-fw pi-image', routerLink: ['/uikit/media'] },
                            { label: 'Menu', icon: 'pi pi-fw pi-bars', routerLink: ['/uikit/menu'] },
                            { label: 'Message', icon: 'pi pi-fw pi-comment', routerLink: ['/uikit/message'] },
                            { label: 'File', icon: 'pi pi-fw pi-file', routerLink: ['/uikit/file'] },
                            { label: 'Chart', icon: 'pi pi-fw pi-chart-bar', routerLink: ['/uikit/charts'] },
                            { label: 'Timeline', icon: 'pi pi-fw pi-calendar', routerLink: ['/uikit/timeline'] },
                            { label: 'Misc', icon: 'pi pi-fw pi-circle', routerLink: ['/uikit/misc'] }
                        ]
                    }
                ]
            }
        ];
    }
}

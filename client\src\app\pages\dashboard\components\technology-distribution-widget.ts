import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';
import { ChartModule } from 'primeng/chart';
import { ButtonModule } from 'primeng/button';
import { SkeletonModule } from 'primeng/skeleton';
import { TechnologyService } from '../../technologies/services/technology.service';

@Component({
    standalone: true,
    selector: 'app-technology-distribution-widget',
    imports: [CommonModule, ChartModule, ButtonModule, SkeletonModule],
    template: `<div class="card !mb-8">
        <div class="flex justify-between align-items-center mb-5">
            <div class="font-semibold text-xl">Teknoloji Dağılımı</div>
            <div>
                <p-button
                    label="Backend"
                    [outlined]="selectedCategory !== 'Backend'"
                    severity="secondary"
                    size="small"
                    class="!mr-2"
                    (onClick)="filterByCategory('Backend')">
                </p-button>
                <p-button
                    label="Frontend"
                    [outlined]="selectedCategory !== 'Frontend'"
                    severity="secondary"
                    size="small"
                    class="!mr-2"
                    (onClick)="filterByCategory('Frontend')">
                </p-button>
                <p-button
                    label="Tümü"
                    [outlined]="selectedCategory !== 'All'"
                    severity="secondary"
                    size="small"
                    (onClick)="filterByCategory('All')">
                </p-button>
            </div>
        </div>

        <div *ngIf="isLoading" class="flex justify-content-center align-items-center" style="height: 300px;">
            <p-skeleton width="300px" height="300px" shape="circle"></p-skeleton>
        </div>

        <p-chart
            *ngIf="!isLoading && chartData"
            type="doughnut"
            [data]="chartData"
            [options]="chartOptions"
            height="300">
        </p-chart>

        <div *ngIf="!isLoading && (!chartData || chartData.datasets[0].data.length === 0)"
             class="text-center py-8">
            <i class="pi pi-info-circle text-4xl text-muted-color mb-4"></i>
            <div class="text-muted-color font-medium">Teknoloji verisi bulunamadı</div>
        </div>
    </div>`
})
export class TechnologyDistributionWidget implements OnInit, OnDestroy {
    private destroy$ = new Subject<void>();

    chartData: any = null;
    isLoading = true;
    selectedCategory = 'All';
    allTechnologies: any[] = [];

    chartOptions = {
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    usePointStyle: true,
                    padding: 20
                }
            },
            tooltip: {
                callbacks: {
                    label: function(context: any) {
                        const label = context.label || '';
                        const value = context.parsed || 0;
                        const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
                        const percentage = Math.round((value / total) * 100);
                        return `${label}: ${value} (${percentage}%)`;
                    }
                }
            }
        },
        cutout: '60%',
        responsive: true,
        maintainAspectRatio: false
    };

    constructor(private technologyService: TechnologyService) {}

    ngOnInit() {
        this.loadTechnologies();
    }

    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
    }

    loadTechnologies() {
        this.isLoading = true;

        this.technologyService.getTechnologies()
            .pipe(takeUntil(this.destroy$))
            .subscribe(technologies => {
                this.allTechnologies = technologies.filter(t => t.isActive);
                this.updateChart();
                this.isLoading = false;
            });
    }

    filterByCategory(category: string) {
        this.selectedCategory = category;
        this.updateChart();
    }

    updateChart() {
        let filteredTechnologies = this.allTechnologies;

        if (this.selectedCategory !== 'All') {
            filteredTechnologies = this.allTechnologies.filter(t => t.category === this.selectedCategory);
        }

        // Popülerlik skoruna göre sırala ve en popüler 8'ini al
        const topTechnologies = filteredTechnologies
            .sort((a, b) => b.popularityScore - a.popularityScore)
            .slice(0, 8);

        if (topTechnologies.length === 0) {
            this.chartData = null;
            return;
        }

        this.chartData = {
            labels: topTechnologies.map(t => t.name),
            datasets: [{
                data: topTechnologies.map(t => t.popularityScore),
                backgroundColor: [
                    '#6366F1', '#F59E0B', '#10B981', '#3B82F6',
                    '#8B5CF6', '#EF4444', '#06B6D4', '#84CC16'
                ],
                hoverBackgroundColor: [
                    '#5B5BD6', '#E08E0B', '#0F9F81', '#3374E6',
                    '#7C4DFF', '#E53E3E', '#0891B2', '#65A30D'
                ],
                borderWidth: 2,
                borderColor: '#ffffff'
            }]
        };
    }
}

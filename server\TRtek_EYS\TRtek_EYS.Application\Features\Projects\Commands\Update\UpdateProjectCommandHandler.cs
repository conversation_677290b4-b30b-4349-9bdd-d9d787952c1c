﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;
using TRtek_EYS.Domain.Enums;

namespace TRtek_EYS.Application.Features.Projects.Commands.Update
{
    public class UpdateProjectCommandHandler : IRequestHandler<UpdateProjectCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public UpdateProjectCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(UpdateProjectCommand request, CancellationToken cancellationToken)
        {
            var project = await _context.Projects
                .FirstOrDefaultAsync(p => p.Id == request.Id, cancellationToken);

            if (project is null)
                throw new Exception("Proje bulunamadı.");

            project.Name = request.Name;
            project.Code = request.Code;
            project.Description = request.Description;
            project.Status = (ProjectStatus)request.Status;
            project.StartDate = request.StartDate;
            project.EndDate = request.EndDate;
            project.CreatedByUserId = request.CreatedByUserId;

            await _context.SaveChangesAsync(cancellationToken);

            return project.Id;
        }
    }
}

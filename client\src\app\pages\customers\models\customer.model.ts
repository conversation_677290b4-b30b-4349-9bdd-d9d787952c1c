export interface Customer {
    id: string;
    customerCode: string;
    companyName: string;
    contactPerson: string;
    email: string;
    phone?: string;
    address?: string;
    city?: string;
    country?: string;
    website?: string;
    taxNumber?: string;
    industry: Industry;
    customerType: CustomerType;
    status: CustomerStatus;
    registrationDate: Date;
    lastContactDate?: Date;
    totalProjects: number;
    activeProjects: number;
    totalRevenue: number;
    notes?: string;
    contracts: CustomerContract[];
    projects: CustomerProject[];
}

export interface CustomerContract {
    id: string;
    contractNumber: string;
    projectId: string;
    projectName: string;
    contractDate: Date;
    startDate: Date;
    endDate?: Date;
    licenseType: LicenseType;
    contractValue: number;
    currency: string;
    status: ContractStatus;
    renewalDate?: Date;
    isActive: boolean;
}

export interface CustomerProject {
    id: string;
    projectId: string;
    projectName: string;
    projectCode: string;
    assignedDate: Date;
    currentVersion: string;
    lastUpdateDate: Date;
    status: string;
    supportLevel: SupportLevel;
}

export enum Industry {
    TECHNOLOGY = 'Teknoloji',
    FINANCE = 'Finans',
    HEALTHCARE = 'Sağlık',
    EDUCATION = 'Eğitim',
    RETAIL = 'Perakende',
    MANUFACTURING = 'İmalat',
    LOGISTICS = 'Lojistik',
    GOVERNMENT = 'Kamu',
    OTHER = 'Diğer'
}

export enum CustomerType {
    ENTERPRISE = 'Kurumsal',
    SME = 'KOBİ',
    STARTUP = 'Startup',
    GOVERNMENT = 'Kamu',
    NGO = 'STK',
    CORPORATE = "CORPORATE"
}

export enum CustomerStatus {
    ACTIVE = 'Aktif',
    INACTIVE = 'Pasif',
    PROSPECT = 'Potansiyel',
    SUSPENDED = 'Askıya Alınmış',
    TERMINATED = 'Sonlandırılmış',
    PENDING = "PENDING",
    POTENTIAL = "POTENTIAL"
}

export enum LicenseType {
    PERPETUAL = 'Kalıcı Lisans',
    SUBSCRIPTION = 'Abonelik',
    TRIAL = 'Deneme',
    CUSTOM = 'Özel',
    SAAS = 'SaaS'
}

export enum ContractStatus {
    ACTIVE = 'Aktif',
    EXPIRED = 'Süresi Dolmuş',
    PENDING = 'Beklemede',
    CANCELLED = 'İptal Edilmiş',
    RENEWED = 'Yenilenmiş'
}

export enum SupportLevel {
    BASIC = 'Temel',
    STANDARD = 'Standart',
    PREMIUM = 'Premium',
    ENTERPRISE = 'Kurumsal'
}

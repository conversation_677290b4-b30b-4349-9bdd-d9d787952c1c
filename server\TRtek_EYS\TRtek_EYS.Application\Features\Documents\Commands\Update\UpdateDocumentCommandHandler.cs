﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;
using TRtek_EYS.Domain.Enums;

namespace TRtek_EYS.Application.Features.Documents.Commands.Update
{
    public class UpdateDocumentCommandHandler : IRequestHandler<UpdateDocumentCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public UpdateDocumentCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(UpdateDocumentCommand request, CancellationToken cancellationToken)
        {
            var document = await _context.Documents
                .FirstOrDefaultAsync(d => d.Id == request.Id, cancellationToken);

            if (document is null)
                throw new Exception("Doküman bulunamadı.");

            document.Type = (DocumentType)request.Type;
            document.FileName = request.FileName;
            document.FilePath = request.FilePath;
            document.FileSize = request.FileSize;
            document.VersionNumber = request.VersionNumber;
            document.ProjectId = request.ProjectId;

            await _context.SaveChangesAsync(cancellationToken);

            return document.Id;
        }
    }
}

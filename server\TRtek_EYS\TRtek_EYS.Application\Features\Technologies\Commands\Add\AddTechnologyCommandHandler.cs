﻿using MediatR;
using TRtek_EYS.Application.Common.Interfaces;
using TRtek_EYS.Domain.Entities;

namespace TRtek_EYS.Application.Features.Technologies.Commands.Add
{
    public class AddTechnologyCommandHandler : IRequestHandler<AddTechnologyCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public AddTechnologyCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(AddTechnologyCommand request, CancellationToken cancellationToken)
        {
            var tech = new Technology
            {
                Language = request.Language,
                Framework = request.Framework,
                Version = request.Version,
                ProjectId = request.ProjectId,
                CreatedAt = DateTime.UtcNow
            };

            await _context.Technologies.AddAsync(tech, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);

            return tech.Id;
        }
    }
}

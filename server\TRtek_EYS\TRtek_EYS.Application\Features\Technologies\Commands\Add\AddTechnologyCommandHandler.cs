using MediatR;
using TRtek_EYS.Application.Common.Interfaces;
using TRtek_EYS.Domain.Entities;

namespace TRtek_EYS.Application.Features.Technologies.Commands.Add
{
    public class AddTechnologyCommandHandler : IRequestHandler<AddTechnologyCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public AddTechnologyCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(AddTechnologyCommand request, CancellationToken cancellationToken)
        {
            var tech = new Technology
            {
                // Yeni V2 alanları
                Name = request.Name,
                TechnologyCode = request.TechnologyCode ?? GenerateTechnologyCode(request.Name),
                Description = request.Description,
                Type = request.Type,
                Category = request.Category,
                Version = request.Version,
                Vendor = request.Vendor,
                Website = request.Website,
                Documentation = request.Documentation,
                Repository = request.Repository,
                LicenseType = request.LicenseType,
                IsActive = request.IsActive,
                IsRecommended = request.IsRecommended,
                IsDeprecated = request.IsDeprecated,
                PopularityScore = request.PopularityScore ?? 0,
                ReleaseDate = request.ReleaseDate,
                LastUpdateDate = request.LastUpdateDate,
                Notes = request.Notes,

                // Eski alanlar (backward compatibility)
                Language = request.Language ?? "Unknown",
                Framework = request.Framework,

                // Sistem alanları
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "system", // TODO: Get from current user
                UpdatedBy = "system"
            };

            await _context.Technologies.AddAsync(tech, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);

            return tech.Id;
        }

        private string GenerateTechnologyCode(string name)
        {
            var cleanName = name.Replace(" ", "").Replace(".", "").ToUpper();
            var timestamp = DateTime.UtcNow.ToString("yyyyMMdd");
            return $"TECH-{cleanName}-{timestamp}";
        }
    }
}

import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { Textarea } from 'primeng/inputtextarea';
import { DropdownModule } from 'primeng/dropdown';
import { SelectButtonModule } from 'primeng/selectbutton';
import { CardModule } from 'primeng/card';
import { ToastModule } from 'primeng/toast';
import { MessageService } from 'primeng/api';
import { TeamService } from '../../services/team.service';
import { TeamStatus } from '../../models/team.model';

@Component({
    standalone: true,
    selector: 'app-team-form',
    imports: [
        CommonModule,
        RouterModule,
        ReactiveFormsModule,
        ButtonModule,
        InputTextModule,
        Textarea,
        DropdownModule,
        SelectButtonModule,
        CardModule,
        ToastModule
    ],
    providers: [MessageService],
    template: `
        <div class="card">
            <div class="flex justify-between align-items-center mb-5">
                <h2 class="text-2xl font-semibold">{{ isEditMode ? 'Edit' : 'Create' }} Team</h2>
                <button pButton 
                    icon="pi pi-arrow-left" 
                    label="Back to Teams"
                    class="p-button-outlined"
                    routerLink=".."></button>
            </div>

            <form [formGroup]="teamForm" (ngSubmit)="onSubmit()">
                <p-card>
                    <div class="grid">
                        <div class="col-12 md:col-6">
                            <div class="field">
                                <label for="name" class="font-medium block mb-2">Team Name</label>
                                <input pInputText 
                                    id="name" 
                                    formControlName="name"
                                    class="w-full" 
                                    placeholder="Enter team name"/>
                                <small class="p-error block" *ngIf="teamForm.get('name')?.touched && teamForm.get('name')?.hasError('required')">
                                    Team name is required
                                </small>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="field">
                                <label for="description" class="font-medium block mb-2">Description</label>
                                <textarea pInputTextarea 
                                    id="description" 
                                    formControlName="description"
                                    class="w-full" 
                                    rows="4"
                                    placeholder="Enter team description"></textarea>
                                <small class="p-error block" *ngIf="teamForm.get('description')?.touched && teamForm.get('description')?.hasError('required')">
                                    Description is required
                                </small>
                            </div>
                        </div>

                        <div class="col-12 md:col-6">
                            <div class="field">
                                <label for="status" class="font-medium block mb-2">Status</label>
                                <p-selectButton 
                                    [options]="statusOptions" 
                                    formControlName="status"
                                    optionLabel="label" 
                                    optionValue="value">
                                </p-selectButton>
                            </div>
                        </div>

                        <div class="col-12 flex justify-content-end">
                            <button pButton 
                                icon="pi pi-times" 
                                label="Cancel"
                                class="p-button-outlined p-button-secondary mr-2"
                                type="button"
                                routerLink=".."></button>
                            <button pButton 
                                icon="pi pi-check" 
                                [label]="isEditMode ? 'Update Team' : 'Create Team'"
                                type="submit"
                                [loading]="isSubmitting"></button>
                        </div>
                    </div>
                </p-card>
            </form>
        </div>

        <p-toast></p-toast>
    `
})
export class TeamFormComponent implements OnInit {
    teamForm: FormGroup;
    isEditMode = false;
    isSubmitting = false;
    teamId: string | null = null;

    statusOptions = [
        { label: 'Active', value: 'active' },
        { label: 'Inactive', value: 'inactive' }
    ];

    constructor(
        private fb: FormBuilder,
        private teamService: TeamService,
        private router: Router,
        private route: ActivatedRoute,
        private messageService: MessageService
    ) {
        this.teamForm = this.fb.group({
            name: ['', Validators.required],
            description: ['', Validators.required],
            status: ['active', Validators.required]
        });
    }

    ngOnInit() {
        this.teamId = this.route.snapshot.paramMap.get('id');
        if (this.teamId) {
            this.isEditMode = true;
            this.loadTeam(this.teamId);
        }
    }

    private loadTeam(id: string) {
        this.teamService.getTeamById(id).subscribe(team => {
            if (team) {
                this.teamForm.patchValue({
                    name: team.name,
                    description: team.description,
                    status: team.status
                });
            } else {
                this.messageService.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Team not found'
                });
                this.router.navigate(['..']);
            }
        });
    }

    onSubmit() {
        if (this.teamForm.invalid) {
            Object.keys(this.teamForm.controls).forEach(key => {
                const control = this.teamForm.get(key);
                if (control?.invalid) {
                    control.markAsTouched();
                }
            });
            return;
        }

        this.isSubmitting = true;
        const formData = this.teamForm.value;

        const operation = this.isEditMode
            ? this.teamService.updateTeam(this.teamId!, formData)
            : this.teamService.createTeam(formData);

        operation.subscribe({
            next: (team) => {
                this.messageService.add({
                    severity: 'success',
                    summary: 'Success',
                    detail: `Team ${this.isEditMode ? 'updated' : 'created'} successfully`
                });
                setTimeout(() => {
                    this.router.navigate(['..'], { relativeTo: this.route });
                }, 1500);
            },
            error: (error) => {
                this.messageService.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: `Failed to ${this.isEditMode ? 'update' : 'create'} team`
                });
                this.isSubmitting = false;
            }
        });
    }
}

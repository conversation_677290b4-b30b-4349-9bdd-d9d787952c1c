import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { Textarea } from 'primeng/inputtextarea';
import { DropdownModule } from 'primeng/dropdown';
import { SelectButtonModule } from 'primeng/selectbutton';
import { CardModule } from 'primeng/card';
import { ToastModule } from 'primeng/toast';
import { TooltipModule } from 'primeng/tooltip';
import { MessageService } from 'primeng/api';
import { TeamService } from '../../services/team.service';
import { TeamStatus } from '../../models/team.model';

@Component({
    standalone: true,
    selector: 'app-team-form',
    imports: [
        CommonModule,
        RouterModule,
        ReactiveFormsModule,
        ButtonModule,
        InputTextModule,
        Textarea,
        DropdownModule,
        SelectButtonModule,
        CardModule,
        ToastModule,
        TooltipModule
    ],
    providers: [MessageService],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <h5>{{ isEditMode ? 'Takım Düzenle' : 'Yeni Takım' }}</h5>
                        <p-button
                            label="Geri"
                            icon="pi pi-arrow-left"
                            class="p-button-secondary"
                            (onClick)="goBack()">
                        </p-button>
                    </div>

                    <form [formGroup]="teamForm" (ngSubmit)="onSubmit()">
                        <div class="grid">
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="name" class="font-medium">Takım Adı *</label>
                                    <input
                                        id="name"
                                        type="text"
                                        pInputText
                                        formControlName="name"
                                        class="w-full"
                                        placeholder="Takım adı" />
                                    <small class="p-error block" *ngIf="teamForm.get('name')?.touched && teamForm.get('name')?.hasError('required')">
                                        Takım adı gereklidir
                                    </small>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="status" class="font-medium">Durum *</label>
                                    <p-selectButton
                                        [options]="statusOptions"
                                        formControlName="status"
                                        optionLabel="label"
                                        optionValue="value"
                                        class="w-full">
                                    </p-selectButton>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field">
                                    <label for="description" class="font-medium">Açıklama *</label>
                                    <textarea
                                        id="description"
                                        pTextarea
                                        formControlName="description"
                                        rows="3"
                                        class="w-full"
                                        placeholder="Takım açıklaması...">
                                    </textarea>
                                    <small class="p-error block" *ngIf="teamForm.get('description')?.touched && teamForm.get('description')?.hasError('required')">
                                        Açıklama gereklidir
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-content-end gap-3 mt-4">
                            <p-button
                                icon="pi pi-times"
                                severity="secondary"
                                rounded
                                outlined
                                type="button"
                                pTooltip="İptal"
                                (onClick)="goBack()">
                            </p-button>
                            <p-button
                                icon="pi pi-check"
                                severity="success"
                                rounded
                                outlined
                                type="submit"
                                [pTooltip]="isEditMode ? 'Güncelle' : 'Kaydet'"
                                [disabled]="teamForm.invalid"
                                [loading]="isSubmitting">
                            </p-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <p-toast></p-toast>
    `
})
export class TeamFormComponent implements OnInit {
    teamForm: FormGroup;
    isEditMode = false;
    isSubmitting = false;
    teamId: string | null = null;

    statusOptions = [
        { label: 'Aktif', value: 'active' },
        { label: 'Pasif', value: 'inactive' }
    ];

    constructor(
        private fb: FormBuilder,
        private teamService: TeamService,
        private router: Router,
        private route: ActivatedRoute,
        private messageService: MessageService
    ) {
        this.teamForm = this.fb.group({
            name: ['', Validators.required],
            description: ['', Validators.required],
            status: ['active', Validators.required]
        });
    }

    ngOnInit() {
        this.teamId = this.route.snapshot.paramMap.get('id');
        if (this.teamId) {
            this.isEditMode = true;
            this.loadTeam(this.teamId);
        }
    }

    private loadTeam(id: string) {
        this.teamService.getTeamById(id).subscribe(team => {
            if (team) {
                this.teamForm.patchValue({
                    name: team.name,
                    description: team.description,
                    status: team.status
                });
            } else {
                this.messageService.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Team not found'
                });
                this.router.navigate(['..']);
            }
        });
    }

    onSubmit() {
        if (this.teamForm.invalid) {
            Object.keys(this.teamForm.controls).forEach(key => {
                const control = this.teamForm.get(key);
                if (control?.invalid) {
                    control.markAsTouched();
                }
            });
            return;
        }

        this.isSubmitting = true;
        const formData = this.teamForm.value;

        const operation = this.isEditMode
            ? this.teamService.updateTeam(this.teamId!, formData)
            : this.teamService.createTeam(formData);

        operation.subscribe({
            next: (team) => {
                this.messageService.add({
                    severity: 'success',
                    summary: 'Başarılı',
                    detail: `Takım ${this.isEditMode ? 'başarıyla güncellendi' : 'başarıyla oluşturuldu'}`
                });
                setTimeout(() => {
                    this.router.navigate(['..'], { relativeTo: this.route });
                }, 1500);
            },
            error: (error) => {
                this.messageService.add({
                    severity: 'error',
                    summary: 'Hata',
                    detail: `Takım ${this.isEditMode ? 'güncellenirken' : 'oluşturulurken'} hata oluştu`
                });
                this.isSubmitting = false;
            }
        });
    }

    goBack() {
        this.router.navigate(['/pages/teams']);
    }
}

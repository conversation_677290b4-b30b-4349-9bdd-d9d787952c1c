import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { TabViewModule } from 'primeng/tabview';
import { TagModule } from 'primeng/tag';
import { TableModule } from 'primeng/table';
import { CardModule } from 'primeng/card';
import { DividerModule } from 'primeng/divider';
import { Contract, ContractStatus, LicenseType } from '../../models/contract.model';

@Component({
    selector: 'app-contract-detail',
    standalone: true,
    imports: [
        CommonModule,
        ButtonModule,
        TabViewModule,
        TagModule,
        TableModule,
        CardModule,
        DividerModule
    ],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <div>
                            <h5 class="m-0">{{ contract?.contractNumber }}</h5>
                            <p class="text-500 mt-1">{{ contract?.customerName }} - {{ contract?.projectName }}</p>
                        </div>
                        <div class="flex gap-2">
                            <p-button 
                                label="Düzenle" 
                                icon="pi pi-pencil" 
                                class="p-button-warning"
                                (onClick)="editContract()">
                            </p-button>
                            <p-button 
                                label="Geri" 
                                icon="pi pi-arrow-left" 
                                class="p-button-secondary"
                                (onClick)="goBack()">
                            </p-button>
                        </div>
                    </div>

                    <p-tabView *ngIf="contract">
                        <p-tabPanel header="Sözleşme Bilgileri" leftIcon="pi pi-file-edit">
                            <div class="grid">
                                <div class="col-12 md:col-6">
                                    <p-card header="Temel Bilgiler">
                                        <div class="field">
                                            <label class="font-medium">Sözleşme No:</label>
                                            <div class="mt-1">{{ contract.contractNumber }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Müşteri:</label>
                                            <div class="mt-1">{{ contract.customerName }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Proje:</label>
                                            <div class="mt-1">{{ contract.projectName }} ({{ contract.projectCode }})</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Lisans Türü:</label>
                                            <div class="mt-1">
                                                <p-tag [value]="contract.licenseType" severity="info"></p-tag>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Durum:</label>
                                            <div class="mt-1">
                                                <p-tag 
                                                    [value]="contract.status" 
                                                    [severity]="getStatusSeverity(contract.status)">
                                                </p-tag>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                                <div class="col-12 md:col-6">
                                    <p-card header="Finansal Bilgiler">
                                        <div class="field">
                                            <label class="font-medium">Sözleşme Değeri:</label>
                                            <div class="mt-1 text-xl font-bold text-primary">
                                                {{ contract.contractValue | currency:contract.currency:'symbol':'1.0-0' }}
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Ödeme Koşulları:</label>
                                            <div class="mt-1">{{ contract.paymentTerms }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Sözleşme Tarihi:</label>
                                            <div class="mt-1">{{ contract.contractDate | date:'dd/MM/yyyy' }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Başlangıç Tarihi:</label>
                                            <div class="mt-1">{{ contract.startDate | date:'dd/MM/yyyy' }}</div>
                                        </div>
                                        <div class="field" *ngIf="contract.endDate">
                                            <label class="font-medium">Bitiş Tarihi:</label>
                                            <div class="mt-1">{{ contract.endDate | date:'dd/MM/yyyy' }}</div>
                                        </div>
                                    </p-card>
                                </div>
                            </div>
                        </p-tabPanel>

                        <p-tabPanel header="Teknik Detaylar" leftIcon="pi pi-cog">
                            <div class="grid">
                                <div class="col-12 md:col-6">
                                    <p-card header="Destek ve Limitler">
                                        <div class="field">
                                            <label class="font-medium">Destek Seviyesi:</label>
                                            <div class="mt-1">
                                                <p-tag [value]="contract.supportLevel" severity="success"></p-tag>
                                            </div>
                                        </div>
                                        <div class="field" *ngIf="contract.maxUsers">
                                            <label class="font-medium">Maksimum Kullanıcı:</label>
                                            <div class="mt-1">{{ contract.maxUsers }} kullanıcı</div>
                                        </div>
                                        <div class="field" *ngIf="contract.renewalDate">
                                            <label class="font-medium">Yenileme Tarihi:</label>
                                            <div class="mt-1">{{ contract.renewalDate | date:'dd/MM/yyyy' }}</div>
                                        </div>
                                    </p-card>
                                </div>
                                <div class="col-12 md:col-6">
                                    <p-card header="Özellikler">
                                        <div *ngIf="contract.features.length === 0" class="text-500">
                                            Henüz özellik tanımlanmamış
                                        </div>
                                        <div *ngFor="let feature of contract.features" class="field">
                                            <div class="flex align-items-center">
                                                <i [class]="feature.isIncluded ? 'pi pi-check text-green-500' : 'pi pi-times text-red-500'" class="mr-2"></i>
                                                <span>{{ feature.featureName }}</span>
                                                <span *ngIf="feature.limitValue" class="ml-2 text-500">({{ feature.limitValue }})</span>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                            </div>
                        </p-tabPanel>

                        <p-tabPanel header="Dokümanlar" leftIcon="pi pi-file">
                            <p-table [value]="contract.documents" [tableStyle]="{ 'min-width': '50rem' }">
                                <ng-template pTemplate="header">
                                    <tr>
                                        <th>Doküman Adı</th>
                                        <th>Tür</th>
                                        <th>Versiyon</th>
                                        <th>Yükleme Tarihi</th>
                                        <th>Yükleyen</th>
                                        <th>Boyut</th>
                                        <th>İşlemler</th>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="body" let-document>
                                    <tr>
                                        <td>{{ document.documentName }}</td>
                                        <td>{{ document.documentType }}</td>
                                        <td>{{ document.version }}</td>
                                        <td>{{ document.uploadDate | date:'dd/MM/yyyy' }}</td>
                                        <td>{{ document.uploadedBy }}</td>
                                        <td>{{ document.fileSize | number:'1.0-0' }} KB</td>
                                        <td>
                                            <p-button 
                                                icon="pi pi-download" 
                                                class="p-button-rounded p-button-text p-button-info"
                                                pTooltip="İndir">
                                            </p-button>
                                        </td>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="emptymessage">
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-500">Bu sözleşmeye ait doküman bulunmuyor</div>
                                        </td>
                                    </tr>
                                </ng-template>
                            </p-table>
                        </p-tabPanel>
                    </p-tabView>
                </div>
            </div>
        </div>
    `
})
export class ContractDetailComponent implements OnInit {
    contract: Contract | null = null;
    contractId: string = '';

    constructor(
        private route: ActivatedRoute,
        private router: Router
    ) {}

    ngOnInit() {
        this.contractId = this.route.snapshot.params['id'];
        this.loadContract();
    }

    loadContract() {
        // Dummy data
        this.contract = {
            id: '1',
            contractNumber: 'CNT-2025-001',
            customerId: '1',
            customerName: 'ABC Teknoloji A.Ş.',
            projectId: '1',
            projectName: 'E-Ticaret Platformu',
            projectCode: 'PRJ-2025-001',
            contractDate: new Date('2025-01-15'),
            startDate: new Date('2025-02-01'),
            endDate: new Date('2025-01-31'),
            licenseType: 'Abonelik' as any,
            contractValue: 500000,
            currency: 'TRY',
            status: 'Aktif' as any,
            renewalDate: new Date('2025-12-01'),
            isActive: true,
            paymentTerms: 'Yıllık' as any,
            supportLevel: 'Premium' as any,
            maxUsers: 100,
            features: [
                { id: '1', featureName: 'Kullanıcı Yönetimi', isIncluded: true, limitValue: 100 },
                { id: '2', featureName: 'Raporlama Modülü', isIncluded: true },
                { id: '3', featureName: 'API Erişimi', isIncluded: true, limitValue: 1000 },
                { id: '4', featureName: 'Mobil Uygulama', isIncluded: false }
            ],
            documents: [],
            notes: 'Yıllık abonelik sözleşmesi',
            createdBy: 'Admin',
            createdDate: new Date('2025-01-15')
        };
    }

    editContract() {
        this.router.navigate(['/pages/contracts', this.contractId, 'edit']);
    }

    goBack() {
        this.router.navigate(['/pages/contracts']);
    }

    getStatusSeverity(status: ContractStatus): string {
        switch (status) {
            case ContractStatus.ACTIVE:
                return 'success';
            case ContractStatus.PENDING:
                return 'warning';
            case ContractStatus.EXPIRED:
                return 'danger';
            case ContractStatus.DRAFT:
                return 'info';
            case ContractStatus.CANCELLED:
                return 'secondary';
            case ContractStatus.RENEWED:
                return 'success';
            case ContractStatus.SUSPENDED:
                return 'warning';
            default:
                return 'info';
        }
    }
}

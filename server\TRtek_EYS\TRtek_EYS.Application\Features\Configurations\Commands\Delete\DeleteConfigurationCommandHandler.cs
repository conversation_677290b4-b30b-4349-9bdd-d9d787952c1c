﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Configurations.Commands.Delete
{
    public class DeleteConfigurationCommandHandler : IRequestHandler<DeleteConfigurationCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public DeleteConfigurationCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(DeleteConfigurationCommand request, CancellationToken cancellationToken)
        {
            var entity = await _context.Configurations
                .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

            if (entity is null)
                throw new Exception("Silinecek konfigürasyon bulunamadı.");

            _context.Configurations.Remove(entity);
            await _context.SaveChangesAsync(cancellationToken);

            return entity.Id;
        }
    }
}

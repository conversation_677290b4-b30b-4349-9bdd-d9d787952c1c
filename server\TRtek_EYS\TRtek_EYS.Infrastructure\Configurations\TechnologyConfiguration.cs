﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TRtek_EYS.Domain.Entities;

namespace TRtek_EYS.Infrastructure.Configurations
{
    public class TechnologyConfiguration : IEntityTypeConfiguration<Technology>
    {
        public void Configure(EntityTypeBuilder<Technology> builder)
        {
            builder.ToTable("Technologies");

            builder.HasKey(t => t.Id);

            builder.Property(t => t.Language)
                   .IsRequired()
                   .HasMaxLength(100);

            builder.Property(t => t.Framework)
                   .HasMaxLength(100);

            builder.Property(t => t.Version)
                   .HasMaxLength(50);

            builder.Property(t => t.CreatedAt)
                   .IsRequired();

            builder.HasOne(t => t.Project)
                   .WithMany(p => p.Technologies)
                   .HasForeignKey(t => t.ProjectId)
                   .OnDelete(DeleteBehavior.Cascade);
        }
    }
}

﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TRtek_EYS.Domain.Entities;

namespace TRtek_EYS.Infrastructure.Configurations
{
    public class TechnologyConfiguration : IEntityTypeConfiguration<Technology>
    {
        public void Configure(EntityTypeBuilder<Technology> builder)
        {
            builder.ToTable("Technologies");

            builder.HasKey(t => t.Id);

            builder.Property(t => t.Language)
                   .IsRequired()
                   .HasMaxLength(100);

            builder.Property(t => t.Framework)
                   .HasMaxLength(100);

            builder.Property(t => t.Version)
                   .HasMaxLength(50);

            // YENİ ALANLAR İÇİN CONFIGURATION
            builder.Property(t => t.Name)
                   .HasMaxLength(200);

            builder.Property(t => t.TechnologyCode)
                   .HasMaxLength(50);

            builder.Property(t => t.Description)
                   .HasMaxLength(1000);

            builder.Property(t => t.Vendor)
                   .HasMaxLength(200);

            builder.Property(t => t.Website)
                   .HasMaxLength(500);

            builder.Property(t => t.Documentation)
                   .HasMaxLength(500);

            builder.Property(t => t.Repository)
                   .HasMaxLength(500);

            builder.Property(t => t.Tags)
                   .HasMaxLength(2000); // JSON string

            builder.Property(t => t.Notes)
                   .HasMaxLength(2000);

            builder.Property(t => t.PopularityScore)
                   .HasPrecision(5, 2); // 999.99 formatında

            builder.Property(t => t.CreatedAt)
                   .IsRequired();

            builder.HasOne(t => t.Project)
                   .WithMany(p => p.Technologies)
                   .HasForeignKey(t => t.ProjectId)
                   .OnDelete(DeleteBehavior.Cascade);
        }
    }
}

﻿using TRtek_EYS.Domain.Enums;

namespace TRtek_EYS.Domain.Entities;

public class Technology : BaseEntity
{
    // MEVCUT ALANLAR
    public string Language { get; set; } = null!;
    public string? Framework { get; set; }
    public string? Version { get; set; }
    public Guid ProjectId { get; set; }
    public Project? Project { get; set; }

    // YENİ ALANLAR
    public string? Name { get; set; }
    public string? TechnologyCode { get; set; }
    public string? Description { get; set; }
    public TechnologyType? Type { get; set; }
    public TechnologyCategory? Category { get; set; }
    public string? Vendor { get; set; }
    public string? Website { get; set; }
    public string? Documentation { get; set; }
    public string? Repository { get; set; }
    public LicenseType? LicenseType { get; set; }
    public bool IsActive { get; set; } = true;
    public bool IsRecommended { get; set; } = false;
    public bool IsDeprecated { get; set; } = false;
    public DateTime? ReleaseDate { get; set; }
    public DateTime? LastUpdateDate { get; set; }
    public DateTime? EndOfLifeDate { get; set; }
    public string? Tags { get; set; } 
    public string? Notes { get; set; }
    public int ProjectCount { get; set; } = 0;
    public int TeamCount { get; set; } = 0;
    public decimal PopularityScore { get; set; } = 0;
}

﻿using System.Net;
using System.Text.Json;
using FluentValidation;
using Microsoft.AspNetCore.Diagnostics;
using TS.Result;

namespace TRtek_EYS.WebAPI.Middlewares;

public class ExceptionHandler : IExceptionHandler
{
    private readonly IWebHostEnvironment _env;
    private readonly ILogger<ExceptionHandler> _logger;

    public ExceptionHandler(IWebHostEnvironment env, ILogger<ExceptionHandler> logger)
    {
        _env = env;
        _logger = logger;
    }

    public async ValueTask<bool> TryHandleAsync(HttpContext httpContext, Exception exception, CancellationToken cancellationToken)
    {
        httpContext.Response.ContentType = "application/json";

        var result = HandleException(exception, out int statusCode);

        httpContext.Response.StatusCode = statusCode;

        var options = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = _env.IsDevelopment()
        };

        var json = JsonSerializer.Serialize(result, options);

        await httpContext.Response.WriteAsync(json, cancellationToken);

        return true;
    }

    private static Result<string> HandleException(Exception exception, out int statusCode)
    {
        statusCode = (int)HttpStatusCode.InternalServerError;

        switch (exception)
        {
            case ValidationException validationException:
                statusCode = (int)HttpStatusCode.BadRequest;
                return Result<string>.Failure(
                    statusCode,
                    validationException.Errors.Select(e => $"{e.PropertyName}: {e.ErrorMessage}").ToList()
                );

            case UnauthorizedAccessException:
                statusCode = (int)HttpStatusCode.Unauthorized;
                return Result<string>.Failure(statusCode, "Yetkisiz erişim.");

            case KeyNotFoundException:
                statusCode = (int)HttpStatusCode.NotFound;
                return Result<string>.Failure(statusCode, "İlgili veri bulunamadı.");

            case ApplicationException appEx when appEx.Message.Contains("already exists"):
                statusCode = (int)HttpStatusCode.Conflict;
                return Result<string>.Failure(statusCode, appEx.Message);

            default:
                var messages = new List<string> { exception.Message };

                if (exception.InnerException is not null)
                    messages.Add($"Inner: {exception.InnerException.Message}");

                return Result<string>.Failure(statusCode, messages);
        }
    }

}


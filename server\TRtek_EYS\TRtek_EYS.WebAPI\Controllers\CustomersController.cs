﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TRtek_EYS.Application.Features.Customers.Commands.Add;
using TRtek_EYS.Application.Features.Customers.Commands.Delete;
using TRtek_EYS.Application.Features.Customers.Commands.Update;
using TRtek_EYS.Application.Features.Customers.Queries.GetAll;
using TRtek_EYS.Application.Features.Customers.Queries.GetById;

namespace TRtek_EYS.WebAPI.Controllers;

[Route("api/[controller]")]
[ApiController]
public class CustomersController : ApiController
{
    public CustomersController(IMediator mediator) : base(mediator) { }

    [HttpGet]
    public async Task<IActionResult> GetAll()
    {
        var result = await Mediator.Send(new GetAllCustomersQuery());
        return Ok(result); // List<CustomerGetAllDto>
    }

    [HttpGet("v2")]
    [Authorize]
    public async Task<IActionResult> GetAllV2()
    {
        var result = await Mediator.Send(new GetAllCustomersV2Query());
        return Ok(result); // List<CustomerGetAllDtoV2>
    }

    [HttpGet("{id:guid}")]
    public async Task<IActionResult> GetById(Guid id)
    {
        var result = await Mediator.Send(new GetCustomerByIdQuery(id));
        return result is null ? NotFound() : Ok(result); // CustomerGetByIdDto
    }

    [HttpPost]
    public async Task<IActionResult> Add([FromBody] AddCustomerCommand command)
    {
        var id = await Mediator.Send(command);
        return CreatedAtAction(nameof(GetById), new { id }, null);
    }

    [HttpPut("{id:guid}")]
    public async Task<IActionResult> Update(Guid id, [FromBody] UpdateCustomerCommand command)
    {
        if (id != command.Id)
            return BadRequest("URL'deki ID uyuşmuyor.");

        await Mediator.Send(command);
        return NoContent();
    }

    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> Delete(Guid id)
    {
        await Mediator.Send(new DeleteCustomerCommand { Id = id });
        return NoContent();
    }
}

﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Configurations.Queries.GetAll
{
    public class GetAllConfigurationsQueryHandler : IRequestHandler<GetAllConfigurationsQuery, List<ConfigurationGetAllDto>>
    {
        private readonly IApplicationDbContext _context;

        public GetAllConfigurationsQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<ConfigurationGetAllDto>> Handle(GetAllConfigurationsQuery request, CancellationToken cancellationToken)
        {
            return await _context.Configurations
                .AsNoTracking()
                .Select(c => new ConfigurationGetAllDto
                {
                    Id = c.Id,
                    Name = c.Name,
                    Description = c.Description,
                    DatabaseType = c.DatabaseType
                })
                .ToListAsync(cancellationToken);
        }
    }
}

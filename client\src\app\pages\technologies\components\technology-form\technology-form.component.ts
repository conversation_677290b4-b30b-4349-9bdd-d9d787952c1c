import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { Select } from 'primeng/select';
import { Textarea } from 'primeng/inputtextarea';
import { Checkbox } from 'primeng/checkbox';
import { InputNumber } from 'primeng/inputnumber';
import { Chips } from 'primeng/chips';
import { ToastModule } from 'primeng/toast';
import { TooltipModule } from 'primeng/tooltip';
import { MessageService } from 'primeng/api';
import { TechnologyType, TechnologyCategory, LicenseType, MaturityLevel, LearningCurve } from '../../models/technology.model';
import { TechnologyService } from '../../services/technology.service';

@Component({
    selector: 'app-technology-form',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        ButtonModule,
        InputTextModule,
        Select,
        Textarea,
        Checkbox,
        InputNumber,
        Chips,
        ToastModule,
        TooltipModule
    ],
    providers: [MessageService],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <h5>{{ isEditMode ? 'Teknoloji Düzenle' : 'Yeni Teknoloji' }}</h5>
                        <p-button 
                            label="Geri" 
                            icon="pi pi-arrow-left" 
                            class="p-button-secondary"
                            (onClick)="goBack()">
                        </p-button>
                    </div>

                    <form [formGroup]="technologyForm" (ngSubmit)="onSubmit()">
                        <div class="grid">
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="technologyCode" class="font-medium">Teknoloji Kodu *</label>
                                    <input 
                                        id="technologyCode"
                                        type="text" 
                                        pInputText 
                                        formControlName="technologyCode"
                                        [readonly]="isEditMode"
                                        class="w-full"
                                        placeholder="Otomatik oluşturulacak" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="name" class="font-medium">Teknoloji Adı *</label>
                                    <input 
                                        id="name"
                                        type="text" 
                                        pInputText 
                                        formControlName="name"
                                        class="w-full"
                                        placeholder="Teknoloji adı" />
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field">
                                    <label for="description" class="font-medium">Açıklama *</label>
                                    <textarea 
                                        id="description"
                                        pTextarea 
                                        formControlName="description"
                                        rows="3"
                                        class="w-full"
                                        placeholder="Teknoloji açıklaması...">
                                    </textarea>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="type" class="font-medium">Teknoloji Tipi *</label>
                                    <p-select 
                                        id="type"
                                        formControlName="type"
                                        [options]="typeOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Teknoloji tipi seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="category" class="font-medium">Kategori *</label>
                                    <p-select 
                                        id="category"
                                        formControlName="category"
                                        [options]="categoryOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Kategori seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="vendor" class="font-medium">Sağlayıcı</label>
                                    <input 
                                        id="vendor"
                                        type="text" 
                                        pInputText 
                                        formControlName="vendor"
                                        class="w-full"
                                        placeholder="Sağlayıcı firma" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="language" class="font-medium">Programlama Dili</label>
                                    <p-select
                                        id="language"
                                        formControlName="language"
                                        [options]="languageOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Programlama dili seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="framework" class="font-medium">Framework</label>
                                    <input
                                        id="framework"
                                        type="text"
                                        pInputText
                                        formControlName="framework"
                                        class="w-full"
                                        placeholder="Framework adı" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="version" class="font-medium">Versiyon *</label>
                                    <input
                                        id="version"
                                        type="text"
                                        pInputText
                                        formControlName="version"
                                        class="w-full"
                                        placeholder="Versiyon numarası" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="latestVersion" class="font-medium">Son Versiyon</label>
                                    <input 
                                        id="latestVersion"
                                        type="text" 
                                        pInputText 
                                        formControlName="latestVersion"
                                        class="w-full"
                                        placeholder="Son versiyon numarası" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="licenseType" class="font-medium">Lisans Tipi *</label>
                                    <p-select 
                                        id="licenseType"
                                        formControlName="licenseType"
                                        [options]="licenseOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Lisans tipi seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="maturityLevel" class="font-medium">Olgunluk Seviyesi *</label>
                                    <p-select 
                                        id="maturityLevel"
                                        formControlName="maturityLevel"
                                        [options]="maturityOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Olgunluk seviyesi seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="learningCurve" class="font-medium">Öğrenme Eğrisi *</label>
                                    <p-select 
                                        id="learningCurve"
                                        formControlName="learningCurve"
                                        [options]="learningCurveOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Öğrenme eğrisi seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="popularityScore" class="font-medium">Popülerlik Skoru *</label>
                                    <p-inputNumber 
                                        id="popularityScore"
                                        formControlName="popularityScore"
                                        [min]="0"
                                        [max]="100"
                                        suffix="%"
                                        placeholder="Popülerlik skoru"
                                        class="w-full">
                                    </p-inputNumber>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="website" class="font-medium">Website</label>
                                    <input 
                                        id="website"
                                        type="url" 
                                        pInputText 
                                        formControlName="website"
                                        class="w-full"
                                        placeholder="https://example.com" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="documentation" class="font-medium">Dokümantasyon</label>
                                    <input 
                                        id="documentation"
                                        type="url" 
                                        pInputText 
                                        formControlName="documentation"
                                        class="w-full"
                                        placeholder="https://docs.example.com" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="repository" class="font-medium">Repository</label>
                                    <input 
                                        id="repository"
                                        type="url" 
                                        pInputText 
                                        formControlName="repository"
                                        class="w-full"
                                        placeholder="https://github.com/example/repo" />
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field">
                                    <label for="tags" class="font-medium">Etiketler</label>
                                    <p-chips 
                                        id="tags"
                                        formControlName="tags"
                                        placeholder="Etiket ekle..."
                                        class="w-full">
                                    </p-chips>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field">
                                    <label for="alternatives" class="font-medium">Alternatifler</label>
                                    <p-chips 
                                        id="alternatives"
                                        formControlName="alternatives"
                                        placeholder="Alternatif teknoloji ekle..."
                                        class="w-full">
                                    </p-chips>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="pros" class="font-medium">Avantajlar</label>
                                    <p-chips 
                                        id="pros"
                                        formControlName="pros"
                                        placeholder="Avantaj ekle..."
                                        class="w-full">
                                    </p-chips>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="cons" class="font-medium">Dezavantajlar</label>
                                    <p-chips 
                                        id="cons"
                                        formControlName="cons"
                                        placeholder="Dezavantaj ekle..."
                                        class="w-full">
                                    </p-chips>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field">
                                    <label for="useCases" class="font-medium">Kullanım Alanları</label>
                                    <p-chips 
                                        id="useCases"
                                        formControlName="useCases"
                                        placeholder="Kullanım alanı ekle..."
                                        class="w-full">
                                    </p-chips>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field">
                                    <label for="notes" class="font-medium">Notlar</label>
                                    <textarea 
                                        id="notes"
                                        pTextarea 
                                        formControlName="notes"
                                        rows="3"
                                        class="w-full"
                                        placeholder="Teknoloji hakkında notlar...">
                                    </textarea>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="flex gap-4">
                                    <div class="field-checkbox">
                                        <p-checkbox
                                            inputId="isActive"
                                            formControlName="isActive"
                                            [binary]="true">
                                        </p-checkbox>
                                        <label for="isActive" class="ml-2 font-medium">Aktif</label>
                                    </div>
                                    <div class="field-checkbox">
                                        <p-checkbox
                                            inputId="isRecommended"
                                            formControlName="isRecommended"
                                            [binary]="true">
                                        </p-checkbox>
                                        <label for="isRecommended" class="ml-2 font-medium">Önerilen</label>
                                    </div>
                                    <div class="field-checkbox">
                                        <p-checkbox
                                            inputId="isDeprecated"
                                            formControlName="isDeprecated"
                                            [binary]="true">
                                        </p-checkbox>
                                        <label for="isDeprecated" class="ml-2 font-medium">Kullanımdan Kaldırıldı</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-content-end gap-3 mt-4">
                            <p-button
                                icon="pi pi-times"
                                severity="secondary"
                                rounded
                                outlined
                                type="button"
                                pTooltip="İptal"
                                (onClick)="goBack()">
                            </p-button>
                            <p-button
                                icon="pi pi-check"
                                severity="success"
                                rounded
                                outlined
                                type="submit"
                                [pTooltip]="isEditMode ? 'Güncelle' : 'Kaydet'"
                                [disabled]="technologyForm.invalid">
                            </p-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <p-toast></p-toast>
    `
})
export class TechnologyFormComponent implements OnInit {
    technologyForm!: FormGroup;
    isEditMode = false;
    technologyId: string = '';

    typeOptions = [
        { label: 'Programlama Dili', value: TechnologyType.PROGRAMMING_LANGUAGE },
        { label: 'Framework', value: TechnologyType.FRAMEWORK },
        { label: 'Kütüphane', value: TechnologyType.LIBRARY },
        { label: 'Veritabanı', value: TechnologyType.DATABASE },
        { label: 'Araç', value: TechnologyType.TOOL },
        { label: 'Platform', value: TechnologyType.PLATFORM },
        { label: 'Runtime', value: TechnologyType.RUNTIME },
        { label: 'Derleyici', value: TechnologyType.COMPILER },
        { label: 'IDE', value: TechnologyType.IDE },
        { label: 'Versiyon Kontrol', value: TechnologyType.VERSION_CONTROL },
        { label: 'Build Aracı', value: TechnologyType.BUILD_TOOL },
        { label: 'Test Aracı', value: TechnologyType.TESTING_TOOL },
        { label: 'Deployment Aracı', value: TechnologyType.DEPLOYMENT_TOOL },
        { label: 'İzleme Aracı', value: TechnologyType.MONITORING_TOOL },
        { label: 'Güvenlik Aracı', value: TechnologyType.SECURITY_TOOL }
    ];

    categoryOptions = [
        { label: 'Frontend', value: TechnologyCategory.FRONTEND },
        { label: 'Backend', value: TechnologyCategory.BACKEND },
        { label: 'Mobil', value: TechnologyCategory.MOBILE },
        { label: 'Masaüstü', value: TechnologyCategory.DESKTOP },
        { label: 'Web', value: TechnologyCategory.WEB },
        { label: 'Veritabanı', value: TechnologyCategory.DATABASE },
        { label: 'DevOps', value: TechnologyCategory.DEVOPS },
        { label: 'Test', value: TechnologyCategory.TESTING },
        { label: 'Güvenlik', value: TechnologyCategory.SECURITY },
        { label: 'AI/ML', value: TechnologyCategory.AI_ML },
        { label: 'Blockchain', value: TechnologyCategory.BLOCKCHAIN },
        { label: 'IoT', value: TechnologyCategory.IOT },
        { label: 'Oyun Geliştirme', value: TechnologyCategory.GAME_DEVELOPMENT },
        { label: 'Veri Bilimi', value: TechnologyCategory.DATA_SCIENCE },
        { label: 'Bulut', value: TechnologyCategory.CLOUD }
    ];

    licenseOptions = [
        { label: 'Açık Kaynak', value: LicenseType.OPEN_SOURCE },
        { label: 'Ticari', value: LicenseType.COMMERCIAL },
        { label: 'Freemium', value: LicenseType.FREEMIUM },
        { label: 'Özel Mülkiyet', value: LicenseType.PROPRIETARY },
        { label: 'Çift Lisans', value: LicenseType.DUAL_LICENSE },
        { label: 'Özel', value: LicenseType.CUSTOM }
    ];

    maturityOptions = [
        { label: 'Deneysel', value: MaturityLevel.EXPERIMENTAL },
        { label: 'Alpha', value: MaturityLevel.ALPHA },
        { label: 'Beta', value: MaturityLevel.BETA },
        { label: 'Kararlı', value: MaturityLevel.STABLE },
        { label: 'Olgun', value: MaturityLevel.MATURE },
        { label: 'Eski', value: MaturityLevel.LEGACY },
        { label: 'Kullanımdan Kaldırıldı', value: MaturityLevel.DEPRECATED }
    ];

    learningCurveOptions = [
        { label: 'Çok Kolay', value: LearningCurve.VERY_EASY },
        { label: 'Kolay', value: LearningCurve.EASY },
        { label: 'Orta', value: LearningCurve.MODERATE },
        { label: 'Zor', value: LearningCurve.HARD },
        { label: 'Çok Zor', value: LearningCurve.VERY_HARD }
    ];

    languageOptions = [
        { label: 'JavaScript', value: 'JavaScript' },
        { label: 'TypeScript', value: 'TypeScript' },
        { label: 'Python', value: 'Python' },
        { label: 'Java', value: 'Java' },
        { label: 'C#', value: 'C#' },
        { label: 'C++', value: 'C++' },
        { label: 'Go', value: 'Go' },
        { label: 'Rust', value: 'Rust' },
        { label: 'PHP', value: 'PHP' },
        { label: 'Ruby', value: 'Ruby' },
        { label: 'Swift', value: 'Swift' },
        { label: 'Kotlin', value: 'Kotlin' },
        { label: 'Dart', value: 'Dart' },
        { label: 'HTML/CSS', value: 'HTML/CSS' },
        { label: 'SQL', value: 'SQL' },
        { label: 'Shell/Bash', value: 'Shell' },
        { label: 'Diğer', value: 'Other' }
    ];

    constructor(
        private fb: FormBuilder,
        private route: ActivatedRoute,
        private router: Router,
        private technologyService: TechnologyService,
        private messageService: MessageService
    ) {}

    ngOnInit() {
        this.initForm();
        this.checkEditMode();
    }

    initForm() {
        this.technologyForm = this.fb.group({
            // Backend'in beklediği alanlar
            technologyCode: ['', Validators.required],
            name: ['', Validators.required],
            description: ['', Validators.required],
            type: [TechnologyType.FRAMEWORK, Validators.required],
            category: [TechnologyCategory.WEB, Validators.required],
            version: ['', Validators.required],
            vendor: [''],
            website: [''],
            documentation: [''],
            repository: [''],
            licenseType: [LicenseType.OPEN_SOURCE, Validators.required],
            isActive: [true],
            isRecommended: [false],
            isDeprecated: [false],
            popularityScore: [0, [Validators.min(0), Validators.max(100)]],
            releaseDate: [null],
            lastUpdateDate: [null],
            tags: [''], // Backend string bekliyor
            notes: [''],
            language: [''],
            framework: ['']
        });

        if (!this.isEditMode) {
            this.generateTechnologyCode();
        }
    }

    checkEditMode() {
        this.technologyId = this.route.snapshot.params['id'];
        this.isEditMode = !!this.technologyId && this.router.url.includes('edit');
        
        if (this.isEditMode) {
            this.loadTechnology();
        }
    }

    generateTechnologyCode() {
        const year = new Date().getFullYear();
        const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        const technologyCode = `TECH-${year}-${randomNum}`;
        this.technologyForm.patchValue({ technologyCode });
    }

    loadTechnology() {
        this.technologyService.getTechnologyById(this.technologyId).subscribe(technology => {
            if (technology) {
                this.technologyForm.patchValue({
                    technologyCode: technology.technologyCode,
                    name: technology.name,
                    description: technology.description,
                    type: technology.type,
                    category: technology.category,
                    vendor: technology.vendor,
                    version: technology.version,
                    latestVersion: technology.latestVersion,
                    licenseType: technology.licenseType,
                    maturityLevel: technology.maturityLevel,
                    learningCurve: technology.learningCurve,
                    popularityScore: technology.popularityScore,
                    website: technology.website,
                    documentation: technology.documentation,
                    repository: technology.repository,
                    isActive: technology.isActive,
                    isRecommended: technology.isRecommended,
                    isDeprecated: technology.isDeprecated,
                    tags: technology.tags,
                    alternatives: technology.alternatives,
                    pros: technology.pros,
                    cons: technology.cons,
                    useCases: technology.useCases,
                    notes: technology.notes
                });
            }
        });
    }

    onSubmit() {
        if (this.technologyForm.valid) {
            const formData = this.technologyForm.value;
            
            if (this.isEditMode) {
                this.technologyService.updateTechnology(this.technologyId, formData).subscribe(() => {
                    this.messageService.add({
                        severity: 'success',
                        summary: 'Başarılı',
                        detail: 'Teknoloji başarıyla güncellendi'
                    });
                    setTimeout(() => {
                        this.router.navigate(['/pages/technologies']);
                    }, 1500);
                });
            } else {
                console.log('Form Data being sent:', formData);
                this.technologyService.createTechnology(formData).subscribe({
                    next: () => {
                        this.messageService.add({
                            severity: 'success',
                            summary: 'Başarılı',
                            detail: 'Teknoloji başarıyla oluşturuldu'
                        });
                        setTimeout(() => {
                            this.router.navigate(['/pages/technologies']);
                        }, 1500);
                    },
                    error: (error) => {
                        console.error('Create technology error:', error);
                        console.error('Error details:', error.error);
                        console.error('Error message:', error.message);
                        console.error('Error status:', error.status);

                        let errorMessage = 'Teknoloji oluşturulurken hata oluştu';
                        if (error.error && error.error.errors) {
                            const validationErrors = Object.values(error.error.errors).flat();
                            errorMessage = validationErrors.join(', ');
                            console.error('Validation errors:', validationErrors);
                        }

                        this.messageService.add({
                            severity: 'error',
                            summary: 'Hata',
                            detail: errorMessage
                        });
                    }
                });
            }
        }
    }

    goBack() {
        this.router.navigate(['/pages/technologies']);
    }
}

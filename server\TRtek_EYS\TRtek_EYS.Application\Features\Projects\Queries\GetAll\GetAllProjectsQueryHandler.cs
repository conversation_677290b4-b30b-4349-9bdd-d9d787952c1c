﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Projects.Queries.GetAll
{
    public class GetAllProjectsQueryHandler : IRequestHandler<GetAllProjectsQuery, List<ProjectGetAllDto>>
    {
        private readonly IApplicationDbContext _context;

        public GetAllProjectsQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<ProjectGetAllDto>> Handle(GetAllProjectsQuery request, CancellationToken cancellationToken)
        {
            return await _context.Projects
                .Include(p => p.CreatedByUser)
                .AsNoTracking()
                .Select(p => new ProjectGetAllDto
                {
                    Id = p.Id,
                    Name = p.Name,
                    Code = p.Code,
                    Description = p.Description,
                    CreatedByUserId = p.CreatedByUserId,
                    CreatedByUserName = p.CreatedByUser != null
                        ? p.CreatedByUser.FullName
                        : string.Empty
                })
                .ToListAsync(cancellationToken);
        }

    }
}

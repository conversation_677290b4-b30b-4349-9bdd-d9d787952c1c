import { Component, OnIni<PERSON>, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil, forkJoin } from 'rxjs';
import { SkeletonModule } from 'primeng/skeleton';
import { UserService } from '../../users/services/user.service';
import { TechnologyService } from '../../technologies/services/technology.service';
import { PlatformService } from '../../platforms/services/platform.service';
import { DocumentService } from '../../documents/services/document.service';

interface StatsData {
    totalUsers: number;
    activeUsers: number;
    totalTechnologies: number;
    activeTechnologies: number;
    totalPlatforms: number;
    activePlatforms: number;
    totalDocuments: number;
    publishedDocuments: number;
}

@Component({
    standalone: true,
    selector: 'app-project-stats-widget',
    imports: [CommonModule, SkeletonModule],
    template: `
        <div class="col-span-12 lg:col-span-6 xl:col-span-3">
            <div class="card mb-0">
                <div class="flex justify-between mb-4">
                    <div>
                        <span class="block text-muted-color font-medium mb-4"><PERSON>llanıcılar</span>
                        <div class="text-surface-900 dark:text-surface-0 font-medium text-xl">
                            <p-skeleton *ngIf="isLoading" width="3rem" height="1.5rem"></p-skeleton>
                            <span *ngIf="!isLoading">{{ stats?.totalUsers || 0 }}</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-center bg-blue-100 dark:bg-blue-400/10 rounded-border" style="width: 2.5rem; height: 2.5rem">
                        <i class="pi pi-users text-blue-500 !text-xl"></i>
                    </div>
                </div>
                <div *ngIf="!isLoading">
                    <span class="text-primary font-medium">{{ stats?.activeUsers || 0 }} aktif </span>
                    <span class="text-muted-color">kullanıcı</span>
                </div>
                <p-skeleton *ngIf="isLoading" width="8rem" height="1rem"></p-skeleton>
            </div>
        </div>

        <div class="col-span-12 lg:col-span-6 xl:col-span-3">
            <div class="card mb-0">
                <div class="flex justify-between mb-4">
                    <div>
                        <span class="block text-muted-color font-medium mb-4">Teknolojiler</span>
                        <div class="text-surface-900 dark:text-surface-0 font-medium text-xl">
                            <p-skeleton *ngIf="isLoading" width="3rem" height="1.5rem"></p-skeleton>
                            <span *ngIf="!isLoading">{{ stats?.totalTechnologies || 0 }}</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-center bg-purple-100 dark:bg-purple-400/10 rounded-border" style="width: 2.5rem; height: 2.5rem">
                        <i class="pi pi-code text-purple-500 !text-xl"></i>
                    </div>
                </div>
                <div *ngIf="!isLoading">
                    <span class="text-primary font-medium">{{ stats?.activeTechnologies || 0 }} aktif </span>
                    <span class="text-muted-color">teknoloji</span>
                </div>
                <p-skeleton *ngIf="isLoading" width="8rem" height="1rem"></p-skeleton>
            </div>
        </div>

        <div class="col-span-12 lg:col-span-6 xl:col-span-3">
            <div class="card mb-0">
                <div class="flex justify-between mb-4">
                    <div>
                        <span class="block text-muted-color font-medium mb-4">Platformlar</span>
                        <div class="text-surface-900 dark:text-surface-0 font-medium text-xl">
                            <p-skeleton *ngIf="isLoading" width="3rem" height="1.5rem"></p-skeleton>
                            <span *ngIf="!isLoading">{{ stats?.totalPlatforms || 0 }}</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-center bg-orange-100 dark:bg-orange-400/10 rounded-border" style="width: 2.5rem; height: 2.5rem">
                        <i class="pi pi-server text-orange-500 !text-xl"></i>
                    </div>
                </div>
                <div *ngIf="!isLoading">
                    <span class="text-primary font-medium">{{ stats?.activePlatforms || 0 }} aktif </span>
                    <span class="text-muted-color">platform</span>
                </div>
                <p-skeleton *ngIf="isLoading" width="8rem" height="1rem"></p-skeleton>
            </div>
        </div>

        <div class="col-span-12 lg:col-span-6 xl:col-span-3">
            <div class="card mb-0">
                <div class="flex justify-between mb-4">
                    <div>
                        <span class="block text-muted-color font-medium mb-4">Dokümanlar</span>
                        <div class="text-surface-900 dark:text-surface-0 font-medium text-xl">
                            <p-skeleton *ngIf="isLoading" width="3rem" height="1.5rem"></p-skeleton>
                            <span *ngIf="!isLoading">{{ stats?.totalDocuments || 0 }}</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-center bg-cyan-100 dark:bg-cyan-400/10 rounded-border" style="width: 2.5rem; height: 2.5rem">
                        <i class="pi pi-file text-cyan-500 !text-xl"></i>
                    </div>
                </div>
                <div *ngIf="!isLoading">
                    <span class="text-primary font-medium">{{ stats?.publishedDocuments || 0 }} yayınlı </span>
                    <span class="text-muted-color">doküman</span>
                </div>
                <p-skeleton *ngIf="isLoading" width="8rem" height="1rem"></p-skeleton>
            </div>
        </div>
    `,
    styles: []
})
export class ProjectStatsWidget implements OnInit, OnDestroy {
    private destroy$ = new Subject<void>();

    stats: StatsData | null = null;
    isLoading = true;

    constructor(
        private userService: UserService,
        private technologyService: TechnologyService,
        private platformService: PlatformService,
        private documentService: DocumentService
    ) {}

    ngOnInit() {
        this.loadStats();
    }

    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
    }

    loadStats() {
        this.isLoading = true;

        forkJoin({
            users: this.userService.getUsers(),
            technologies: this.technologyService.getTechnologies(),
            platforms: this.platformService.getPlatforms(),
            documents: this.documentService.getDocuments()
        }).pipe(
            takeUntil(this.destroy$)
        ).subscribe(({ users, technologies, platforms, documents }) => {
            this.stats = {
                totalUsers: users.length,
                activeUsers: users.filter(u => u.isActive).length,
                totalTechnologies: technologies.length,
                activeTechnologies: technologies.filter(t => t.isActive).length,
                totalPlatforms: platforms.length,
                activePlatforms: platforms.filter(p => p.isActive).length,
                totalDocuments: documents.length,
                publishedDocuments: documents.filter(d => d.status === 'Yayınlandı').length
            };
            this.isLoading = false;
        });
    }
}

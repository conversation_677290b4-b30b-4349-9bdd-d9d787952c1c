import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { InputTextModule } from 'primeng/inputtext';
import { AvatarModule } from 'primeng/avatar';
import { DividerModule } from 'primeng/divider';
import { AuthService } from '../../../../core/services/auth.service';
import { User } from '../../../../core/models/user.model';

@Component({
    selector: 'app-profile',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        ButtonModule,
        CardModule,
        InputTextModule,
        AvatarModule,
        DividerModule
    ],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <h5>Profil</h5>
                    <p-divider></p-divider>

                    <div class="grid">
                        <div class="col-12 md:col-3">
                            <div class="flex flex-column align-items-center">
                                <p-avatar 
                                    [label]="getInitials(currentUser)"
                                    styleClass="mr-2"
                                    size="xlarge"
                                    shape="circle">
                                </p-avatar>
                                <h6 class="mt-2 mb-1">{{currentUser?.firstName}} {{currentUser?.lastName}}</h6>
                                <span class="text-500">{{currentUser?.email}}</span>
                            </div>
                        </div>

                        <div class="col-12 md:col-9">
                            <form [formGroup]="profileForm" (ngSubmit)="onSubmit()">
                                <div class="grid formgrid">
                                    <div class="field col-12 md:col-6">
                                        <label for="firstName">Ad</label>
                                        <input pInputText
                                            id="firstName"
                                            type="text"
                                            formControlName="firstName"
                                            class="w-full"
                                            [readonly]="true">
                                    </div>

                                    <div class="field col-12 md:col-6">
                                        <label for="lastName">Soyad</label>
                                        <input pInputText
                                            id="lastName"
                                            type="text"
                                            formControlName="lastName"
                                            class="w-full"
                                            [readonly]="true">
                                    </div>

                                    <div class="field col-12">
                                        <label for="email">E-posta</label>
                                        <input pInputText
                                            id="email"
                                            type="email"
                                            formControlName="email"
                                            class="w-full"
                                            [readonly]="true">
                                    </div>

                                    <div class="field col-12">
                                        <p-divider></p-divider>
                                        <h6>Roller</h6>
                                        <div class="flex flex-wrap gap-2">
                                            <span *ngFor="let role of currentUser?.roles"
                                                class="inline-flex align-items-center py-1 px-2 rounded-2xl text-sm"
                                                [style.background-color]="role.isSystem ? 'var(--primary-100)' : 'var(--surface-200)'">
                                                {{role.name}}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `
})
export class ProfileComponent implements OnInit {
    profileForm: FormGroup;
    currentUser: User | null = null;

    constructor(
        private fb: FormBuilder,
        private authService: AuthService
    ) {
        this.profileForm = this.fb.group({
            firstName: ['', Validators.required],
            lastName: ['', Validators.required],
            email: ['', [Validators.required, Validators.email]]
        });
    }

    ngOnInit() {
        const auth = this.authService.getCurrentUser();
        if (auth) {
            this.currentUser = auth.user;
            this.profileForm.patchValue({
                firstName: auth.user.firstName,
                lastName: auth.user.lastName,
                email: auth.user.email
            });
        }
    }

    getInitials(user: User | null): string {
        if (!user) return '?';
        return (user.firstName.charAt(0) + user.lastName.charAt(0)).toUpperCase();
    }

    onSubmit() {
        if (this.profileForm.valid) {
            // TODO: Profil güncelleme işlemleri backend entegrasyonunda yapılacak
            console.log('Form submitted:', this.profileForm.value);
        }
    }
}

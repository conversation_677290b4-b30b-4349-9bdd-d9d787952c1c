import { HttpInterceptorFn, HttpErrorResponse } from '@angular/common/http';
import { inject } from '@angular/core';
import { catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';

export const errorInterceptor: HttpInterceptorFn = (req, next) => {
  const router = inject(Router);
  const messageService = inject(MessageService);

  return next(req).pipe(
    catchError((error: HttpErrorResponse) => {
      if ([401, 403].includes(error.status)) {
        // Auto logout if 401 or 403 response returned from api
        localStorage.clear();
        router.navigate(['/auth/login']);
      }

      const errorMessage = error.error?.message || 'Bir hata oluştu';
      messageService.add({
        severity: 'error',
        summary: 'Hata',
        detail: errorMessage
      });

      return throwError(() => error);
    })
  );
};

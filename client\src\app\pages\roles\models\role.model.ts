export interface Role {
    id: string;
    roleCode: string;
    name: string;
    description: string;
    permissions: Permission[];
    userCount: number;
    isSystemRole: boolean;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
    createdBy: string;
    updatedBy?: string;
    notes?: string;
}

export interface Permission {
    id: string;
    name: string;
    code: string;
    module: string;
    action: PermissionAction;
    resource: string;
    description: string;
    isSystemPermission: boolean;
    category: PermissionCategory;
}

export interface RoleFormData {
    roleCode: string;
    name: string;
    description: string;
    permissions: string[];
    isActive: boolean;
    notes?: string;
}

export interface RolePermissionGroup {
    module: string;
    permissions: Permission[];
}

export interface RoleAssignment {
    id: string;
    roleId: string;
    roleName: string;
    userId: string;
    userName: string;
    userEmail: string;
    assignedAt: Date;
    assignedBy: string;
    isActive: boolean;
}

export enum PermissionAction {
    CREATE = 'Oluştur',
    READ = 'Oku',
    UPDATE = 'Güncelle',
    DELETE = 'Sil',
    APPROVE = 'Onayla',
    REJECT = 'Reddet',
    EXPORT = 'Dışa Aktar',
    IMPORT = 'İçe Aktar',
    MANAGE = 'Yönet',
    VIEW_ALL = 'Tümünü Görüntüle',
    ASSIGN = 'Ata',
    UNASSIGN = 'Atamayı Kaldır'
}

export enum PermissionCategory {
    PROJECT_MANAGEMENT = 'Proje Yönetimi',
    USER_MANAGEMENT = 'Kullanıcı Yönetimi',
    TEAM_MANAGEMENT = 'Takım Yönetimi',
    CUSTOMER_MANAGEMENT = 'Müşteri Yönetimi',
    SYSTEM_ADMINISTRATION = 'Sistem Yönetimi',
    REPORTING = 'Raporlama',
    CONFIGURATION = 'Konfigürasyon',
    SECURITY = 'Güvenlik',
    AUDIT = 'Denetim',
    INTEGRATION = 'Entegrasyon'
}

export const SYSTEM_PERMISSIONS: Permission[] = [
    // Proje Yönetimi
    {
        id: 'p1',
        name: 'Proje Görüntüleme',
        code: 'PROJECT_READ',
        module: 'Projects',
        action: PermissionAction.READ,
        resource: 'projects',
        description: 'Projeleri görüntüleme yetkisi',
        isSystemPermission: true,
        category: PermissionCategory.PROJECT_MANAGEMENT
    },
    {
        id: 'p2',
        name: 'Proje Oluşturma',
        code: 'PROJECT_CREATE',
        module: 'Projects',
        action: PermissionAction.CREATE,
        resource: 'projects',
        description: 'Yeni proje oluşturma yetkisi',
        isSystemPermission: true,
        category: PermissionCategory.PROJECT_MANAGEMENT
    },
    {
        id: 'p3',
        name: 'Proje Güncelleme',
        code: 'PROJECT_UPDATE',
        module: 'Projects',
        action: PermissionAction.UPDATE,
        resource: 'projects',
        description: 'Proje bilgilerini güncelleme yetkisi',
        isSystemPermission: true,
        category: PermissionCategory.PROJECT_MANAGEMENT
    },
    {
        id: 'p4',
        name: 'Proje Silme',
        code: 'PROJECT_DELETE',
        module: 'Projects',
        action: PermissionAction.DELETE,
        resource: 'projects',
        description: 'Proje silme yetkisi',
        isSystemPermission: true,
        category: PermissionCategory.PROJECT_MANAGEMENT
    },
    // Kullanıcı Yönetimi
    {
        id: 'p5',
        name: 'Kullanıcı Görüntüleme',
        code: 'USER_READ',
        module: 'Users',
        action: PermissionAction.READ,
        resource: 'users',
        description: 'Kullanıcıları görüntüleme yetkisi',
        isSystemPermission: true,
        category: PermissionCategory.USER_MANAGEMENT
    },
    {
        id: 'p6',
        name: 'Kullanıcı Oluşturma',
        code: 'USER_CREATE',
        module: 'Users',
        action: PermissionAction.CREATE,
        resource: 'users',
        description: 'Yeni kullanıcı oluşturma yetkisi',
        isSystemPermission: true,
        category: PermissionCategory.USER_MANAGEMENT
    },
    {
        id: 'p7',
        name: 'Kullanıcı Güncelleme',
        code: 'USER_UPDATE',
        module: 'Users',
        action: PermissionAction.UPDATE,
        resource: 'users',
        description: 'Kullanıcı bilgilerini güncelleme yetkisi',
        isSystemPermission: true,
        category: PermissionCategory.USER_MANAGEMENT
    },
    {
        id: 'p8',
        name: 'Kullanıcı Silme',
        code: 'USER_DELETE',
        module: 'Users',
        action: PermissionAction.DELETE,
        resource: 'users',
        description: 'Kullanıcı silme yetkisi',
        isSystemPermission: true,
        category: PermissionCategory.USER_MANAGEMENT
    },
    // Takım Yönetimi
    {
        id: 'p9',
        name: 'Takım Görüntüleme',
        code: 'TEAM_READ',
        module: 'Teams',
        action: PermissionAction.READ,
        resource: 'teams',
        description: 'Takımları görüntüleme yetkisi',
        isSystemPermission: true,
        category: PermissionCategory.TEAM_MANAGEMENT
    },
    {
        id: 'p10',
        name: 'Takım Yönetimi',
        code: 'TEAM_MANAGE',
        module: 'Teams',
        action: PermissionAction.MANAGE,
        resource: 'teams',
        description: 'Takım yönetimi yetkisi',
        isSystemPermission: true,
        category: PermissionCategory.TEAM_MANAGEMENT
    },
    // Müşteri Yönetimi
    {
        id: 'p11',
        name: 'Müşteri Görüntüleme',
        code: 'CUSTOMER_READ',
        module: 'Customers',
        action: PermissionAction.READ,
        resource: 'customers',
        description: 'Müşterileri görüntüleme yetkisi',
        isSystemPermission: true,
        category: PermissionCategory.CUSTOMER_MANAGEMENT
    },
    {
        id: 'p12',
        name: 'Müşteri Yönetimi',
        code: 'CUSTOMER_MANAGE',
        module: 'Customers',
        action: PermissionAction.MANAGE,
        resource: 'customers',
        description: 'Müşteri yönetimi yetkisi',
        isSystemPermission: true,
        category: PermissionCategory.CUSTOMER_MANAGEMENT
    },
    // Sistem Yönetimi
    {
        id: 'p13',
        name: 'Rol Yönetimi',
        code: 'ROLE_MANAGE',
        module: 'Roles',
        action: PermissionAction.MANAGE,
        resource: 'roles',
        description: 'Rol yönetimi yetkisi',
        isSystemPermission: true,
        category: PermissionCategory.SYSTEM_ADMINISTRATION
    },
    {
        id: 'p14',
        name: 'Sistem Konfigürasyonu',
        code: 'SYSTEM_CONFIG',
        module: 'System',
        action: PermissionAction.MANAGE,
        resource: 'system',
        description: 'Sistem konfigürasyonu yetkisi',
        isSystemPermission: true,
        category: PermissionCategory.SYSTEM_ADMINISTRATION
    },
    // Raporlama
    {
        id: 'p15',
        name: 'Rapor Görüntüleme',
        code: 'REPORT_READ',
        module: 'Reports',
        action: PermissionAction.READ,
        resource: 'reports',
        description: 'Raporları görüntüleme yetkisi',
        isSystemPermission: true,
        category: PermissionCategory.REPORTING
    },
    {
        id: 'p16',
        name: 'Rapor Dışa Aktarma',
        code: 'REPORT_EXPORT',
        module: 'Reports',
        action: PermissionAction.EXPORT,
        resource: 'reports',
        description: 'Rapor dışa aktarma yetkisi',
        isSystemPermission: true,
        category: PermissionCategory.REPORTING
    }
];

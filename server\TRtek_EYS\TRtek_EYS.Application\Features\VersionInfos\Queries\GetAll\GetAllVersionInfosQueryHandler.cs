﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.VersionInfos.Queries.GetAll
{
    public class GetAllVersionInfosQueryHandler : IRequestHandler<GetAllVersionInfosQuery, List<VersionInfoGetAllDto>>
    {
        private readonly IApplicationDbContext _context;

        public GetAllVersionInfosQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<VersionInfoGetAllDto>> Handle(GetAllVersionInfosQuery request, CancellationToken cancellationToken)
        {
            return await _context.VersionInfos
                .AsNoTracking()
                .Select(v => new VersionInfoGetAllDto
                {
                    Id = v.Id,
                    VersionNumber = v.VersionNumber,
                    ReleaseDate = v.ReleaseDate,
                    ReleaseNotes = v.ReleaseNotes,
                    Status = v.Status,
                    ProjectId = v.ProjectId
                })
                .ToListAsync(cancellationToken);
        }
    }
}

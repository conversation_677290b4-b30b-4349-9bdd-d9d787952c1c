export interface TeamMember {
    id: string;
    userId: string;
    name: string;
    email: string;
    role: TeamRole;
    joinDate: Date;
    status: MemberStatus;
    skills: string[];
    workloadPercentage: number;
    isTeamLead: boolean;
}

export interface Team {
    id: string;
    teamCode: string;
    name: string;
    description: string;
    department: Department;
    teamLead: string;
    members: TeamMember[];
    createdAt: Date;
    updatedAt: Date;
    status: TeamStatus;
    activeProjects: number;
    completedProjects: number;
    totalProjects: number;
    budget?: number;
    location?: string;
    technologies: string[];
    notes?: string;
}

export interface TeamFormData {
    teamCode: string;
    name: string;
    description: string;
    department: Department;
    teamLead: string;
    status: TeamStatus;
    budget?: number;
    location?: string;
    notes?: string;
}

export enum TeamStatus {
    ACTIVE = 'Aktif',
    INACTIVE = 'Pasif',
    SUSPENDED = 'Askıya Alındı',
    DISBANDED = 'Dağıtıldı'
}

export enum MemberStatus {
    ACTIVE = 'Aktif',
    INACTIVE = 'Pasif',
    ON_LEAVE = 'İzinli',
    TRANSFERRED = 'Transfer Edildi'
}

export enum TeamRole {
    TEAM_LEAD = 'Takım Lideri',
    SENIOR_DEVELOPER = 'Kıdemli Geliştirici',
    DEVELOPER = 'Geliştirici',
    JUNIOR_DEVELOPER = 'Junior Geliştirici',
    FRONTEND_DEVELOPER = 'Frontend Geliştirici',
    BACKEND_DEVELOPER = 'Backend Geliştirici',
    FULLSTACK_DEVELOPER = 'Fullstack Geliştirici',
    MOBILE_DEVELOPER = 'Mobil Geliştirici',
    DEVOPS_ENGINEER = 'DevOps Mühendisi',
    QA_ENGINEER = 'QA Mühendisi',
    UI_UX_DESIGNER = 'UI/UX Tasarımcı',
    BUSINESS_ANALYST = 'İş Analisti',
    SCRUM_MASTER = 'Scrum Master',
    ARCHITECT = 'Mimar'
}

export enum Department {
    DEVELOPMENT = 'Geliştirme',
    DESIGN = 'Tasarım',
    QA = 'Kalite Güvence',
    DEVOPS = 'DevOps',
    MANAGEMENT = 'Yönetim',
    BUSINESS_ANALYSIS = 'İş Analizi',
    SUPPORT = 'Destek'
}

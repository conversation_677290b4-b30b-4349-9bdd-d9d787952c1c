﻿using MediatR;
using TRtek_EYS.Application.Common.Interfaces;
using TRtek_EYS.Domain.Entities;

namespace TRtek_EYS.Application.Features.Roles.Commands.Add
{
    public class AddRoleCommandHandler : IRequestHandler<AddRoleCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public AddRoleCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(AddRoleCommand request, CancellationToken cancellationToken)
        {
            var role = new Role
            {
                Name = request.Name,
                Description = request.Description,
                TeamId = request.TeamId,
                CreatedAt = DateTime.UtcNow
            };

            await _context.Roles.AddAsync(role, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);

            return role.Id;
        }
    }
}

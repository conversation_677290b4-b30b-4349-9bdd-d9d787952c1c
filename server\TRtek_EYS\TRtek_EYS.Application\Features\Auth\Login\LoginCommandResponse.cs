﻿namespace TRtek_EYS.Application.Features.Auth.Login
{
    public class LoginCommandResponse
    {
        public Guid UserId { get; init; }
        public string Username { get; init; } = string.Empty;
        public string? FullName { get; set; }

        public string Token { get; init; } = string.Empty;
        public string RefreshToken { get; init; } = string.Empty;
        public DateTime RefreshTokenExpires { get; init; }
    }
}

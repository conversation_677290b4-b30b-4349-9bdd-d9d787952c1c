﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<TreatWarningsAsErrors>true</TreatWarningsAsErrors>
	</PropertyGroup>

	<ItemGroup>
		
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.4" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.4">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.4" />
		<PackageReference Include="Scrutor" Version="4.2.2" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\TRtek_EYS.Application\TRtek_EYS.Application.csproj" />
		<ProjectReference Include="..\TRtek_EYS.Domain\TRtek_EYS.Domain.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Services\" />
	</ItemGroup>

</Project>

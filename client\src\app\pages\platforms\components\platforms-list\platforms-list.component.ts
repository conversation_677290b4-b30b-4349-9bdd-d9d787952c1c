import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Table, TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { InputIconModule } from 'primeng/inputicon';
import { IconFieldModule } from 'primeng/iconfield';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToastModule } from 'primeng/toast';
import { ConfirmationService, MessageService } from 'primeng/api';
import { Platform, PlatformType, PlatformCategory, SupportLevel, LicenseType } from '../../models/platform.model';
import { PlatformService } from '../../services/platform.service';

@Component({
    selector: 'app-platforms-list',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        TableModule,
        ButtonModule,
        InputTextModule,
        InputIconModule,
        IconFieldModule,
        TagModule,
        TooltipModule,
        ConfirmDialogModule,
        ToastModule
    ],
    providers: [ConfirmationService, MessageService],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <h5>Platform Yönetimi</h5>
                        <p-button
                            icon="pi pi-plus"
                            (onClick)="createPlatform()"
                            severity="primary"
                            rounded
                            outlined
                            pTooltip="Yeni Platform">
                        </p-button>
                    </div>

                    <p-table 
                        #dt 
                        [value]="platforms" 
                        [rows]="10" 
                        [paginator]="true"
                        [globalFilterFields]="['platformCode', 'name', 'vendor', 'type', 'category']"
                        [tableStyle]="{ 'min-width': '75rem' }"
                        [rowHover]="true"
                        dataKey="id"
                        currentPageReportTemplate="{first} - {last} / {totalRecords} platform"
                        [showCurrentPageReport]="true">
                        
                        <ng-template pTemplate="caption">
                            <div class="flex align-items-center justify-content-between">
                                <h5 class="m-0">Platformlar</h5>
                                <p-iconfield iconPosition="left">
                                    <p-inputicon>
                                        <i class="pi pi-search"></i>
                                    </p-inputicon>
                                    <input 
                                        pInputText 
                                        type="text" 
                                        (input)="dt.filterGlobal($any($event.target).value, 'contains')" 
                                        placeholder="Platform ara..." />
                                </p-iconfield>
                            </div>
                        </ng-template>

                        <ng-template pTemplate="header">
                            <tr>
                                <th pSortableColumn="platformCode">
                                    Platform Kodu <p-sortIcon field="platformCode"></p-sortIcon>
                                </th>
                                <th pSortableColumn="name">
                                    Platform Adı <p-sortIcon field="name"></p-sortIcon>
                                </th>
                                <th pSortableColumn="vendor">
                                    Sağlayıcı <p-sortIcon field="vendor"></p-sortIcon>
                                </th>
                                <th pSortableColumn="type">
                                    Tip <p-sortIcon field="type"></p-sortIcon>
                                </th>
                                <th pSortableColumn="version">
                                    Versiyon <p-sortIcon field="version"></p-sortIcon>
                                </th>
                                <th>Proje Sayısı</th>
                                <th pSortableColumn="supportLevel">
                                    Destek <p-sortIcon field="supportLevel"></p-sortIcon>
                                </th>
                                <th pSortableColumn="licenseType">
                                    Lisans <p-sortIcon field="licenseType"></p-sortIcon>
                                </th>
                                <th pSortableColumn="isActive">
                                    Durum <p-sortIcon field="isActive"></p-sortIcon>
                                </th>
                                <th>İşlemler</th>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="body" let-platform>
                            <tr>
                                <td>
                                    <span class="font-medium">{{ platform.platformCode }}</span>
                                </td>
                                <td>
                                    <div>
                                        <span class="font-medium">{{ platform.name }}</span>
                                        <div class="text-sm text-500 mt-1">{{ platform.description | slice:0:50 }}...</div>
                                    </div>
                                </td>
                                <td>
                                    <span>{{ platform.vendor }}</span>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="platform.type" 
                                        severity="info">
                                    </p-tag>
                                </td>
                                <td>
                                    <span class="font-medium">{{ platform.version }}</span>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <span class="font-medium" 
                                              [class]="platform.projectCount > 0 ? 'text-green-600' : 'text-500'">
                                            {{ platform.projectCount }}
                                        </span>
                                        <span class="text-500"> proje</span>
                                    </div>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="platform.supportLevel" 
                                        [severity]="getSupportSeverity(platform.supportLevel)">
                                    </p-tag>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="platform.licenseType" 
                                        [severity]="getLicenseSeverity(platform.licenseType)">
                                    </p-tag>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="platform.isActive ? 'Aktif' : 'Pasif'" 
                                        [severity]="platform.isActive ? 'success' : 'secondary'">
                                    </p-tag>
                                </td>
                                <td>
                                    <div class="flex gap-2">
                                        <p-button 
                                            icon="pi pi-eye" 
                                            class="p-button-rounded p-button-text p-button-info"
                                            pTooltip="Detay"
                                            (onClick)="viewPlatform(platform.id)">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-pencil" 
                                            class="p-button-rounded p-button-text p-button-warning"
                                            pTooltip="Düzenle"
                                            (onClick)="editPlatform(platform.id)">
                                        </p-button>
                                        <p-button 
                                            [icon]="platform.isActive ? 'pi pi-pause' : 'pi pi-play'" 
                                            class="p-button-rounded p-button-text"
                                            [class.p-button-secondary]="platform.isActive"
                                            [class.p-button-success]="!platform.isActive"
                                            [pTooltip]="platform.isActive ? 'Pasifleştir' : 'Aktifleştir'"
                                            (onClick)="togglePlatformStatus(platform)">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-trash" 
                                            class="p-button-rounded p-button-text p-button-danger"
                                            pTooltip="Sil"
                                            (onClick)="deletePlatform(platform)"
                                            [disabled]="platform.projectCount > 0">
                                        </p-button>
                                    </div>
                                </td>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="emptymessage">
                            <tr>
                                <td colspan="10" class="text-center py-4">
                                    <i class="pi pi-info-circle text-4xl text-500 mb-3"></i>
                                    <div class="text-500 font-medium">Henüz platform bulunmuyor</div>
                                    <div class="text-500">Yeni platform eklemek için "Yeni Platform" butonunu kullanın</div>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                </div>
            </div>
        </div>

        <p-confirmDialog></p-confirmDialog>
        <p-toast></p-toast>
    `
})
export class PlatformsListComponent implements OnInit {
    @ViewChild('dt') table!: Table;
    
    platforms: Platform[] = [];

    constructor(
        private platformService: PlatformService,
        private router: Router,
        private confirmationService: ConfirmationService,
        private messageService: MessageService
    ) {}

    ngOnInit() {
        this.loadPlatforms();
    }

    loadPlatforms() {
        // V2 API'yi kullan
        this.platformService.getPlatformsV2().subscribe({
            next: (platforms) => {
                this.platforms = platforms;
                console.log('V2 API Platforms loaded:', platforms);
            },
            error: (error) => {
                console.error('V2 API Error, falling back to V1:', error);
                // Hata durumunda V1 API'ye geri dön
                this.platformService.getPlatforms().subscribe(platforms => {
                    this.platforms = platforms;
                });
            }
        });
    }

    createPlatform() {
        this.router.navigate(['/pages/platforms/new']);
    }

    viewPlatform(id: string) {
        this.router.navigate(['/pages/platforms', id]);
    }

    editPlatform(id: string) {
        this.router.navigate(['/pages/platforms', id, 'edit']);
    }

    togglePlatformStatus(platform: Platform) {
        const action = platform.isActive ? 'pasifleştirmek' : 'aktifleştirmek';
        this.confirmationService.confirm({
            message: `"${platform.name}" platformunu ${action} istediğinizden emin misiniz?`,
            header: 'Platform Durumu Değiştirme Onayı',
            icon: platform.isActive ? 'pi pi-pause' : 'pi pi-play',
            acceptLabel: 'Evet',
            rejectLabel: 'Hayır',
            accept: () => {
                this.platformService.togglePlatformStatus(platform.id).subscribe(() => {
                    this.loadPlatforms();
                    this.messageService.add({
                        severity: 'info',
                        summary: 'Başarılı',
                        detail: `Platform ${platform.isActive ? 'pasifleştirildi' : 'aktifleştirildi'}`
                    });
                });
            }
        });
    }

    deletePlatform(platform: Platform) {
        this.confirmationService.confirm({
            message: `"${platform.name}" platformunu silmek istediğinizden emin misiniz?`,
            header: 'Platform Silme Onayı',
            icon: 'pi pi-exclamation-triangle',
            acceptLabel: 'Evet',
            rejectLabel: 'Hayır',
            accept: () => {
                this.platformService.deletePlatform(platform.id).subscribe(success => {
                    if (success) {
                        this.platforms = this.platforms.filter(p => p.id !== platform.id);
                        this.messageService.add({
                            severity: 'success',
                            summary: 'Başarılı',
                            detail: 'Platform başarıyla silindi'
                        });
                    } else {
                        this.messageService.add({
                            severity: 'error',
                            summary: 'Hata',
                            detail: 'Platform silinemedi. Kullanımda olan platformlar silinemez.'
                        });
                    }
                });
            }
        });
    }

    getSupportSeverity(supportLevel: SupportLevel): string {
        switch (supportLevel) {
            case SupportLevel.FULL_SUPPORT:
                return 'success';
            case SupportLevel.LIMITED_SUPPORT:
                return 'warning';
            case SupportLevel.COMMUNITY_SUPPORT:
                return 'info';
            case SupportLevel.NO_SUPPORT:
                return 'secondary';
            case SupportLevel.DEPRECATED:
                return 'danger';
            default:
                return 'info';
        }
    }

    getLicenseSeverity(licenseType: LicenseType): string {
        switch (licenseType) {
            case LicenseType.OPEN_SOURCE:
                return 'success';
            case LicenseType.COMMERCIAL:
                return 'warning';
            case LicenseType.FREEMIUM:
                return 'info';
            case LicenseType.SUBSCRIPTION:
                return 'secondary';
            case LicenseType.ENTERPRISE:
                return 'danger';
            case LicenseType.CUSTOM:
                return 'info';
            default:
                return 'info';
        }
    }
}

import { Component, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { MenuItem } from './service/menu.service';
import { MenuService } from './service/menu.service';
import { SidebarMenuComponent } from './component/sidebar-menu/sidebar-menu.component';
import { TopbarComponent } from './component/topbar/topbar.component';
import { FooterComponent } from './component/footer/footer.component';

@Component({
    selector: 'app-layout',
    standalone: true,
    imports: [RouterOutlet, SidebarMenuComponent, TopbarComponent, FooterComponent],
    template: `
        <div class="layout-wrapper">
            <app-topbar></app-topbar>
            <app-sidebar-menu></app-sidebar-menu>
            <div class="layout-main-container">
                <div class="layout-main">
                    <router-outlet></router-outlet>
                </div>
            </div>
            <app-footer></app-footer>
        </div>
    `,
    styles: [`
        .layout-wrapper {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .layout-main-container {
            flex: 1;
            padding: 2rem;
        }
    `]
})
export class LayoutComponent implements OnInit {
    menuItems: MenuItem[] = [];

    constructor(private menuService: MenuService) {}

    ngOnInit() {
        this.menuService.menuItems$.subscribe(items => {
            this.menuItems = items;
        });
    }
}

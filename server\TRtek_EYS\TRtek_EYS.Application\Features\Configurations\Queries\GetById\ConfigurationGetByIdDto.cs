﻿namespace TRtek_EYS.Application.Features.Configurations.Queries.GetById
{
    public class ConfigurationGetByIdDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = null!;
        public string? Description { get; set; }
        public string? WebServerIp { get; set; }
        public string? WebServerUrl { get; set; }
        public string? DatabaseType { get; set; }
        public string? DatabaseVersion { get; set; }
        public string? Username { get; set; }
        public Guid CustomerId { get; set; }
    }
}

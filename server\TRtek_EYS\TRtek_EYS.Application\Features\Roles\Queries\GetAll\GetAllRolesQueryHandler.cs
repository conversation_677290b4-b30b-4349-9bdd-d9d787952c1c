﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Roles.Queries.GetAll
{
    public class GetAllRolesQueryHandler : IRequestHandler<GetAllRolesQuery, List<RoleGetAllDto>>
    {
        private readonly IApplicationDbContext _context;

        public GetAllRolesQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<RoleGetAllDto>> Handle(GetAllRolesQuery request, CancellationToken cancellationToken)
        {
            return await _context.Roles
                .AsNoTracking()
                .Select(r => new RoleGetAllDto
                {
                    Id = r.Id,
                    Name = r.Name,
                    Description = r.Description,
                    TeamId = r.TeamId
                })
                .ToListAsync(cancellationToken);
        }
    }
}

import { Role } from './role.model';

export class User {
    id!: string;
    email!: string;
    firstName!: string;
    lastName!: string;
    roles!: Role[];
    isActive!: boolean;
    createdAt!: Date;
    updatedAt!: Date;
    lastLogin?: Date;

    constructor(data: Partial<User> = {}) {
        Object.assign(this, data);
    }

    get fullName(): string {
        return `${this.firstName} ${this.lastName}`;
    }
}

export class UserProfile extends User {
    avatar?: string;
    phone?: string;
    department?: string;
    position?: string;

    constructor(data: Partial<UserProfile> = {}) {
        super(data);
        Object.assign(this, data);
    }
}

export interface AuthUser {
    user: User;
    accessToken: string;
    refreshToken: string;
    tokenExpires: Date;
}

export interface TokenResponse {
    accessToken: string;
    refreshToken: string;
    tokenExpires: Date;
}

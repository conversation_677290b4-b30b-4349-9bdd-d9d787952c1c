﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Configurations.Queries.GetById
{
    public class GetConfigurationByIdQueryHandler : IRequestHandler<GetConfigurationByIdQuery, ConfigurationGetByIdDto>
    {
        private readonly IApplicationDbContext _context;

        public GetConfigurationByIdQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<ConfigurationGetByIdDto> Handle(GetConfigurationByIdQuery request, CancellationToken cancellationToken)
        {
            var entity = await _context.Configurations
                .AsNoTracking()
                .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken);

            if (entity == null)
                throw new Exception("Konfigürasyon bulunamadı.");

            return new ConfigurationGetByIdDto
            {
                Id = entity.Id,
                Name = entity.Name,
                Description = entity.Description,
                WebServerIp = entity.WebServerIp,
                WebServerUrl = entity.WebServerUrl,
                DatabaseType = entity.DatabaseType,
                DatabaseVersion = entity.DatabaseVersion,
                Username = entity.Username,
                CustomerId = entity.CustomerId
            };
        }
    }
}

import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Table, TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { InputIconModule } from 'primeng/inputicon';
import { IconFieldModule } from 'primeng/iconfield';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToastModule } from 'primeng/toast';
import { ConfirmationService, MessageService } from 'primeng/api';
import { Team, TeamStatus, Department } from '../../models/team.model';
import { TeamService } from '../../services/team.service';

@Component({
    standalone: true,
    selector: 'app-teams-list',
    imports: [
        CommonModule,
        FormsModule,
        TableModule,
        ButtonModule,
        InputTextModule,
        InputIconModule,
        IconFieldModule,
        TagModule,
        TooltipModule,
        ConfirmDialogModule,
        ToastModule
    ],
    providers: [ConfirmationService, MessageService],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <h5>Takım Yönetimi</h5>
                        <p-button
                            label="Yeni Takım"
                            icon="pi pi-plus"
                            (onClick)="createTeam()"
                            severity="primary"
                            size="small">
                        </p-button>
                    </div>

                    <p-table
                        #dt
                        [value]="teams"
                        [rows]="10"
                        [paginator]="true"
                        [globalFilterFields]="['teamCode', 'name', 'department', 'teamLead']"
                        [tableStyle]="{ 'min-width': '75rem' }"
                        [rowHover]="true"
                        dataKey="id"
                        currentPageReportTemplate="{first} - {last} / {totalRecords} takım"
                        [showCurrentPageReport]="true">

                        <ng-template pTemplate="caption">
                            <div class="flex align-items-center justify-content-between">
                                <h5 class="m-0">Takımlar</h5>
                                <p-iconfield iconPosition="left">
                                    <p-inputicon>
                                        <i class="pi pi-search"></i>
                                    </p-inputicon>
                                    <input
                                        pInputText
                                        type="text"
                                        (input)="dt.filterGlobal($any($event.target).value, 'contains')"
                                        placeholder="Takım ara..." />
                                </p-iconfield>
                            </div>
                        </ng-template>

                        <ng-template pTemplate="header">
                            <tr>
                                <th pSortableColumn="teamCode">
                                    Takım Kodu <p-sortIcon field="teamCode"></p-sortIcon>
                                </th>
                                <th pSortableColumn="name">
                                    Takım Adı <p-sortIcon field="name"></p-sortIcon>
                                </th>
                                <th pSortableColumn="department">
                                    Departman <p-sortIcon field="department"></p-sortIcon>
                                </th>
                                <th pSortableColumn="teamLead">
                                    Takım Lideri <p-sortIcon field="teamLead"></p-sortIcon>
                                </th>
                                <th>Üye Sayısı</th>
                                <th>Projeler</th>
                                <th pSortableColumn="status">
                                    Durum <p-sortIcon field="status"></p-sortIcon>
                                </th>
                                <th>İşlemler</th>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="body" let-team>
                            <tr>
                                <td>
                                    <span class="font-medium">{{ team.teamCode }}</span>
                                </td>
                                <td>
                                    <div>
                                        <span class="font-medium">{{ team.name }}</span>
                                        <div class="text-sm text-500 mt-1">{{ team.description | slice:0:50 }}...</div>
                                    </div>
                                </td>
                                <td>
                                    <p-tag
                                        [value]="team.department"
                                        severity="info">
                                    </p-tag>
                                </td>
                                <td>
                                    <span>{{ team.teamLead }}</span>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <span class="font-medium text-primary">{{ team.members.length }}</span>
                                        <span class="text-500"> üye</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <div class="text-sm">
                                            <span class="font-medium text-green-600">{{ team.activeProjects }}</span> aktif
                                        </div>
                                        <div class="text-sm">
                                            <span class="font-medium text-blue-600">{{ team.completedProjects }}</span> tamamlandı
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <p-tag
                                        [value]="team.status"
                                        [severity]="getStatusSeverity(team.status)">
                                    </p-tag>
                                </td>
                                <td>
                                    <div class="flex gap-2">
                                        <p-button
                                            icon="pi pi-eye"
                                            class="p-button-rounded p-button-text p-button-info"
                                            pTooltip="Detay"
                                            (onClick)="viewTeam(team.id)">
                                        </p-button>
                                        <p-button
                                            icon="pi pi-pencil"
                                            class="p-button-rounded p-button-text p-button-warning"
                                            pTooltip="Düzenle"
                                            (onClick)="editTeam(team.id)">
                                        </p-button>
                                        <p-button
                                            icon="pi pi-trash"
                                            class="p-button-rounded p-button-text p-button-danger"
                                            pTooltip="Sil"
                                            (onClick)="deleteTeam(team)">
                                        </p-button>
                                    </div>
                                </td>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="emptymessage">
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <i class="pi pi-info-circle text-4xl text-500 mb-3"></i>
                                    <div class="text-500 font-medium">Henüz takım bulunmuyor</div>
                                    <div class="text-500">Yeni takım eklemek için "Yeni Takım" butonunu kullanın</div>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                </div>
            </div>
        </div>

        <p-confirmDialog></p-confirmDialog>
        <p-toast></p-toast>
    `
})
export class TeamsListComponent implements OnInit {
    @ViewChild('dt') table!: Table;

    teams: Team[] = [];

    constructor(
        private teamService: TeamService,
        private router: Router,
        private confirmationService: ConfirmationService,
        private messageService: MessageService
    ) {}

    ngOnInit() {
        this.loadTeams();
    }

    loadTeams() {
        this.teamService.getTeams().subscribe(teams => {
            this.teams = teams;
        });
    }

    createTeam() {
        this.router.navigate(['/pages/teams/create']);
    }

    viewTeam(id: string) {
        this.router.navigate(['/pages/teams', id]);
    }

    editTeam(id: string) {
        this.router.navigate(['/pages/teams', id, 'edit']);
    }

    deleteTeam(team: Team) {
        this.confirmationService.confirm({
            message: `"${team.name}" takımını silmek istediğinizden emin misiniz?`,
            header: 'Takım Silme Onayı',
            icon: 'pi pi-exclamation-triangle',
            acceptLabel: 'Evet',
            rejectLabel: 'Hayır',
            accept: () => {
                this.teamService.deleteTeam(team.id).subscribe(() => {
                    this.teams = this.teams.filter(t => t.id !== team.id);
                    this.messageService.add({
                        severity: 'success',
                        summary: 'Başarılı',
                        detail: 'Takım başarıyla silindi'
                    });
                });
            }
        });
    }

    getStatusSeverity(status: TeamStatus): string {
        switch (status) {
            case TeamStatus.ACTIVE:
                return 'success';
            case TeamStatus.INACTIVE:
                return 'secondary';
            case TeamStatus.SUSPENDED:
                return 'warning';
            case TeamStatus.DISBANDED:
                return 'danger';
            default:
                return 'info';
        }
    }
}
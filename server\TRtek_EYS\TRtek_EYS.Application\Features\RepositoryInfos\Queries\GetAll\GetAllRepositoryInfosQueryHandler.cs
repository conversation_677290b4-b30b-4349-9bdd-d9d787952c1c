﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.RepositoryInfos.Queries.GetAll
{
    public class GetAllRepositoryInfosQueryHandler : IRequestHandler<GetAllRepositoryInfosQuery, List<RepositoryInfoGetAllDto>>
    {
        private readonly IApplicationDbContext _context;

        public GetAllRepositoryInfosQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<RepositoryInfoGetAllDto>> Handle(GetAllRepositoryInfosQuery request, CancellationToken cancellationToken)
        {
            return await _context.RepositoryInfos
                .AsNoTracking()
                .Select(r => new RepositoryInfoGetAllDto
                {
                    Id = r.Id,
                    Url = r.Url,
                    Provider = r.Provider,
                    IsPrivate = r.IsPrivate,
                    ProjectId = r.ProjectId
                })
                .ToListAsync(cancellationToken);
        }
    }
}

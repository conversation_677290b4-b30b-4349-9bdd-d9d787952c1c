﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.RepositoryInfos.Commands.Update
{
    public class UpdateRepositoryInfoCommandHandler : IRequestHandler<UpdateRepositoryInfoCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public UpdateRepositoryInfoCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(UpdateRepositoryInfoCommand request, CancellationToken cancellationToken)
        {
            var repo = await _context.RepositoryInfos
                .FirstOrDefaultAsync(r => r.Id == request.Id, cancellationToken);

            if (repo is null)
                throw new Exception("Repository bilgisi bulunamadı.");

            repo.Url = request.Url;
            repo.Provider = request.Provider;
            repo.IsPrivate = request.IsPrivate;
            repo.ProjectId = request.ProjectId;

            await _context.SaveChangesAsync(cancellationToken);

            return repo.Id;
        }
    }
}

﻿using MediatR;
using TRtek_EYS.Application.Common.Interfaces;
using TRtek_EYS.Domain.Entities;
using TRtek_EYS.Domain.Enums;

namespace TRtek_EYS.Application.Features.Documents.Commands.Add
{
    public class AddDocumentCommandHandler : IRequestHandler<AddDocumentCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public AddDocumentCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(AddDocumentCommand request, CancellationToken cancellationToken)
        {
            var document = new Document
            {
                Type = (DocumentType)request.Type,
                FileName = request.FileName,
                FilePath = request.FilePath,
                FileSize = request.FileSize,
                VersionNumber = request.VersionNumber,
                ProjectId = request.ProjectId,
                CreatedAt = DateTime.UtcNow
            };

            await _context.Documents.AddAsync(document, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);

            return document.Id;
        }
    }
}

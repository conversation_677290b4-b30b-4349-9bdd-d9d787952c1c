import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { TabViewModule } from 'primeng/tabview';
import { TagModule } from 'primeng/tag';
import { CardModule } from 'primeng/card';
import { ProgressBar } from 'primeng/progressbar';
import { Technology, MaturityLevel, LearningCurve, LicenseType } from '../../models/technology.model';
import { TechnologyService } from '../../services/technology.service';

@Component({
    selector: 'app-technology-detail',
    standalone: true,
    imports: [
        CommonModule,
        ButtonModule,
        TabViewModule,
        TagModule,
        CardModule,
        ProgressBar
    ],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <div>
                            <h5 class="m-0">Tek<PERSON><PERSON>ji Detayı</h5>
                            <p class="text-500 mt-1">{{ technology?.technologyCode }}</p>
                        </div>
                        <div class="flex gap-2">
                            <p-button 
                                label="Düzenle" 
                                icon="pi pi-pencil" 
                                class="p-button-warning"
                                (onClick)="editTechnology()">
                            </p-button>
                            <p-button 
                                label="Geri" 
                                icon="pi pi-arrow-left" 
                                class="p-button-secondary"
                                (onClick)="goBack()">
                            </p-button>
                        </div>
                    </div>

                    <p-tabView *ngIf="technology">
                        <p-tabPanel header="Teknoloji Bilgileri" leftIcon="pi pi-cog">
                            <div class="grid">
                                <div class="col-12 md:col-6">
                                    <p-card header="Temel Bilgiler">
                                        <div class="field">
                                            <label class="font-medium">Teknoloji Adı:</label>
                                            <div class="mt-1">{{ technology.name }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Açıklama:</label>
                                            <div class="mt-1">{{ technology.description }}</div>
                                        </div>
                                        <div class="field" *ngIf="technology.vendor">
                                            <label class="font-medium">Sağlayıcı:</label>
                                            <div class="mt-1">{{ technology.vendor }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Versiyon:</label>
                                            <div class="mt-1">
                                                <span class="font-bold text-primary">{{ technology.version }}</span>
                                                <span *ngIf="technology.latestVersion && technology.version !== technology.latestVersion" 
                                                      class="text-orange-500 ml-2">
                                                    (Son: {{ technology.latestVersion }})
                                                </span>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Teknoloji Tipi:</label>
                                            <div class="mt-1">
                                                <p-tag [value]="technology.type" severity="info"></p-tag>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Kategori:</label>
                                            <div class="mt-1">
                                                <p-tag [value]="technology.category" severity="secondary"></p-tag>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                                <div class="col-12 md:col-6">
                                    <p-card header="Durum ve Değerlendirme">
                                        <div class="field">
                                            <label class="font-medium">Durum:</label>
                                            <div class="mt-1 flex gap-2">
                                                <p-tag 
                                                    [value]="technology.isActive ? 'Aktif' : 'Pasif'" 
                                                    [severity]="technology.isActive ? 'success' : 'secondary'">
                                                </p-tag>
                                                <p-tag 
                                                    value="Önerilen" 
                                                    severity="success"
                                                    *ngIf="technology.isRecommended">
                                                </p-tag>
                                                <p-tag 
                                                    value="Deprecated" 
                                                    severity="danger"
                                                    *ngIf="technology.isDeprecated">
                                                </p-tag>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Olgunluk Seviyesi:</label>
                                            <div class="mt-1">
                                                <p-tag 
                                                    [value]="technology.maturityLevel" 
                                                    [severity]="getMaturitySeverity(technology.maturityLevel)">
                                                </p-tag>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Lisans Tipi:</label>
                                            <div class="mt-1">
                                                <p-tag 
                                                    [value]="technology.licenseType" 
                                                    [severity]="getLicenseSeverity(technology.licenseType)">
                                                </p-tag>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Öğrenme Eğrisi:</label>
                                            <div class="mt-1">
                                                <p-tag 
                                                    [value]="technology.learningCurve" 
                                                    [severity]="getLearningCurveSeverity(technology.learningCurve)">
                                                </p-tag>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Popülerlik Skoru:</label>
                                            <div class="mt-2">
                                                <div class="mb-1">
                                                    <span class="font-bold text-primary text-xl">{{ technology.popularityScore }}%</span>
                                                </div>
                                                <p-progressBar 
                                                    [value]="technology.popularityScore" 
                                                    [style]="{'height': '8px'}"
                                                    [showValue]="false">
                                                </p-progressBar>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Kullanım İstatistikleri:</label>
                                            <div class="mt-1">
                                                <div class="text-sm">
                                                    <span class="font-bold text-green-600 text-lg">{{ technology.projectCount }}</span>
                                                    <span class="text-500 ml-1">proje</span>
                                                </div>
                                                <div class="text-sm mt-1">
                                                    <span class="font-bold text-blue-600 text-lg">{{ technology.teamCount }}</span>
                                                    <span class="text-500 ml-1">takım</span>
                                                </div>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                            </div>
                        </p-tabPanel>

                        <p-tabPanel header="Özellikler ve Bağımlılıklar" leftIcon="pi pi-star">
                            <div class="grid">
                                <div class="col-12 md:col-6">
                                    <p-card header="Özellikler">
                                        <div *ngIf="technology.features.length === 0" class="text-center py-4">
                                            <i class="pi pi-info-circle text-4xl text-500 mb-3"></i>
                                            <div class="text-500 font-medium">Bu teknoloji için henüz özellik tanımlanmamış</div>
                                        </div>
                                        <div *ngIf="technology.features.length > 0">
                                            <div *ngFor="let feature of technology.features" class="border-1 border-200 border-round p-3 mb-2">
                                                <div class="flex align-items-start gap-2">
                                                    <i class="pi pi-check text-green-500 mt-1" *ngIf="feature.isAvailable"></i>
                                                    <i class="pi pi-times text-red-500 mt-1" *ngIf="!feature.isAvailable"></i>
                                                    <div class="flex-1">
                                                        <div class="font-medium">{{ feature.name }}</div>
                                                        <div class="text-sm text-500 mt-1">{{ feature.description }}</div>
                                                        <div class="flex gap-2 mt-2">
                                                            <p-tag [value]="feature.importance" severity="info" class="text-xs" *ngIf="feature.importance"></p-tag>
                                                            <p-tag [value]="'v' + feature.version" severity="secondary" class="text-xs" *ngIf="feature.version"></p-tag>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                                <div class="col-12 md:col-6">
                                    <p-card header="Bağımlılıklar">
                                        <div *ngIf="technology.dependencies.length === 0" class="text-center py-4">
                                            <i class="pi pi-info-circle text-4xl text-500 mb-3"></i>
                                            <div class="text-500 font-medium">Bu teknoloji için henüz bağımlılık tanımlanmamış</div>
                                        </div>
                                        <div *ngIf="technology.dependencies.length > 0">
                                            <div *ngFor="let dependency of technology.dependencies" class="border-1 border-200 border-round p-3 mb-2">
                                                <div class="flex align-items-start gap-2">
                                                    <i class="pi pi-link text-blue-500 mt-1"></i>
                                                    <div class="flex-1">
                                                        <div class="font-medium">{{ dependency.name }}</div>
                                                        <div class="text-sm text-500 mt-1" *ngIf="dependency.description">{{ dependency.description }}</div>
                                                        <div class="flex gap-2 mt-2">
                                                            <p-tag [value]="dependency.type" severity="secondary" class="text-xs"></p-tag>
                                                            <p-tag [value]="dependency.version" severity="info" class="text-xs"></p-tag>
                                                            <p-tag value="Gerekli" severity="danger" class="text-xs" *ngIf="dependency.isRequired"></p-tag>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                            </div>
                        </p-tabPanel>

                        <p-tabPanel header="Değerlendirme" leftIcon="pi pi-chart-line">
                            <div class="grid">
                                <div class="col-12 md:col-6">
                                    <p-card header="Avantajlar">
                                        <div *ngIf="technology.pros.length === 0" class="text-500">
                                            Henüz avantaj eklenmemiş
                                        </div>
                                        <ul *ngIf="technology.pros.length > 0" class="list-none p-0 m-0">
                                            <li *ngFor="let pro of technology.pros" class="flex align-items-start gap-2 mb-2">
                                                <i class="pi pi-check text-green-500 mt-1"></i>
                                                <span>{{ pro }}</span>
                                            </li>
                                        </ul>
                                    </p-card>
                                </div>
                                <div class="col-12 md:col-6">
                                    <p-card header="Dezavantajlar">
                                        <div *ngIf="technology.cons.length === 0" class="text-500">
                                            Henüz dezavantaj eklenmemiş
                                        </div>
                                        <ul *ngIf="technology.cons.length > 0" class="list-none p-0 m-0">
                                            <li *ngFor="let con of technology.cons" class="flex align-items-start gap-2 mb-2">
                                                <i class="pi pi-times text-red-500 mt-1"></i>
                                                <span>{{ con }}</span>
                                            </li>
                                        </ul>
                                    </p-card>
                                </div>
                                <div class="col-12">
                                    <p-card header="Kullanım Alanları">
                                        <div *ngIf="technology.useCases.length === 0" class="text-500">
                                            Henüz kullanım alanı eklenmemiş
                                        </div>
                                        <div *ngIf="technology.useCases.length > 0" class="flex flex-wrap gap-2">
                                            <p-tag *ngFor="let useCase of technology.useCases" [value]="useCase" severity="info"></p-tag>
                                        </div>
                                    </p-card>
                                </div>
                            </div>
                        </p-tabPanel>

                        <p-tabPanel header="Bağlantılar ve Notlar" leftIcon="pi pi-link">
                            <div class="grid">
                                <div class="col-12 md:col-6">
                                    <p-card header="Bağlantılar">
                                        <div class="field" *ngIf="technology.website">
                                            <label class="font-medium">Website:</label>
                                            <div class="mt-1">
                                                <a [href]="technology.website" target="_blank" class="text-primary">
                                                    {{ technology.website }}
                                                    <i class="pi pi-external-link ml-1"></i>
                                                </a>
                                            </div>
                                        </div>
                                        <div class="field" *ngIf="technology.documentation">
                                            <label class="font-medium">Dokümantasyon:</label>
                                            <div class="mt-1">
                                                <a [href]="technology.documentation" target="_blank" class="text-primary">
                                                    {{ technology.documentation }}
                                                    <i class="pi pi-external-link ml-1"></i>
                                                </a>
                                            </div>
                                        </div>
                                        <div class="field" *ngIf="technology.repository">
                                            <label class="font-medium">Repository:</label>
                                            <div class="mt-1">
                                                <a [href]="technology.repository" target="_blank" class="text-primary">
                                                    {{ technology.repository }}
                                                    <i class="pi pi-external-link ml-1"></i>
                                                </a>
                                            </div>
                                        </div>
                                        <div class="field" *ngIf="technology.alternatives.length > 0">
                                            <label class="font-medium">Alternatifler:</label>
                                            <div class="mt-2 flex flex-wrap gap-1">
                                                <p-tag *ngFor="let alternative of technology.alternatives" [value]="alternative" severity="warning" class="text-xs"></p-tag>
                                            </div>
                                        </div>
                                        <div class="field" *ngIf="technology.tags.length > 0">
                                            <label class="font-medium">Etiketler:</label>
                                            <div class="mt-2 flex flex-wrap gap-1">
                                                <p-tag *ngFor="let tag of technology.tags" [value]="tag" severity="info" class="text-xs"></p-tag>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                                <div class="col-12 md:col-6">
                                    <p-card header="Notlar">
                                        <div *ngIf="!technology.notes" class="text-500">
                                            Henüz not eklenmemiş
                                        </div>
                                        <div *ngIf="technology.notes" class="white-space-pre-line">
                                            {{ technology.notes }}
                                        </div>
                                    </p-card>
                                </div>
                            </div>
                        </p-tabPanel>
                    </p-tabView>
                </div>
            </div>
        </div>
    `
})
export class TechnologyDetailComponent implements OnInit {
    technology: Technology | null = null;
    technologyId: string = '';

    constructor(
        private route: ActivatedRoute,
        private router: Router,
        private technologyService: TechnologyService
    ) {}

    ngOnInit() {
        this.technologyId = this.route.snapshot.params['id'];
        this.loadTechnology();
    }

    loadTechnology() {
        this.technologyService.getTechnologyById(this.technologyId).subscribe(technology => {
            this.technology = technology || null;
        });
    }

    editTechnology() {
        this.router.navigate(['/pages/technologies', this.technologyId, 'edit']);
    }

    goBack() {
        this.router.navigate(['/pages/technologies']);
    }

    getMaturitySeverity(maturityLevel: MaturityLevel): string {
        switch (maturityLevel) {
            case MaturityLevel.EXPERIMENTAL:
                return 'danger';
            case MaturityLevel.ALPHA:
                return 'warning';
            case MaturityLevel.BETA:
                return 'info';
            case MaturityLevel.STABLE:
                return 'success';
            case MaturityLevel.MATURE:
                return 'success';
            case MaturityLevel.LEGACY:
                return 'secondary';
            case MaturityLevel.DEPRECATED:
                return 'danger';
            default:
                return 'info';
        }
    }

    getLearningCurveSeverity(learningCurve: LearningCurve): string {
        switch (learningCurve) {
            case LearningCurve.VERY_EASY:
                return 'success';
            case LearningCurve.EASY:
                return 'success';
            case LearningCurve.MODERATE:
                return 'warning';
            case LearningCurve.HARD:
                return 'danger';
            case LearningCurve.VERY_HARD:
                return 'danger';
            default:
                return 'info';
        }
    }

    getLicenseSeverity(licenseType: LicenseType): string {
        switch (licenseType) {
            case LicenseType.OPEN_SOURCE:
                return 'success';
            case LicenseType.COMMERCIAL:
                return 'warning';
            case LicenseType.FREEMIUM:
                return 'info';
            case LicenseType.PROPRIETARY:
                return 'danger';

            case LicenseType.CUSTOM:
                return 'info';
            default:
                return 'info';
        }
    }
}

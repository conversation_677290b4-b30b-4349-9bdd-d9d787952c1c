﻿using MediatR;

namespace TRtek_EYS.Application.Features.Documents.Commands.Update
{
    public class UpdateDocumentCommand : IRequest<Guid>
    {
        public Guid Id { get; set; }
        public int Type { get; set; }
        public string FileName { get; set; } = null!;
        public string FilePath { get; set; } = null!;
        public long FileSize { get; set; }
        public int VersionNumber { get; set; }
        public Guid ProjectId { get; set; }
    }
}

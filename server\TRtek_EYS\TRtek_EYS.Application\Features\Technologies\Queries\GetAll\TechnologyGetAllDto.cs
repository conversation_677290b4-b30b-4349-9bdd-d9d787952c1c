using TRtek_EYS.Domain.Enums;

namespace TRtek_EYS.Application.Features.Technologies.Queries.GetAll
{
    // V1 DTO
    public class TechnologyGetAllDto
    {
        public Guid Id { get; set; }
        public string Language { get; set; } = null!;
        public string? Framework { get; set; }
        public string? Version { get; set; }
        public Guid ProjectId { get; set; }
    }

    // V2 DTO - Genişletilmiş
    public class TechnologyGetAllDtoV2
    {
        public Guid Id { get; set; }
        public string Language { get; set; } = null!;
        public string? Framework { get; set; }
        public string? Version { get; set; }
        public Guid ProjectId { get; set; }
        public string? Name { get; set; }
        public string? TechnologyCode { get; set; }
        public string? Description { get; set; }
        public TechnologyType? Type { get; set; }
        public TechnologyCategory? Category { get; set; }
        public string? Vendor { get; set; }
        public string? Website { get; set; }
        public string? Documentation { get; set; }
        public string? Repository { get; set; }
        public LicenseType? LicenseType { get; set; }
        public bool IsActive { get; set; }
        public bool IsRecommended { get; set; }
        public bool IsDeprecated { get; set; }
        public DateTime? ReleaseDate { get; set; }
        public DateTime? LastUpdateDate { get; set; }
        public DateTime? EndOfLifeDate { get; set; }
        public string[]? Tags { get; set; }
        public string? Notes { get; set; }
        public int ProjectCount { get; set; }
        public int TeamCount { get; set; }
        public decimal PopularityScore { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}

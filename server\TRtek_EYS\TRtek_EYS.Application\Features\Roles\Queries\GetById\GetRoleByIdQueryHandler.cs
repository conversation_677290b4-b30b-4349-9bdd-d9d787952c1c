﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Roles.Queries.GetById
{
    public class GetRoleByIdQueryHandler : IRequestHandler<GetRoleByIdQuery, RoleGetByIdDto>
    {
        private readonly IApplicationDbContext _context;

        public GetRoleByIdQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<RoleGetByIdDto> Handle(GetRoleByIdQuery request, CancellationToken cancellationToken)
        {
            var role = await _context.Roles
                .AsNoTracking()
                .FirstOrDefaultAsync(r => r.Id == request.Id, cancellationToken);

            if (role is null)
                throw new Exception("Rol bulunamadı.");

            return new RoleGetByIdDto
            {
                Id = role.Id,
                Name = role.Name,
                Description = role.Description,
                TeamId = role.TeamId
            };
        }
    }
}

using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;
using System.Text.Json;

namespace TRtek_EYS.Application.Features.Projects.Queries.GetAll
{
    public class GetAllProjectsV2QueryHandler : IRequestHandler<GetAllProjectsV2Query, List<ProjectGetAllDtoV2>>
    {
        private readonly IApplicationDbContext _context;

        public GetAllProjectsV2QueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<ProjectGetAllDtoV2>> Handle(GetAllProjectsV2Query request, CancellationToken cancellationToken)
        {
            var projects = await _context.Projects
                .Include(p => p.CreatedByUser)
                .AsNoTracking()
                .ToListAsync(cancellationToken);

            var result = projects.Select(project => new ProjectGetAllDtoV2
            {
                Id = project.Id,
                Name = project.Name,
                Code = project.Code,
                Description = project.Description,
                Status = project.Status,
                StartDate = project.StartDate,
                EndDate = project.EndDate,
                CreatedByUserId = project.CreatedByUserId,
                CreatedByUserName = project.CreatedByUser?.FullName,
                
                // Yeni alanlar
                ProjectType = project.ProjectType,
                Priority = project.Priority,
                Budget = project.Budget,
                Currency = project.Currency,
                ActualCost = project.ActualCost,
                EstimatedHours = project.EstimatedHours,
                ActualHours = project.ActualHours,
                ProgressPercentage = project.ProgressPercentage,
                ProjectManager = project.ProjectManager,
                TechnicalLead = project.TechnicalLead,
                ClientContact = project.ClientContact,
                Repository = project.Repository,
                DeploymentUrl = project.DeploymentUrl,
                StagingUrl = project.StagingUrl,
                DocumentationUrl = project.DocumentationUrl,
                ActualStartDate = project.ActualStartDate,
                ActualEndDate = project.ActualEndDate,
                DeploymentDate = project.DeploymentDate,
                Tags = ParseTags(project.Tags),
                Notes = project.Notes,
                IsActive = project.IsActive,
                IsPublic = project.IsPublic,
                Methodology = project.Methodology,
                TeamSize = project.TeamSize,
                RiskAssessment = project.RiskAssessment,
                CreatedAt = project.CreatedAt,
                UpdatedAt = project.UpdatedAt
            }).ToList();

            return result;
        }

        private string[]? ParseTags(string? tagsJson)
        {
            if (string.IsNullOrEmpty(tagsJson))
                return null;

            try
            {
                return JsonSerializer.Deserialize<string[]>(tagsJson);
            }
            catch
            {
                return null;
            }
        }
    }
}

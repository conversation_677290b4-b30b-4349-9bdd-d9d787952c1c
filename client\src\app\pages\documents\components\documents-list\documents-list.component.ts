import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Table, TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { InputIconModule } from 'primeng/inputicon';
import { IconFieldModule } from 'primeng/iconfield';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToastModule } from 'primeng/toast';
import { ConfirmationService, MessageService } from 'primeng/api';
import { Document, DocumentType, DocumentCategory, DocumentFormat, DocumentStatus, DocumentVisibility } from '../../models/document.model';
import { DocumentService } from '../../services/document.service';

@Component({
    selector: 'app-documents-list',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        TableModule,
        ButtonModule,
        InputTextModule,
        InputIconModule,
        IconFieldModule,
        TagModule,
        TooltipModule,
        ConfirmDialogModule,
        ToastModule
    ],
    providers: [ConfirmationService, MessageService],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <h5>Doküman Yönetimi</h5>
                        <p-button 
                            label="Yeni Doküman" 
                            icon="pi pi-plus" 
                            (onClick)="createDocument()"
                            severity="primary"
                            size="small">
                        </p-button>
                    </div>

                    <p-table 
                        #dt 
                        [value]="documents" 
                        [rows]="10" 
                        [paginator]="true"
                        [globalFilterFields]="['documentCode', 'title', 'authorName', 'type', 'category']"
                        [tableStyle]="{ 'min-width': '75rem' }"
                        [rowHover]="true"
                        dataKey="id"
                        currentPageReportTemplate="{first} - {last} / {totalRecords} doküman"
                        [showCurrentPageReport]="true">
                        
                        <ng-template pTemplate="caption">
                            <div class="flex align-items-center justify-content-between">
                                <h5 class="m-0">Dokümanlar</h5>
                                <p-iconfield iconPosition="left">
                                    <p-inputicon>
                                        <i class="pi pi-search"></i>
                                    </p-inputicon>
                                    <input 
                                        pInputText 
                                        type="text" 
                                        (input)="dt.filterGlobal($any($event.target).value, 'contains')" 
                                        placeholder="Doküman ara..." />
                                </p-iconfield>
                            </div>
                        </ng-template>

                        <ng-template pTemplate="header">
                            <tr>
                                <th pSortableColumn="documentCode">
                                    Doküman Kodu <p-sortIcon field="documentCode"></p-sortIcon>
                                </th>
                                <th pSortableColumn="title">
                                    Başlık <p-sortIcon field="title"></p-sortIcon>
                                </th>
                                <th pSortableColumn="type">
                                    Tip <p-sortIcon field="type"></p-sortIcon>
                                </th>
                                <th pSortableColumn="category">
                                    Kategori <p-sortIcon field="category"></p-sortIcon>
                                </th>
                                <th pSortableColumn="format">
                                    Format <p-sortIcon field="format"></p-sortIcon>
                                </th>
                                <th pSortableColumn="authorName">
                                    Yazar <p-sortIcon field="authorName"></p-sortIcon>
                                </th>
                                <th pSortableColumn="version">
                                    Versiyon <p-sortIcon field="version"></p-sortIcon>
                                </th>
                                <th>İstatistikler</th>
                                <th pSortableColumn="status">
                                    Durum <p-sortIcon field="status"></p-sortIcon>
                                </th>
                                <th>İşlemler</th>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="body" let-document>
                            <tr [class]="document.isArchived ? 'bg-gray-50' : ''">
                                <td>
                                    <span class="font-medium">{{ document.documentCode }}</span>
                                </td>
                                <td>
                                    <div>
                                        <span class="font-medium">{{ document.title }}</span>
                                        <div class="text-sm text-500 mt-1">{{ document.description | slice:0:50 }}...</div>
                                        <div class="flex gap-1 mt-1" *ngIf="document.isTemplate || document.isArchived">
                                            <p-tag 
                                                value="Şablon" 
                                                severity="info" 
                                                class="text-xs"
                                                *ngIf="document.isTemplate">
                                            </p-tag>
                                            <p-tag 
                                                value="Arşivlendi" 
                                                severity="secondary" 
                                                class="text-xs"
                                                *ngIf="document.isArchived">
                                            </p-tag>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="document.type" 
                                        severity="info">
                                    </p-tag>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="document.category" 
                                        severity="secondary">
                                    </p-tag>
                                </td>
                                <td>
                                    <div class="flex align-items-center gap-2">
                                        <i [class]="getFormatIcon(document.format)" class="text-lg"></i>
                                        <span>{{ document.format }}</span>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <span>{{ document.authorName }}</span>
                                        <div class="text-sm text-500 mt-1">{{ document.createdAt | date:'dd/MM/yyyy' }}</div>
                                    </div>
                                </td>
                                <td>
                                    <span class="font-medium">{{ document.version }}</span>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <div class="text-sm">
                                            <i class="pi pi-eye text-blue-500"></i>
                                            <span class="ml-1">{{ document.viewCount }}</span>
                                        </div>
                                        <div class="text-sm mt-1">
                                            <i class="pi pi-download text-green-500"></i>
                                            <span class="ml-1">{{ document.downloadCount }}</span>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="flex flex-column gap-1">
                                        <p-tag 
                                            [value]="document.status" 
                                            [severity]="getStatusSeverity(document.status)"
                                            class="text-xs">
                                        </p-tag>
                                        <p-tag 
                                            [value]="document.visibility" 
                                            [severity]="getVisibilitySeverity(document.visibility)"
                                            class="text-xs">
                                        </p-tag>
                                    </div>
                                </td>
                                <td>
                                    <div class="flex gap-2">
                                        <p-button 
                                            icon="pi pi-eye" 
                                            class="p-button-rounded p-button-text p-button-info"
                                            pTooltip="Detay"
                                            (onClick)="viewDocument(document.id)">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-download" 
                                            class="p-button-rounded p-button-text p-button-success"
                                            pTooltip="İndir"
                                            (onClick)="downloadDocument(document)"
                                            *ngIf="document.filePath">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-external-link" 
                                            class="p-button-rounded p-button-text p-button-help"
                                            pTooltip="Aç"
                                            (onClick)="openDocument(document)"
                                            *ngIf="document.url">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-pencil" 
                                            class="p-button-rounded p-button-text p-button-warning"
                                            pTooltip="Düzenle"
                                            (onClick)="editDocument(document.id)"
                                            [disabled]="document.isArchived">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-send" 
                                            class="p-button-rounded p-button-text p-button-primary"
                                            pTooltip="Yayınla"
                                            (onClick)="publishDocument(document)"
                                            *ngIf="document.status === 'Taslak' || document.status === 'İncelemede'">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-archive" 
                                            class="p-button-rounded p-button-text p-button-secondary"
                                            pTooltip="Arşivle"
                                            (onClick)="archiveDocument(document)"
                                            *ngIf="!document.isArchived">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-trash" 
                                            class="p-button-rounded p-button-text p-button-danger"
                                            pTooltip="Sil"
                                            (onClick)="deleteDocument(document)">
                                        </p-button>
                                    </div>
                                </td>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="emptymessage">
                            <tr>
                                <td colspan="10" class="text-center py-4">
                                    <i class="pi pi-info-circle text-4xl text-500 mb-3"></i>
                                    <div class="text-500 font-medium">Henüz doküman bulunmuyor</div>
                                    <div class="text-500">Yeni doküman eklemek için "Yeni Doküman" butonunu kullanın</div>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                </div>
            </div>
        </div>

        <p-confirmDialog></p-confirmDialog>
        <p-toast></p-toast>
    `
})
export class DocumentsListComponent implements OnInit {
    @ViewChild('dt') table!: Table;
    
    documents: Document[] = [];

    constructor(
        private documentService: DocumentService,
        private router: Router,
        private confirmationService: ConfirmationService,
        private messageService: MessageService
    ) {}

    ngOnInit() {
        this.loadDocuments();
    }

    loadDocuments() {
        this.documentService.getDocuments().subscribe(documents => {
            this.documents = documents;
        });
    }

    createDocument() {
        this.router.navigate(['/pages/documents/new']);
    }

    viewDocument(id: string) {
        this.documentService.incrementViewCount(id).subscribe();
        this.router.navigate(['/pages/documents', id]);
    }

    editDocument(id: string) {
        this.router.navigate(['/pages/documents', id, 'edit']);
    }

    downloadDocument(document: Document) {
        this.documentService.incrementDownloadCount(document.id).subscribe();
        this.messageService.add({
            severity: 'info',
            summary: 'İndiriliyor',
            detail: `${document.title} dosyası indiriliyor...`
        });
        // Gerçek uygulamada dosya indirme işlemi burada yapılır
    }

    openDocument(document: Document) {
        if (document.url) {
            this.documentService.incrementViewCount(document.id).subscribe();
            window.open(document.url, '_blank');
        }
    }

    publishDocument(document: Document) {
        this.confirmationService.confirm({
            message: `"${document.title}" dokümanını yayınlamak istediğinizden emin misiniz?`,
            header: 'Doküman Yayınlama Onayı',
            icon: 'pi pi-send',
            acceptLabel: 'Evet',
            rejectLabel: 'Hayır',
            accept: () => {
                this.documentService.publishDocument(document.id).subscribe(() => {
                    this.loadDocuments();
                    this.messageService.add({
                        severity: 'success',
                        summary: 'Başarılı',
                        detail: 'Doküman başarıyla yayınlandı'
                    });
                });
            }
        });
    }

    archiveDocument(document: Document) {
        this.confirmationService.confirm({
            message: `"${document.title}" dokümanını arşivlemek istediğinizden emin misiniz?`,
            header: 'Doküman Arşivleme Onayı',
            icon: 'pi pi-archive',
            acceptLabel: 'Evet',
            rejectLabel: 'Hayır',
            accept: () => {
                this.documentService.archiveDocument(document.id).subscribe(() => {
                    this.loadDocuments();
                    this.messageService.add({
                        severity: 'info',
                        summary: 'Başarılı',
                        detail: 'Doküman başarıyla arşivlendi'
                    });
                });
            }
        });
    }

    deleteDocument(document: Document) {
        this.confirmationService.confirm({
            message: `"${document.title}" dokümanını silmek istediğinizden emin misiniz?`,
            header: 'Doküman Silme Onayı',
            icon: 'pi pi-exclamation-triangle',
            acceptLabel: 'Evet',
            rejectLabel: 'Hayır',
            accept: () => {
                this.documentService.deleteDocument(document.id).subscribe(() => {
                    this.documents = this.documents.filter(d => d.id !== document.id);
                    this.messageService.add({
                        severity: 'success',
                        summary: 'Başarılı',
                        detail: 'Doküman başarıyla silindi'
                    });
                });
            }
        });
    }

    getStatusSeverity(status: DocumentStatus): string {
        switch (status) {
            case DocumentStatus.DRAFT:
                return 'secondary';
            case DocumentStatus.IN_REVIEW:
                return 'warning';
            case DocumentStatus.APPROVED:
                return 'info';
            case DocumentStatus.PUBLISHED:
                return 'success';
            case DocumentStatus.ARCHIVED:
                return 'secondary';
            case DocumentStatus.REJECTED:
                return 'danger';
            case DocumentStatus.EXPIRED:
                return 'danger';
            case DocumentStatus.WITHDRAWN:
                return 'warning';
            default:
                return 'info';
        }
    }

    getVisibilitySeverity(visibility: DocumentVisibility): string {
        switch (visibility) {
            case DocumentVisibility.PUBLIC:
                return 'success';
            case DocumentVisibility.INTERNAL:
                return 'info';
            case DocumentVisibility.TEAM:
                return 'warning';
            case DocumentVisibility.PROJECT:
                return 'secondary';
            case DocumentVisibility.CONFIDENTIAL:
                return 'danger';
            case DocumentVisibility.RESTRICTED:
                return 'danger';
            default:
                return 'info';
        }
    }

    getFormatIcon(format: DocumentFormat): string {
        switch (format) {
            case DocumentFormat.PDF:
                return 'pi pi-file-pdf text-red-500';
            case DocumentFormat.WORD:
                return 'pi pi-file-word text-blue-500';
            case DocumentFormat.EXCEL:
                return 'pi pi-file-excel text-green-500';
            case DocumentFormat.POWERPOINT:
                return 'pi pi-file text-orange-500';
            case DocumentFormat.MARKDOWN:
                return 'pi pi-code text-purple-500';
            case DocumentFormat.HTML:
                return 'pi pi-globe text-blue-500';
            case DocumentFormat.TEXT:
                return 'pi pi-file text-gray-500';
            case DocumentFormat.IMAGE:
                return 'pi pi-image text-pink-500';
            case DocumentFormat.VIDEO:
                return 'pi pi-video text-red-500';
            case DocumentFormat.AUDIO:
                return 'pi pi-volume-up text-green-500';
            default:
                return 'pi pi-file text-gray-500';
        }
    }
}

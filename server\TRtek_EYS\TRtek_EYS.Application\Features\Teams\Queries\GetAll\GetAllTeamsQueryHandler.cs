﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Teams.Queries.GetAll
{
    public class GetAllTeamsQueryHandler : IRequestHandler<GetAllTeamsQuery, List<TeamGetAllDto>>
    {
        private readonly IApplicationDbContext _context;

        public GetAllTeamsQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<TeamGetAllDto>> Handle(GetAllTeamsQuery request, CancellationToken cancellationToken)
        {
            return await _context.Teams
                .AsNoTracking()
                .Select(t => new TeamGetAllDto
                {
                    Id = t.Id,
                    Name = t.Name,
                    Description = t.Description
                })
                .ToListAsync(cancellationToken);
        }
    }
}

﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TRtek_EYS.Domain.Entities;

namespace TRtek_EYS.Infrastructure.Configurations
{
    public class ProjectConfiguration : IEntityTypeConfiguration<Project>
    {
        public void Configure(EntityTypeBuilder<Project> builder)
        {
            builder.ToTable("Projects");

            builder.HasKey(p => p.Id);

            builder.Property(p => p.Name)
                   .IsRequired()
                   .HasMaxLength(150);

            builder.Property(p => p.Code)
                   .IsRequired()
                   .HasMaxLength(50);

            builder.Property(p => p.Description)
                   .HasMaxLength(500);

            builder.Property(p => p.Status)
                   .IsRequired(); // Enum: ProjectStatus

            builder.Property(p => p.StartDate)
                   .IsRequired();

            builder.Property(p => p.EndDate)
                   .IsRequired();

            builder.Property(p => p.CreatedAt)
                   .IsRequired();

            builder.Property(p => p.CreatedByUserId)
                   .IsRequired();

            // YENİ ALANLAR İÇİN CONFIGURATION
            builder.Property(p => p.Budget)
                   .HasPrecision(15, 2);

            builder.Property(p => p.Currency)
                   .HasMaxLength(10);

            builder.Property(p => p.ActualCost)
                   .HasPrecision(15, 2);

            builder.Property(p => p.ProjectManager)
                   .HasMaxLength(200);

            builder.Property(p => p.TechnicalLead)
                   .HasMaxLength(200);

            builder.Property(p => p.ClientContact)
                   .HasMaxLength(200);

            builder.Property(p => p.Repository)
                   .HasMaxLength(500);

            builder.Property(p => p.DeploymentUrl)
                   .HasMaxLength(500);

            builder.Property(p => p.StagingUrl)
                   .HasMaxLength(500);

            builder.Property(p => p.DocumentationUrl)
                   .HasMaxLength(500);

            builder.Property(p => p.Tags)
                   .HasMaxLength(1000);

            builder.Property(p => p.Notes)
                   .HasMaxLength(2000);

            builder.Property(p => p.Methodology)
                   .HasMaxLength(100);

            builder.Property(p => p.RiskAssessment)
                   .HasMaxLength(1000);

            builder.HasOne(p => p.CreatedByUser)
                   .WithMany()
                   .HasForeignKey(p => p.CreatedByUserId)
                   .OnDelete(DeleteBehavior.Restrict);

            // İlişkiler: Collection navigation'lar sadece tanımlanır, fluent tarafı genelde karşı taraf tarafından yapılır
            builder.HasMany(p => p.Platforms)
                   .WithOne(p => p.Project)
                   .HasForeignKey(p => p.ProjectId);

            builder.HasMany(p => p.Technologies)
                   .WithOne(t => t.Project)
                   .HasForeignKey(t => t.ProjectId);

            builder.HasMany(p => p.ProjectTeams)
                   .WithOne(pt => pt.Project)
                   .HasForeignKey(pt => pt.ProjectId);

            builder.HasMany(p => p.CustomerProjects)
                   .WithOne(cp => cp.Project)
                   .HasForeignKey(cp => cp.ProjectId);

            builder.HasMany(p => p.Documents)
                   .WithOne(d => d.Project)
                   .HasForeignKey(d => d.ProjectId);

            builder.HasMany(p => p.Contracts)
                   .WithOne(c => c.Project)
                   .HasForeignKey(c => c.ProjectId);

            builder.HasMany(p => p.Repositories)
                   .WithOne(r => r.Project)
                   .HasForeignKey(r => r.ProjectId);

            builder.HasMany(p => p.Versions)
                   .WithOne(v => v.Project)
                   .HasForeignKey(v => v.ProjectId);
        }
    }
}

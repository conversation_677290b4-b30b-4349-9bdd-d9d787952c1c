{"name": "sakai-ng", "version": "19.0.1", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "format": "prettier --write \"**/*.{js,mjs,ts,mts,d.ts,html}\" --cache", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.0.0", "@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/core": "^19.0.0", "@angular/forms": "^19.0.0", "@angular/platform-browser": "^19.0.0", "@angular/platform-browser-dynamic": "^19.0.0", "@angular/router": "^19.0.0", "@primeng/themes": "^19.0.5", "chart.js": "4.4.2", "primeclt": "^0.1.5", "primeicons": "^7.0.0", "primeng": "^19.0.8", "rxjs": "~7.8.0", "tailwindcss-primeui": "^0.5.1", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.0.6", "@angular/cli": "^19.0.6", "@angular/compiler-cli": "^19.0.0", "@types/jasmine": "~5.1.0", "autoprefixer": "^10.4.20", "eslint": "^9.14.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prefer-arrow": "^1.2.3", "eslint-plugin-prettier": "^4.2.1", "jasmine-core": "~5.4.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.49", "prettier": "^3.0.0", "tailwindcss": "^3.4.17", "typescript": "~5.6.2"}}
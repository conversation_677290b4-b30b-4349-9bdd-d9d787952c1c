import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Table, TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { InputIconModule } from 'primeng/inputicon';
import { IconFieldModule } from 'primeng/iconfield';
import { TagModule } from 'primeng/tag';
import { MultiSelectModule } from 'primeng/multiselect';
import { SelectModule } from 'primeng/select';
import { TooltipModule } from 'primeng/tooltip';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToastModule } from 'primeng/toast';
import { ConfirmationService, MessageService } from 'primeng/api';
import { Project, ProjectStatus, Platform, Priority } from '../../models/project.model';

@Component({
    selector: 'app-projects-list',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        TableModule,
        ButtonModule,
        InputTextModule,
        InputIconModule,
        IconFieldModule,
        TagModule,
        MultiSelectModule,
        SelectModule,
        TooltipModule,
        ConfirmDialogModule,
        ToastModule
    ],
    providers: [ConfirmationService, MessageService],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <h5>Proje Yönetimi</h5>
                        <p-button
                            icon="pi pi-plus"
                            (onClick)="createProject()"
                            severity="primary"
                            rounded
                            outlined
                            pTooltip="Yeni Proje">
                        </p-button>
                    </div>

                    <p-table 
                        #dt 
                        [value]="projects" 
                        [rows]="10" 
                        [paginator]="true"
                        [globalFilterFields]="['projectCode', 'name', 'status', 'projectManager']"
                        [tableStyle]="{ 'min-width': '75rem' }"
                        [rowHover]="true"
                        dataKey="id"
                        currentPageReportTemplate="{first} - {last} / {totalRecords} proje"
                        [showCurrentPageReport]="true">
                        
                        <ng-template pTemplate="caption">
                            <div class="flex align-items-center justify-content-between">
                                <h5 class="m-0">Projeler</h5>
                                <p-iconfield iconPosition="left">
                                    <p-inputicon>
                                        <i class="pi pi-search"></i>
                                    </p-inputicon>
                                    <input 
                                        pInputText 
                                        type="text" 
                                        (input)="dt.filterGlobal($any($event.target).value, 'contains')" 
                                        placeholder="Proje ara..." />
                                </p-iconfield>
                            </div>
                        </ng-template>

                        <ng-template pTemplate="header">
                            <tr>
                                <th pSortableColumn="projectCode">
                                    Proje Kodu <p-sortIcon field="projectCode"></p-sortIcon>
                                </th>
                                <th pSortableColumn="name">
                                    Proje Adı <p-sortIcon field="name"></p-sortIcon>
                                </th>
                                <th pSortableColumn="status">
                                    Durum <p-sortIcon field="status"></p-sortIcon>
                                </th>
                                <th>Platformlar</th>
                                <th pSortableColumn="currentVersion">
                                    Versiyon <p-sortIcon field="currentVersion"></p-sortIcon>
                                </th>
                                <th>Müşteri Sayısı</th>
                                <th pSortableColumn="priority">
                                    Öncelik <p-sortIcon field="priority"></p-sortIcon>
                                </th>
                                <th pSortableColumn="projectManager">
                                    Proje Yöneticisi <p-sortIcon field="projectManager"></p-sortIcon>
                                </th>
                                <th>İşlemler</th>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="body" let-project>
                            <tr>
                                <td>
                                    <span class="font-medium">{{ project.projectCode }}</span>
                                </td>
                                <td>
                                    <span class="font-medium">{{ project.name }}</span>
                                    <div class="text-sm text-500 mt-1">{{ project.description | slice:0:50 }}...</div>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="project.status" 
                                        [severity]="getStatusSeverity(project.status)">
                                    </p-tag>
                                </td>
                                <td>
                                    <div class="flex gap-1">
                                        <p-tag 
                                            *ngFor="let platform of project.platforms" 
                                            [value]="platform" 
                                            severity="info"
                                            class="text-xs">
                                        </p-tag>
                                    </div>
                                </td>
                                <td>
                                    <span class="font-medium">{{ project.currentVersion }}</span>
                                </td>
                                <td>
                                    <span class="font-medium">{{ project.customers.length }}</span>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="project.priority" 
                                        [severity]="getPrioritySeverity(project.priority)">
                                    </p-tag>
                                </td>
                                <td>
                                    <span>{{ project.projectManager || '-' }}</span>
                                </td>
                                <td>
                                    <div class="flex gap-2">
                                        <p-button 
                                            icon="pi pi-eye" 
                                            class="p-button-rounded p-button-text p-button-info"
                                            pTooltip="Detay"
                                            (onClick)="viewProject(project.id)">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-pencil" 
                                            class="p-button-rounded p-button-text p-button-warning"
                                            pTooltip="Düzenle"
                                            (onClick)="editProject(project.id)">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-trash" 
                                            class="p-button-rounded p-button-text p-button-danger"
                                            pTooltip="Sil"
                                            (onClick)="deleteProject(project)">
                                        </p-button>
                                    </div>
                                </td>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="emptymessage">
                            <tr>
                                <td colspan="9" class="text-center py-4">
                                    <i class="pi pi-info-circle text-4xl text-500 mb-3"></i>
                                    <div class="text-500 font-medium">Henüz proje bulunmuyor</div>
                                    <div class="text-500">Yeni proje eklemek için "Yeni Proje" butonunu kullanın</div>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                </div>
            </div>
        </div>

        <p-confirmDialog></p-confirmDialog>
        <p-toast></p-toast>
    `
})
export class ProjectsListComponent implements OnInit {
    @ViewChild('dt') table!: Table;
    
    projects: Project[] = [];

    constructor(
        private router: Router,
        private confirmationService: ConfirmationService,
        private messageService: MessageService
    ) {}

    ngOnInit() {
        this.loadProjects();
    }

    loadProjects() {
        this.projects = [
            {
                id: '1',
                projectCode: 'PRJ-2025-001',
                name: 'E-Ticaret Platformu',
                description: 'Kapsamlı e-ticaret çözümü. Ürün yönetimi, sipariş takibi, ödeme entegrasyonu ve müşteri yönetimi modülleri içerir.',
                status: ProjectStatus.DEVELOPMENT,
                platforms: [Platform.WEB, Platform.MOBILE],
                technologies: [],
                customers: [
                    { id: '1', name: 'ABC Şirketi', contactPerson: 'Ahmet Yılmaz', email: '<EMAIL>', contractDate: new Date('2025-01-15'), licenseType: 'PERPETUAL' as any, isActive: true },
                    { id: '2', name: 'XYZ Ltd.', contactPerson: 'Ayşe Demir', email: '<EMAIL>', contractDate: new Date('2025-02-01'), licenseType: 'SUBSCRIPTION' as any, isActive: true }
                ],
                teamMembers: [],
                repositoryUrl: 'https://github.com/company/ecommerce-platform',
                currentVersion: 'v2.1.0',
                createdDate: new Date('2025-01-01'),
                lastUpdated: new Date('2025-01-15'),
                startDate: new Date('2025-01-01'),
                priority: Priority.HIGH,
                projectManager: 'Mehmet Özkan'
            },
            {
                id: '2',
                projectCode: 'PRJ-2025-002',
                name: 'CRM Sistemi',
                description: 'Müşteri ilişkileri yönetim sistemi. Lead takibi, satış süreci yönetimi ve raporlama özellikleri.',
                status: ProjectStatus.TESTING,
                platforms: [Platform.WEB],
                technologies: [],
                customers: [
                    { id: '3', name: 'DEF A.Ş.', contactPerson: 'Can Kaya', email: '<EMAIL>', contractDate: new Date('2025-01-20'), licenseType: 'SUBSCRIPTION' as any, isActive: true }
                ],
                teamMembers: [],
                repositoryUrl: 'https://github.com/company/crm-system',
                currentVersion: 'v1.5.2',
                createdDate: new Date('2023-11-01'),
                lastUpdated: new Date('2025-01-10'),
                startDate: new Date('2023-11-01'),
                priority: Priority.MEDIUM,
                projectManager: 'Zeynep Arslan'
            },
            {
                id: '3',
                projectCode: 'PRJ-2025-003',
                name: 'Mobil Bankacılık',
                description: 'Güvenli mobil bankacılık uygulaması. Hesap yönetimi, para transferi ve yatırım işlemleri.',
                status: ProjectStatus.MAINTENANCE,
                platforms: [Platform.MOBILE],
                technologies: [],
                customers: [
                    { id: '4', name: 'GHI Bank', contactPerson: 'Fatma Şen', email: '<EMAIL>', contractDate: new Date('2023-08-15'), licenseType: 'CUSTOM' as any, isActive: true }
                ],
                teamMembers: [],
                repositoryUrl: 'https://github.com/company/mobile-banking',
                currentVersion: 'v3.2.1',
                createdDate: new Date('2023-06-01'),
                lastUpdated: new Date('2025-01-05'),
                startDate: new Date('2023-06-01'),
                endDate: new Date('2023-12-15'),
                priority: Priority.CRITICAL,
                projectManager: 'Ali Çelik'
            }
        ];
    }

    createProject() {
        this.router.navigate(['/pages/projects/new']);
    }

    viewProject(id: string) {
        this.router.navigate(['/pages/projects', id]);
    }

    editProject(id: string) {
        this.router.navigate(['/pages/projects', id, 'edit']);
    }

    deleteProject(project: Project) {
        this.confirmationService.confirm({
            message: `"${project.name}" projesini silmek istediğinizden emin misiniz?`,
            header: 'Proje Silme Onayı',
            icon: 'pi pi-exclamation-triangle',
            acceptLabel: 'Evet',
            rejectLabel: 'Hayır',
            accept: () => {
                this.projects = this.projects.filter(p => p.id !== project.id);
                this.messageService.add({
                    severity: 'success',
                    summary: 'Başarılı',
                    detail: 'Proje başarıyla silindi'
                });
            }
        });
    }

    getStatusSeverity(status: ProjectStatus): string {
        switch (status) {
            case ProjectStatus.COMPLETED:
                return 'success';
            case ProjectStatus.DEVELOPMENT:
                return 'info';
            case ProjectStatus.TESTING:
                return 'warning';
            case ProjectStatus.MAINTENANCE:
                return 'secondary';
            case ProjectStatus.CANCELLED:
                return 'danger';
            default:
                return 'info';
        }
    }

    getPrioritySeverity(priority: Priority): string {
        switch (priority) {
            case Priority.CRITICAL:
                return 'danger';
            case Priority.HIGH:
                return 'warning';
            case Priority.MEDIUM:
                return 'info';
            case Priority.LOW:
                return 'secondary';
            default:
                return 'info';
        }
    }
}

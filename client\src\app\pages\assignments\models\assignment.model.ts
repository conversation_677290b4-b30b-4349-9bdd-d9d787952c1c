export interface ProjectAssignment {
    id: string;
    assignmentCode: string;
    projectId: string;
    projectName: string;
    projectCode: string;
    teamId: string;
    teamName: string;
    userId: string;
    userName: string;
    userEmail: string;
    role: ProjectRole;
    assignmentType: AssignmentType;
    startDate: Date;
    endDate?: Date;
    workloadPercentage: number;
    status: AssignmentStatus;
    priority: AssignmentPriority;
    responsibilities: string[];
    skills: Skill[];
    assignedBy: string;
    assignedDate: Date;
    lastModifiedBy?: string;
    lastModifiedDate?: Date;
    notes?: string;
    isActive: boolean;
}

export interface Skill {
    id: string;
    name: string;
    level: SkillLevel;
    category: SkillCategory;
}

export interface AssignmentHistory {
    id: string;
    assignmentId: string;
    action: AssignmentAction;
    previousValue?: string;
    newValue?: string;
    actionDate: Date;
    actionBy: string;
    reason?: string;
}

export enum ProjectRole {
    PROJECT_MANAGER = 'Proje <PERSON>',
    TECH_LEAD = 'Teknik <PERSON>',
    SENIOR_DEVELOPER = '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    DEVELOPER = 'G<PERSON><PERSON><PERSON><PERSON><PERSON>',
    JUNIOR_DEVELOPER = 'Junior <PERSON>',
    FRONTEND_DEVELOPER = 'Frontend Geliştirici',
    BACKEND_DEVELOPER = 'Backend Geliştirici',
    FULLSTACK_DEVELOPER = 'Fullstack Geliştirici',
    MOBILE_DEVELOPER = 'Mobil Geliştirici',
    DEVOPS_ENGINEER = 'DevOps Mühendisi',
    QA_ENGINEER = 'QA Mühendisi',
    UI_UX_DESIGNER = 'UI/UX Tasarımcı',
    BUSINESS_ANALYST = 'İş Analisti',
    SCRUM_MASTER = 'Scrum Master',
    ARCHITECT = 'Mimar'
}

export enum AssignmentType {
    FULL_TIME = 'Tam Zamanlı',
    PART_TIME = 'Yarı Zamanlı',
    CONSULTANT = 'Danışman',
    TEMPORARY = 'Geçici',
    CONTRACT = 'Sözleşmeli'
}

export enum AssignmentStatus {
    ACTIVE = 'Aktif',
    PENDING = 'Beklemede',
    COMPLETED = 'Tamamlandı',
    SUSPENDED = 'Askıya Alındı',
    CANCELLED = 'İptal Edildi',
    ON_HOLD = 'Beklemede'
}

export enum AssignmentPriority {
    LOW = 'Düşük',
    MEDIUM = 'Orta',
    HIGH = 'Yüksek',
    CRITICAL = 'Kritik'
}

export enum SkillLevel {
    BEGINNER = 'Başlangıç',
    INTERMEDIATE = 'Orta',
    ADVANCED = 'İleri',
    EXPERT = 'Uzman'
}

export enum SkillCategory {
    PROGRAMMING = 'Programlama',
    FRAMEWORK = 'Framework',
    DATABASE = 'Veritabanı',
    CLOUD = 'Bulut',
    DEVOPS = 'DevOps',
    DESIGN = 'Tasarım',
    MANAGEMENT = 'Yönetim',
    SOFT_SKILLS = 'Soft Skills'
}

export enum AssignmentAction {
    CREATED = 'Oluşturuldu',
    UPDATED = 'Güncellendi',
    ROLE_CHANGED = 'Rol Değiştirildi',
    WORKLOAD_CHANGED = 'İş Yükü Değiştirildi',
    STATUS_CHANGED = 'Durum Değiştirildi',
    SUSPENDED = 'Askıya Alındı',
    REACTIVATED = 'Yeniden Aktifleştirildi',
    COMPLETED = 'Tamamlandı',
    CANCELLED = 'İptal Edildi'
}

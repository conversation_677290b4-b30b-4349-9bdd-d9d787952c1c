﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Documents.Commands.Delete
{
    public class DeleteDocumentCommandHandler : IRequestHandler<DeleteDocumentCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public DeleteDocumentCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(DeleteDocumentCommand request, CancellationToken cancellationToken)
        {
            var document = await _context.Documents
                .FirstOrDefaultAsync(d => d.Id == request.Id, cancellationToken);

            if (document is null)
                throw new Exception("Silinecek doküman bulunamadı.");

            _context.Documents.Remove(document);
            await _context.SaveChangesAsync(cancellationToken);

            return document.Id;
        }
    }
}

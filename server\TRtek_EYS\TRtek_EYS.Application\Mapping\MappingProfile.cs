﻿using AutoMapper;
using TRtek_EYS.Domain.Entities;

// Customers
using TRtek_EYS.Application.Features.Customers.Commands.Add;
using TRtek_EYS.Application.Features.Customers.Commands.Update;
using TRtek_EYS.Application.Features.Customers.Queries.GetAll;
using TRtek_EYS.Application.Features.Customers.Queries.GetById;

// Teams
using TRtek_EYS.Application.Features.Teams.Commands.Add;
using TRtek_EYS.Application.Features.Teams.Commands.Update;
using TRtek_EYS.Application.Features.Teams.Queries.GetAll;
using TRtek_EYS.Application.Features.Teams.Queries.GetById;

// Roles
using TRtek_EYS.Application.Features.Roles.Commands.Add;
using TRtek_EYS.Application.Features.Roles.Commands.Update;
using TRtek_EYS.Application.Features.Roles.Queries.GetAll;
using TRtek_EYS.Application.Features.Roles.Queries.GetById;

// Projects
using TRtek_EYS.Application.Features.Projects.Commands.Add;
using TRtek_EYS.Application.Features.Projects.Commands.Update;
using TRtek_EYS.Application.Features.Projects.Queries.GetAll;
using TRtek_EYS.Application.Features.Projects.Queries.GetById;

// Platforms
using TRtek_EYS.Application.Features.Platforms.Commands.Add;
using TRtek_EYS.Application.Features.Platforms.Commands.Update;
using TRtek_EYS.Application.Features.Platforms.Queries.GetAll;
using TRtek_EYS.Application.Features.Platforms.Queries.GetById;

// Technologies
using TRtek_EYS.Application.Features.Technologies.Commands.Add;
using TRtek_EYS.Application.Features.Technologies.Commands.Update;
using TRtek_EYS.Application.Features.Technologies.Queries.GetAll;
using TRtek_EYS.Application.Features.Technologies.Queries.GetById;

// Documents
using TRtek_EYS.Application.Features.Documents.Commands.Add;
using TRtek_EYS.Application.Features.Documents.Commands.Update;
using TRtek_EYS.Application.Features.Documents.Queries.GetAll;
using TRtek_EYS.Application.Features.Documents.Queries.GetById;

// Contracts
using TRtek_EYS.Application.Features.Contracts.Commands.Add;
using TRtek_EYS.Application.Features.Contracts.Commands.Update;
using TRtek_EYS.Application.Features.Contracts.Queries.GetAll;
using TRtek_EYS.Application.Features.Contracts.Queries.GetById;

// Configurations
using TRtek_EYS.Application.Features.Configurations.Commands.Add;
using TRtek_EYS.Application.Features.Configurations.Commands.Update;
using TRtek_EYS.Application.Features.Configurations.Queries.GetAll;
using TRtek_EYS.Application.Features.Configurations.Queries.GetById;

// RepositoryInfos
using TRtek_EYS.Application.Features.RepositoryInfos.Commands.Add;
using TRtek_EYS.Application.Features.RepositoryInfos.Commands.Update;
using TRtek_EYS.Application.Features.RepositoryInfos.Queries.GetAll;
using TRtek_EYS.Application.Features.RepositoryInfos.Queries.GetById;

// VersionInfos
using TRtek_EYS.Application.Features.VersionInfos.Commands.Add;
using TRtek_EYS.Application.Features.VersionInfos.Commands.Update;
using TRtek_EYS.Application.Features.VersionInfos.Queries.GetAll;
using TRtek_EYS.Application.Features.VersionInfos.Queries.GetById;

namespace TRtek_EYS.Application.Mapping
{
    public sealed class MappingProfile : Profile
    {
        public MappingProfile()
        {
            // Customers
            CreateMap<AddCustomerCommand, Customer>();
            CreateMap<UpdateCustomerCommand, Customer>();
            CreateMap<Customer, CustomerGetAllDto>();
            CreateMap<Customer, CustomerGetByIdDto>();

            // Teams
            CreateMap<AddTeamCommand, Team>();
            CreateMap<UpdateTeamCommand, Team>();
            CreateMap<Team, TeamGetAllDto>();
            CreateMap<Team, TeamGetByIdDto>();

            // Roles
            CreateMap<AddRoleCommand, Role>();
            CreateMap<UpdateRoleCommand, Role>();
            CreateMap<Role, RoleGetAllDto>();
            CreateMap<Role, RoleGetByIdDto>();

            // Projects
            CreateMap<AddProjectCommand, Project>();
            CreateMap<UpdateProjectCommand, Project>();
            CreateMap<Project, ProjectGetAllDto>();
            CreateMap<Project, ProjectGetByIdDto>();

            // Platforms
            CreateMap<AddPlatformCommand, Platform>();
            CreateMap<UpdatePlatformCommand, Platform>();
            CreateMap<Platform, PlatformGetAllDto>();
            CreateMap<Platform, PlatformGetByIdDto>();

            // Technologies
            CreateMap<AddTechnologyCommand, Technology>();
            CreateMap<UpdateTechnologyCommand, Technology>();
            CreateMap<Technology, TechnologyGetAllDto>();
            CreateMap<Technology, TechnologyGetByIdDto>();

            // Documents
            CreateMap<AddDocumentCommand, Document>();
            CreateMap<UpdateDocumentCommand, Document>();
            CreateMap<Document, DocumentGetAllDto>();
            CreateMap<Document, DocumentGetByIdDto>();

            // Contracts
            CreateMap<AddContractCommand, Contract>();
            CreateMap<UpdateContractCommand, Contract>();
            CreateMap<Contract, ContractGetAllDto>();
            CreateMap<Contract, ContractGetByIdDto>();

            // Configurations
            CreateMap<AddConfigurationCommand, Configuration>();
            CreateMap<UpdateConfigurationCommand, Configuration>();
            CreateMap<Configuration, ConfigurationGetAllDto>();
            CreateMap<Configuration, ConfigurationGetByIdDto>();

            // RepositoryInfos
            CreateMap<AddRepositoryInfoCommand, RepositoryInfo>();
            CreateMap<UpdateRepositoryInfoCommand, RepositoryInfo>();
            CreateMap<RepositoryInfo, RepositoryInfoGetAllDto>();
            CreateMap<RepositoryInfo, RepositoryInfoGetByIdDto>();

            // VersionInfos
            CreateMap<AddVersionInfoCommand, VersionInfo>();
            CreateMap<UpdateVersionInfoCommand, VersionInfo>();
            CreateMap<VersionInfo, VersionInfoGetAllDto>();
            CreateMap<VersionInfo, VersionInfoGetByIdDto>();
        }
    }
}

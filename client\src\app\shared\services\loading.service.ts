import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class LoadingService {
    private loadingSubject = new BehaviorSubject<boolean>(false);
    private loadingCountSubject = new BehaviorSubject<number>(0);

    loading$: Observable<boolean> = this.loadingSubject.asObservable();
    loadingCount$: Observable<number> = this.loadingCountSubject.asObservable();

    show(): void {
        const currentCount = this.loadingCountSubject.value;
        this.loadingCountSubject.next(currentCount + 1);
        this.loadingSubject.next(true);
    }

    hide(): void {
        const currentCount = this.loadingCountSubject.value;
        if (currentCount > 0) {
            const newCount = currentCount - 1;
            this.loadingCountSubject.next(newCount);
            if (newCount === 0) {
                this.loadingSubject.next(false);
            }
        }
    }

    forceHide(): void {
        this.loadingCountSubject.next(0);
        this.loadingSubject.next(false);
    }

    isLoading(): boolean {
        return this.loadingSubject.value;
    }
}

import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { InputTextModule } from 'primeng/inputtext';
import { PasswordModule } from 'primeng/password';
import { CommonModule } from '@angular/common';
import { AuthService } from '../../../../core/services/auth.service';

@Component({
    selector: 'app-login',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        ButtonModule,
        CardModule,
        InputTextModule,
        PasswordModule
    ],
    template: `
        <div class="login-page">
            <div class="login-container">
                <div class="login-card">
                    <div class="text-center mb-5">
                        <img src="assets/logo-b.png" alt="TRtek Logo" class="login-logo mb-4">
                        <div class="text-900 text-3xl font-medium mb-3">TRtek EYS</div>
                        <span class="text-600 font-medium">Giriş yapın</span>
                    </div>

                    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
                        <div class="mb-4">
                            <label for="username" class="block text-900 text-xl font-medium mb-2">Kullanıcı Adı</label>
                            <input id="username" type="text" placeholder="Kullanıcı adınız" pInputText formControlName="username"
                                class="w-full md:w-30rem" style="padding:1rem">
                            <small *ngIf="loginForm.get('username')?.errors?.['required'] && loginForm.get('username')?.touched"
                                class="p-error block">Kullanıcı adı gereklidir</small>
                        </div>

                        <div class="mb-4">
                            <label for="password" class="block text-900 font-medium text-xl mb-2">Şifre</label>
                            <p-password id="password" formControlName="password" [toggleMask]="true"
                                placeholder="Şifreniz" [style]="{'width':'100%'}" [inputStyle]="{'width':'100%', 'padding':'1rem'}"
                                [feedback]="false"></p-password>
                            <small *ngIf="loginForm.get('password')?.errors?.['required'] && loginForm.get('password')?.touched"
                                class="p-error block">Şifre gereklidir</small>
                        </div>

                        <div class="flex align-items-center justify-content-between mb-4">
                            <div class="flex align-items-center">
                                <a class="font-medium no-underline ml-2 text-right cursor-pointer" style="color: #B71C1C">Şifremi unuttum</a>
                            </div>
                        </div>

                        <button pButton pRipple label="Giriş" class="w-full p-3 text-xl"
                            [loading]="isLoading" [disabled]="loginForm.invalid || isLoading"></button>
                    </form>
                </div>
            </div>
        </div>
    `,
    styles: [`
        .login-page {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            width: 100vw;
            background: linear-gradient(180deg, #B71C1C 10%, rgba(183, 28, 28, 0) 30%);
        }
        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            max-width: 500px;
        }
        .login-card {
            width: 100%;
            border-radius: 20px;
            background: white;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .login-logo {
            height: 4rem;
            width: auto;
            object-fit: contain;
            margin: 0 auto;
            display: block;
        }
        @media (max-width: 768px) {
            .login-logo {
                height: 3rem;
            }
        }
    `]
})
export class LoginComponent {
    loginForm: FormGroup;
    isLoading = false;

    constructor(
        private formBuilder: FormBuilder,
        private authService: AuthService,
        private router: Router
    ) {
        this.loginForm = this.formBuilder.group({
            username: ['', Validators.required],
            password: ['', Validators.required]
        });
    }

    onSubmit() {
        if (this.loginForm.valid) {
            this.isLoading = true;
            const { username, password } = this.loginForm.value;

            this.authService.login(username, password).subscribe({
                next: () => {
                    this.router.navigate(['/']);
                },
                error: (error) => {
                    console.error('Login error:', error);
                    this.isLoading = false;
                },
                complete: () => {
                    this.isLoading = false;
                }
            });
        }
    }
}

import { Component } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { InputTextModule } from 'primeng/inputtext';
import { PasswordModule } from 'primeng/password';
import { CommonModule } from '@angular/common';
import { AuthService } from '../../../../core/services/auth.service';

@Component({
    selector: 'app-login',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        ButtonModule,
        CardModule,
        InputTextModule,
        PasswordModule
    ],
    template: `
        <div class="surface-ground flex align-items-center justify-content-center min-h-screen min-w-screen overflow-hidden">
            <div class="flex flex-column align-items-center justify-content-center w-full">
                <div class="w-full max-w-md" style="border-radius:56px; padding:0.3rem; background: linear-gradient(180deg, var(--primary-color) 10%, rgba(33, 150, 243, 0) 30%);">
                    <div class="w-full surface-card py-8 px-5 sm:px-8" style="border-radius:53px">
                        <div class="text-center mb-5">
                            <img src="assets/logo-b.png" alt="TRtek Logo" class="login-logo mb-4">
                            <div class="text-900 text-3xl font-medium mb-3">TRtek EYS</div>
                            <span class="text-600 font-medium">Giriş yapın</span>
                        </div>

                        <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
                            <div class="mb-4">
                                <label for="email" class="block text-900 text-xl font-medium mb-2">Email</label>
                                <input id="email" type="text" placeholder="Email adresiniz" pInputText formControlName="email" 
                                    class="w-full md:w-30rem" style="padding:1rem">
                                <small *ngIf="loginForm.get('email')?.errors?.['required'] && loginForm.get('email')?.touched" 
                                    class="p-error block">Email gereklidir</small>
                                <small *ngIf="loginForm.get('email')?.errors?.['email'] && loginForm.get('email')?.touched" 
                                    class="p-error block">Geçerli bir email adresi giriniz</small>
                            </div>

                            <div class="mb-4">
                                <label for="password" class="block text-900 font-medium text-xl mb-2">Şifre</label>
                                <p-password id="password" formControlName="password" [toggleMask]="true" 
                                    placeholder="Şifreniz" [style]="{'width':'100%'}" [inputStyle]="{'width':'100%', 'padding':'1rem'}" 
                                    [feedback]="false"></p-password>
                                <small *ngIf="loginForm.get('password')?.errors?.['required'] && loginForm.get('password')?.touched" 
                                    class="p-error block">Şifre gereklidir</small>
                            </div>

                            <div class="flex align-items-center justify-content-between mb-4">
                                <div class="flex align-items-center">
                                    <a class="font-medium no-underline ml-2 text-right cursor-pointer" style="color: var(--primary-color)">Şifremi unuttum</a>
                                </div>
                            </div>

                            <button pButton pRipple label="Giriş" class="w-full p-3 text-xl" 
                                [loading]="isLoading" [disabled]="loginForm.invalid || isLoading"></button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    `,
    styles: [`
        .login-logo {
            height: 4rem;
            width: auto;
            object-fit: contain;
            margin: 0 auto;
            display: block;
        }

        @media (max-width: 768px) {
            .login-logo {
                height: 3rem;
            }
        }
    `]
})
export class LoginComponent {
    loginForm: FormGroup;
    isLoading = false;

    constructor(
        private formBuilder: FormBuilder,
        private authService: AuthService,
        private router: Router
    ) {
        this.loginForm = this.formBuilder.group({
            email: ['', [Validators.required, Validators.email]],
            password: ['', Validators.required]
        });
    }

    onSubmit() {
        if (this.loginForm.valid) {
            this.isLoading = true;
            const { email, password } = this.loginForm.value;
            
            this.authService.login(email, password).subscribe({
                next: () => {
                    this.router.navigate(['/']);
                },
                error: (error) => {
                    // TODO: Error handling
                    console.error('Login error:', error);
                    this.isLoading = false;
                },
                complete: () => {
                    this.isLoading = false;
                }
            });
        }
    }
}

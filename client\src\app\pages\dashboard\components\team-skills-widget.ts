import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, debounceTime, Subscription } from 'rxjs';
import { ChartModule } from 'primeng/chart';
import { DropdownModule } from 'primeng/dropdown';
import { SkeletonModule } from 'primeng/skeleton';
import { LayoutService } from '../../../layout/service/layout.service';

interface Team {
    name: string;
    value: string;
}

@Component({
    standalone: true,
    selector: 'app-team-skills-widget',
    imports: [CommonModule, FormsModule, ChartModule, DropdownModule, SkeletonModule],
    template: `<div class="card">
        <div class="flex justify-between align-items-center mb-5">
            <div class="font-semibold text-xl">Takım <PERSON>enekleri</div>
            <p-dropdown 
                [options]="teams" 
                [(ngModel)]="selectedTeam" 
                optionLabel="name" 
                optionValue="value"
                (onChange)="onTeamChange()"
                placeholder="Takım Seçin"
                class="w-12rem">
            </p-dropdown>
        </div>
        
        <div *ngIf="isLoading" class="flex justify-content-center align-items-center" style="height: 300px;">
            <p-skeleton shape="circle" size="250px"></p-skeleton>
        </div>
        
        <div *ngIf="!isLoading && radarData" class="flex justify-content-center">
            <p-chart 
                type="radar" 
                [data]="radarData" 
                [options]="radarOptions"
                width="300px"
                height="300px">
            </p-chart>
        </div>
    </div>`
})
export class TeamSkillsWidget implements OnInit, OnDestroy {
    private destroy$ = new Subject<void>();
    private subscription: Subscription;
    
    radarData: any;
    radarOptions: any;
    isLoading = true;
    selectedTeam = 'frontend';

    teams: Team[] = [
        { name: 'Frontend Takımı', value: 'frontend' },
        { name: 'Backend Takımı', value: 'backend' },
        { name: 'DevOps Takımı', value: 'devops' },
        { name: 'QA Takımı', value: 'qa' }
    ];

    constructor(private layoutService: LayoutService) {
        this.subscription = this.layoutService.configUpdate$
            .pipe(debounceTime(25))
            .subscribe(() => {
                this.initChart();
            });
    }

    ngOnInit() {
        this.initChart();
        setTimeout(() => {
            this.isLoading = false;
        }, 1200);
    }

    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
        if (this.subscription) {
            this.subscription.unsubscribe();
        }
    }

    onTeamChange() {
        this.initChart();
    }

    initChart() {
        const documentStyle = getComputedStyle(document.documentElement);
        const textColor = documentStyle.getPropertyValue('--text-color');
        const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');

        const skillsData = this.getSkillsForTeam(this.selectedTeam);

        this.radarData = {
            labels: skillsData.labels,
            datasets: [
                {
                    label: 'Mevcut Seviye',
                    borderColor: documentStyle.getPropertyValue('--p-primary-500'),
                    pointBackgroundColor: documentStyle.getPropertyValue('--p-primary-500'),
                    pointBorderColor: documentStyle.getPropertyValue('--p-primary-500'),
                    pointHoverBackgroundColor: textColor,
                    pointHoverBorderColor: documentStyle.getPropertyValue('--p-primary-500'),
                    backgroundColor: documentStyle.getPropertyValue('--p-primary-500') + '40',
                    data: skillsData.current
                },
                {
                    label: 'Hedef Seviye',
                    borderColor: documentStyle.getPropertyValue('--p-green-500'),
                    pointBackgroundColor: documentStyle.getPropertyValue('--p-green-500'),
                    pointBorderColor: documentStyle.getPropertyValue('--p-green-500'),
                    pointHoverBackgroundColor: textColor,
                    pointHoverBorderColor: documentStyle.getPropertyValue('--p-green-500'),
                    backgroundColor: documentStyle.getPropertyValue('--p-green-500') + '40',
                    data: skillsData.target
                }
            ]
        };

        this.radarOptions = {
            plugins: {
                legend: {
                    labels: {
                        color: textColor
                    }
                }
            },
            scales: {
                r: {
                    grid: {
                        color: textColorSecondary
                    },
                    pointLabels: {
                        color: textColorSecondary
                    },
                    ticks: {
                        color: textColorSecondary,
                        backdropColor: 'transparent'
                    },
                    min: 0,
                    max: 100
                }
            }
        };
    }

    getSkillsForTeam(team: string) {
        const skillsMap: Record<string, any> = {
            frontend: {
                labels: ['Angular', 'React', 'Vue.js', 'TypeScript', 'CSS/SCSS', 'UI/UX'],
                current: [85, 70, 60, 90, 80, 75],
                target: [90, 80, 70, 95, 85, 85]
            },
            backend: {
                labels: ['.NET Core', 'Node.js', 'Python', 'Database', 'API Design', 'Microservices'],
                current: [90, 75, 65, 85, 80, 70],
                target: [95, 85, 75, 90, 90, 80]
            },
            devops: {
                labels: ['Docker', 'Kubernetes', 'CI/CD', 'AWS/Azure', 'Monitoring', 'Security'],
                current: [85, 70, 90, 75, 80, 70],
                target: [90, 80, 95, 85, 90, 80]
            },
            qa: {
                labels: ['Manual Testing', 'Automation', 'Performance', 'Security Testing', 'API Testing', 'Mobile Testing'],
                current: [90, 75, 70, 65, 80, 70],
                target: [95, 85, 80, 75, 90, 80]
            }
        };

        return skillsMap[team] || skillsMap['frontend'];
    }
}

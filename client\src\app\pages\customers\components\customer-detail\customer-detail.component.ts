import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { TabViewModule } from 'primeng/tabview';
import { TagModule } from 'primeng/tag';
import { TableModule } from 'primeng/table';
import { CardModule } from 'primeng/card';
import { DividerModule } from 'primeng/divider';
import { Customer, CustomerStatus, CustomerType } from '../../models/customer.model';

@Component({
    selector: 'app-customer-detail',
    standalone: true,
    imports: [
        CommonModule,
        ButtonModule,
        TabViewModule,
        TagModule,
        TableModule,
        CardModule,
        DividerModule
    ],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <div>
                            <h5 class="m-0">{{ customer?.companyName }}</h5>
                            <p class="text-500 mt-1">{{ customer?.customerCode }}</p>
                        </div>
                        <div class="flex gap-2">
                            <p-button 
                                label="Düzenle" 
                                icon="pi pi-pencil" 
                                class="p-button-warning"
                                (onClick)="editCustomer()">
                            </p-button>
                            <p-button 
                                label="Geri" 
                                icon="pi pi-arrow-left" 
                                class="p-button-secondary"
                                (onClick)="goBack()">
                            </p-button>
                        </div>
                    </div>

                    <p-tabView *ngIf="customer">
                        <p-tabPanel header="Genel Bilgiler" leftIcon="pi pi-info-circle">
                            <div class="grid">
                                <div class="col-12 md:col-6">
                                    <p-card header="Şirket Bilgileri">
                                        <div class="field">
                                            <label class="font-medium">Müşteri Kodu:</label>
                                            <div class="mt-1">{{ customer.customerCode }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Şirket Adı:</label>
                                            <div class="mt-1">{{ customer.companyName }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Sektör:</label>
                                            <div class="mt-1">
                                                <p-tag [value]="customer.industry" severity="info"></p-tag>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Müşteri Türü:</label>
                                            <div class="mt-1">
                                                <p-tag [value]="customer.customerType" severity="success"></p-tag>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Durum:</label>
                                            <div class="mt-1">
                                                <p-tag 
                                                    [value]="customer.status" 
                                                    [severity]="getStatusSeverity(customer.status)">
                                                </p-tag>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                                <div class="col-12 md:col-6">
                                    <p-card header="İletişim Bilgileri">
                                        <div class="field">
                                            <label class="font-medium">İletişim Kişisi:</label>
                                            <div class="mt-1">{{ customer.contactPerson }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">E-posta:</label>
                                            <div class="mt-1">{{ customer.email }}</div>
                                        </div>
                                        <div class="field" *ngIf="customer.phone">
                                            <label class="font-medium">Telefon:</label>
                                            <div class="mt-1">{{ customer.phone }}</div>
                                        </div>
                                        <div class="field" *ngIf="customer.address">
                                            <label class="font-medium">Adres:</label>
                                            <div class="mt-1">{{ customer.address }}</div>
                                        </div>
                                        <div class="field" *ngIf="customer.website">
                                            <label class="font-medium">Web Sitesi:</label>
                                            <div class="mt-1">
                                                <a [href]="customer.website" target="_blank" class="text-primary">
                                                    {{ customer.website }}
                                                </a>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                            </div>
                        </p-tabPanel>

                        <p-tabPanel header="Projeler" leftIcon="pi pi-briefcase">
                            <p-table [value]="customer.projects" [tableStyle]="{ 'min-width': '50rem' }">
                                <ng-template pTemplate="header">
                                    <tr>
                                        <th>Proje Kodu</th>
                                        <th>Proje Adı</th>
                                        <th>Atanma Tarihi</th>
                                        <th>Mevcut Versiyon</th>
                                        <th>Son Güncelleme</th>
                                        <th>Durum</th>
                                        <th>Destek Seviyesi</th>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="body" let-project>
                                    <tr>
                                        <td>{{ project.projectCode }}</td>
                                        <td>{{ project.projectName }}</td>
                                        <td>{{ project.assignedDate | date:'dd/MM/yyyy' }}</td>
                                        <td>{{ project.currentVersion }}</td>
                                        <td>{{ project.lastUpdateDate | date:'dd/MM/yyyy' }}</td>
                                        <td>{{ project.status }}</td>
                                        <td>
                                            <p-tag [value]="project.supportLevel" severity="info"></p-tag>
                                        </td>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="emptymessage">
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-500">Bu müşteriye henüz proje atanmamış</div>
                                        </td>
                                    </tr>
                                </ng-template>
                            </p-table>
                        </p-tabPanel>

                        <p-tabPanel header="Sözleşmeler" leftIcon="pi pi-file-edit">
                            <p-table [value]="customer.contracts" [tableStyle]="{ 'min-width': '50rem' }">
                                <ng-template pTemplate="header">
                                    <tr>
                                        <th>Sözleşme No</th>
                                        <th>Proje</th>
                                        <th>Sözleşme Tarihi</th>
                                        <th>Başlangıç</th>
                                        <th>Bitiş</th>
                                        <th>Lisans Türü</th>
                                        <th>Değer</th>
                                        <th>Durum</th>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="body" let-contract>
                                    <tr>
                                        <td>{{ contract.contractNumber }}</td>
                                        <td>{{ contract.projectName }}</td>
                                        <td>{{ contract.contractDate | date:'dd/MM/yyyy' }}</td>
                                        <td>{{ contract.startDate | date:'dd/MM/yyyy' }}</td>
                                        <td>{{ contract.endDate | date:'dd/MM/yyyy' }}</td>
                                        <td>{{ contract.licenseType }}</td>
                                        <td>{{ contract.contractValue | currency:contract.currency:'symbol':'1.0-0' }}</td>
                                        <td>
                                            <p-tag 
                                                [value]="contract.status" 
                                                [severity]="contract.isActive ? 'success' : 'danger'">
                                            </p-tag>
                                        </td>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="emptymessage">
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="text-500">Bu müşterinin henüz sözleşmesi bulunmuyor</div>
                                        </td>
                                    </tr>
                                </ng-template>
                            </p-table>
                        </p-tabPanel>
                    </p-tabView>
                </div>
            </div>
        </div>
    `
})
export class CustomerDetailComponent implements OnInit {
    customer: Customer | null = null;
    customerId: string = '';

    constructor(
        private route: ActivatedRoute,
        private router: Router
    ) {}

    ngOnInit() {
        this.customerId = this.route.snapshot.params['id'];
        this.loadCustomer();
    }

    loadCustomer() {
        // Dummy data
        this.customer = {
            id: '1',
            customerCode: 'CUS-2025-001',
            companyName: 'ABC Teknoloji A.Ş.',
            contactPerson: 'Ahmet Yılmaz',
            email: '<EMAIL>',
            phone: '+90 ************',
            address: 'Maslak Mahallesi, Teknoloji Caddesi No:15',
            city: 'İstanbul',
            country: 'Türkiye',
            website: 'https://www.abc.com',
            taxNumber: '1234567890',
            industry: 'Teknoloji' as any,
            customerType: 'Kurumsal' as any,
            status: 'Aktif' as any,
            registrationDate: new Date('2023-01-15'),
            lastContactDate: new Date('2025-01-10'),
            totalProjects: 3,
            activeProjects: 2,
            totalRevenue: 750000,
            notes: 'Stratejik müşteri, uzun vadeli işbirliği',
            contracts: [],
            projects: []
        };
    }

    editCustomer() {
        this.router.navigate(['/pages/customers', this.customerId, 'edit']);
    }

    goBack() {
        this.router.navigate(['/pages/customers']);
    }

    getStatusSeverity(status: CustomerStatus): string {
        switch (status) {
            case CustomerStatus.ACTIVE:
                return 'success';
            case CustomerStatus.PROSPECT:
                return 'info';
            case CustomerStatus.INACTIVE:
                return 'warning';
            case CustomerStatus.SUSPENDED:
                return 'danger';
            case CustomerStatus.TERMINATED:
                return 'secondary';
            default:
                return 'info';
        }
    }
}

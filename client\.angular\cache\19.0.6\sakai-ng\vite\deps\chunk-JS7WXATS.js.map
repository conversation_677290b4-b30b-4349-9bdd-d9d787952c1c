{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-inputtext.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, inject, booleanAttribute, HostListener, Input, Optional, Directive, NgModule } from '@angular/core';\nimport { isEmpty } from '@primeuix/utils';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { BaseStyle } from 'primeng/base';\nimport * as i1 from '@angular/forms';\nconst theme = ({\n  dt\n}) => `\n.p-inputtext {\n    font-family: inherit;\n    font-feature-settings: inherit;\n    font-size: 1rem;\n    color: ${dt('inputtext.color')};\n    background: ${dt('inputtext.background')};\n    padding-block: ${dt('inputtext.padding.y')};\n    padding-inline: ${dt('inputtext.padding.x')};\n    border: 1px solid ${dt('inputtext.border.color')};\n    transition: background ${dt('inputtext.transition.duration')}, color ${dt('inputtext.transition.duration')}, border-color ${dt('inputtext.transition.duration')}, outline-color ${dt('inputtext.transition.duration')}, box-shadow ${dt('inputtext.transition.duration')};\n    appearance: none;\n    border-radius: ${dt('inputtext.border.radius')};\n    outline-color: transparent;\n    box-shadow: ${dt('inputtext.shadow')};\n}\n\n.p-inputtext.ng-invalid.ng-dirty {\n    border-color: ${dt('inputtext.invalid.border.color')};\n}\n\n.p-inputtext:enabled:hover {\n    border-color: ${dt('inputtext.hover.border.color')};\n}\n\n.p-inputtext:enabled:focus {\n    border-color: ${dt('inputtext.focus.border.color')};\n    box-shadow: ${dt('inputtext.focus.ring.shadow')};\n    outline: ${dt('inputtext.focus.ring.width')} ${dt('inputtext.focus.ring.style')} ${dt('inputtext.focus.ring.color')};\n    outline-offset: ${dt('inputtext.focus.ring.offset')};\n}\n\n.p-inputtext.p-invalid {\n    border-color: ${dt('inputtext.invalid.border.color')};\n}\n\n.p-inputtext.p-variant-filled {\n    background: ${dt('inputtext.filled.background')};\n}\n    \n.p-inputtext.p-variant-filled:enabled:hover {\n    background: ${dt('inputtext.filled.hover.background')};\n}\n\n.p-inputtext.p-variant-filled:enabled:focus {\n    background: ${dt('inputtext.filled.focus.background')};\n}\n\n.p-inputtext:disabled {\n    opacity: 1;\n    background: ${dt('inputtext.disabled.background')};\n    color: ${dt('inputtext.disabled.color')};\n}\n\n.p-inputtext::placeholder {\n    color: ${dt('inputtext.placeholder.color')};\n}\n\n.p-inputtext.ng-invalid.ng-dirty::placeholder {\n    color: ${dt('inputtext.invalid.placeholder.color')};\n}\n\n.p-inputtext-sm {\n    font-size: ${dt('inputtext.sm.font.size')};\n    padding-block: ${dt('inputtext.sm.padding.y')};\n    padding-inline: ${dt('inputtext.sm.padding.x')};\n}\n\n.p-inputtext-lg {\n    font-size: ${dt('inputtext.lg.font.size')};\n    padding-block: ${dt('inputtext.lg.padding.y')};\n    padding-inline: ${dt('inputtext.lg.padding.x')};\n}\n\n.p-inputtext-fluid {\n    width: 100%;\n}\n`;\nconst classes = {\n  root: ({\n    instance,\n    props\n  }) => ['p-inputtext p-component', {\n    'p-filled': instance.filled,\n    'p-inputtext-sm': props.size === 'small',\n    'p-inputtext-lg': props.size === 'large',\n    'p-invalid': props.invalid,\n    'p-variant-filled': props.variant === 'filled',\n    'p-inputtext-fluid': props.fluid\n  }]\n};\nclass InputTextStyle extends BaseStyle {\n  name = 'inputtext';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵInputTextStyle_BaseFactory;\n    return function InputTextStyle_Factory(__ngFactoryType__) {\n      return (ɵInputTextStyle_BaseFactory || (ɵInputTextStyle_BaseFactory = i0.ɵɵgetInheritedFactory(InputTextStyle)))(__ngFactoryType__ || InputTextStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: InputTextStyle,\n    factory: InputTextStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputTextStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * InputText renders a text field to enter data.\n *\n * [Live Demo](https://www.primeng.org/inputtext/)\n *\n * @module inputtextstyle\n *\n */\nvar InputTextClasses;\n(function (InputTextClasses) {\n  /**\n   * The class of root element\n   */\n  InputTextClasses[\"root\"] = \"p-inputtext\";\n})(InputTextClasses || (InputTextClasses = {}));\n\n/**\n * InputText directive is an extension to standard input element with theming.\n * @group Components\n */\nclass InputText extends BaseComponent {\n  ngModel;\n  /**\n   * Specifies the input variant of the component.\n   * @group Props\n   */\n  variant;\n  /**\n   * Spans 100% width of the container when enabled.\n   * @group Props\n   */\n  fluid;\n  /**\n   * Defines the size of the component.\n   * @group Props\n   */\n  pSize;\n  filled;\n  _componentStyle = inject(InputTextStyle);\n  get hasFluid() {\n    const nativeElement = this.el.nativeElement;\n    const fluidComponent = nativeElement.closest('p-fluid');\n    return isEmpty(this.fluid) ? !!fluidComponent : this.fluid;\n  }\n  constructor(ngModel) {\n    super();\n    this.ngModel = ngModel;\n  }\n  ngAfterViewInit() {\n    super.ngAfterViewInit();\n    this.updateFilledState();\n    this.cd.detectChanges();\n  }\n  ngDoCheck() {\n    this.updateFilledState();\n  }\n  onInput() {\n    this.updateFilledState();\n  }\n  updateFilledState() {\n    this.filled = this.el.nativeElement.value && this.el.nativeElement.value.length || this.ngModel && this.ngModel.model;\n  }\n  static ɵfac = function InputText_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || InputText)(i0.ɵɵdirectiveInject(i1.NgModel, 8));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: InputText,\n    selectors: [[\"\", \"pInputText\", \"\"]],\n    hostAttrs: [1, \"p-inputtext\", \"p-component\"],\n    hostVars: 14,\n    hostBindings: function InputText_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"input\", function InputText_input_HostBindingHandler($event) {\n          return ctx.onInput($event);\n        });\n      }\n      if (rf & 2) {\n        let tmp_1_0;\n        i0.ɵɵclassProp(\"p-filled\", ctx.filled)(\"p-variant-filled\", ((tmp_1_0 = ctx.variant) !== null && tmp_1_0 !== undefined ? tmp_1_0 : ctx.config.inputStyle() || ctx.config.inputVariant()) === \"filled\")(\"p-inputtext-fluid\", ctx.hasFluid)(\"p-inputtext-sm\", ctx.pSize === \"small\")(\"p-inputfield-sm\", ctx.pSize === \"small\")(\"p-inputtext-lg\", ctx.pSize === \"large\")(\"p-inputfield-lg\", ctx.pSize === \"large\");\n      }\n    },\n    inputs: {\n      variant: \"variant\",\n      fluid: [2, \"fluid\", \"fluid\", booleanAttribute],\n      pSize: \"pSize\"\n    },\n    features: [i0.ɵɵProvidersFeature([InputTextStyle]), i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputText, [{\n    type: Directive,\n    args: [{\n      selector: '[pInputText]',\n      standalone: true,\n      host: {\n        class: 'p-inputtext p-component',\n        '[class.p-filled]': 'filled',\n        '[class.p-variant-filled]': '(variant ?? (config.inputStyle() || config.inputVariant())) === \"filled\"',\n        '[class.p-inputtext-fluid]': 'hasFluid',\n        '[class.p-inputtext-sm]': 'pSize === \"small\"',\n        '[class.p-inputfield-sm]': 'pSize === \"small\"',\n        '[class.p-inputtext-lg]': 'pSize === \"large\"',\n        '[class.p-inputfield-lg]': 'pSize === \"large\"'\n      },\n      providers: [InputTextStyle]\n    }]\n  }], () => [{\n    type: i1.NgModel,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    variant: [{\n      type: Input\n    }],\n    fluid: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    pSize: [{\n      type: Input,\n      args: ['pSize']\n    }],\n    onInput: [{\n      type: HostListener,\n      args: ['input', ['$event']]\n    }]\n  });\n})();\nclass InputTextModule {\n  static ɵfac = function InputTextModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || InputTextModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: InputTextModule,\n    imports: [InputText],\n    exports: [InputText]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputTextModule, [{\n    type: NgModule,\n    args: [{\n      imports: [InputText],\n      exports: [InputText]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { InputText, InputTextClasses, InputTextModule, InputTextStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,aAKO,GAAG,iBAAiB,CAAC;AAAA,kBAChB,GAAG,sBAAsB,CAAC;AAAA,qBACvB,GAAG,qBAAqB,CAAC;AAAA,sBACxB,GAAG,qBAAqB,CAAC;AAAA,wBACvB,GAAG,wBAAwB,CAAC;AAAA,6BACvB,GAAG,+BAA+B,CAAC,WAAW,GAAG,+BAA+B,CAAC,kBAAkB,GAAG,+BAA+B,CAAC,mBAAmB,GAAG,+BAA+B,CAAC,gBAAgB,GAAG,+BAA+B,CAAC;AAAA;AAAA,qBAEvP,GAAG,yBAAyB,CAAC;AAAA;AAAA,kBAEhC,GAAG,kBAAkB,CAAC;AAAA;AAAA;AAAA;AAAA,oBAIpB,GAAG,gCAAgC,CAAC;AAAA;AAAA;AAAA;AAAA,oBAIpC,GAAG,8BAA8B,CAAC;AAAA;AAAA;AAAA;AAAA,oBAIlC,GAAG,8BAA8B,CAAC;AAAA,kBACpC,GAAG,6BAA6B,CAAC;AAAA,eACpC,GAAG,4BAA4B,CAAC,IAAI,GAAG,4BAA4B,CAAC,IAAI,GAAG,4BAA4B,CAAC;AAAA,sBACjG,GAAG,6BAA6B,CAAC;AAAA;AAAA;AAAA;AAAA,oBAInC,GAAG,gCAAgC,CAAC;AAAA;AAAA;AAAA;AAAA,kBAItC,GAAG,6BAA6B,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIjC,GAAG,mCAAmC,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIvC,GAAG,mCAAmC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,kBAKvC,GAAG,+BAA+B,CAAC;AAAA,aACxC,GAAG,0BAA0B,CAAC;AAAA;AAAA;AAAA;AAAA,aAI9B,GAAG,6BAA6B,CAAC;AAAA;AAAA;AAAA;AAAA,aAIjC,GAAG,qCAAqC,CAAC;AAAA;AAAA;AAAA;AAAA,iBAIrC,GAAG,wBAAwB,CAAC;AAAA,qBACxB,GAAG,wBAAwB,CAAC;AAAA,sBAC3B,GAAG,wBAAwB,CAAC;AAAA;AAAA;AAAA;AAAA,iBAIjC,GAAG,wBAAwB,CAAC;AAAA,qBACxB,GAAG,wBAAwB,CAAC;AAAA,sBAC3B,GAAG,wBAAwB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOlD,IAAM,UAAU;AAAA,EACd,MAAM,CAAC;AAAA,IACL;AAAA,IACA;AAAA,EACF,MAAM,CAAC,2BAA2B;AAAA,IAChC,YAAY,SAAS;AAAA,IACrB,kBAAkB,MAAM,SAAS;AAAA,IACjC,kBAAkB,MAAM,SAAS;AAAA,IACjC,aAAa,MAAM;AAAA,IACnB,oBAAoB,MAAM,YAAY;AAAA,IACtC,qBAAqB,MAAM;AAAA,EAC7B,CAAC;AACH;AACA,IAAM,iBAAN,MAAM,wBAAuB,UAAU;AAAA,EACrC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,uBAAuB,mBAAmB;AACxD,cAAQ,gCAAgC,8BAAiC,sBAAsB,eAAc,IAAI,qBAAqB,eAAc;AAAA,IACtJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,gBAAe;AAAA,EAC1B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,mBAAkB;AAI3B,EAAAA,kBAAiB,MAAM,IAAI;AAC7B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAM9C,IAAM,YAAN,MAAM,mBAAkB,cAAc;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA;AAAA,EACA,kBAAkB,OAAO,cAAc;AAAA,EACvC,IAAI,WAAW;AACb,UAAM,gBAAgB,KAAK,GAAG;AAC9B,UAAM,iBAAiB,cAAc,QAAQ,SAAS;AACtD,WAAO,QAAQ,KAAK,KAAK,IAAI,CAAC,CAAC,iBAAiB,KAAK;AAAA,EACvD;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AACN,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,kBAAkB;AAChB,UAAM,gBAAgB;AACtB,SAAK,kBAAkB;AACvB,SAAK,GAAG,cAAc;AAAA,EACxB;AAAA,EACA,YAAY;AACV,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,UAAU;AACR,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,oBAAoB;AAClB,SAAK,SAAS,KAAK,GAAG,cAAc,SAAS,KAAK,GAAG,cAAc,MAAM,UAAU,KAAK,WAAW,KAAK,QAAQ;AAAA,EAClH;AAAA,EACA,OAAO,OAAO,SAAS,kBAAkB,mBAAmB;AAC1D,WAAO,KAAK,qBAAqB,YAAc,kBAAqB,SAAS,CAAC,CAAC;AAAA,EACjF;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,CAAC;AAAA,IAClC,WAAW,CAAC,GAAG,eAAe,aAAa;AAAA,IAC3C,UAAU;AAAA,IACV,cAAc,SAAS,uBAAuB,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,mCAAmC,QAAQ;AACzE,iBAAO,IAAI,QAAQ,MAAM;AAAA,QAC3B,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,YAAY,YAAY,IAAI,MAAM,EAAE,sBAAsB,UAAU,IAAI,aAAa,QAAQ,YAAY,SAAY,UAAU,IAAI,OAAO,WAAW,KAAK,IAAI,OAAO,aAAa,OAAO,QAAQ,EAAE,qBAAqB,IAAI,QAAQ,EAAE,kBAAkB,IAAI,UAAU,OAAO,EAAE,mBAAmB,IAAI,UAAU,OAAO,EAAE,kBAAkB,IAAI,UAAU,OAAO,EAAE,mBAAmB,IAAI,UAAU,OAAO;AAAA,MAC/Y;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,OAAO,CAAC,GAAG,SAAS,SAAS,gBAAgB;AAAA,MAC7C,OAAO;AAAA,IACT;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,cAAc,CAAC,GAAM,0BAA6B,0BAA0B;AAAA,EAChH,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,oBAAoB;AAAA,QACpB,4BAA4B;AAAA,QAC5B,6BAA6B;AAAA,QAC7B,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,MAC7B;AAAA,MACA,WAAW,CAAC,cAAc;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,SAAS;AAAA,IACnB,SAAS,CAAC,SAAS;AAAA,EACrB,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,SAAS;AAAA,MACnB,SAAS,CAAC,SAAS;AAAA,IACrB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["InputTextClasses"]}
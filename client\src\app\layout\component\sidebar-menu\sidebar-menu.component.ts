import { Component, OnInit } from '@angular/core';
import { RouterLink, RouterLinkActive } from '@angular/router';
import { MenuItem, MenuService } from '../../service/menu.service';
import { PanelMenuModule } from 'primeng/panelmenu';
import { AuthService } from '../../../core/services/auth.service';

@Component({
    selector: 'app-sidebar-menu',
    standalone: true,
    imports: [RouterLink, RouterLinkActive, PanelMenuModule],
    template: `
        <div class="layout-sidebar">
            <div class="layout-menu">
                <p-panelMenu [model]="menuItems" [multiple]="true"></p-panelMenu>
            </div>
        </div>
    `,
    styles: [`
        .layout-sidebar {
            width: 280px;
            height: calc(100vh - 4rem);
            position: fixed;
            left: 0;
            top: 4rem;
            padding: 2rem;
            background-color: var(--surface-card);
            border-right: 1px solid var(--surface-border);
            overflow-y: auto;
        }
    `]
})
export class SidebarMenuComponent implements OnInit {
    menuItems: MenuItem[] = [];

    constructor(
        private menuService: MenuService,
        private authService: AuthService
    ) {}

    ngOnInit() {
        this.menuService.menuItems$.subscribe(items => {
            this.menuItems = items;
        });
    }
}

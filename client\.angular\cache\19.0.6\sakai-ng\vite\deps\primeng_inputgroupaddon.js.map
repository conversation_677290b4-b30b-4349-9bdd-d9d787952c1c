{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-inputgroupaddon.mjs"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, HostBinding, Input, Component, NgModule } from '@angular/core';\nimport { SharedModule } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"*\"];\nconst classes = {\n  root: 'p-inputgroupaddon'\n};\nclass InputGroupAddonStyle extends BaseStyle {\n  name = 'inputgroupaddon';\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵInputGroupAddonStyle_BaseFactory;\n    return function InputGroupAddonStyle_Factory(__ngFactoryType__) {\n      return (ɵInputGroupAddonStyle_BaseFactory || (ɵInputGroupAddonStyle_BaseFactory = i0.ɵɵgetInheritedFactory(InputGroupAddonStyle)))(__ngFactoryType__ || InputGroupAddonStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: InputGroupAddonStyle,\n    factory: InputGroupAddonStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputGroupAddonStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * InputGroupAddon displays text, icon, buttons and other content can be grouped next to an input.\n * @group Components\n */\nclass InputGroupAddon extends BaseComponent {\n  /**\n   * Inline style of the element.\n   * @group Props\n   */\n  style;\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  styleClass;\n  _componentStyle = inject(InputGroupAddonStyle);\n  get hostStyle() {\n    return this.style;\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵInputGroupAddon_BaseFactory;\n    return function InputGroupAddon_Factory(__ngFactoryType__) {\n      return (ɵInputGroupAddon_BaseFactory || (ɵInputGroupAddon_BaseFactory = i0.ɵɵgetInheritedFactory(InputGroupAddon)))(__ngFactoryType__ || InputGroupAddon);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: InputGroupAddon,\n    selectors: [[\"p-inputgroup-addon\"], [\"p-inputGroupAddon\"]],\n    hostVars: 7,\n    hostBindings: function InputGroupAddon_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"data-pc-name\", \"inputgroupaddon\");\n        i0.ɵɵstyleMap(ctx.hostStyle);\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵclassProp(\"p-inputgroupaddon\", true);\n      }\n    },\n    inputs: {\n      style: \"style\",\n      styleClass: \"styleClass\"\n    },\n    features: [i0.ɵɵProvidersFeature([InputGroupAddonStyle]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function InputGroupAddon_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    dependencies: [CommonModule],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputGroupAddon, [{\n    type: Component,\n    args: [{\n      selector: 'p-inputgroup-addon, p-inputGroupAddon',\n      template: ` <ng-content></ng-content> `,\n      standalone: true,\n      imports: [CommonModule],\n      host: {\n        '[class]': 'styleClass',\n        '[class.p-inputgroupaddon]': 'true',\n        '[attr.data-pc-name]': '\"inputgroupaddon\"'\n      },\n      providers: [InputGroupAddonStyle]\n    }]\n  }], null, {\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    hostStyle: [{\n      type: HostBinding,\n      args: ['style']\n    }]\n  });\n})();\nclass InputGroupAddonModule {\n  static ɵfac = function InputGroupAddonModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || InputGroupAddonModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: InputGroupAddonModule,\n    imports: [InputGroupAddon, SharedModule],\n    exports: [InputGroupAddon, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [InputGroupAddon, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputGroupAddonModule, [{\n    type: NgModule,\n    args: [{\n      imports: [InputGroupAddon, SharedModule],\n      exports: [InputGroupAddon, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { InputGroupAddon, InputGroupAddonModule, InputGroupAddonStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,UAAU;AAAA,EACd,MAAM;AACR;AACA,IAAM,uBAAN,MAAM,8BAA6B,UAAU;AAAA,EAC3C,OAAO;AAAA,EACP,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,6BAA6B,mBAAmB;AAC9D,cAAQ,sCAAsC,oCAAuC,sBAAsB,qBAAoB,IAAI,qBAAqB,qBAAoB;AAAA,IAC9K;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,sBAAqB;AAAA,EAChC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,kBAAN,MAAM,yBAAwB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA,kBAAkB,OAAO,oBAAoB;AAAA,EAC7C,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,wBAAwB,mBAAmB;AACzD,cAAQ,iCAAiC,+BAAkC,sBAAsB,gBAAe,IAAI,qBAAqB,gBAAe;AAAA,IAC1J;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,oBAAoB,GAAG,CAAC,mBAAmB,CAAC;AAAA,IACzD,UAAU;AAAA,IACV,cAAc,SAAS,6BAA6B,IAAI,KAAK;AAC3D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,gBAAgB,iBAAiB;AAChD,QAAG,WAAW,IAAI,SAAS;AAC3B,QAAG,WAAW,IAAI,UAAU;AAC5B,QAAG,YAAY,qBAAqB,IAAI;AAAA,MAC1C;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,oBAAoB,CAAC,GAAM,0BAA0B;AAAA,IACvF,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,yBAAyB,IAAI,KAAK;AACnD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,cAAc,CAAC,YAAY;AAAA,IAC3B,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,YAAY;AAAA,MACtB,MAAM;AAAA,QACJ,WAAW;AAAA,QACX,6BAA6B;AAAA,QAC7B,uBAAuB;AAAA,MACzB;AAAA,MACA,WAAW,CAAC,oBAAoB;AAAA,IAClC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,YAAY;AAAA,IACvC,SAAS,CAAC,iBAAiB,YAAY;AAAA,EACzC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,iBAAiB,cAAc,YAAY;AAAA,EACvD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,YAAY;AAAA,MACvC,SAAS,CAAC,iBAAiB,YAAY;AAAA,IACzC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}
﻿using MediatR;
using TRtek_EYS.Application.Common.Interfaces;
using TRtek_EYS.Domain.Entities;

namespace TRtek_EYS.Application.Features.Configurations.Commands.Add
{
    public class AddConfigurationCommandHandler : IRequestHandler<AddConfigurationCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public AddConfigurationCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(AddConfigurationCommand request, CancellationToken cancellationToken)
        {
            var entity = new Configuration
            {
                Name = request.Name,
                Description = request.Description,
                WebServerIp = request.WebServerIp,
                WebServerUrl = request.WebServerUrl,
                DatabaseType = request.DatabaseType,
                DatabaseVersion = request.DatabaseVersion,
                Username = request.Username,
                EncryptedPassword = request.EncryptedPassword,
                CustomerId = request.CustomerId,
                CreatedAt = DateTime.UtcNow
            };

            await _context.Configurations.AddAsync(entity, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);

            return entity.Id;
        }
    }
}

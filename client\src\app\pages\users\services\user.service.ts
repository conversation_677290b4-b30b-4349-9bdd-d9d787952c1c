import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { User, UserFormData, UserStatus, Department, PermissionAction, SkillLevel, SkillCategory, LanguageLevel } from '../models/user.model';

@Injectable({
    providedIn: 'root'
})
export class UserService {
    private mockUsers: User[] = [
        {
            id: '1',
            userCode: 'USR-2024-001',
            firstName: 'Ahmet',
            lastName: '<PERSON>ı<PERSON><PERSON>',
            fullName: 'Ah<PERSON> Yılmaz',
            email: '<EMAIL>',
            phone: '+90 ************',
            department: Department.DEVELOPMENT,
            position: 'Senior Frontend Developer',
            roles: [
                {
                    id: 'r1',
                    name: '<PERSON><PERSON><PERSON>',
                    code: 'DEV',
                    description: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rol<PERSON>',
                    permissions: [],
                    isActive: true
                }
            ],
            permissions: [
                {
                    id: 'p1',
                    name: '<PERSON><PERSON>',
                    code: 'PROJECT_READ',
                    module: 'Projects',
                    action: PermissionAction.READ,
                    resource: 'projects',
                    description: 'Proje<PERSON>i görünt<PERSON><PERSON><PERSON> yetkis<PERSON>'
                }
            ],
            status: UserStatus.ACTIVE,
            isActive: true,
            lastLoginDate: new Date('2024-07-16T10:30:00'),
            createdAt: new Date('2024-01-15'),
            updatedAt: new Date('2024-07-16'),
            createdBy: 'System',
            employeeId: 'EMP-001',
            hireDate: new Date('2024-01-15'),
            birthDate: new Date('1990-05-15'),
            skills: [
                {
                    id: 's1',
                    name: 'React',
                    level: SkillLevel.EXPERT,
                    category: SkillCategory.FRAMEWORK,
                    yearsOfExperience: 5
                },
                {
                    id: 's2',
                    name: 'TypeScript',
                    level: SkillLevel.ADVANCED,
                    category: SkillCategory.PROGRAMMING,
                    yearsOfExperience: 4
                }
            ],
            languages: [
                {
                    id: 'l1',
                    name: 'Türkçe',
                    level: LanguageLevel.NATIVE,
                    isNative: true
                },
                {
                    id: 'l2',
                    name: 'İngilizce',
                    level: LanguageLevel.ADVANCED,
                    isNative: false
                }
            ],
            notes: 'Deneyimli frontend geliştirici'
        },
        {
            id: '2',
            userCode: 'USR-2024-002',
            firstName: 'Ayşe',
            lastName: 'Demir',
            fullName: 'Ayşe Demir',
            email: '<EMAIL>',
            phone: '+90 ************',
            department: Department.DESIGN,
            position: 'UI/UX Designer',
            roles: [
                {
                    id: 'r2',
                    name: 'Designer',
                    code: 'DES',
                    description: 'Tasarımcı rolü',
                    permissions: [],
                    isActive: true
                }
            ],
            permissions: [
                {
                    id: 'p2',
                    name: 'Tasarım Oluşturma',
                    code: 'DESIGN_CREATE',
                    module: 'Design',
                    action: PermissionAction.CREATE,
                    resource: 'designs',
                    description: 'Tasarım oluşturma yetkisi'
                }
            ],
            status: UserStatus.ACTIVE,
            isActive: true,
            lastLoginDate: new Date('2024-07-16T09:15:00'),
            createdAt: new Date('2024-02-01'),
            updatedAt: new Date('2024-07-15'),
            createdBy: 'Admin',
            employeeId: 'EMP-002',
            hireDate: new Date('2024-02-01'),
            birthDate: new Date('1992-08-22'),
            skills: [
                {
                    id: 's3',
                    name: 'Figma',
                    level: SkillLevel.EXPERT,
                    category: SkillCategory.DESIGN,
                    yearsOfExperience: 3
                },
                {
                    id: 's4',
                    name: 'Adobe XD',
                    level: SkillLevel.ADVANCED,
                    category: SkillCategory.DESIGN,
                    yearsOfExperience: 2
                }
            ],
            languages: [
                {
                    id: 'l3',
                    name: 'Türkçe',
                    level: LanguageLevel.NATIVE,
                    isNative: true
                },
                {
                    id: 'l4',
                    name: 'İngilizce',
                    level: LanguageLevel.INTERMEDIATE,
                    isNative: false
                }
            ],
            notes: 'Yaratıcı UI/UX tasarımcı'
        },
        {
            id: '3',
            userCode: 'USR-2024-003',
            firstName: 'Mehmet',
            lastName: 'Özkan',
            fullName: 'Mehmet Özkan',
            email: '<EMAIL>',
            phone: '+90 ************',
            department: Department.MANAGEMENT,
            position: 'Proje Yöneticisi',
            roles: [
                {
                    id: 'r3',
                    name: 'Project Manager',
                    code: 'PM',
                    description: 'Proje yöneticisi rolü',
                    permissions: [],
                    isActive: true
                },
                {
                    id: 'r4',
                    name: 'Admin',
                    code: 'ADMIN',
                    description: 'Yönetici rolü',
                    permissions: [],
                    isActive: true
                }
            ],
            permissions: [
                {
                    id: 'p3',
                    name: 'Proje Yönetimi',
                    code: 'PROJECT_MANAGE',
                    module: 'Projects',
                    action: PermissionAction.UPDATE,
                    resource: 'projects',
                    description: 'Proje yönetimi yetkisi'
                }
            ],
            status: UserStatus.ACTIVE,
            isActive: true,
            lastLoginDate: new Date('2024-07-16T08:45:00'),
            createdAt: new Date('2023-12-01'),
            updatedAt: new Date('2024-07-16'),
            createdBy: 'System',
            employeeId: 'EMP-003',
            hireDate: new Date('2023-12-01'),
            birthDate: new Date('1985-03-10'),
            skills: [
                {
                    id: 's5',
                    name: 'Proje Yönetimi',
                    level: SkillLevel.EXPERT,
                    category: SkillCategory.MANAGEMENT,
                    yearsOfExperience: 8
                },
                {
                    id: 's6',
                    name: 'Scrum',
                    level: SkillLevel.ADVANCED,
                    category: SkillCategory.MANAGEMENT,
                    yearsOfExperience: 5
                }
            ],
            languages: [
                {
                    id: 'l5',
                    name: 'Türkçe',
                    level: LanguageLevel.NATIVE,
                    isNative: true
                },
                {
                    id: 'l6',
                    name: 'İngilizce',
                    level: LanguageLevel.ADVANCED,
                    isNative: false
                },
                {
                    id: 'l7',
                    name: 'Almanca',
                    level: LanguageLevel.INTERMEDIATE,
                    isNative: false
                }
            ],
            notes: 'Deneyimli proje yöneticisi ve takım lideri'
        },
        {
            id: '4',
            userCode: 'USR-2024-004',
            firstName: 'Zeynep',
            lastName: 'Arslan',
            fullName: 'Zeynep Arslan',
            email: '<EMAIL>',
            phone: '+90 ************',
            department: Department.QA,
            position: 'QA Engineer',
            roles: [
                {
                    id: 'r5',
                    name: 'QA Engineer',
                    code: 'QA',
                    description: 'Kalite güvence mühendisi rolü',
                    permissions: [],
                    isActive: true
                }
            ],
            permissions: [],
            status: UserStatus.SUSPENDED,
            isActive: false,
            lastLoginDate: new Date('2024-07-10T16:20:00'),
            createdAt: new Date('2024-03-01'),
            updatedAt: new Date('2024-07-12'),
            createdBy: 'Admin',
            employeeId: 'EMP-004',
            hireDate: new Date('2024-03-01'),
            skills: [
                {
                    id: 's7',
                    name: 'Test Automation',
                    level: SkillLevel.ADVANCED,
                    category: SkillCategory.PROGRAMMING,
                    yearsOfExperience: 3
                }
            ],
            languages: [
                {
                    id: 'l8',
                    name: 'Türkçe',
                    level: LanguageLevel.NATIVE,
                    isNative: true
                }
            ],
            notes: 'Geçici olarak askıya alındı'
        }
    ];

    getUsers(): Observable<User[]> {
        return of(this.mockUsers);
    }

    getUserById(id: string): Observable<User | undefined> {
        return of(this.mockUsers.find(user => user.id === id));
    }

    createUser(userData: UserFormData): Observable<User> {
        const newUser: User = {
            id: Date.now().toString(),
            userCode: userData.userCode,
            firstName: userData.firstName,
            lastName: userData.lastName,
            fullName: `${userData.firstName} ${userData.lastName}`,
            email: userData.email,
            phone: userData.phone || '',
            department: userData.department,
            position: userData.position,
            roles: [],
            permissions: [],
            status: userData.status,
            isActive: userData.status === UserStatus.ACTIVE,
            createdAt: new Date(),
            updatedAt: new Date(),
            createdBy: 'Current User',
            employeeId: userData.employeeId || '',
            hireDate: userData.hireDate,
            birthDate: userData.birthDate,
            skills: [],
            languages: [],
            notes: userData.notes || ''
        };
        this.mockUsers.push(newUser);
        return of(newUser);
    }

    updateUser(id: string, userData: UserFormData): Observable<User | undefined> {
        const index = this.mockUsers.findIndex(user => user.id === id);
        if (index === -1) return of(undefined);

        const existingUser = this.mockUsers[index];
        const updatedUser: User = {
            ...existingUser,
            userCode: userData.userCode,
            firstName: userData.firstName,
            lastName: userData.lastName,
            fullName: `${userData.firstName} ${userData.lastName}`,
            email: userData.email,
            phone: userData.phone || existingUser.phone,
            department: userData.department,
            position: userData.position,
            status: userData.status,
            isActive: userData.status === UserStatus.ACTIVE,
            employeeId: userData.employeeId || existingUser.employeeId,
            hireDate: userData.hireDate || existingUser.hireDate,
            birthDate: userData.birthDate || existingUser.birthDate,
            notes: userData.notes || existingUser.notes,
            updatedAt: new Date(),
            updatedBy: 'Current User'
        };
        this.mockUsers[index] = updatedUser;
        return of(updatedUser);
    }

    deleteUser(id: string): Observable<boolean> {
        const index = this.mockUsers.findIndex(user => user.id === id);
        if (index === -1) return of(false);
        
        this.mockUsers.splice(index, 1);
        return of(true);
    }

    suspendUser(id: string): Observable<boolean> {
        const user = this.mockUsers.find(u => u.id === id);
        if (!user) return of(false);
        
        user.status = UserStatus.SUSPENDED;
        user.isActive = false;
        user.updatedAt = new Date();
        return of(true);
    }

    activateUser(id: string): Observable<boolean> {
        const user = this.mockUsers.find(u => u.id === id);
        if (!user) return of(false);
        
        user.status = UserStatus.ACTIVE;
        user.isActive = true;
        user.updatedAt = new Date();
        return of(true);
    }
}

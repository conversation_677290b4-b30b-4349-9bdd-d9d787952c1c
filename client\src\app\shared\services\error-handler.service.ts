import { Injectable } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { MessageService } from 'primeng/api';
import { Observable, throwError } from 'rxjs';

export interface ApiError {
    success: boolean;
    message: string;
    errors: string[];
    timestamp: string;
}

@Injectable({
    providedIn: 'root'
})
export class ErrorHandlerService {
    constructor(private messageService: MessageService) {}

    handleError(error: HttpErrorResponse): Observable<never> {
        let errorMessage = 'Beklenmeyen bir hata oluştu';
        let errorDetails: string[] = [];

        if (error.error instanceof ErrorEvent) {
            // Client-side error
            errorMessage = `Bağlantı hatası: ${error.error.message}`;
        } else {
            // Server-side error
            if (error.error && typeof error.error === 'object') {
                const apiError = error.error as ApiError;
                if (apiError.message) {
                    errorMessage = apiError.message;
                }
                if (apiError.errors && apiError.errors.length > 0) {
                    errorDetails = apiError.errors;
                }
            } else {
                switch (error.status) {
                    case 400:
                        errorMessage = 'Geçersiz istek';
                        break;
                    case 401:
                        errorMessage = 'Oturum süreniz dolmuş. Lütfen tekrar giriş yapın.';
                        break;
                    case 403:
                        errorMessage = 'Bu işlem için yetkiniz bulunmuyor';
                        break;
                    case 404:
                        errorMessage = 'İstenen kaynak bulunamadı';
                        break;
                    case 500:
                        errorMessage = 'Sunucu hatası oluştu';
                        break;
                    case 0:
                        errorMessage = 'Sunucuya bağlanılamıyor. İnternet bağlantınızı kontrol edin.';
                        break;
                    default:
                        errorMessage = `Hata kodu: ${error.status}`;
                }
            }
        }

        // Show error message
        this.showError(errorMessage, errorDetails);

        // Log error
        console.error('HTTP Error:', error);

        return throwError(() => error);
    }

    showError(message: string, details: string[] = []): void {
        this.messageService.add({
            severity: 'error',
            summary: 'Hata',
            detail: message,
            life: 5000
        });

        // Show details if available
        if (details.length > 0) {
            details.forEach(detail => {
                this.messageService.add({
                    severity: 'error',
                    summary: 'Detay',
                    detail: detail,
                    life: 5000
                });
            });
        }
    }

    showSuccess(message: string): void {
        this.messageService.add({
            severity: 'success',
            summary: 'Başarılı',
            detail: message,
            life: 3000
        });
    }

    showWarning(message: string): void {
        this.messageService.add({
            severity: 'warn',
            summary: 'Uyarı',
            detail: message,
            life: 4000
        });
    }

    showInfo(message: string): void {
        this.messageService.add({
            severity: 'info',
            summary: 'Bilgi',
            detail: message,
            life: 3000
        });
    }
}

import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { Textarea } from 'primeng/inputtextarea';
import { Select } from 'primeng/select';

import { ToastModule } from 'primeng/toast';
import { MessageService } from 'primeng/api';
import { CustomerStatus, CustomerType, Industry } from '../../models/customer.model';
import { TooltipModule } from 'primeng/tooltip';

@Component({
    selector: 'app-customer-form',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        ButtonModule,
        InputTextModule,
        Textarea,
        Select,
        ToastModule,
        TooltipModule
    ],
    providers: [MessageService],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <h5>{{ isEditMode ? 'Müşteri Düzenle' : 'Yeni Müşteri' }}</h5>
                        <p-button 
                            label="Geri" 
                            icon="pi pi-arrow-left" 
                            class="p-button-secondary"
                            (onClick)="goBack()">
                        </p-button>
                    </div>

                    <form [formGroup]="customerForm" (ngSubmit)="onSubmit()">
                        <div class="grid">
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="customerCode" class="font-medium">Müşteri Kodu *</label>
                                    <input 
                                        id="customerCode"
                                        type="text" 
                                        pInputText 
                                        formControlName="customerCode"
                                        [readonly]="isEditMode"
                                        class="w-full"
                                        placeholder="Otomatik oluşturulacak" />
                                    <small class="text-500">Benzersiz müşteri kodu otomatik oluşturulur</small>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="companyName" class="font-medium">Şirket Adı *</label>
                                    <input 
                                        id="companyName"
                                        type="text" 
                                        pInputText 
                                        formControlName="companyName"
                                        class="w-full"
                                        placeholder="Şirket adını girin" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="contactPerson" class="font-medium">İletişim Kişisi *</label>
                                    <input 
                                        id="contactPerson"
                                        type="text" 
                                        pInputText 
                                        formControlName="contactPerson"
                                        class="w-full"
                                        placeholder="İletişim kişisi adı" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="email" class="font-medium">E-posta *</label>
                                    <input 
                                        id="email"
                                        type="email" 
                                        pInputText 
                                        formControlName="email"
                                        class="w-full"
                                        placeholder="<EMAIL>" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="phone" class="font-medium">Telefon</label>
                                    <input 
                                        id="phone"
                                        type="tel" 
                                        pInputText 
                                        formControlName="phone"
                                        class="w-full"
                                        placeholder="+90 ************" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="website" class="font-medium">Web Sitesi</label>
                                    <input 
                                        id="website"
                                        type="url" 
                                        pInputText 
                                        formControlName="website"
                                        class="w-full"
                                        placeholder="https://www.sirket.com" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="industry" class="font-medium">Sektör *</label>
                                    <p-select 
                                        id="industry"
                                        formControlName="industry"
                                        [options]="industryOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Sektör seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="customerType" class="font-medium">Müşteri Türü *</label>
                                    <p-select 
                                        id="customerType"
                                        formControlName="customerType"
                                        [options]="customerTypeOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Müşteri türü seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="status" class="font-medium">Durum *</label>
                                    <p-select 
                                        id="status"
                                        formControlName="status"
                                        [options]="statusOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Durum seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="city" class="font-medium">Şehir</label>
                                    <input 
                                        id="city"
                                        type="text" 
                                        pInputText 
                                        formControlName="city"
                                        class="w-full"
                                        placeholder="Şehir" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="country" class="font-medium">Ülke</label>
                                    <input 
                                        id="country"
                                        type="text" 
                                        pInputText 
                                        formControlName="country"
                                        class="w-full"
                                        placeholder="Ülke" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="taxNumber" class="font-medium">Vergi Numarası</label>
                                    <input 
                                        id="taxNumber"
                                        type="text" 
                                        pInputText 
                                        formControlName="taxNumber"
                                        class="w-full"
                                        placeholder="Vergi numarası" />
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field">
                                    <label for="address" class="font-medium">Adres</label>
                                    <textarea
                                        id="address"
                                        pTextarea
                                        formControlName="address"
                                        rows="3"
                                        class="w-full"
                                        placeholder="Tam adres..."
                                        autoResize>
                                    </textarea>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field">
                                    <label for="notes" class="font-medium">Notlar</label>
                                    <textarea
                                        id="notes"
                                        pTextarea
                                        formControlName="notes"
                                        rows="3"
                                        class="w-full"
                                        placeholder="Ek notlar..."
                                        autoResize>
                                    </textarea>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-content-end gap-2 mt-4">
                            <p-button 
                                icon="pi pi-times"
                                severity="secondary"
                                rounded
                                outlined
                                type="button"
                                pTooltip="İptal"
                                (onClick)="goBack()">
                            </p-button>
                            <p-button 
                                icon="pi pi-check"
                                severity="success"
                                rounded
                                outlined
                                type="submit"
                                [pTooltip]="isEditMode ? 'Güncelle' : 'Kaydet'"
                                [disabled]="!formReady || customerForm.invalid">
                            </p-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <p-toast></p-toast>
    `
})
export class CustomerFormComponent implements OnInit {
    customerForm!: FormGroup;
    isEditMode = false;
    customerId: string = '';

    industryOptions = [
        { label: 'Teknoloji', value: Industry.TECHNOLOGY },
        { label: 'Finans', value: Industry.FINANCE },
        { label: 'Sağlık', value: Industry.HEALTHCARE },
        { label: 'Eğitim', value: Industry.EDUCATION },
        { label: 'Perakende', value: Industry.RETAIL },
        { label: 'İmalat', value: Industry.MANUFACTURING },
        { label: 'Lojistik', value: Industry.LOGISTICS },
        { label: 'Kamu', value: Industry.GOVERNMENT },
        { label: 'Diğer', value: Industry.OTHER }
    ];

    customerTypeOptions = [
        { label: 'Kurumsal', value: CustomerType.ENTERPRISE },
        { label: 'KOBİ', value: CustomerType.SME },
        { label: 'Startup', value: CustomerType.STARTUP },
        { label: 'Kamu', value: CustomerType.GOVERNMENT },
        { label: 'STK', value: CustomerType.NGO }
    ];

    statusOptions = [
        { label: 'Aktif', value: CustomerStatus.ACTIVE },
        { label: 'Potansiyel', value: CustomerStatus.PROSPECT },
        { label: 'Pasif', value: CustomerStatus.INACTIVE },
        { label: 'Askıya Alınmış', value: CustomerStatus.SUSPENDED },
        { label: 'Sonlandırılmış', value: CustomerStatus.TERMINATED }
    ];

    constructor(
        private fb: FormBuilder,
        private route: ActivatedRoute,
        private router: Router,
        private messageService: MessageService
    ) {}

    formReady = false;

    ngOnInit() {
        this.initForm();
        setTimeout(() => {
        this.formReady = true;
         });
        this.checkEditMode();
    }

    initForm() {
        this.customerForm = this.fb.group({
            customerCode: ['', Validators.required],
            companyName: ['', Validators.required],
            contactPerson: ['', Validators.required],
            email: ['', [Validators.required, Validators.email]],
            phone: [''],
            website: [''],
            industry: [Industry.TECHNOLOGY, Validators.required],
            customerType: [CustomerType.ENTERPRISE, Validators.required],
            status: [CustomerStatus.PROSPECT, Validators.required],
            city: [''],
            country: ['Türkiye'],
            taxNumber: [''],
            address: [''],
            notes: ['']
        });

        if (!this.isEditMode) {
            this.generateCustomerCode();
        }
    }

    checkEditMode() {
        this.customerId = this.route.snapshot.params['id'];
        this.isEditMode = !!this.customerId && this.router.url.includes('edit');
        
        if (this.isEditMode) {
            this.loadCustomer();
        }
    }

    generateCustomerCode() {
        const year = new Date().getFullYear();
        const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        const customerCode = `CUS-${year}-${randomNum}`;
        this.customerForm.patchValue({ customerCode });
    }

    loadCustomer() {
        // Dummy data for edit mode
        const dummyCustomer = {
            customerCode: 'CUS-2024-001',
            companyName: 'ABC Teknoloji A.Ş.',
            contactPerson: 'Ahmet Yılmaz',
            email: '<EMAIL>',
            phone: '+90 ************',
            website: 'https://www.abc.com',
            industry: Industry.TECHNOLOGY,
            customerType: CustomerType.ENTERPRISE,
            status: CustomerStatus.ACTIVE,
            city: 'İstanbul',
            country: 'Türkiye',
            taxNumber: '1234567890',
            address: 'Maslak Mahallesi, Teknoloji Caddesi No:15',
            notes: 'Stratejik müşteri, uzun vadeli işbirliği'
        };

        this.customerForm.patchValue(dummyCustomer);
    }

    onSubmit() {
        if (this.customerForm.valid) {
            this.messageService.add({
                severity: 'success',
                summary: 'Başarılı',
                detail: this.isEditMode ? 'Müşteri başarıyla güncellendi' : 'Müşteri başarıyla oluşturuldu'
            });

            setTimeout(() => {
                this.router.navigate(['/pages/customers']);
            }, 1500);
        }
    }

    goBack() {
        this.router.navigate(['/pages/customers']);
    }
    
}

﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TRtek_EYS.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class ExtendTechnologyModel : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "Category",
                table: "Technologies",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "Technologies",
                type: "character varying(1000)",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Documentation",
                table: "Technologies",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "EndOfLifeDate",
                table: "Technologies",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "Technologies",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeprecated",
                table: "Technologies",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsRecommended",
                table: "Technologies",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastUpdateDate",
                table: "Technologies",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "LicenseType",
                table: "Technologies",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Name",
                table: "Technologies",
                type: "character varying(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Notes",
                table: "Technologies",
                type: "character varying(2000)",
                maxLength: 2000,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "PopularityScore",
                table: "Technologies",
                type: "numeric(5,2)",
                precision: 5,
                scale: 2,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<int>(
                name: "ProjectCount",
                table: "Technologies",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<DateTime>(
                name: "ReleaseDate",
                table: "Technologies",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Repository",
                table: "Technologies",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Tags",
                table: "Technologies",
                type: "character varying(2000)",
                maxLength: 2000,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "TeamCount",
                table: "Technologies",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "TechnologyCode",
                table: "Technologies",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Type",
                table: "Technologies",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Vendor",
                table: "Technologies",
                type: "character varying(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Website",
                table: "Technologies",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Category",
                table: "Technologies");

            migrationBuilder.DropColumn(
                name: "Description",
                table: "Technologies");

            migrationBuilder.DropColumn(
                name: "Documentation",
                table: "Technologies");

            migrationBuilder.DropColumn(
                name: "EndOfLifeDate",
                table: "Technologies");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "Technologies");

            migrationBuilder.DropColumn(
                name: "IsDeprecated",
                table: "Technologies");

            migrationBuilder.DropColumn(
                name: "IsRecommended",
                table: "Technologies");

            migrationBuilder.DropColumn(
                name: "LastUpdateDate",
                table: "Technologies");

            migrationBuilder.DropColumn(
                name: "LicenseType",
                table: "Technologies");

            migrationBuilder.DropColumn(
                name: "Name",
                table: "Technologies");

            migrationBuilder.DropColumn(
                name: "Notes",
                table: "Technologies");

            migrationBuilder.DropColumn(
                name: "PopularityScore",
                table: "Technologies");

            migrationBuilder.DropColumn(
                name: "ProjectCount",
                table: "Technologies");

            migrationBuilder.DropColumn(
                name: "ReleaseDate",
                table: "Technologies");

            migrationBuilder.DropColumn(
                name: "Repository",
                table: "Technologies");

            migrationBuilder.DropColumn(
                name: "Tags",
                table: "Technologies");

            migrationBuilder.DropColumn(
                name: "TeamCount",
                table: "Technologies");

            migrationBuilder.DropColumn(
                name: "TechnologyCode",
                table: "Technologies");

            migrationBuilder.DropColumn(
                name: "Type",
                table: "Technologies");

            migrationBuilder.DropColumn(
                name: "Vendor",
                table: "Technologies");

            migrationBuilder.DropColumn(
                name: "Website",
                table: "Technologies");
        }
    }
}

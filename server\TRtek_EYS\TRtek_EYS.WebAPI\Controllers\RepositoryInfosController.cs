﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using TRtek_EYS.Application.Features.RepositoryInfos.Commands.Add;
using TRtek_EYS.Application.Features.RepositoryInfos.Commands.Delete;
using TRtek_EYS.Application.Features.RepositoryInfos.Commands.Update;
using TRtek_EYS.Application.Features.RepositoryInfos.Queries.GetAll;
using TRtek_EYS.Application.Features.RepositoryInfos.Queries.GetById;

namespace TRtek_EYS.WebAPI.Controllers;

[Route("api/[controller]")]
[ApiController]
public class RepositoryInfosController : ApiController
{
    public RepositoryInfosController(IMediator mediator) : base(mediator) { }

    [HttpGet]
    public async Task<IActionResult> GetAll()
    {
        var result = await Mediator.Send(new GetAllRepositoryInfosQuery());
        return Ok(result);
    }

    [HttpGet("{id:guid}")]
    public async Task<IActionResult> GetById(Guid id)
    {
        var result = await Mediator.Send(new GetRepositoryInfoByIdQuery(id));
        return result is null ? NotFound() : Ok(result); 
    }

    [HttpPost]
    public async Task<IActionResult> Add([FromBody] AddRepositoryInfoCommand command)
    {
        var id = await Mediator.Send(command);
        return CreatedAtAction(nameof(GetById), new { id }, null);
    }

    [HttpPut("{id:guid}")]
    public async Task<IActionResult> Update(Guid id, [FromBody] UpdateRepositoryInfoCommand command)
    {
        if (id != command.Id)
            return BadRequest("URL'deki ID uyuşmuyor.");

        await Mediator.Send(command);
        return NoContent();
    }

    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> Delete(Guid id)
    {
        await Mediator.Send(new DeleteRepositoryInfoCommand { Id = id });
        return NoContent();
    }
}

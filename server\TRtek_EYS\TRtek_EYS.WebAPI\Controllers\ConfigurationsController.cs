﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using TRtek_EYS.Application.Features.Configurations.Commands.Add;
using TRtek_EYS.Application.Features.Configurations.Commands.Delete;
using TRtek_EYS.Application.Features.Configurations.Commands.Update;
using TRtek_EYS.Application.Features.Configurations.Queries.GetAll;
using TRtek_EYS.Application.Features.Configurations.Queries.GetById;

namespace TRtek_EYS.WebAPI.Controllers;

[Route("api/[controller]")]
[ApiController]
public class ConfigurationsController : ApiController
{
    public ConfigurationsController(IMediator mediator) : base(mediator) { }

    [HttpGet]
    public async Task<IActionResult> GetAll()
    {
        var result = await Mediator.Send(new GetAllConfigurationsQuery());
        return Ok(result); // List<ConfigurationGetAllDto>
    }

    [HttpGet("{id:guid}")]
    public async Task<IActionResult> GetById(Guid id)
    {
        var result = await Mediator.Send(new GetConfigurationByIdQuery(id));
        return result == null ? NotFound() : Ok(result); // ConfigurationGetByIdDto
    }

    [HttpPost]
    public async Task<IActionResult> Add([FromBody] AddConfigurationCommand command)
    {
        var id = await Mediator.Send(command);
        return CreatedAtAction(nameof(GetById), new { id }, null);
    }

    [HttpPut("{id:guid}")]
    public async Task<IActionResult> Update(Guid id, [FromBody] UpdateConfigurationCommand command)
    {
        if (id != command.Id)
            return BadRequest("URL ve body ID'si uyuşmuyor.");

        await Mediator.Send(command);
        return NoContent();
    }

    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> Delete(Guid id)
    {
        await Mediator.Send(new DeleteConfigurationCommand { Id = id });
        return NoContent();
    }
}

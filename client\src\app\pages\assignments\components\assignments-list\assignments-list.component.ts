import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Table, TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { InputIconModule } from 'primeng/inputicon';
import { IconFieldModule } from 'primeng/iconfield';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToastModule } from 'primeng/toast';
import { ProgressBarModule } from 'primeng/progressbar';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ProjectAssignment, AssignmentStatus, ProjectRole, AssignmentPriority } from '../../models/assignment.model';

@Component({
    selector: 'app-assignments-list',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        TableModule,
        ButtonModule,
        InputTextModule,
        InputIconModule,
        IconFieldModule,
        TagModule,
        TooltipModule,
        ConfirmDialogModule,
        ToastModule,
        ProgressBarModule
    ],
    providers: [ConfirmationService, MessageService],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <h5>Proje Atamaları</h5>
                        <p-button 
                            label="Yeni Atama" 
                            icon="pi pi-plus" 
                            (onClick)="createAssignment()"
                            severity="primary"
                            size="small">
                        </p-button>
                    </div>

                    <p-table 
                        #dt 
                        [value]="assignments" 
                        [rows]="10" 
                        [paginator]="true"
                        [globalFilterFields]="['assignmentCode', 'userName', 'projectName', 'teamName', 'role']"
                        [tableStyle]="{ 'min-width': '75rem' }"
                        [rowHover]="true"
                        dataKey="id"
                        currentPageReportTemplate="{first} - {last} / {totalRecords} atama"
                        [showCurrentPageReport]="true">
                        
                        <ng-template pTemplate="caption">
                            <div class="flex align-items-center justify-content-between">
                                <h5 class="m-0">Proje Atamaları</h5>
                                <p-iconfield iconPosition="left">
                                    <p-inputicon>
                                        <i class="pi pi-search"></i>
                                    </p-inputicon>
                                    <input 
                                        pInputText 
                                        type="text" 
                                        (input)="dt.filterGlobal($any($event.target).value, 'contains')" 
                                        placeholder="Atama ara..." />
                                </p-iconfield>
                            </div>
                        </ng-template>

                        <ng-template pTemplate="header">
                            <tr>
                                <th pSortableColumn="assignmentCode">
                                    Atama Kodu <p-sortIcon field="assignmentCode"></p-sortIcon>
                                </th>
                                <th pSortableColumn="userName">
                                    Kullanıcı <p-sortIcon field="userName"></p-sortIcon>
                                </th>
                                <th pSortableColumn="projectName">
                                    Proje <p-sortIcon field="projectName"></p-sortIcon>
                                </th>
                                <th pSortableColumn="teamName">
                                    Takım <p-sortIcon field="teamName"></p-sortIcon>
                                </th>
                                <th pSortableColumn="role">
                                    Rol <p-sortIcon field="role"></p-sortIcon>
                                </th>
                                <th pSortableColumn="workloadPercentage">
                                    İş Yükü <p-sortIcon field="workloadPercentage"></p-sortIcon>
                                </th>
                                <th pSortableColumn="priority">
                                    Öncelik <p-sortIcon field="priority"></p-sortIcon>
                                </th>
                                <th pSortableColumn="status">
                                    Durum <p-sortIcon field="status"></p-sortIcon>
                                </th>
                                <th pSortableColumn="startDate">
                                    Başlangıç <p-sortIcon field="startDate"></p-sortIcon>
                                </th>
                                <th>İşlemler</th>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="body" let-assignment>
                            <tr>
                                <td>
                                    <span class="font-medium">{{ assignment.assignmentCode }}</span>
                                </td>
                                <td>
                                    <div>
                                        <span class="font-medium">{{ assignment.userName }}</span>
                                        <div class="text-sm text-500 mt-1">{{ assignment.userEmail }}</div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <span class="font-medium">{{ assignment.projectName }}</span>
                                        <div class="text-sm text-500 mt-1">{{ assignment.projectCode }}</div>
                                    </div>
                                </td>
                                <td>
                                    <span>{{ assignment.teamName }}</span>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="assignment.role" 
                                        [severity]="getRoleSeverity(assignment.role)">
                                    </p-tag>
                                </td>
                                <td>
                                    <div class="flex align-items-center">
                                        <div class="w-full bg-gray-200 border-round-lg h-1rem mr-2">
                                            <div 
                                                class="bg-blue-500 h-full border-round-lg" 
                                                [style.width.%]="assignment.workloadPercentage">
                                            </div>
                                        </div>
                                        <span class="text-sm font-medium">{{ assignment.workloadPercentage }}%</span>
                                    </div>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="assignment.priority" 
                                        [severity]="getPrioritySeverity(assignment.priority)">
                                    </p-tag>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="assignment.status" 
                                        [severity]="getStatusSeverity(assignment.status)">
                                    </p-tag>
                                </td>
                                <td>
                                    <span>{{ assignment.startDate | date:'dd/MM/yyyy' }}</span>
                                </td>
                                <td>
                                    <div class="flex gap-2">
                                        <p-button 
                                            icon="pi pi-eye" 
                                            class="p-button-rounded p-button-text p-button-info"
                                            pTooltip="Detay"
                                            (onClick)="viewAssignment(assignment.id)">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-pencil" 
                                            class="p-button-rounded p-button-text p-button-warning"
                                            pTooltip="Düzenle"
                                            (onClick)="editAssignment(assignment.id)">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-pause" 
                                            class="p-button-rounded p-button-text p-button-secondary"
                                            pTooltip="Askıya Al"
                                            (onClick)="suspendAssignment(assignment)"
                                            *ngIf="assignment.status === 'Aktif'">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-trash" 
                                            class="p-button-rounded p-button-text p-button-danger"
                                            pTooltip="Sil"
                                            (onClick)="deleteAssignment(assignment)">
                                        </p-button>
                                    </div>
                                </td>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="emptymessage">
                            <tr>
                                <td colspan="10" class="text-center py-4">
                                    <i class="pi pi-info-circle text-4xl text-500 mb-3"></i>
                                    <div class="text-500 font-medium">Henüz proje ataması bulunmuyor</div>
                                    <div class="text-500">Yeni atama eklemek için "Yeni Atama" butonunu kullanın</div>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                </div>
            </div>
        </div>

        <p-confirmDialog></p-confirmDialog>
        <p-toast></p-toast>
    `
})
export class AssignmentsListComponent implements OnInit {
    @ViewChild('dt') table!: Table;
    
    assignments: ProjectAssignment[] = [];

    constructor(
        private router: Router,
        private confirmationService: ConfirmationService,
        private messageService: MessageService
    ) {}

    ngOnInit() {
        this.loadAssignments();
    }

    loadAssignments() {
        this.assignments = [
            {
                id: '1',
                assignmentCode: 'ASG-2025-001',
                projectId: '1',
                projectName: 'E-Ticaret Platformu',
                projectCode: 'PRJ-2025-001',
                teamId: '1',
                teamName: 'Frontend Takımı',
                userId: '1',
                userName: 'Ahmet Yılmaz',
                userEmail: '<EMAIL>',
                role: ProjectRole.FRONTEND_DEVELOPER,
                assignmentType: 'Tam Zamanlı' as any,
                startDate: new Date('2025-01-15'),
                endDate: new Date('2025-12-31'),
                workloadPercentage: 80,
                status: AssignmentStatus.ACTIVE,
                priority: AssignmentPriority.HIGH,
                responsibilities: ['UI Geliştirme', 'Component Tasarımı'],
                skills: [],
                assignedBy: 'Proje Yöneticisi',
                assignedDate: new Date('2025-01-10'),
                isActive: true
            },
            {
                id: '2',
                assignmentCode: 'ASG-2025-002',
                projectId: '2',
                projectName: 'CRM Sistemi',
                projectCode: 'PRJ-2025-002',
                teamId: '2',
                teamName: 'Backend Takımı',
                userId: '2',
                userName: 'Ayşe Demir',
                userEmail: '<EMAIL>',
                role: ProjectRole.BACKEND_DEVELOPER,
                assignmentType: 'Tam Zamanlı' as any,
                startDate: new Date('2025-02-01'),
                workloadPercentage: 100,
                status: AssignmentStatus.ACTIVE,
                priority: AssignmentPriority.MEDIUM,
                responsibilities: ['API Geliştirme', 'Veritabanı Tasarımı'],
                skills: [],
                assignedBy: 'Teknik Lider',
                assignedDate: new Date('2025-01-25'),
                isActive: true
            },
            {
                id: '3',
                assignmentCode: 'ASG-2025-003',
                projectId: '3',
                projectName: 'Mobil Bankacılık',
                projectCode: 'PRJ-2025-003',
                teamId: '3',
                teamName: 'Mobile Takımı',
                userId: '3',
                userName: 'Mehmet Özkan',
                userEmail: '<EMAIL>',
                role: ProjectRole.PROJECT_MANAGER,
                assignmentType: 'Tam Zamanlı' as any,
                startDate: new Date('2023-06-01'),
                endDate: new Date('2025-03-31'),
                workloadPercentage: 90,
                status: AssignmentStatus.COMPLETED,
                priority: AssignmentPriority.CRITICAL,
                responsibilities: ['Proje Yönetimi', 'Takım Koordinasyonu'],
                skills: [],
                assignedBy: 'Departman Müdürü',
                assignedDate: new Date('2023-05-20'),
                isActive: false
            }
        ];
    }

    createAssignment() {
        this.router.navigate(['/pages/assignments/new']);
    }

    viewAssignment(id: string) {
        this.router.navigate(['/pages/assignments', id]);
    }

    editAssignment(id: string) {
        this.router.navigate(['/pages/assignments', id, 'edit']);
    }

    suspendAssignment(assignment: ProjectAssignment) {
        this.confirmationService.confirm({
            message: `"${assignment.userName}" kullanıcısının "${assignment.projectName}" projesindeki atamasını askıya almak istediğinizden emin misiniz?`,
            header: 'Atama Askıya Alma Onayı',
            icon: 'pi pi-pause',
            acceptLabel: 'Evet',
            rejectLabel: 'Hayır',
            accept: () => {
                assignment.status = AssignmentStatus.SUSPENDED;
                this.messageService.add({
                    severity: 'info',
                    summary: 'Başarılı',
                    detail: 'Atama askıya alındı'
                });
            }
        });
    }

    deleteAssignment(assignment: ProjectAssignment) {
        this.confirmationService.confirm({
            message: `"${assignment.assignmentCode}" atamasını silmek istediğinizden emin misiniz?`,
            header: 'Atama Silme Onayı',
            icon: 'pi pi-exclamation-triangle',
            acceptLabel: 'Evet',
            rejectLabel: 'Hayır',
            accept: () => {
                this.assignments = this.assignments.filter(a => a.id !== assignment.id);
                this.messageService.add({
                    severity: 'success',
                    summary: 'Başarılı',
                    detail: 'Atama başarıyla silindi'
                });
            }
        });
    }

    getStatusSeverity(status: AssignmentStatus): string {
        switch (status) {
            case AssignmentStatus.ACTIVE:
                return 'success';
            case AssignmentStatus.PENDING:
                return 'warning';
            case AssignmentStatus.COMPLETED:
                return 'info';
            case AssignmentStatus.SUSPENDED:
                return 'secondary';
            case AssignmentStatus.CANCELLED:
                return 'danger';
            case AssignmentStatus.ON_HOLD:
                return 'warning';
            default:
                return 'info';
        }
    }

    getRoleSeverity(role: ProjectRole): string {
        switch (role) {
            case ProjectRole.PROJECT_MANAGER:
                return 'danger';
            case ProjectRole.TECH_LEAD:
                return 'warning';
            case ProjectRole.SENIOR_DEVELOPER:
                return 'success';
            case ProjectRole.DEVELOPER:
                return 'info';
            case ProjectRole.JUNIOR_DEVELOPER:
                return 'secondary';
            default:
                return 'info';
        }
    }

    getPrioritySeverity(priority: AssignmentPriority): string {
        switch (priority) {
            case AssignmentPriority.CRITICAL:
                return 'danger';
            case AssignmentPriority.HIGH:
                return 'warning';
            case AssignmentPriority.MEDIUM:
                return 'info';
            case AssignmentPriority.LOW:
                return 'secondary';
            default:
                return 'info';
        }
    }
}

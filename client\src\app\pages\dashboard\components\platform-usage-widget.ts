import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';
import { ChartModule } from 'primeng/chart';
import { ButtonModule } from 'primeng/button';
import { SkeletonModule } from 'primeng/skeleton';
import { ProgressBar } from 'primeng/progressbar';
import { PlatformService } from '../../platforms/services/platform.service';

@Component({
    standalone: true,
    selector: 'app-platform-usage-widget',
    imports: [CommonModule, ChartModule, ButtonModule, SkeletonModule, ProgressBar],
    template: `<div class="card">
        <div class="flex justify-between align-items-center mb-5">
            <div class="font-semibold text-xl">Platform Kullanımı</div>
            <p-button 
                label="Detaylar" 
                size="small" 
                [outlined]="true"
                routerLink="/pages/platforms">
            </p-button>
        </div>
        
        <div *ngIf="isLoading" class="space-y-4">
            <div *ngFor="let item of [1,2,3,4,5]" class="flex align-items-center gap-3">
                <p-skeleton shape="circle" size="2.5rem"></p-skeleton>
                <div class="flex-1">
                    <p-skeleton width="70%" height="1rem" class="mb-2"></p-skeleton>
                    <p-skeleton width="100%" height="0.5rem"></p-skeleton>
                </div>
                <p-skeleton width="3rem" height="1rem"></p-skeleton>
            </div>
        </div>
        
        <div *ngIf="!isLoading && platforms.length > 0" class="space-y-4">
            <div *ngFor="let platform of platforms" 
                 class="flex align-items-center gap-3 p-3 border-round hover:bg-gray-50 transition-all transition-duration-300">
                <div class="flex align-items-center justify-content-center bg-blue-100 border-round" 
                     style="width: 2.5rem; height: 2.5rem;">
                    <i class="pi pi-server text-blue-500"></i>
                </div>
                <div class="flex-1">
                    <div class="font-medium text-surface-900">{{ platform.name }}</div>
                    <div class="flex align-items-center gap-2 mt-1">
                        <p-progressBar 
                            [value]="platform.percentage" 
                            [style]="{'height': '6px', 'width': '120px'}"
                            [showValue]="false">
                        </p-progressBar>
                        <span class="text-sm text-muted-color">{{ platform.projectCount }} proje</span>
                    </div>
                </div>
                <div class="text-right">
                    <div class="font-bold text-primary text-lg">{{ platform.percentage }}%</div>
                    <div class="text-xs text-muted-color">kullanım</div>
                </div>
            </div>
        </div>
        
        <div *ngIf="!isLoading && platforms.length === 0" class="text-center py-8">
            <i class="pi pi-info-circle text-4xl text-muted-color mb-4"></i>
            <div class="text-muted-color font-medium">Platform verisi bulunamadı</div>
        </div>
    </div>`,
    styles: [`
        .space-y-4 > * + * {
            margin-top: 1rem;
        }
        .hover\\:bg-gray-50:hover {
            background-color: #f9fafb;
        }
        .transition-all {
            transition: all 0.3s ease;
        }
        .transition-duration-300 {
            transition-duration: 300ms;
        }
    `]
})
export class PlatformUsageWidget implements OnInit, OnDestroy {
    private destroy$ = new Subject<void>();
    
    platforms: any[] = [];
    isLoading = true;

    constructor(private platformService: PlatformService) {}

    ngOnInit() {
        this.loadPlatforms();
    }

    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
    }

    loadPlatforms() {
        this.isLoading = true;
        
        this.platformService.getPlatforms()
            .pipe(takeUntil(this.destroy$))
            .subscribe(platforms => {
                const activePlatforms = platforms.filter(p => p.isActive && p.projectCount > 0);
                const totalProjects = activePlatforms.reduce((sum, p) => sum + p.projectCount, 0);
                
                this.platforms = activePlatforms
                    .map(p => ({
                        name: p.name,
                        projectCount: p.projectCount,
                        percentage: totalProjects > 0 ? Math.round((p.projectCount / totalProjects) * 100) : 0
                    }))
                    .sort((a, b) => b.projectCount - a.projectCount)
                    .slice(0, 5);
                
                this.isLoading = false;
            });
    }
}

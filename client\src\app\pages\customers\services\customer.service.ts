import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Customer, CustomerStatus, CustomerType, Industry } from '../models/customer.model';

@Injectable({
    providedIn: 'root'
})
export class CustomerService {

    private customers: Customer[] = [
        {
            id: '1',
            customerCode: 'CUS-2025-001',
            companyName: 'TechCorp A.Ş.',
            contactPerson: 'Ahmet Yılmaz',
            email: '<EMAIL>',
            phone: '+90 212 123 45 67',
            address: 'Teknoloji Cad. No:123, Beşiktaş',
            city: 'İstanbul',
            country: 'Türkiye',
            website: 'www.techcorp.com',
            taxNumber: '1234567890',
            industry: Industry.TECHNOLOGY,
            customerType: CustomerType.CORPORATE,
            status: CustomerStatus.ACTIVE,
            registrationDate: new Date('2024-01-15'),
            lastContactDate: new Date('2025-01-10'),
            totalProjects: 5,
            activeProjects: 2,
            totalRevenue: 250000,
            notes: '<PERSON><PERSON><PERSON><PERSON> mü<PERSON>teri, ö<PERSON><PERSON>li destek',
            contracts: [],
            projects: []
        },
        {
            id: '2',
            customerCode: 'CUS-2025-002',
            companyName: 'HealthCare Ltd.',
            contactPerson: 'Fatma Demir',
            email: '<EMAIL>',
            phone: '+90 216 234 56 78',
            address: 'Sağlık Sok. No:45, Kadıköy',
            city: 'İstanbul',
            country: 'Türkiye',
            website: 'www.healthcare.com',
            taxNumber: '**********',
            industry: Industry.HEALTHCARE,
            customerType: CustomerType.CORPORATE,
            status: CustomerStatus.ACTIVE,
            registrationDate: new Date('2024-03-01'),
            lastContactDate: new Date('2025-01-08'),
            totalProjects: 3,
            activeProjects: 1,
            totalRevenue: 180000,
            notes: 'Sağlık sektörü uzmanı',
            contracts: [],
            projects: []
        },
        {
            id: '3',
            customerCode: 'CUS-2025-003',
            companyName: 'DataViz Solutions',
            contactPerson: 'Mehmet Kaya',
            email: '<EMAIL>',
            phone: '+90 212 345 67 89',
            address: 'Veri Cad. No:67, Şişli',
            city: 'İstanbul',
            country: 'Türkiye',
            website: 'www.dataviz.com',
            taxNumber: '**********',
            industry: Industry.FINANCE,
            customerType: CustomerType.CORPORATE,
            status: CustomerStatus.POTENTIAL,
            registrationDate: new Date('2024-06-01'),
            lastContactDate: new Date('2025-01-05'),
            totalProjects: 2,
            activeProjects: 1,
            totalRevenue: 120000,
            notes: 'Potansiyel büyük müşteri',
            contracts: [],
            projects: []
        },
        {
            id: '4',
            customerCode: 'CUS-2025-004',
            companyName: 'Logistics Co',
            contactPerson: 'Ayşe Özkan',
            email: '<EMAIL>',
            phone: '+90 212 456 78 90',
            address: 'Lojistik Bulvarı No:89, Bakırköy',
            city: 'İstanbul',
            country: 'Türkiye',
            website: 'www.logistics.com',
            taxNumber: '4567890123',
            industry: Industry.LOGISTICS,
            customerType: CustomerType.CORPORATE,
            status: CustomerStatus.ACTIVE,
            registrationDate: new Date('2024-09-01'),
            lastContactDate: new Date('2025-01-03'),
            totalProjects: 4,
            activeProjects: 2,
            totalRevenue: 320000,
            notes: 'Lojistik sektörü lideri',
            contracts: [],
            projects: []
        },
        {
            id: '5',
            customerCode: 'CUS-2025-005',
            companyName: 'FinTech Innovations',
            contactPerson: 'Can Arslan',
            email: '<EMAIL>',
            phone: '+90 212 567 89 01',
            address: 'Finans Merkezi No:12, Levent',
            city: 'İstanbul',
            country: 'Türkiye',
            website: 'www.fintech.com',
            taxNumber: '5678901234',
            industry: Industry.FINANCE,
            customerType: CustomerType.CORPORATE,
            status: CustomerStatus.PENDING,
            registrationDate: new Date('2024-12-01'),
            lastContactDate: new Date('2025-01-01'),
            totalProjects: 1,
            activeProjects: 1,
            totalRevenue: 80000,
            notes: 'Yeni fintech müşterisi',
            contracts: [],
            projects: []
        }
    ];

    getCustomers(): Observable<Customer[]> {
        return of(this.customers);
    }

    getCustomer(id: string): Observable<Customer | undefined> {
        const customer = this.customers.find(c => c.id === id);
        return of(customer);
    }

    createCustomer(customer: Omit<Customer, 'id'>): Observable<Customer> {
        const newCustomer: Customer = {
            ...customer,
            id: Date.now().toString()
        };
        this.customers.push(newCustomer);
        return of(newCustomer);
    }

    updateCustomer(id: string, customer: Partial<Customer>): Observable<Customer> {
        const index = this.customers.findIndex(c => c.id === id);
        if (index !== -1) {
            this.customers[index] = {
                ...this.customers[index],
                ...customer
            };
            return of(this.customers[index]);
        }
        throw new Error('Customer not found');
    }

    deleteCustomer(id: string): Observable<boolean> {
        const index = this.customers.findIndex(c => c.id === id);
        if (index !== -1) {
            this.customers.splice(index, 1);
            return of(true);
        }
        return of(false);
    }
}

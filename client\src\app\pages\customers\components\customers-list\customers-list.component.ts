import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Table, TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { InputIconModule } from 'primeng/inputicon';
import { IconFieldModule } from 'primeng/iconfield';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToastModule } from 'primeng/toast';
import { ConfirmationService, MessageService } from 'primeng/api';
import { Customer, CustomerStatus, CustomerType, Industry } from '../../models/customer.model';

@Component({
    selector: 'app-customers-list',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        TableModule,
        ButtonModule,
        InputTextModule,
        InputIconModule,
        IconFieldModule,
        TagModule,
        TooltipModule,
        ConfirmDialogModule,
        ToastModule
    ],
    providers: [ConfirmationService, MessageService],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <h5>Müşteri Yönetimi</h5>
                        <p-button
                            label="Yeni Müşteri"
                            icon="pi pi-plus"
                            (onClick)="createCustomer()"
                            severity="primary"
                            size="small">
                        </p-button>
                    </div>

                    <p-table 
                        #dt 
                        [value]="customers" 
                        [rows]="10" 
                        [paginator]="true"
                        [globalFilterFields]="['customerCode', 'companyName', 'contactPerson', 'email', 'industry']"
                        [tableStyle]="{ 'min-width': '75rem' }"
                        [rowHover]="true"
                        dataKey="id"
                        currentPageReportTemplate="{first} - {last} / {totalRecords} müşteri"
                        [showCurrentPageReport]="true">
                        
                        <ng-template pTemplate="caption">
                            <div class="flex align-items-center justify-content-between">
                                <h5 class="m-0">Müşteriler</h5>
                                <p-iconfield iconPosition="left">
                                    <p-inputicon>
                                        <i class="pi pi-search"></i>
                                    </p-inputicon>
                                    <input 
                                        pInputText 
                                        type="text" 
                                        (input)="dt.filterGlobal($any($event.target).value, 'contains')" 
                                        placeholder="Müşteri ara..." />
                                </p-iconfield>
                            </div>
                        </ng-template>

                        <ng-template pTemplate="header">
                            <tr>
                                <th pSortableColumn="customerCode">
                                    Müşteri Kodu <p-sortIcon field="customerCode"></p-sortIcon>
                                </th>
                                <th pSortableColumn="companyName">
                                    Şirket Adı <p-sortIcon field="companyName"></p-sortIcon>
                                </th>
                                <th pSortableColumn="contactPerson">
                                    İletişim Kişisi <p-sortIcon field="contactPerson"></p-sortIcon>
                                </th>
                                <th>İletişim</th>
                                <th pSortableColumn="industry">
                                    Sektör <p-sortIcon field="industry"></p-sortIcon>
                                </th>
                                <th pSortableColumn="customerType">
                                    Tür <p-sortIcon field="customerType"></p-sortIcon>
                                </th>
                                <th pSortableColumn="status">
                                    Durum <p-sortIcon field="status"></p-sortIcon>
                                </th>
                                <th>Projeler</th>
                                <th pSortableColumn="totalRevenue">
                                    Toplam Gelir <p-sortIcon field="totalRevenue"></p-sortIcon>
                                </th>
                                <th>İşlemler</th>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="body" let-customer>
                            <tr>
                                <td>
                                    <span class="font-medium">{{ customer.customerCode }}</span>
                                </td>
                                <td>
                                    <div>
                                        <span class="font-medium">{{ customer.companyName }}</span>
                                        <div class="text-sm text-500 mt-1" *ngIf="customer.city">{{ customer.city }}</div>
                                    </div>
                                </td>
                                <td>
                                    <span>{{ customer.contactPerson }}</span>
                                </td>
                                <td>
                                    <div class="text-sm">
                                        <div><i class="pi pi-envelope mr-1"></i>{{ customer.email }}</div>
                                        <div *ngIf="customer.phone" class="mt-1">
                                            <i class="pi pi-phone mr-1"></i>{{ customer.phone }}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="customer.industry" 
                                        severity="info">
                                    </p-tag>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="customer.customerType" 
                                        [severity]="getCustomerTypeSeverity(customer.customerType)">
                                    </p-tag>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="customer.status" 
                                        [severity]="getStatusSeverity(customer.status)">
                                    </p-tag>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <span class="font-medium text-primary">{{ customer.activeProjects }}</span>
                                        <span class="text-500">/ {{ customer.totalProjects }}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-right">
                                        <span class="font-medium">{{ customer.totalRevenue | currency:'TRY':'symbol':'1.0-0' }}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="flex gap-2">
                                        <p-button 
                                            icon="pi pi-eye" 
                                            class="p-button-rounded p-button-text p-button-info"
                                            pTooltip="Detay"
                                            (onClick)="viewCustomer(customer.id)">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-pencil" 
                                            class="p-button-rounded p-button-text p-button-warning"
                                            pTooltip="Düzenle"
                                            (onClick)="editCustomer(customer.id)">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-trash" 
                                            class="p-button-rounded p-button-text p-button-danger"
                                            pTooltip="Sil"
                                            (onClick)="deleteCustomer(customer)">
                                        </p-button>
                                    </div>
                                </td>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="emptymessage">
                            <tr>
                                <td colspan="10" class="text-center py-4">
                                    <i class="pi pi-info-circle text-4xl text-500 mb-3"></i>
                                    <div class="text-500 font-medium">Henüz müşteri bulunmuyor</div>
                                    <div class="text-500">Yeni müşteri eklemek için "Yeni Müşteri" butonunu kullanın</div>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                </div>
            </div>
        </div>

        <p-confirmDialog></p-confirmDialog>
        <p-toast></p-toast>
    `
})
export class CustomersListComponent implements OnInit {
    @ViewChild('dt') table!: Table;
    
    customers: Customer[] = [];

    constructor(
        private router: Router,
        private confirmationService: ConfirmationService,
        private messageService: MessageService
    ) {}

    ngOnInit() {
        this.loadCustomers();
    }

    loadCustomers() {
        this.customers = [
            {
                id: '1',
                customerCode: 'CUS-2025-001',
                companyName: 'ABC Teknoloji A.Ş.',
                contactPerson: 'Ahmet Yılmaz',
                email: '<EMAIL>',
                phone: '+90 ************',
                address: 'Maslak Mahallesi, Teknoloji Caddesi No:15',
                city: 'İstanbul',
                country: 'Türkiye',
                website: 'www.abc.com',
                taxNumber: '1234567890',
                industry: Industry.TECHNOLOGY,
                customerType: CustomerType.ENTERPRISE,
                status: CustomerStatus.ACTIVE,
                registrationDate: new Date('2023-01-15'),
                lastContactDate: new Date('2025-01-10'),
                totalProjects: 3,
                activeProjects: 2,
                totalRevenue: 750000,
                notes: 'Stratejik müşteri, uzun vadeli işbirliği',
                contracts: [],
                projects: []
            },
            {
                id: '2',
                customerCode: 'CUS-2025-002',
                companyName: 'XYZ Finans Ltd.',
                contactPerson: 'Ayşe Demir',
                email: '<EMAIL>',
                phone: '+90 ************',
                address: 'Ataşehir, Finans Merkezi',
                city: 'İstanbul',
                country: 'Türkiye',
                industry: Industry.FINANCE,
                customerType: CustomerType.ENTERPRISE,
                status: CustomerStatus.ACTIVE,
                registrationDate: new Date('2023-03-20'),
                lastContactDate: new Date('2025-01-08'),
                totalProjects: 2,
                activeProjects: 1,
                totalRevenue: 450000,
                contracts: [],
                projects: []
            },
            {
                id: '3',
                customerCode: 'CUS-2025-003',
                companyName: 'DEF Sağlık Hizmetleri',
                contactPerson: 'Dr. Mehmet Özkan',
                email: '<EMAIL>',
                phone: '+90 ************',
                city: 'Ankara',
                country: 'Türkiye',
                industry: Industry.HEALTHCARE,
                customerType: CustomerType.SME,
                status: CustomerStatus.PROSPECT,
                registrationDate: new Date('2025-01-05'),
                totalProjects: 0,
                activeProjects: 0,
                totalRevenue: 0,
                notes: 'Potansiyel müşteri, teklif aşamasında',
                contracts: [],
                projects: []
            }
        ];
    }

    createCustomer() {
        this.router.navigate(['/pages/customers/new']);
    }

    viewCustomer(id: string) {
        this.router.navigate(['/pages/customers', id]);
    }

    editCustomer(id: string) {
        this.router.navigate(['/pages/customers', id, 'edit']);
    }

    deleteCustomer(customer: Customer) {
        this.confirmationService.confirm({
            message: `"${customer.companyName}" müşterisini silmek istediğinizden emin misiniz?`,
            header: 'Müşteri Silme Onayı',
            icon: 'pi pi-exclamation-triangle',
            acceptLabel: 'Evet',
            rejectLabel: 'Hayır',
            accept: () => {
                this.customers = this.customers.filter(c => c.id !== customer.id);
                this.messageService.add({
                    severity: 'success',
                    summary: 'Başarılı',
                    detail: 'Müşteri başarıyla silindi'
                });
            }
        });
    }

    getStatusSeverity(status: CustomerStatus): string {
        switch (status) {
            case CustomerStatus.ACTIVE:
                return 'success';
            case CustomerStatus.PROSPECT:
                return 'info';
            case CustomerStatus.INACTIVE:
                return 'warning';
            case CustomerStatus.SUSPENDED:
                return 'danger';
            case CustomerStatus.TERMINATED:
                return 'secondary';
            default:
                return 'info';
        }
    }

    getCustomerTypeSeverity(type: CustomerType): string {
        switch (type) {
            case CustomerType.ENTERPRISE:
                return 'success';
            case CustomerType.SME:
                return 'info';
            case CustomerType.STARTUP:
                return 'warning';
            case CustomerType.GOVERNMENT:
                return 'secondary';
            case CustomerType.NGO:
                return 'help';
            default:
                return 'info';
        }
    }
}

import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { Select } from 'primeng/select';
import { Textarea } from 'primeng/inputtextarea';
import { Checkbox } from 'primeng/checkbox';
import { Calendar } from 'primeng/calendar';
import { Chips } from 'primeng/chips';
import { ToastModule } from 'primeng/toast';
import { MessageService } from 'primeng/api';
import { DocumentType, DocumentCategory, DocumentFormat, DocumentStatus, DocumentVisibility } from '../../models/document.model';
import { DocumentService } from '../../services/document.service';

@Component({
    selector: 'app-document-form',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        ButtonModule,
        InputTextModule,
        Select,
        Textarea,
        Checkbox,
        Calendar,
        Chips,
        ToastModule
    ],
    providers: [MessageService],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <h5>{{ isEditMode ? 'Doküman Düzenle' : 'Yeni Doküman' }}</h5>
                        <p-button 
                            label="Geri" 
                            icon="pi pi-arrow-left" 
                            class="p-button-secondary"
                            (onClick)="goBack()">
                        </p-button>
                    </div>

                    <form [formGroup]="documentForm" (ngSubmit)="onSubmit()">
                        <div class="grid">
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="documentCode" class="font-medium">Doküman Kodu *</label>
                                    <input 
                                        id="documentCode"
                                        type="text" 
                                        pInputText 
                                        formControlName="documentCode"
                                        [readonly]="isEditMode"
                                        class="w-full"
                                        placeholder="Otomatik oluşturulacak" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="version" class="font-medium">Versiyon *</label>
                                    <input 
                                        id="version"
                                        type="text" 
                                        pInputText 
                                        formControlName="version"
                                        class="w-full"
                                        placeholder="1.0" />
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field">
                                    <label for="title" class="font-medium">Başlık *</label>
                                    <input 
                                        id="title"
                                        type="text" 
                                        pInputText 
                                        formControlName="title"
                                        class="w-full"
                                        placeholder="Doküman başlığı" />
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field">
                                    <label for="description" class="font-medium">Açıklama *</label>
                                    <textarea 
                                        id="description"
                                        pTextarea 
                                        formControlName="description"
                                        rows="3"
                                        class="w-full"
                                        placeholder="Doküman açıklaması...">
                                    </textarea>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="type" class="font-medium">Doküman Tipi *</label>
                                    <p-select 
                                        id="type"
                                        formControlName="type"
                                        [options]="typeOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Doküman tipi seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="category" class="font-medium">Kategori *</label>
                                    <p-select 
                                        id="category"
                                        formControlName="category"
                                        [options]="categoryOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Kategori seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="format" class="font-medium">Format *</label>
                                    <p-select 
                                        id="format"
                                        formControlName="format"
                                        [options]="formatOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Format seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="status" class="font-medium">Durum *</label>
                                    <p-select 
                                        id="status"
                                        formControlName="status"
                                        [options]="statusOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Durum seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="visibility" class="font-medium">Görünürlük *</label>
                                    <p-select 
                                        id="visibility"
                                        formControlName="visibility"
                                        [options]="visibilityOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Görünürlük seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="url" class="font-medium">URL</label>
                                    <input 
                                        id="url"
                                        type="url" 
                                        pInputText 
                                        formControlName="url"
                                        class="w-full"
                                        placeholder="https://example.com/document" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="projectId" class="font-medium">Proje</label>
                                    <p-select 
                                        id="projectId"
                                        formControlName="projectId"
                                        [options]="projectOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Proje seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="reviewerId" class="font-medium">İnceleyici</label>
                                    <p-select 
                                        id="reviewerId"
                                        formControlName="reviewerId"
                                        [options]="userOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="İnceleyici seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="expiryDate" class="font-medium">Son Kullanma Tarihi</label>
                                    <p-calendar 
                                        id="expiryDate"
                                        formControlName="expiryDate"
                                        dateFormat="dd/mm/yy"
                                        placeholder="Son kullanma tarihi"
                                        class="w-full">
                                    </p-calendar>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field">
                                    <label for="content" class="font-medium">İçerik</label>
                                    <textarea 
                                        id="content"
                                        pTextarea 
                                        formControlName="content"
                                        rows="6"
                                        class="w-full"
                                        placeholder="Doküman içeriği...">
                                    </textarea>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field">
                                    <label for="tags" class="font-medium">Etiketler</label>
                                    <p-chips 
                                        id="tags"
                                        formControlName="tags"
                                        placeholder="Etiket ekle..."
                                        class="w-full">
                                    </p-chips>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field">
                                    <label for="notes" class="font-medium">Notlar</label>
                                    <textarea 
                                        id="notes"
                                        pTextarea 
                                        formControlName="notes"
                                        rows="3"
                                        class="w-full"
                                        placeholder="Doküman hakkında notlar...">
                                    </textarea>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field-checkbox">
                                    <p-checkbox 
                                        inputId="isTemplate"
                                        formControlName="isTemplate">
                                    </p-checkbox>
                                    <label for="isTemplate" class="ml-2 font-medium">Bu bir şablon</label>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-content-end gap-2 mt-4">
                            <p-button 
                                label="İptal" 
                                icon="pi pi-times" 
                                class="p-button-secondary"
                                type="button"
                                (onClick)="goBack()">
                            </p-button>
                            <p-button 
                                [label]="isEditMode ? 'Güncelle' : 'Kaydet'" 
                                icon="pi pi-check" 
                                class="p-button-success"
                                type="submit"
                                [disabled]="documentForm.invalid">
                            </p-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <p-toast></p-toast>
    `
})
export class DocumentFormComponent implements OnInit {
    documentForm!: FormGroup;
    isEditMode = false;
    documentId: string = '';

    typeOptions = [
        { label: 'Spesifikasyon', value: DocumentType.SPECIFICATION },
        { label: 'Tasarım', value: DocumentType.DESIGN },
        { label: 'Teknik', value: DocumentType.TECHNICAL },
        { label: 'Kullanıcı Kılavuzu', value: DocumentType.USER_MANUAL },
        { label: 'API Dokümantasyonu', value: DocumentType.API_DOCUMENTATION },
        { label: 'Eğitim', value: DocumentType.TUTORIAL },
        { label: 'Politika', value: DocumentType.POLICY },
        { label: 'Prosedür', value: DocumentType.PROCEDURE },
        { label: 'Rapor', value: DocumentType.REPORT },
        { label: 'Sunum', value: DocumentType.PRESENTATION },
        { label: 'Şablon', value: DocumentType.TEMPLATE },
        { label: 'Toplantı Notları', value: DocumentType.MEETING_NOTES },
        { label: 'Gereksinimler', value: DocumentType.REQUIREMENTS },
        { label: 'Test Planı', value: DocumentType.TEST_PLAN },
        { label: 'Deployment Kılavuzu', value: DocumentType.DEPLOYMENT_GUIDE }
    ];

    categoryOptions = [
        { label: 'Proje Yönetimi', value: DocumentCategory.PROJECT_MANAGEMENT },
        { label: 'Geliştirme', value: DocumentCategory.DEVELOPMENT },
        { label: 'Tasarım', value: DocumentCategory.DESIGN },
        { label: 'Test', value: DocumentCategory.TESTING },
        { label: 'Deployment', value: DocumentCategory.DEPLOYMENT },
        { label: 'Bakım', value: DocumentCategory.MAINTENANCE },
        { label: 'Eğitim', value: DocumentCategory.TRAINING },
        { label: 'Uyumluluk', value: DocumentCategory.COMPLIANCE },
        { label: 'Güvenlik', value: DocumentCategory.SECURITY },
        { label: 'Mimari', value: DocumentCategory.ARCHITECTURE },
        { label: 'İş', value: DocumentCategory.BUSINESS },
        { label: 'Hukuki', value: DocumentCategory.LEGAL },
        { label: 'İnsan Kaynakları', value: DocumentCategory.HR },
        { label: 'Finans', value: DocumentCategory.FINANCE },
        { label: 'Pazarlama', value: DocumentCategory.MARKETING }
    ];

    formatOptions = [
        { label: 'PDF', value: DocumentFormat.PDF },
        { label: 'Word', value: DocumentFormat.WORD },
        { label: 'Excel', value: DocumentFormat.EXCEL },
        { label: 'PowerPoint', value: DocumentFormat.POWERPOINT },
        { label: 'Markdown', value: DocumentFormat.MARKDOWN },
        { label: 'HTML', value: DocumentFormat.HTML },
        { label: 'Text', value: DocumentFormat.TEXT },
        { label: 'Resim', value: DocumentFormat.IMAGE },
        { label: 'Video', value: DocumentFormat.VIDEO },
        { label: 'Ses', value: DocumentFormat.AUDIO },
        { label: 'Arşiv', value: DocumentFormat.ARCHIVE },
        { label: 'Kod', value: DocumentFormat.CODE },
        { label: 'Diyagram', value: DocumentFormat.DIAGRAM },
        { label: 'Elektronik Tablo', value: DocumentFormat.SPREADSHEET },
        { label: 'Sunum', value: DocumentFormat.PRESENTATION }
    ];

    statusOptions = [
        { label: 'Taslak', value: DocumentStatus.DRAFT },
        { label: 'İncelemede', value: DocumentStatus.IN_REVIEW },
        { label: 'Onaylandı', value: DocumentStatus.APPROVED },
        { label: 'Yayınlandı', value: DocumentStatus.PUBLISHED },
        { label: 'Arşivlendi', value: DocumentStatus.ARCHIVED },
        { label: 'Reddedildi', value: DocumentStatus.REJECTED },
        { label: 'Süresi Doldu', value: DocumentStatus.EXPIRED },
        { label: 'Geri Çekildi', value: DocumentStatus.WITHDRAWN }
    ];

    visibilityOptions = [
        { label: 'Herkese Açık', value: DocumentVisibility.PUBLIC },
        { label: 'Dahili', value: DocumentVisibility.INTERNAL },
        { label: 'Takım', value: DocumentVisibility.TEAM },
        { label: 'Proje', value: DocumentVisibility.PROJECT },
        { label: 'Gizli', value: DocumentVisibility.CONFIDENTIAL },
        { label: 'Kısıtlı', value: DocumentVisibility.RESTRICTED }
    ];

    projectOptions = [
        { label: 'Proje seçin...', value: null },
        { label: 'E-Ticaret Platformu', value: '1' },
        { label: 'Mobil Uygulama', value: '2' },
        { label: 'CRM Sistemi', value: '3' },
        { label: 'ERP Modernizasyonu', value: '4' }
    ];

    userOptions = [
        { label: 'İnceleyici seçin...', value: null },
        { label: 'Ahmet Yılmaz', value: '101' },
        { label: 'Mehmet Öztürk', value: '102' },
        { label: 'Zeynep Kaya', value: '103' },
        { label: 'Ayşe Yıldız', value: '105' },
        { label: 'Can Şahin', value: '106' }
    ];

    constructor(
        private fb: FormBuilder,
        private route: ActivatedRoute,
        private router: Router,
        private documentService: DocumentService,
        private messageService: MessageService
    ) {}

    ngOnInit() {
        this.initForm();
        this.checkEditMode();
    }

    initForm() {
        this.documentForm = this.fb.group({
            documentCode: ['', Validators.required],
            title: ['', Validators.required],
            description: ['', Validators.required],
            type: [DocumentType.TECHNICAL, Validators.required],
            category: [DocumentCategory.DEVELOPMENT, Validators.required],
            format: [DocumentFormat.PDF, Validators.required],
            version: ['1.0', Validators.required],
            status: [DocumentStatus.DRAFT, Validators.required],
            visibility: [DocumentVisibility.INTERNAL, Validators.required],
            url: [''],
            content: [''],
            projectId: [null],
            reviewerId: [null],
            isTemplate: [false],
            expiryDate: [null],
            tags: [[]],
            notes: ['']
        });

        if (!this.isEditMode) {
            this.generateDocumentCode();
        }
    }

    checkEditMode() {
        this.documentId = this.route.snapshot.params['id'];
        this.isEditMode = !!this.documentId && this.router.url.includes('edit');
        
        if (this.isEditMode) {
            this.loadDocument();
        }
    }

    generateDocumentCode() {
        const year = new Date().getFullYear();
        const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        const documentCode = `DOC-${year}-${randomNum}`;
        this.documentForm.patchValue({ documentCode });
    }

    loadDocument() {
        this.documentService.getDocumentById(this.documentId).subscribe(document => {
            if (document) {
                this.documentForm.patchValue({
                    documentCode: document.documentCode,
                    title: document.title,
                    description: document.description,
                    type: document.type,
                    category: document.category,
                    format: document.format,
                    version: document.version,
                    status: document.status,
                    visibility: document.visibility,
                    url: document.url,
                    content: document.content,
                    projectId: document.projectId,
                    reviewerId: document.reviewerId,
                    isTemplate: document.isTemplate,
                    expiryDate: document.expiryDate,
                    tags: document.tags,
                    notes: document.notes
                });
            }
        });
    }

    onSubmit() {
        if (this.documentForm.valid) {
            const formData = this.documentForm.value;
            
            if (this.isEditMode) {
                this.documentService.updateDocument(this.documentId, formData).subscribe(() => {
                    this.messageService.add({
                        severity: 'success',
                        summary: 'Başarılı',
                        detail: 'Doküman başarıyla güncellendi'
                    });
                    setTimeout(() => {
                        this.router.navigate(['/pages/documents']);
                    }, 1500);
                });
            } else {
                this.documentService.createDocument(formData).subscribe(() => {
                    this.messageService.add({
                        severity: 'success',
                        summary: 'Başarılı',
                        detail: 'Doküman başarıyla oluşturuldu'
                    });
                    setTimeout(() => {
                        this.router.navigate(['/pages/documents']);
                    }, 1500);
                });
            }
        }
    }

    goBack() {
        this.router.navigate(['/pages/documents']);
    }
}

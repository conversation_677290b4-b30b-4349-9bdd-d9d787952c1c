import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { InputTextModule } from 'primeng/inputtext';
import { PasswordModule } from 'primeng/password';
import { MessageModule } from 'primeng/message';
import { CommonModule } from '@angular/common';
import { AuthService } from '../../../../core/services/auth.service';

@Component({
    selector: 'app-register',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        RouterModule,
        ButtonModule,
        CardModule,
        InputTextModule,
        PasswordModule,
        MessageModule
    ],
    template: `
        <div class="register-page">
            <div class="register-container">
                <div class="register-card">
                    <div class="text-center mb-5">
                        <img src="assets/logo-b.png" alt="TRtek Logo" class="register-logo mb-4">
                        <div class="text-900 text-3xl font-medium mb-3">TRtek EYS</div>
                        <span class="text-600 font-medium">Hesap oluşturun</span>
                    </div>

                    <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
                        <div class="mb-4">
                            <label for="username" class="block text-900 text-xl font-medium mb-2">Kullanıcı Adı</label>
                            <input id="username" type="text" placeholder="Kullanıcı adınız" pInputText formControlName="username"
                                class="w-full md:w-30rem" style="padding:1rem">
                            <small *ngIf="registerForm.get('username')?.errors?.['required'] && registerForm.get('username')?.touched"
                                class="p-error block">Kullanıcı adı gereklidir</small>
                        </div>

                        <div class="mb-4">
                            <label for="fullName" class="block text-900 text-xl font-medium mb-2">Ad Soyad</label>
                            <input id="fullName" type="text" placeholder="Ad soyadınız" pInputText formControlName="fullName"
                                class="w-full md:w-30rem" style="padding:1rem">
                            <small *ngIf="registerForm.get('fullName')?.errors?.['required'] && registerForm.get('fullName')?.touched"
                                class="p-error block">Ad soyad gereklidir</small>
                        </div>

                        <div class="mb-4">
                            <label for="email" class="block text-900 text-xl font-medium mb-2">E-posta</label>
                            <input id="email" type="email" placeholder="E-posta adresiniz" pInputText formControlName="email"
                                class="w-full md:w-30rem" style="padding:1rem">
                            <small *ngIf="registerForm.get('email')?.errors?.['required'] && registerForm.get('email')?.touched"
                                class="p-error block">E-posta gereklidir</small>
                            <small *ngIf="registerForm.get('email')?.errors?.['email'] && registerForm.get('email')?.touched"
                                class="p-error block">Geçerli bir e-posta adresi girin</small>
                        </div>

                        <div class="mb-4">
                            <label for="password" class="block text-900 font-medium text-xl mb-2">Şifre</label>
                            <p-password id="password" formControlName="password" [toggleMask]="true" showToggleIcon="true"
                                placeholder="Şifreniz" [style]="{'width':'100%'}" [inputStyle]="{'width':'100%', 'padding':'1rem'}"
                                [feedback]="true"></p-password>
                            <small *ngIf="registerForm.get('password')?.errors?.['required'] && registerForm.get('password')?.touched"
                                class="p-error block">Şifre gereklidir</small>
                            <small *ngIf="registerForm.get('password')?.errors?.['minlength'] && registerForm.get('password')?.touched"
                                class="p-error block">Şifre en az 6 karakter olmalıdır</small>
                        </div>

                        <div class="mb-4">
                            <label for="confirmPassword" class="block text-900 font-medium text-xl mb-2">Şifre Tekrar</label>
                            <p-password id="confirmPassword" formControlName="confirmPassword" [toggleMask]="true" showToggleIcon="true"
                                placeholder="Şifrenizi tekrar girin" [style]="{'width':'100%'}" [inputStyle]="{'width':'100%', 'padding':'1rem'}"
                                [feedback]="false"></p-password>
                            <small *ngIf="registerForm.get('confirmPassword')?.errors?.['required'] && registerForm.get('confirmPassword')?.touched"
                                class="p-error block">Şifre tekrarı gereklidir</small>
                            <small *ngIf="registerForm.errors?.['passwordMismatch'] && registerForm.get('confirmPassword')?.touched"
                                class="p-error block">Şifreler eşleşmiyor</small>
                        </div>

                        <!-- Error Message -->
                        <div *ngIf="errorMessage" class="mb-4">
                            <p-message severity="error" [text]="errorMessage"></p-message>
                        </div>

                        <!-- Success Message -->
                        <div *ngIf="successMessage" class="mb-4">
                            <p-message severity="success" [text]="successMessage"></p-message>
                        </div>

                        <button pButton pRipple label="Kayıt Ol" class="w-full p-3 text-xl"
                            [loading]="isLoading" [disabled]="registerForm.invalid || isLoading"></button>
                        
                        <!-- Login Link -->
                        <div class="text-center mt-4">
                            <span class="text-600">Zaten hesabınız var mı? </span>
                            <a routerLink="/auth/login" class="font-medium no-underline cursor-pointer" style="color: #B71C1C">Giriş yapın</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `,
    styles: [`
        .register-page {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            width: 100vw;
            background: linear-gradient(180deg, #B71C1C 10%, rgba(183, 28, 28, 0) 30%);
            padding: 2rem 0;
        }
        .register-container {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            max-width: 500px;
        }
        .register-card {
            width: 100%;
            border-radius: 20px;
            background: white;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .register-logo {
            height: 4rem;
            width: auto;
            object-fit: contain;
            margin: 0 auto;
            display: block;
        }
        @media (max-width: 768px) {
            .register-logo {
                height: 3rem;
            }
        }
    `]
})
export class RegisterComponent {
    registerForm: FormGroup;
    isLoading = false;
    errorMessage = '';
    successMessage = '';

    constructor(
        private formBuilder: FormBuilder,
        private authService: AuthService,
        private router: Router
    ) {
        this.registerForm = this.formBuilder.group({
            username: ['', Validators.required],
            fullName: ['', Validators.required],
            email: ['', [Validators.required, Validators.email]],
            password: ['', [Validators.required, Validators.minLength(6)]],
            confirmPassword: ['', Validators.required]
        }, { validators: this.passwordMatchValidator });
    }

    passwordMatchValidator(form: FormGroup) {
        const password = form.get('password');
        const confirmPassword = form.get('confirmPassword');
        
        if (password && confirmPassword && password.value !== confirmPassword.value) {
            return { passwordMismatch: true };
        }
        return null;
    }

    onSubmit() {
        if (this.registerForm.valid) {
            this.isLoading = true;
            this.errorMessage = '';
            this.successMessage = '';
            
            const { username, fullName, email, password } = this.registerForm.value;

            this.authService.register(username, password, fullName, email).subscribe({
                next: () => {
                    this.successMessage = 'Kayıt başarılı! Giriş sayfasına yönlendiriliyorsunuz...';
                    setTimeout(() => {
                        this.router.navigate(['/auth/login']);
                    }, 2000);
                },
                error: (error) => {
                    console.error('Register error:', error);
                    this.errorMessage = error.message || 'Kayıt olurken bir hata oluştu. Lütfen bilgilerinizi kontrol edin.';
                    this.isLoading = false;
                },
                complete: () => {
                    this.isLoading = false;
                }
            });
        }
    }
}

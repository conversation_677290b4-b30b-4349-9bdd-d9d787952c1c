import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { Select } from 'primeng/select';
import { InputNumber } from 'primeng/inputnumber';
import { DatePicker } from 'primeng/datepicker';
import { ToastModule } from 'primeng/toast';
import { MessageService } from 'primeng/api';
import { ProjectRole, AssignmentType, AssignmentStatus, AssignmentPriority } from '../../models/assignment.model';
import { TooltipModule } from 'primeng/tooltip';

@Component({
    selector: 'app-assignment-form',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        ButtonModule,
        InputTextModule,
        Select,
        InputNumber,
        DatePicker,
        ToastModule,
        TooltipModule
    ],
    providers: [MessageService],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <h5>{{ isEditMode ? 'Atama Düzenle' : 'Yeni Atama' }}</h5>
                        <p-button 
                            label="Geri" 
                            icon="pi pi-arrow-left" 
                            class="p-button-secondary"
                            (onClick)="goBack()">
                        </p-button>
                    </div>

                    <form [formGroup]="assignmentForm" (ngSubmit)="onSubmit()">
                        <div class="grid">
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="assignmentCode" class="font-medium">Atama Kodu *</label>
                                    <input 
                                        id="assignmentCode"
                                        type="text" 
                                        pInputText 
                                        formControlName="assignmentCode"
                                        [readonly]="isEditMode"
                                        class="w-full"
                                        placeholder="Otomatik oluşturulacak" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="userName" class="font-medium">Kullanıcı *</label>
                                    <input 
                                        id="userName"
                                        type="text" 
                                        pInputText 
                                        formControlName="userName"
                                        class="w-full"
                                        placeholder="Kullanıcı adı" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="projectName" class="font-medium">Proje *</label>
                                    <input 
                                        id="projectName"
                                        type="text" 
                                        pInputText 
                                        formControlName="projectName"
                                        class="w-full"
                                        placeholder="Proje adı" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="teamName" class="font-medium">Takım *</label>
                                    <input 
                                        id="teamName"
                                        type="text" 
                                        pInputText 
                                        formControlName="teamName"
                                        class="w-full"
                                        placeholder="Takım adı" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="role" class="font-medium">Rol *</label>
                                    <p-select 
                                        id="role"
                                        formControlName="role"
                                        [options]="roleOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Rol seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="assignmentType" class="font-medium">Atama Türü *</label>
                                    <p-select 
                                        id="assignmentType"
                                        formControlName="assignmentType"
                                        [options]="assignmentTypeOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Atama türü seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="workloadPercentage" class="font-medium">İş Yükü (%) *</label>
                                    <p-inputnumber 
                                        id="workloadPercentage"
                                        formControlName="workloadPercentage"
                                        [min]="1"
                                        [max]="100"
                                        suffix="%"
                                        placeholder="İş yükü yüzdesi"
                                        class="w-full">
                                    </p-inputnumber>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="priority" class="font-medium">Öncelik *</label>
                                    <p-select 
                                        id="priority"
                                        formControlName="priority"
                                        [options]="priorityOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Öncelik seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="startDate" class="font-medium">Başlangıç Tarihi *</label>
                                    <p-datepicker 
                                        id="startDate"
                                        formControlName="startDate"
                                        dateFormat="dd/mm/yy"
                                        placeholder="Başlangıç tarihi"
                                        class="w-full">
                                    </p-datepicker>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="endDate" class="font-medium">Bitiş Tarihi</label>
                                    <p-datepicker 
                                        id="endDate"
                                        formControlName="endDate"
                                        dateFormat="dd/mm/yy"
                                        placeholder="Bitiş tarihi"
                                        class="w-full">
                                    </p-datepicker>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="status" class="font-medium">Durum *</label>
                                    <p-select 
                                        id="status"
                                        formControlName="status"
                                        [options]="statusOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Durum seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-content-end gap-2 mt-4">
                            <p-button 
                                icon="pi pi-times"
                                severity="secondary"
                                rounded
                                outlined
                                type="button"
                                pTooltip="İptal"
                                (onClick)="goBack()">
                            </p-button>
                            <p-button 
                                icon="pi pi-check"
                                severity="success"
                                rounded
                                outlined
                                type="submit"
                                [pTooltip]="isEditMode ? 'Güncelle' : 'Kaydet'"
                                [disabled]="assignmentForm.invalid">
                            </p-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <p-toast></p-toast>
    `
})
export class AssignmentFormComponent implements OnInit {
    assignmentForm!: FormGroup;
    isEditMode = false;
    assignmentId: string = '';

    roleOptions = [
        { label: 'Proje Yöneticisi', value: ProjectRole.PROJECT_MANAGER },
        { label: 'Teknik Lider', value: ProjectRole.TECH_LEAD },
        { label: 'Kıdemli Geliştirici', value: ProjectRole.SENIOR_DEVELOPER },
        { label: 'Geliştirici', value: ProjectRole.DEVELOPER },
        { label: 'Junior Geliştirici', value: ProjectRole.JUNIOR_DEVELOPER },
        { label: 'Frontend Geliştirici', value: ProjectRole.FRONTEND_DEVELOPER },
        { label: 'Backend Geliştirici', value: ProjectRole.BACKEND_DEVELOPER },
        { label: 'Fullstack Geliştirici', value: ProjectRole.FULLSTACK_DEVELOPER },
        { label: 'Mobil Geliştirici', value: ProjectRole.MOBILE_DEVELOPER },
        { label: 'DevOps Mühendisi', value: ProjectRole.DEVOPS_ENGINEER },
        { label: 'QA Mühendisi', value: ProjectRole.QA_ENGINEER }
    ];

    assignmentTypeOptions = [
        { label: 'Tam Zamanlı', value: AssignmentType.FULL_TIME },
        { label: 'Yarı Zamanlı', value: AssignmentType.PART_TIME },
        { label: 'Danışman', value: AssignmentType.CONSULTANT },
        { label: 'Geçici', value: AssignmentType.TEMPORARY },
        { label: 'Sözleşmeli', value: AssignmentType.CONTRACT }
    ];

    priorityOptions = [
        { label: 'Düşük', value: AssignmentPriority.LOW },
        { label: 'Orta', value: AssignmentPriority.MEDIUM },
        { label: 'Yüksek', value: AssignmentPriority.HIGH },
        { label: 'Kritik', value: AssignmentPriority.CRITICAL }
    ];

    statusOptions = [
        { label: 'Aktif', value: AssignmentStatus.ACTIVE },
        { label: 'Beklemede', value: AssignmentStatus.PENDING },
        { label: 'Askıya Alındı', value: AssignmentStatus.SUSPENDED }
    ];

    constructor(
        private fb: FormBuilder,
        private route: ActivatedRoute,
        private router: Router,
        private messageService: MessageService
    ) {}

    ngOnInit() {
        this.initForm();
        this.checkEditMode();
    }

    initForm() {
        this.assignmentForm = this.fb.group({
            assignmentCode: ['', Validators.required],
            userName: ['', Validators.required],
            projectName: ['', Validators.required],
            teamName: ['', Validators.required],
            role: [ProjectRole.DEVELOPER, Validators.required],
            assignmentType: [AssignmentType.FULL_TIME, Validators.required],
            workloadPercentage: [100, [Validators.required, Validators.min(1), Validators.max(100)]],
            priority: [AssignmentPriority.MEDIUM, Validators.required],
            startDate: [new Date(), Validators.required],
            endDate: [null],
            status: [AssignmentStatus.ACTIVE, Validators.required]
        });

        if (!this.isEditMode) {
            this.generateAssignmentCode();
        }
    }

    checkEditMode() {
        this.assignmentId = this.route.snapshot.params['id'];
        this.isEditMode = !!this.assignmentId && this.router.url.includes('edit');
        
        if (this.isEditMode) {
            this.loadAssignment();
        }
    }

    generateAssignmentCode() {
        const year = new Date().getFullYear();
        const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        const assignmentCode = `ASG-${year}-${randomNum}`;
        this.assignmentForm.patchValue({ assignmentCode });
    }

    loadAssignment() {
        // Dummy data for edit mode
        const dummyAssignment = {
            assignmentCode: 'ASG-2024-001',
            userName: 'Ahmet Yılmaz',
            projectName: 'E-Ticaret Platformu',
            teamName: 'Frontend Takımı',
            role: ProjectRole.FRONTEND_DEVELOPER,
            assignmentType: AssignmentType.FULL_TIME,
            workloadPercentage: 80,
            priority: AssignmentPriority.HIGH,
            startDate: new Date('2024-01-15'),
            endDate: new Date('2024-12-31'),
            status: AssignmentStatus.ACTIVE
        };

        this.assignmentForm.patchValue(dummyAssignment);
    }

    onSubmit() {
        if (this.assignmentForm.valid) {
            this.messageService.add({
                severity: 'success',
                summary: 'Başarılı',
                detail: this.isEditMode ? 'Atama başarıyla güncellendi' : 'Atama başarıyla oluşturuldu'
            });

            setTimeout(() => {
                this.router.navigate(['/pages/assignments']);
            }, 1500);
        }
    }

    goBack() {
        this.router.navigate(['/pages/assignments']);
    }
}

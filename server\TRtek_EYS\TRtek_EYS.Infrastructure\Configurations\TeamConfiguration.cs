﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TRtek_EYS.Domain.Entities;

namespace TRtek_EYS.Infrastructure.Configurations
{
    public class TeamConfiguration : IEntityTypeConfiguration<Team>
    {
        public void Configure(EntityTypeBuilder<Team> builder)
        {
            builder.ToTable("Teams");

            builder.HasKey(t => t.Id);

            builder.Property(t => t.Name)
                   .IsRequired()
                   .HasMaxLength(100);

            builder.Property(t => t.Description)
                   .HasMaxLength(250);

            builder.Property(t => t.CreatedAt)
                   .IsRequired();

            builder.HasMany(t => t.Roles)
                   .WithOne(r => r.Team)
                   .HasForeignKey(r => r.TeamId)
                   .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(t => t.ProjectTeams)
                   .WithOne(pt => pt.Team)
                   .HasForeignKey(pt => pt.TeamId)
                   .OnDelete(DeleteBehavior.Cascade);
        }
    }
}

import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { Select } from 'primeng/select';
import { Textarea } from 'primeng/inputtextarea';
import { ToastModule } from 'primeng/toast';
import { MessageService } from 'primeng/api';
import { UserStatus, Department } from '../../models/user.model';
import { UserService } from '../../services/user.service';

@Component({
    selector: 'app-user-form',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        ButtonModule,
        InputTextModule,
        Select,
        Textarea,
        ToastModule
    ],
    providers: [MessageService],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <h5>{{ isEditMode ? 'Kullanıcı Düzenle' : 'Yeni Kullanıcı' }}</h5>
                        <p-button 
                            label="Geri" 
                            icon="pi pi-arrow-left" 
                            class="p-button-secondary"
                            (onClick)="goBack()">
                        </p-button>
                    </div>

                    <form [formGroup]="userForm" (ngSubmit)="onSubmit()">
                        <div class="grid">
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="userCode" class="font-medium">Kullanıcı Kodu *</label>
                                    <input 
                                        id="userCode"
                                        type="text" 
                                        pInputText 
                                        formControlName="userCode"
                                        [readonly]="isEditMode"
                                        class="w-full"
                                        placeholder="Otomatik oluşturulacak" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="firstName" class="font-medium">Ad *</label>
                                    <input 
                                        id="firstName"
                                        type="text" 
                                        pInputText 
                                        formControlName="firstName"
                                        class="w-full"
                                        placeholder="Ad" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="lastName" class="font-medium">Soyad *</label>
                                    <input 
                                        id="lastName"
                                        type="text" 
                                        pInputText 
                                        formControlName="lastName"
                                        class="w-full"
                                        placeholder="Soyad" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="email" class="font-medium">E-posta *</label>
                                    <input 
                                        id="email"
                                        type="email" 
                                        pInputText 
                                        formControlName="email"
                                        class="w-full"
                                        placeholder="E-posta adresi" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="department" class="font-medium">Departman *</label>
                                    <p-select 
                                        id="department"
                                        formControlName="department"
                                        [options]="departmentOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Departman seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="position" class="font-medium">Pozisyon *</label>
                                    <input 
                                        id="position"
                                        type="text" 
                                        pInputText 
                                        formControlName="position"
                                        class="w-full"
                                        placeholder="Pozisyon" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="status" class="font-medium">Durum *</label>
                                    <p-select 
                                        id="status"
                                        formControlName="status"
                                        [options]="statusOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Durum seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field">
                                    <label for="notes" class="font-medium">Notlar</label>
                                    <textarea 
                                        id="notes"
                                        pInputTextarea 
                                        formControlName="notes"
                                        rows="3"
                                        class="w-full"
                                        placeholder="Kullanıcı hakkında notlar...">
                                    </textarea>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-content-end gap-2 mt-4">
                            <p-button 
                                label="İptal" 
                                icon="pi pi-times" 
                                class="p-button-secondary"
                                type="button"
                                (onClick)="goBack()">
                            </p-button>
                            <p-button 
                                [label]="isEditMode ? 'Güncelle' : 'Kaydet'" 
                                icon="pi pi-check" 
                                class="p-button-success"
                                type="submit"
                                [disabled]="userForm.invalid">
                            </p-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <p-toast></p-toast>
    `
})
export class UserFormComponent implements OnInit {
    userForm!: FormGroup;
    isEditMode = false;
    userId: string = '';

    departmentOptions = [
        { label: 'Geliştirme', value: Department.DEVELOPMENT },
        { label: 'Tasarım', value: Department.DESIGN },
        { label: 'Kalite Güvence', value: Department.QA },
        { label: 'DevOps', value: Department.DEVOPS },
        { label: 'Yönetim', value: Department.MANAGEMENT },
        { label: 'İş Analizi', value: Department.BUSINESS_ANALYSIS },
        { label: 'Destek', value: Department.SUPPORT }
    ];

    statusOptions = [
        { label: 'Aktif', value: UserStatus.ACTIVE },
        { label: 'Pasif', value: UserStatus.INACTIVE },
        { label: 'Beklemede', value: UserStatus.PENDING },
        { label: 'Askıya Alındı', value: UserStatus.SUSPENDED }
    ];

    constructor(
        private fb: FormBuilder,
        private route: ActivatedRoute,
        private router: Router,
        private userService: UserService,
        private messageService: MessageService
    ) {}

    ngOnInit() {
        this.initForm();
        this.checkEditMode();
    }

    initForm() {
        this.userForm = this.fb.group({
            userCode: ['', Validators.required],
            firstName: ['', Validators.required],
            lastName: ['', Validators.required],
            email: ['', [Validators.required, Validators.email]],
            department: [Department.DEVELOPMENT, Validators.required],
            position: ['', Validators.required],
            status: [UserStatus.ACTIVE, Validators.required],
            notes: ['']
        });

        if (!this.isEditMode) {
            this.generateUserCode();
        }
    }

    checkEditMode() {
        this.userId = this.route.snapshot.params['id'];
        this.isEditMode = !!this.userId && this.router.url.includes('edit');
        
        if (this.isEditMode) {
            this.loadUser();
        }
    }

    generateUserCode() {
        const year = new Date().getFullYear();
        const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        const userCode = `USR-${year}-${randomNum}`;
        this.userForm.patchValue({ userCode });
    }

    loadUser() {
        this.userService.getUserById(this.userId).subscribe(user => {
            if (user) {
                this.userForm.patchValue({
                    userCode: user.userCode,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    email: user.email,
                    department: user.department,
                    position: user.position,
                    status: user.status,
                    notes: user.notes
                });
            }
        });
    }

    onSubmit() {
        if (this.userForm.valid) {
            const formData = this.userForm.value;
            
            if (this.isEditMode) {
                this.userService.updateUser(this.userId, formData).subscribe(() => {
                    this.messageService.add({
                        severity: 'success',
                        summary: 'Başarılı',
                        detail: 'Kullanıcı başarıyla güncellendi'
                    });
                    setTimeout(() => {
                        this.router.navigate(['/pages/users']);
                    }, 1500);
                });
            } else {
                this.userService.createUser(formData).subscribe(() => {
                    this.messageService.add({
                        severity: 'success',
                        summary: 'Başarılı',
                        detail: 'Kullanıcı başarıyla oluşturuldu'
                    });
                    setTimeout(() => {
                        this.router.navigate(['/pages/users']);
                    }, 1500);
                });
            }
        }
    }

    goBack() {
        this.router.navigate(['/pages/users']);
    }
}

﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Teams.Commands.Update
{
    public class UpdateTeamCommandHandler : IRequestHandler<UpdateTeamCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public UpdateTeamCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(UpdateTeamCommand request, CancellationToken cancellationToken)
        {
            var team = await _context.Teams
                .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);

            if (team is null)
                throw new Exception("Takım bulunamadı.");

            team.Name = request.Name;
            team.Description = request.Description;

            await _context.SaveChangesAsync(cancellationToken);

            return team.Id;
        }
    }
}

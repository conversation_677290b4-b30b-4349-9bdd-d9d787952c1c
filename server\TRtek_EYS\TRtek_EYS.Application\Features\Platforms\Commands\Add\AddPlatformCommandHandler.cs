﻿using MediatR;
using TRtek_EYS.Application.Common.Interfaces;
using TRtek_EYS.Domain.Entities;
using TRtek_EYS.Domain.Enums;

namespace TRtek_EYS.Application.Features.Platforms.Commands.Add
{
    public class AddPlatformCommandHandler : IRequestHandler<AddPlatformCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public AddPlatformCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(AddPlatformCommand request, CancellationToken cancellationToken)
        {
            var platform = new Platform
            {
                Type = (PlatformType)request.Type,
                ProjectId = request.ProjectId,
                CreatedAt = DateTime.UtcNow
            };

            await _context.Platforms.AddAsync(platform, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);

            return platform.Id;
        }
    }
}

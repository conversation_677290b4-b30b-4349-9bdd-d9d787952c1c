import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Team, TeamFormData, TeamStatus, MemberStatus, TeamRole, Department } from '../models/team.model';

@Injectable({
    providedIn: 'root'
})
export class TeamService {
    // Bu mock data'yı daha sonra API çağrıları ile değiştireceğiz
    private mockTeams: Team[] = [
        {
            id: '1',
            teamCode: 'TM-2024-001',
            name: 'Frontend Takımı',
            description: 'Ana frontend geliştirme takımı. UI/UX implementasyonu ve kullanıcı deneyimi geliştirmelerinden sorumlu.',
            department: Department.DEVELOPMENT,
            teamLead: '<PERSON><PERSON> Yılmaz',
            members: [
                {
                    id: '101',
                    userId: 'u101',
                    name: '<PERSON><PERSON>ılma<PERSON>',
                    email: '<EMAIL>',
                    role: TeamRole.TEAM_LEAD,
                    joinDate: new Date('2024-01-01'),
                    status: MemberStatus.ACTIVE,
                    skills: ['React', 'TypeScript', 'CSS'],
                    workloadPercentage: 90,
                    isTeamLead: true
                },
                {
                    id: '102',
                    userId: 'u102',
                    name: 'Ayşe Demir',
                    email: '<EMAIL>',
                    role: TeamRole.SENIOR_DEVELOPER,
                    joinDate: new Date('2024-02-01'),
                    status: MemberStatus.ACTIVE,
                    skills: ['Angular', 'Vue.js', 'SCSS'],
                    workloadPercentage: 85,
                    isTeamLead: false
                },
                {
                    id: '103',
                    userId: 'u103',
                    name: 'Mehmet Özkan',
                    email: '<EMAIL>',
                    role: TeamRole.FRONTEND_DEVELOPER,
                    joinDate: new Date('2024-03-01'),
                    status: MemberStatus.ACTIVE,
                    skills: ['React', 'JavaScript', 'HTML/CSS'],
                    workloadPercentage: 80,
                    isTeamLead: false
                }
            ],
            createdAt: new Date('2024-01-01'),
            updatedAt: new Date('2024-07-16'),
            status: TeamStatus.ACTIVE,
            activeProjects: 3,
            completedProjects: 5,
            totalProjects: 8,
            budget: 250000,
            location: 'İstanbul Ofis',
            technologies: ['React', 'Angular', 'Vue.js', 'TypeScript'],
            notes: 'Yüksek performanslı frontend takımı'
        },
        {
            id: '2',
            teamCode: 'TM-2024-002',
            name: 'Backend Takımı',
            description: 'Temel backend geliştirme takımı. API ve veritabanı yönetiminden sorumlu.',
            department: Department.DEVELOPMENT,
            teamLead: 'Zeynep Arslan',
            members: [
                {
                    id: '201',
                    userId: 'u201',
                    name: 'Zeynep Arslan',
                    email: '<EMAIL>',
                    role: TeamRole.TEAM_LEAD,
                    joinDate: new Date('2024-01-01'),
                    status: MemberStatus.ACTIVE,
                    skills: ['.NET Core', 'SQL Server', 'Azure'],
                    workloadPercentage: 95,
                    isTeamLead: true
                },
                {
                    id: '202',
                    userId: 'u202',
                    name: 'Can Kaya',
                    email: '<EMAIL>',
                    role: TeamRole.BACKEND_DEVELOPER,
                    joinDate: new Date('2024-01-15'),
                    status: MemberStatus.ACTIVE,
                    skills: ['Node.js', 'MongoDB', 'Docker'],
                    workloadPercentage: 75,
                    isTeamLead: false
                }
            ],
            createdAt: new Date('2024-01-01'),
            updatedAt: new Date('2024-07-15'),
            status: TeamStatus.ACTIVE,
            activeProjects: 2,
            completedProjects: 3,
            totalProjects: 5,
            budget: 180000,
            location: 'İstanbul Ofis',
            technologies: ['.NET Core', 'Node.js', 'SQL Server', 'MongoDB'],
            notes: 'Güvenilir backend çözümleri sunan takım'
        },
        {
            id: '3',
            teamCode: 'TM-2024-003',
            name: 'Mobile Takımı',
            description: 'Mobil uygulama geliştirme takımı. iOS ve Android platformları için native ve cross-platform çözümler.',
            department: Department.DEVELOPMENT,
            teamLead: 'Ali Çelik',
            members: [
                {
                    id: '301',
                    userId: 'u301',
                    name: 'Ali Çelik',
                    email: '<EMAIL>',
                    role: TeamRole.TEAM_LEAD,
                    joinDate: new Date('2023-12-01'),
                    status: MemberStatus.ACTIVE,
                    skills: ['React Native', 'Flutter', 'Swift'],
                    workloadPercentage: 85,
                    isTeamLead: true
                }
            ],
            createdAt: new Date('2023-12-01'),
            updatedAt: new Date('2024-07-10'),
            status: TeamStatus.ACTIVE,
            activeProjects: 1,
            completedProjects: 2,
            totalProjects: 3,
            budget: 120000,
            location: 'Ankara Ofis',
            technologies: ['React Native', 'Flutter', 'Swift', 'Kotlin'],
            notes: 'Yeni kurulan mobil takımı'
        }
    ];

    getTeams(): Observable<Team[]> {
        return of(this.mockTeams);
    }

    getTeamById(id: string): Observable<Team | undefined> {
        return of(this.mockTeams.find(team => team.id === id));
    }

    createTeam(teamData: TeamFormData): Observable<Team> {
        const newTeam: Team = {
            id: Date.now().toString(),
            ...teamData,
            members: [], // Üyeler ayrı bir API çağrısı ile eklenecek
            createdAt: new Date(),
            updatedAt: new Date(),
            activeProjects: 0,
            completedProjects: 0,
            totalProjects: 0,
            technologies: []
        };
        this.mockTeams.push(newTeam);
        return of(newTeam);
    }

    updateTeam(id: string, teamData: TeamFormData): Observable<Team | undefined> {
        const index = this.mockTeams.findIndex(team => team.id === id);
        if (index === -1) return of(undefined);

        const updatedTeam: Team = {
            ...this.mockTeams[index],
            ...teamData,
            updatedAt: new Date()
        };
        this.mockTeams[index] = updatedTeam;
        return of(updatedTeam);
    }

    deleteTeam(id: string): Observable<boolean> {
        const index = this.mockTeams.findIndex(team => team.id === id);
        if (index === -1) return of(false);
        
        this.mockTeams.splice(index, 1);
        return of(true);
    }
}

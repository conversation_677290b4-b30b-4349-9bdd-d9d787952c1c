import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { TabViewModule } from 'primeng/tabview';
import { TagModule } from 'primeng/tag';
import { CardModule } from 'primeng/card';
import { TimelineModule } from 'primeng/timeline';
import { Document, DocumentStatus, DocumentVisibility, DocumentFormat } from '../../models/document.model';
import { DocumentService } from '../../services/document.service';

@Component({
    selector: 'app-document-detail',
    standalone: true,
    imports: [
        CommonModule,
        ButtonModule,
        TabViewModule,
        TagModule,
        CardModule,
        TimelineModule
    ],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <div>
                            <h5 class="m-0"><PERSON><PERSON><PERSON><PERSON>ayı</h5>
                            <p class="text-500 mt-1">{{ document?.documentCode }}</p>
                        </div>
                        <div class="flex gap-2">
                            <p-button 
                                label="İndir" 
                                icon="pi pi-download" 
                                class="p-button-success"
                                (onClick)="downloadDocument()"
                                *ngIf="document?.filePath">
                            </p-button>
                            <p-button 
                                label="Aç" 
                                icon="pi pi-external-link" 
                                class="p-button-help"
                                (onClick)="openDocument()"
                                *ngIf="document?.url">
                            </p-button>
                            <p-button 
                                label="Düzenle" 
                                icon="pi pi-pencil" 
                                class="p-button-warning"
                                (onClick)="editDocument()"
                                [disabled]="document?.isArchived">
                            </p-button>
                            <p-button 
                                label="Geri" 
                                icon="pi pi-arrow-left" 
                                class="p-button-secondary"
                                (onClick)="goBack()">
                            </p-button>
                        </div>
                    </div>

                    <p-tabView *ngIf="document">
                        <p-tabPanel header="Doküman Bilgileri" leftIcon="pi pi-file">
                            <div class="grid">
                                <div class="col-12 md:col-8">
                                    <p-card header="Temel Bilgiler">
                                        <div class="field">
                                            <label class="font-medium">Başlık:</label>
                                            <div class="mt-1 text-xl font-bold">{{ document.title }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Açıklama:</label>
                                            <div class="mt-1">{{ document.description }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Yazar:</label>
                                            <div class="mt-1">{{ document.authorName }}</div>
                                        </div>
                                        <div class="field" *ngIf="document.reviewerName">
                                            <label class="font-medium">İnceleyici:</label>
                                            <div class="mt-1">{{ document.reviewerName }}</div>
                                        </div>
                                        <div class="field" *ngIf="document.approvedByName">
                                            <label class="font-medium">Onaylayan:</label>
                                            <div class="mt-1">{{ document.approvedByName }}</div>
                                        </div>
                                        <div class="field" *ngIf="document.projectName">
                                            <label class="font-medium">Proje:</label>
                                            <div class="mt-1">{{ document.projectName }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Versiyon:</label>
                                            <div class="mt-1">
                                                <span class="font-bold text-primary text-lg">{{ document.version }}</span>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                                <div class="col-12 md:col-4">
                                    <p-card header="Durum ve Özellikler">
                                        <div class="field">
                                            <label class="font-medium">Durum:</label>
                                            <div class="mt-1">
                                                <p-tag 
                                                    [value]="document.status" 
                                                    [severity]="getStatusSeverity(document.status)">
                                                </p-tag>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Görünürlük:</label>
                                            <div class="mt-1">
                                                <p-tag 
                                                    [value]="document.visibility" 
                                                    [severity]="getVisibilitySeverity(document.visibility)">
                                                </p-tag>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Tip:</label>
                                            <div class="mt-1">
                                                <p-tag [value]="document.type" severity="info"></p-tag>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Kategori:</label>
                                            <div class="mt-1">
                                                <p-tag [value]="document.category" severity="secondary"></p-tag>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Format:</label>
                                            <div class="mt-1 flex align-items-center gap-2">
                                                <i [class]="getFormatIcon(document.format)" class="text-lg"></i>
                                                <span>{{ document.format }}</span>
                                            </div>
                                        </div>
                                        <div class="field" *ngIf="document.fileSize">
                                            <label class="font-medium">Dosya Boyutu:</label>
                                            <div class="mt-1">{{ formatFileSize(document.fileSize) }}</div>
                                        </div>
                                        <div class="field" *ngIf="document.isTemplate || document.isArchived">
                                            <label class="font-medium">Özellikler:</label>
                                            <div class="mt-1 flex gap-1">
                                                <p-tag value="Şablon" severity="info" class="text-xs" *ngIf="document.isTemplate"></p-tag>
                                                <p-tag value="Arşivlendi" severity="secondary" class="text-xs" *ngIf="document.isArchived"></p-tag>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                            </div>
                        </p-tabPanel>

                        <p-tabPanel header="İstatistikler ve Tarihler" leftIcon="pi pi-chart-line">
                            <div class="grid">
                                <div class="col-12 md:col-6">
                                    <p-card header="İstatistikler">
                                        <div class="flex justify-content-around text-center">
                                            <div>
                                                <div class="text-2xl font-bold text-blue-500">{{ document.viewCount }}</div>
                                                <div class="text-500">Görüntülenme</div>
                                            </div>
                                            <div>
                                                <div class="text-2xl font-bold text-green-500">{{ document.downloadCount }}</div>
                                                <div class="text-500">İndirme</div>
                                            </div>
                                            <div>
                                                <div class="text-2xl font-bold text-orange-500">{{ document.revisions.length + 1 }}</div>
                                                <div class="text-500">Versiyon</div>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                                <div class="col-12 md:col-6">
                                    <p-card header="Önemli Tarihler">
                                        <div class="field">
                                            <label class="font-medium">Oluşturulma:</label>
                                            <div class="mt-1">{{ document.createdAt | date:'dd/MM/yyyy HH:mm' }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Son Güncelleme:</label>
                                            <div class="mt-1">{{ document.updatedAt | date:'dd/MM/yyyy HH:mm' }}</div>
                                        </div>
                                        <div class="field" *ngIf="document.publishedAt">
                                            <label class="font-medium">Yayınlanma:</label>
                                            <div class="mt-1">{{ document.publishedAt | date:'dd/MM/yyyy HH:mm' }}</div>
                                        </div>
                                        <div class="field" *ngIf="document.reviewedAt">
                                            <label class="font-medium">İnceleme:</label>
                                            <div class="mt-1">{{ document.reviewedAt | date:'dd/MM/yyyy HH:mm' }}</div>
                                        </div>
                                        <div class="field" *ngIf="document.approvedAt">
                                            <label class="font-medium">Onaylama:</label>
                                            <div class="mt-1">{{ document.approvedAt | date:'dd/MM/yyyy HH:mm' }}</div>
                                        </div>
                                        <div class="field" *ngIf="document.expiryDate">
                                            <label class="font-medium">Son Kullanma:</label>
                                            <div class="mt-1 text-orange-600">{{ document.expiryDate | date:'dd/MM/yyyy' }}</div>
                                        </div>
                                    </p-card>
                                </div>
                            </div>
                        </p-tabPanel>

                        <p-tabPanel header="Ekler ve Versiyonlar" leftIcon="pi pi-paperclip">
                            <div class="grid">
                                <div class="col-12 md:col-6">
                                    <p-card header="Ekler">
                                        <div *ngIf="document.attachments.length === 0" class="text-center py-4">
                                            <i class="pi pi-info-circle text-4xl text-500 mb-3"></i>
                                            <div class="text-500 font-medium">Bu doküman için henüz ek dosya bulunmuyor</div>
                                        </div>
                                        <div *ngIf="document.attachments.length > 0">
                                            <div *ngFor="let attachment of document.attachments" class="border-1 border-200 border-round p-3 mb-2">
                                                <div class="flex align-items-center gap-3">
                                                    <i class="pi pi-file text-2xl text-blue-500"></i>
                                                    <div class="flex-1">
                                                        <div class="font-medium">{{ attachment.name }}</div>
                                                        <div class="text-sm text-500">{{ attachment.fileName }}</div>
                                                        <div class="text-xs text-500 mt-1">
                                                            {{ formatFileSize(attachment.fileSize) }} • 
                                                            {{ attachment.uploadedAt | date:'dd/MM/yyyy' }} • 
                                                            {{ attachment.uploadedBy }}
                                                        </div>
                                                    </div>
                                                    <p-button 
                                                        icon="pi pi-download" 
                                                        class="p-button-rounded p-button-text p-button-sm"
                                                        pTooltip="İndir">
                                                    </p-button>
                                                </div>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                                <div class="col-12 md:col-6">
                                    <p-card header="Versiyon Geçmişi">
                                        <div *ngIf="document.revisions.length === 0" class="text-center py-4">
                                            <i class="pi pi-info-circle text-4xl text-500 mb-3"></i>
                                            <div class="text-500 font-medium">Bu doküman için henüz versiyon geçmişi bulunmuyor</div>
                                        </div>
                                        <p-timeline 
                                            *ngIf="document.revisions.length > 0"
                                            [value]="document.revisions" 
                                            align="left">
                                            <ng-template pTemplate="content" let-revision>
                                                <div class="border-1 border-200 border-round p-3">
                                                    <div class="font-medium text-primary">Versiyon {{ revision.version }}</div>
                                                    <div class="text-sm text-500 mt-1">{{ revision.description }}</div>
                                                    <div class="text-xs text-500 mt-2">
                                                        {{ revision.createdAt | date:'dd/MM/yyyy HH:mm' }} • {{ revision.createdBy }}
                                                    </div>
                                                    <div *ngIf="revision.changes.length > 0" class="mt-2">
                                                        <div class="text-sm font-medium">Değişiklikler:</div>
                                                        <ul class="text-sm text-500 mt-1 pl-3">
                                                            <li *ngFor="let change of revision.changes">{{ change }}</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </ng-template>
                                        </p-timeline>
                                    </p-card>
                                </div>
                            </div>
                        </p-tabPanel>

                        <p-tabPanel header="Yorumlar ve Metadata" leftIcon="pi pi-comments">
                            <div class="grid">
                                <div class="col-12 md:col-6">
                                    <p-card header="Yorumlar">
                                        <div *ngIf="document.comments.length === 0" class="text-center py-4">
                                            <i class="pi pi-info-circle text-4xl text-500 mb-3"></i>
                                            <div class="text-500 font-medium">Bu doküman için henüz yorum bulunmuyor</div>
                                        </div>
                                        <div *ngIf="document.comments.length > 0">
                                            <div *ngFor="let comment of document.comments" class="border-1 border-200 border-round p-3 mb-3">
                                                <div class="flex align-items-start gap-2">
                                                    <i class="pi pi-user text-blue-500 mt-1"></i>
                                                    <div class="flex-1">
                                                        <div class="font-medium">{{ comment.authorName }}</div>
                                                        <div class="text-sm text-500">{{ comment.createdAt | date:'dd/MM/yyyy HH:mm' }}</div>
                                                        <div class="mt-2">{{ comment.content }}</div>
                                                        <div class="mt-2" *ngIf="comment.isResolved">
                                                            <p-tag value="Çözüldü" severity="success" class="text-xs"></p-tag>
                                                        </div>
                                                        <div *ngIf="comment.replies.length > 0" class="ml-4 mt-3">
                                                            <div *ngFor="let reply of comment.replies" class="border-l-3 border-blue-200 pl-3 mb-2">
                                                                <div class="font-medium text-sm">{{ reply.authorName }}</div>
                                                                <div class="text-xs text-500">{{ reply.createdAt | date:'dd/MM/yyyy HH:mm' }}</div>
                                                                <div class="text-sm mt-1">{{ reply.content }}</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                                <div class="col-12 md:col-6">
                                    <p-card header="Metadata">
                                        <div class="field" *ngIf="document.metadata.keywords.length > 0">
                                            <label class="font-medium">Anahtar Kelimeler:</label>
                                            <div class="mt-2 flex flex-wrap gap-1">
                                                <p-tag *ngFor="let keyword of document.metadata.keywords" [value]="keyword" severity="info" class="text-xs"></p-tag>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Dil:</label>
                                            <div class="mt-1">{{ document.metadata.language }}</div>
                                        </div>
                                        <div class="field" *ngIf="document.metadata.audience.length > 0">
                                            <label class="font-medium">Hedef Kitle:</label>
                                            <div class="mt-2 flex flex-wrap gap-1">
                                                <p-tag *ngFor="let audience of document.metadata.audience" [value]="audience" severity="secondary" class="text-xs"></p-tag>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Zorluk Seviyesi:</label>
                                            <div class="mt-1">
                                                <p-tag [value]="document.metadata.difficulty" severity="warning"></p-tag>
                                            </div>
                                        </div>
                                        <div class="field" *ngIf="document.metadata.estimatedReadTime > 0">
                                            <label class="font-medium">Tahmini Okuma Süresi:</label>
                                            <div class="mt-1">{{ document.metadata.estimatedReadTime }} dakika</div>
                                        </div>
                                        <div class="field" *ngIf="document.metadata.nextReviewDate">
                                            <label class="font-medium">Sonraki İnceleme:</label>
                                            <div class="mt-1 text-orange-600">{{ document.metadata.nextReviewDate | date:'dd/MM/yyyy' }}</div>
                                        </div>
                                        <div class="field" *ngIf="document.tags.length > 0">
                                            <label class="font-medium">Etiketler:</label>
                                            <div class="mt-2 flex flex-wrap gap-1">
                                                <p-tag *ngFor="let tag of document.tags" [value]="tag" severity="help" class="text-xs"></p-tag>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                            </div>
                        </p-tabPanel>

                        <p-tabPanel header="Notlar" leftIcon="pi pi-file-edit" *ngIf="document.notes">
                            <div class="grid">
                                <div class="col-12">
                                    <p-card>
                                        <div class="white-space-pre-line">{{ document.notes }}</div>
                                    </p-card>
                                </div>
                            </div>
                        </p-tabPanel>
                    </p-tabView>
                </div>
            </div>
        </div>
    `
})
export class DocumentDetailComponent implements OnInit {
    document: Document | null = null;
    documentId: string = '';

    constructor(
        private route: ActivatedRoute,
        private router: Router,
        private documentService: DocumentService
    ) {}

    ngOnInit() {
        this.documentId = this.route.snapshot.params['id'];
        this.loadDocument();
    }

    loadDocument() {
        this.documentService.getDocumentById(this.documentId).subscribe(document => {
            this.document = document || null;
        });
    }

    editDocument() {
        this.router.navigate(['/pages/documents', this.documentId, 'edit']);
    }

    downloadDocument() {
        if (this.document) {
            this.documentService.incrementDownloadCount(this.document.id).subscribe();
            // Gerçek uygulamada dosya indirme işlemi burada yapılır
        }
    }

    openDocument() {
        if (this.document?.url) {
            this.documentService.incrementViewCount(this.document.id).subscribe();
            window.open(this.document.url, '_blank');
        }
    }

    goBack() {
        this.router.navigate(['/pages/documents']);
    }

    getStatusSeverity(status: DocumentStatus): string {
        switch (status) {
            case DocumentStatus.DRAFT:
                return 'secondary';
            case DocumentStatus.IN_REVIEW:
                return 'warning';
            case DocumentStatus.APPROVED:
                return 'info';
            case DocumentStatus.PUBLISHED:
                return 'success';
            case DocumentStatus.ARCHIVED:
                return 'secondary';
            case DocumentStatus.REJECTED:
                return 'danger';
            case DocumentStatus.EXPIRED:
                return 'danger';
            case DocumentStatus.WITHDRAWN:
                return 'warning';
            default:
                return 'info';
        }
    }

    getVisibilitySeverity(visibility: DocumentVisibility): string {
        switch (visibility) {
            case DocumentVisibility.PUBLIC:
                return 'success';
            case DocumentVisibility.INTERNAL:
                return 'info';
            case DocumentVisibility.TEAM:
                return 'warning';
            case DocumentVisibility.PROJECT:
                return 'secondary';
            case DocumentVisibility.CONFIDENTIAL:
                return 'danger';
            case DocumentVisibility.RESTRICTED:
                return 'danger';
            default:
                return 'info';
        }
    }

    getFormatIcon(format: DocumentFormat): string {
        switch (format) {
            case DocumentFormat.PDF:
                return 'pi pi-file-pdf text-red-500';
            case DocumentFormat.WORD:
                return 'pi pi-file-word text-blue-500';
            case DocumentFormat.EXCEL:
                return 'pi pi-file-excel text-green-500';
            case DocumentFormat.POWERPOINT:
                return 'pi pi-file text-orange-500';
            case DocumentFormat.MARKDOWN:
                return 'pi pi-code text-purple-500';
            case DocumentFormat.HTML:
                return 'pi pi-globe text-blue-500';
            case DocumentFormat.TEXT:
                return 'pi pi-file text-gray-500';
            case DocumentFormat.IMAGE:
                return 'pi pi-image text-pink-500';
            case DocumentFormat.VIDEO:
                return 'pi pi-video text-red-500';
            case DocumentFormat.AUDIO:
                return 'pi pi-volume-up text-green-500';
            default:
                return 'pi pi-file text-gray-500';
        }
    }

    formatFileSize(bytes: number): string {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

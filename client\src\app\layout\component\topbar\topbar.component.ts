import { Component, OnInit } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { MenuItem } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { MenuModule } from 'primeng/menu';
import { AvatarModule } from 'primeng/avatar';
import { AuthService } from '../../../core/services/auth.service';

@Component({
    selector: 'app-topbar',
    standalone: true,
    imports: [RouterLink, ButtonModule, MenuModule, AvatarModule],
    template: `
        <div class="layout-topbar">
            <div class="layout-topbar-left">
                <a class="layout-topbar-logo" routerLink="/">
                    <img src="assets/layout/images/logo.svg" alt="Logo">
                    <span>TRtek EYS</span>
                </a>
            </div>
            <div class="layout-topbar-right">
                <div class="layout-topbar-actions">
                    <div class="layout-topbar-menu">
                        <div class="layout-topbar-menu-content">
                            <button class="layout-topbar-action" (click)="userMenu.toggle($event)">
                                <i class="pi pi-user"></i>
                            </button>
                            <p-menu #userMenu [popup]="true" [model]="menuItems" appendTo="body"></p-menu>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `,
    styles: [`
        .layout-topbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 2rem;
            background-color: var(--surface-card);
            border-bottom: 1px solid var(--surface-border);
        }
        .layout-topbar-logo {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: var(--text-color);
            img {
                height: 2.5rem;
                margin-right: 0.5rem;
            }
            span {
                font-size: 1.5rem;
                font-weight: 500;
            }
        }
        .layout-topbar-right {
            display: flex;
            align-items: center;
        }
        .layout-topbar-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .layout-topbar-menu {
            display: flex;
            align-items: center;
        }
        .layout-topbar-action {
            width: 3rem;
            height: 3rem;
            border: none;
            border-radius: 50%;
            background-color: transparent;
            transition: background-color 0.2s;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;

            &:hover {
                background-color: var(--surface-hover);
            }

            i {
                font-size: 1.25rem;
                color: var(--text-color-secondary);
            }
        }
    `]
})
export class TopbarComponent implements OnInit {
    menuItems: MenuItem[] = [];

    constructor(
        private authService: AuthService,
        private router: Router
    ) {}

    ngOnInit() {
        this.initializeMenu();

        // Kullanıcı durumu değiştiğinde menu'yu güncelle
        this.authService.currentUser$.subscribe(() => {
            this.initializeMenu();
        });
    }

    private initializeMenu() {
        if (this.currentUser) {
            this.menuItems = [
                {
                    label: this.currentUser.firstName + ' ' + this.currentUser.lastName,
                    items: [
                        {
                            label: 'Profil',
                            icon: 'pi pi-user',
                            routerLink: '/auth/profile'
                        },
                        {
                            separator: true
                        },
                        {
                            label: 'Çıkış Yap',
                            icon: 'pi pi-power-off',
                            command: () => this.logout()
                        }
                    ]
                }
            ];
        } else {
            this.menuItems = [
                {
                    label: 'Giriş Yap',
                    icon: 'pi pi-sign-in',
                    routerLink: '/auth/login'
                }
            ];
        }
    }

    get currentUser() {
        const auth = this.authService.getCurrentUser();
        return auth?.user;
    }

    get currentUserName(): string {
        return this.currentUser ? `${this.currentUser.firstName} ${this.currentUser.lastName}` : 'Kullanıcı';
    }

    logout() {
        this.authService.logout();
        this.router.navigate(['/auth/login']);
    }
}

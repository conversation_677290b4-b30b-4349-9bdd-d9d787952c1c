import { Component, OnInit } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { MenuItem } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { MenuModule } from 'primeng/menu';
import { AvatarModule } from 'primeng/avatar';
import { CommonModule } from '@angular/common';
import { AuthService } from '../../../core/services/auth.service';

@Component({
    selector: 'app-topbar',
    standalone: true,
    imports: [CommonModule, RouterLink, ButtonModule, MenuModule, AvatarModule],
    template: `
        <div class="layout-topbar">
            <div class="layout-topbar-left">
                <a class="layout-topbar-logo" routerLink="/">
                    <img src="assets/layout/images/logo.svg" alt="Logo">
                    <span>TRtek EYS</span>
                </a>
            </div>
            <div class="layout-topbar-right">
                <div class="layout-topbar-actions">
                    <div class="layout-topbar-menu">
                        <div class="layout-topbar-menu-content">
                            <button class="layout-topbar-action user-button" (click)="userMenu.toggle($event)">
                                <div class="user-info" *ngIf="currentUser; else loginButton">
                                    <p-avatar
                                        [label]="getUserInitials()"
                                        styleClass="mr-2"
                                        size="normal"
                                        [style]="{'background-color': '#B71C1C', 'color': 'white'}">
                                    </p-avatar>
                                    <span class="user-name hidden-mobile">{{ currentUserName }}</span>
                                    <i class="pi pi-chevron-down ml-2"></i>
                                </div>
                                <ng-template #loginButton>
                                    <i class="pi pi-user mr-2"></i>
                                    <span>Giriş Yap</span>
                                </ng-template>
                            </button>
                            <p-menu #userMenu [popup]="true" [model]="menuItems" appendTo="body"></p-menu>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `,
    styles: [`
        .layout-topbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 2rem;
            background-color: var(--surface-card);
            border-bottom: 1px solid var(--surface-border);
        }
        .layout-topbar-logo {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: var(--text-color);
            img {
                height: 2.5rem;
                margin-right: 0.5rem;
            }
            span {
                font-size: 1.5rem;
                font-weight: 500;
            }
        }
        .layout-topbar-right {
            display: flex;
            align-items: center;
        }
        .layout-topbar-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .layout-topbar-menu {
            display: flex;
            align-items: center;
        }
        .layout-topbar-action {
            border: none;
            background-color: transparent;
            border-radius: 8px;
            transition: background-color 0.2s;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            min-height: 3rem;

            &:hover {
                background-color: var(--surface-hover);
            }

            i {
                font-size: 1.25rem;
                color: var(--text-color-secondary);
            }

            &.user-button {
                .user-info {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;

                    .user-name {
                        font-weight: 500;
                        color: var(--text-color);
                        white-space: nowrap;
                        max-width: 150px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    .pi-chevron-down {
                        font-size: 0.875rem;
                        color: var(--text-color-secondary);
                    }
                }
            }
        }

        @media (max-width: 768px) {
            .hidden-mobile {
                display: none !important;
            }

            .layout-topbar {
                padding: 1rem;
            }

            .layout-topbar-action.user-button {
                padding: 0.5rem;
                min-width: 3rem;
            }
        }
    `]
})
export class TopbarComponent implements OnInit {
    menuItems: MenuItem[] = [];

    constructor(
        private authService: AuthService,
        private router: Router
    ) {}

    ngOnInit() {
        this.initializeMenu();

        // Kullanıcı durumu değiştiğinde menu'yu güncelle
        this.authService.currentUser$.subscribe(() => {
            this.initializeMenu();
        });
    }

    private initializeMenu() {
        if (this.currentUser) {
            this.menuItems = [
                {
                    label: this.currentUserName,
                    items: [
                        {
                            label: 'Profil',
                            icon: 'pi pi-user',
                            routerLink: '/auth/profile'
                        },
                        {
                            label: 'Ayarlar',
                            icon: 'pi pi-cog',
                            routerLink: '/settings'
                        },
                        {
                            separator: true
                        },
                        {
                            label: 'Çıkış Yap',
                            icon: 'pi pi-power-off',
                            command: () => this.logout()
                        }
                    ]
                }
            ];
        } else {
            this.menuItems = [
                {
                    label: 'Giriş Yap',
                    icon: 'pi pi-sign-in',
                    routerLink: '/auth/login'
                },
                {
                    label: 'Kayıt Ol',
                    icon: 'pi pi-user-plus',
                    routerLink: '/auth/register'
                }
            ];
        }
    }

    get currentUser() {
        const auth = this.authService.getCurrentUser();
        return auth?.user;
    }

    get currentUserName(): string {
        return this.currentUser ? `${this.currentUser.firstName} ${this.currentUser.lastName}` : 'Kullanıcı';
    }

    getUserInitials(): string {
        if (!this.currentUser) return 'U';

        const firstName = this.currentUser.firstName || '';
        const lastName = this.currentUser.lastName || '';

        return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
    }

    logout() {
        this.authService.logout();
        this.router.navigate(['/auth/login']);
    }
}

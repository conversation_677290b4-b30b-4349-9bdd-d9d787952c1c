﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Contracts.Commands.Delete
{
    public class DeleteContractCommandHandler : IRequestHandler<DeleteContractCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public DeleteContractCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(DeleteContractCommand request, CancellationToken cancellationToken)
        {
            var contract = await _context.Contracts
                .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken);

            if (contract is null)
                throw new Exception("Silinecek sözleşme bulunamadı.");

            _context.Contracts.Remove(contract);
            await _context.SaveChangesAsync(cancellationToken);

            return contract.Id;
        }
    }
}

export enum Permission {
    // Teams
    TEAMS_VIEW = 'TEAMS_VIEW',
    TEAMS_CREATE = 'TEAMS_CREATE',
    TEAMS_EDIT = 'TEAMS_EDIT',
    TEAMS_DELETE = 'TEAMS_DELETE',
    
    // Roles
    MANAGE_ROLES = '<PERSON>NA<PERSON>_ROLES',
    VIEW_ROLES = 'VIEW_ROLES',
    
    // Customers
    MANAGE_CUSTOMERS = 'MANAGE_CUSTOMERS',
    VIEW_CUSTOMERS = 'VIEW_CUSTOMERS',
    MANAGE_CUSTOMER_PROJECT_RELATIONS = 'MANAGE_CUSTOMER_PROJECT_RELATIONS',
    MANAGE_CONTRACTS = 'MANAGE_CONTRACTS',
    VIEW_CONTRACTS = 'VIEW_CONTRACTS',
    MANAGE_CUSTOMER_CONFIGS = 'MANAGE_CUSTOMER_CONFIGS',
    VIEW_CUSTOMER_CONFIGS = 'VIEW_CUSTOMER_CONFIGS',
    
    // Projects
    MANAGE_PROJECTS = 'MANAGE_PROJECTS',
    VIEW_PROJECTS = 'VIEW_PROJECTS',
    MANAGE_PLATFORMS = 'MANAGE_PLATFORMS',
    VIEW_PLATFORMS = 'VIEW_PLATFORMS',
    MANAGE_TECHNOLOGIES = 'MANAGE_TECHNOLOGIES',
    VIEW_TECHNOLOGIES = 'VIEW_TECHNOLOGIES',
    MANAGE_DOCUMENTS = 'MANAGE_DOCUMENTS',
    VIEW_DOCUMENTS = 'VIEW_DOCUMENTS',
    
    // Team Management
    ASSIGN_TEAM_MEMBERS = 'ASSIGN_TEAM_MEMBERS',
    MANAGE_TEAM_ROLES = 'MANAGE_TEAM_ROLES',
    
    // Repository
    MANAGE_REPOSITORY = 'MANAGE_REPOSITORY',
    VIEW_REPOSITORY = 'VIEW_REPOSITORY',
    
    // Versions
    MANAGE_PROJECT_VERSIONS = 'MANAGE_PROJECT_VERSIONS',
    VIEW_PROJECT_VERSIONS = 'VIEW_PROJECT_VERSIONS'
}

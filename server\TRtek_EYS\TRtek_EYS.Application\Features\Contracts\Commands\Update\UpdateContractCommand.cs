﻿using MediatR;

namespace TRtek_EYS.Application.Features.Contracts.Commands.Update
{
    public class UpdateContractCommand : IRequest<Guid>
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = null!;
        public string Number { get; set; } = null!;
        public int Status { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string? FilePath { get; set; }
        public Guid CustomerId { get; set; }
        public Guid ProjectId { get; set; }
    }
}

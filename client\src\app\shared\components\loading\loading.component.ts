import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { LoadingService } from '../../services/loading.service';
import { Observable } from 'rxjs';

@Component({
    selector: 'app-loading',
    standalone: true,
    imports: [CommonModule, ProgressSpinnerModule],
    template: `
        <div class="loading-overlay" *ngIf="loading$ | async">
            <div class="loading-container">
                <p-progressSpinner 
                    styleClass="w-4rem h-4rem" 
                    strokeWidth="4" 
                    fill="transparent"
                    animationDuration=".8s">
                </p-progressSpinner>
                <p class="loading-text">Yükleniyor...</p>
            </div>
        </div>
    `,
    styles: [`
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .loading-text {
            margin: 0;
            color: #6c757d;
            font-size: 0.9rem;
        }
    `]
})
export class LoadingComponent implements OnInit {
    loading$: Observable<boolean>;

    constructor(private loadingService: LoadingService) {
        this.loading$ = this.loadingService.loading$;
    }

    ngOnInit(): void {}
}

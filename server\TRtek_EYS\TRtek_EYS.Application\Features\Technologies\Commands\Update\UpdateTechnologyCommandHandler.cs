﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Technologies.Commands.Update
{
    public class UpdateTechnologyCommandHandler : IRequestHandler<UpdateTechnologyCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public UpdateTechnologyCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(UpdateTechnologyCommand request, CancellationToken cancellationToken)
        {
            var tech = await _context.Technologies
                .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);

            if (tech is null)
                throw new Exception("Teknoloji bulunamadı.");

            tech.Language = request.Language;
            tech.Framework = request.Framework;
            tech.Version = request.Version;
            tech.ProjectId = request.ProjectId;

            await _context.SaveChangesAsync(cancellationToken);

            return tech.Id;
        }
    }
}

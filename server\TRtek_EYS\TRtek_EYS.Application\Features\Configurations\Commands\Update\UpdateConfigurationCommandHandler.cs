﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Configurations.Commands.Update
{
    public class UpdateConfigurationCommandHandler : IRequestHandler<UpdateConfigurationCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public UpdateConfigurationCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(UpdateConfigurationCommand request, CancellationToken cancellationToken)
        {
            var entity = await _context.Configurations
                .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

            if (entity is null)
                throw new Exception("Konfigürasyon bulunamadı.");

            entity.Name = request.Name;
            entity.Description = request.Description;
            entity.WebServerIp = request.WebServerIp;
            entity.WebServerUrl = request.WebServerUrl;
            entity.DatabaseType = request.DatabaseType;
            entity.DatabaseVersion = request.DatabaseVersion;
            entity.Username = request.Username;
            entity.EncryptedPassword = request.EncryptedPassword;
            entity.CustomerId = request.CustomerId;

            await _context.SaveChangesAsync(cancellationToken);

            return entity.Id;
        }
    }
}

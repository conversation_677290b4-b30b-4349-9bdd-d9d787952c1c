import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { ButtonModule } from 'primeng/button';
import { SkeletonModule } from 'primeng/skeleton';
import { RouterModule } from '@angular/router';
import { CustomerService } from '../../customers/services/customer.service';
import { Customer } from '../../customers/models/customer.model';

interface CustomerOverview {
    id: string;
    name: string;
    code: string;
    industry: string;
    status: string;
    activeProjects: number;
    totalProjects: number;
    lastContact: Date;
}

@Component({
    standalone: true,
    selector: 'app-customer-overview-widget',
    imports: [CommonModule, TableModule, TagModule, ButtonModule, SkeletonModule, RouterModule],
    template: `<div class="card !mb-8">
        <div class="flex justify-between align-items-center mb-5">
            <div class="font-semibold text-xl">Müşteri Genel Bakış</div>
            <p-button 
                label="Tümünü Gör" 
                size="small" 
                [outlined]="true"
                routerLink="/pages/customers">
            </p-button>
        </div>
        
        <div *ngIf="isLoading" class="space-y-4">
            <div *ngFor="let item of [1,2,3,4,5]">
                <p-skeleton height="3rem"></p-skeleton>
            </div>
        </div>
        
        <p-table 
            *ngIf="!isLoading && customers.length > 0" 
            [value]="customers" 
            [rows]="5" 
            [paginator]="true" 
            responsiveLayout="scroll">
            <ng-template pTemplate="header">
                <tr>
                    <th>Müşteri</th>
                    <th>Sektör</th>
                    <th>Durum</th>
                    <th>Projeler</th>
                    <th>Son İletişim</th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-customer>
                <tr class="cursor-pointer hover:bg-surface-hover" 
                    [routerLink]="['/pages/customers', customer.id]">
                    <td style="min-width: 12rem;">
                        <div class="font-medium">{{ customer.name }}</div>
                        <div class="text-sm text-muted-color">{{ customer.code }}</div>
                    </td>
                    <td style="min-width: 8rem;">{{ customer.industry }}</td>
                    <td style="min-width: 8rem;">
                        <p-tag [value]="customer.status" [severity]="getStatusSeverity(customer.status)"></p-tag>
                    </td>
                    <td style="min-width: 8rem;">
                        <div class="flex align-items-center gap-2">
                            <span class="font-medium text-primary">{{ customer.activeProjects }}</span>
                            <span class="text-muted-color">/ {{ customer.totalProjects }}</span>
                        </div>
                    </td>
                    <td style="min-width: 10rem;">{{ customer.lastContact | date:'dd/MM/yyyy' }}</td>
                </tr>
            </ng-template>
        </p-table>
        
        <div *ngIf="!isLoading && customers.length === 0" class="text-center py-8">
            <i class="pi pi-info-circle text-4xl text-muted-color mb-4"></i>
            <div class="text-muted-color font-medium">Henüz müşteri bulunmuyor</div>
        </div>
    </div>`,
    styles: [`
        .space-y-4 > * + * {
            margin-top: 1rem;
        }
    `]
})
export class CustomerOverviewWidget implements OnInit, OnDestroy {
    private destroy$ = new Subject<void>();
    
    customers: CustomerOverview[] = [];
    isLoading = true;

    constructor(private customerService: CustomerService) {}

    ngOnInit() {
        this.loadCustomers();
    }

    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
    }

    loadCustomers() {
        this.isLoading = true;
        
        this.customerService.getCustomers()
            .pipe(takeUntil(this.destroy$))
            .subscribe((customers: Customer[]) => {
                this.customers = customers
                    .map((c: Customer) => ({
                        id: c.id,
                        name: c.companyName,
                        code: c.customerCode,
                        industry: c.industry,
                        status: c.status,
                        activeProjects: c.activeProjects,
                        totalProjects: c.totalProjects,
                        lastContact: c.lastContactDate || c.registrationDate
                    }))
                    .sort((a: CustomerOverview, b: CustomerOverview) => b.lastContact.getTime() - a.lastContact.getTime())
                    .slice(0, 5);

                this.isLoading = false;
            });
    }

    getStatusSeverity(status: string): string {
        switch (status) {
            case 'Aktif':
                return 'success';
            case 'Potansiyel':
                return 'info';
            case 'Beklemede':
                return 'warning';
            case 'Pasif':
                return 'danger';
            default:
                return 'secondary';
        }
    }
}

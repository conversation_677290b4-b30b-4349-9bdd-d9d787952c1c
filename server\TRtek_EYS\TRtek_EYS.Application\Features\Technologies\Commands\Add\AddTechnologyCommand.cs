using MediatR;
using TRtek_EYS.Domain.Enums;

namespace TRtek_EYS.Application.Features.Technologies.Commands.Add
{
    public class AddTechnologyCommand : IRequest<Guid>
    {
        // Temel bilgiler
        public string Name { get; set; } = null!;
        public string? TechnologyCode { get; set; }
        public string? Description { get; set; }
        public TechnologyType? Type { get; set; }
        public TechnologyCategory? Category { get; set; }
        public string? Version { get; set; }

        // Vendor bilgileri
        public string? Vendor { get; set; }
        public string? Website { get; set; }
        public string? Documentation { get; set; }
        public string? Repository { get; set; }

        // Lisans ve durum
        public LicenseType? LicenseType { get; set; }
        public bool IsActive { get; set; } = true;
        public bool IsRecommended { get; set; } = false;
        public bool IsDeprecated { get; set; } = false;

        // İstatistikler
        public int? PopularityScore { get; set; }
        public DateTime? ReleaseDate { get; set; }
        public DateTime? LastUpdateDate { get; set; }

        // Ek bilgiler
        public string? Tags { get; set; }
        public string? Notes { get; set; }
        public string? Requirements { get; set; }
        public string? Features { get; set; }
        public string? Dependencies { get; set; }

        // Eski alanlar (backward compatibility)
        public string? Language { get; set; }
        public string? Framework { get; set; }
        public Guid? ProjectId { get; set; }
    }
}

﻿using System.Diagnostics.Contracts;
using System.Reflection.Metadata;
using TRtek_EYS.Domain.Enums;

namespace TRtek_EYS.Domain.Entities;

public class Project : BaseEntity
{
    // MEVCUT ALANLAR
    public string Name { get; set; } = null!;
    public string Code { get; set; } = null!;
    public string? Description { get; set; }
    public ProjectStatus Status { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public Guid CreatedByUserId { get; set; }
    public User? CreatedByUser { get; set; }

    // YENİ ALANLAR
    public ProjectType? ProjectType { get; set; }
    public ProjectPriority? Priority { get; set; }
    public decimal? Budget { get; set; }
    public string? Currency { get; set; }
    public decimal? ActualCost { get; set; }
    public int? EstimatedHours { get; set; }
    public int? ActualHours { get; set; }
    public int? ProgressPercentage { get; set; } = 0;
    public string? ProjectManager { get; set; }
    public string? TechnicalLead { get; set; }
    public string? ClientContact { get; set; }
    public string? Repository { get; set; }
    public string? DeploymentUrl { get; set; }
    public string? StagingUrl { get; set; }
    public string? DocumentationUrl { get; set; }
    public DateTime? ActualStartDate { get; set; }
    public DateTime? ActualEndDate { get; set; }
    public DateTime? DeploymentDate { get; set; }
    public string? Tags { get; set; }
    public string? Notes { get; set; }
    public bool IsActive { get; set; } = true;
    public bool IsPublic { get; set; } = false;
    public string? Methodology { get; set; } // Agile, Waterfall, etc.
    public int? TeamSize { get; set; }
    public string? RiskAssessment { get; set; }

    public ICollection<Platform> Platforms { get; set; } = new List<Platform>();
    public ICollection<Technology> Technologies { get; set; } = new List<Technology>();
    public ICollection<ProjectTeam> ProjectTeams { get; set; } = new List<ProjectTeam>();
    public ICollection<CustomerProject> CustomerProjects { get; set; } = new List<CustomerProject>();
    public ICollection<Document> Documents { get; set; } = new List<Document>();
    public ICollection<Contract> Contracts { get; set; } = new List<Contract>();
    public ICollection<RepositoryInfo> Repositories { get; set; } = new List<RepositoryInfo>();
    public ICollection<VersionInfo> Versions { get; set; } = new List<VersionInfo>();
    public ICollection<ProjectUserRole> ProjectUserRoles { get; set; } = new List<ProjectUserRole>();

}

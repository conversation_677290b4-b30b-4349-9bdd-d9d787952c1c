﻿using System.Diagnostics.Contracts;
using System.Reflection.Metadata;
using TRtek_EYS.Domain.Enums;

namespace TRtek_EYS.Domain.Entities;

public class Project : BaseEntity
{
    public string Name { get; set; } = null!;
    public string Code { get; set; } = null!;
    public string? Description { get; set; }
    public ProjectStatus Status { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public Guid CreatedByUserId { get; set; }
    public User? CreatedByUser { get; set; }

    public ICollection<Platform> Platforms { get; set; } = new List<Platform>();
    public ICollection<Technology> Technologies { get; set; } = new List<Technology>();
    public ICollection<ProjectTeam> ProjectTeams { get; set; } = new List<ProjectTeam>();
    public ICollection<CustomerProject> CustomerProjects { get; set; } = new List<CustomerProject>();
    public ICollection<Document> Documents { get; set; } = new List<Document>();
    public ICollection<Contract> Contracts { get; set; } = new List<Contract>();
    public ICollection<RepositoryInfo> Repositories { get; set; } = new List<RepositoryInfo>();
    public ICollection<VersionInfo> Versions { get; set; } = new List<VersionInfo>();
    public ICollection<ProjectUserRole> ProjectUserRoles { get; set; } = new List<ProjectUserRole>();

}

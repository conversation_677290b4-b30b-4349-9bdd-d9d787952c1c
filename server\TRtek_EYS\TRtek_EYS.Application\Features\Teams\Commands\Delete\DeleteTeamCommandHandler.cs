﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Teams.Commands.Delete
{
    public class DeleteTeamCommandHandler : IRequestHandler<DeleteTeamCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public DeleteTeamCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(DeleteTeamCommand request, CancellationToken cancellationToken)
        {
            var team = await _context.Teams
                .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);

            if (team is null)
                throw new Exception("Silinecek takım bulunamadı.");

            _context.Teams.Remove(team);
            await _context.SaveChangesAsync(cancellationToken);

            return team.Id;
        }
    }
}

import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, debounceTime, Subscription } from 'rxjs';
import { ChartModule } from 'primeng/chart';
import { SkeletonModule } from 'primeng/skeleton';
import { TagModule } from 'primeng/tag';
import { LayoutService } from '../../../layout/service/layout.service';

@Component({
    standalone: true,
    selector: 'app-project-status-widget',
    imports: [CommonModule, ChartModule, SkeletonModule, TagModule],
    template: `<div class="card">
        <div class="flex justify-between align-items-center mb-5">
            <div class="font-semibold text-xl">Proje <PERSON></div>
        </div>
        
        <div *ngIf="isLoading" class="flex justify-content-center align-items-center" style="height: 350px;">
            <p-skeleton shape="circle" size="250px"></p-skeleton>
        </div>
        
        <div *ngIf="!isLoading && polarData" class="flex flex-column align-items-center">
            <p-chart 
                type="polarArea" 
                [data]="polarData" 
                [options]="polarOptions"
                width="300px"
                height="300px">
            </p-chart>
            
            <!-- Durum Açıklamaları -->
            <div class="grid w-full mt-4">
                <div class="col-6 text-center">
                    <p-tag value="Aktif" severity="success" class="mb-2"></p-tag>
                    <div class="text-sm text-500">{{ statusCounts.active }} proje</div>
                </div>
                <div class="col-6 text-center">
                    <p-tag value="Planlama" severity="info" class="mb-2"></p-tag>
                    <div class="text-sm text-500">{{ statusCounts.planning }} proje</div>
                </div>
                <div class="col-6 text-center">
                    <p-tag value="Test" severity="warning" class="mb-2"></p-tag>
                    <div class="text-sm text-500">{{ statusCounts.testing }} proje</div>
                </div>
                <div class="col-6 text-center">
                    <p-tag value="Tamamlandı" severity="primary" class="mb-2"></p-tag>
                    <div class="text-sm text-500">{{ statusCounts.completed }} proje</div>
                </div>
            </div>
        </div>
    </div>`
})
export class ProjectStatusWidget implements OnInit, OnDestroy {
    private destroy$ = new Subject<void>();
    private subscription: Subscription;
    
    polarData: any;
    polarOptions: any;
    isLoading = true;
    
    statusCounts = {
        active: 12,
        planning: 8,
        testing: 5,
        completed: 24
    };

    constructor(private layoutService: LayoutService) {
        this.subscription = this.layoutService.configUpdate$
            .pipe(debounceTime(25))
            .subscribe(() => {
                this.initChart();
            });
    }

    ngOnInit() {
        this.initChart();
        setTimeout(() => {
            this.isLoading = false;
        }, 1000);
    }

    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
        if (this.subscription) {
            this.subscription.unsubscribe();
        }
    }

    initChart() {
        const documentStyle = getComputedStyle(document.documentElement);
        const textColor = documentStyle.getPropertyValue('--text-color');

        this.polarData = {
            labels: ['Aktif', 'Planlama', 'Test', 'Tamamlandı'],
            datasets: [
                {
                    data: [
                        this.statusCounts.active,
                        this.statusCounts.planning,
                        this.statusCounts.testing,
                        this.statusCounts.completed
                    ],
                    backgroundColor: [
                        documentStyle.getPropertyValue('--p-green-500'),
                        documentStyle.getPropertyValue('--p-blue-500'),
                        documentStyle.getPropertyValue('--p-yellow-500'),
                        documentStyle.getPropertyValue('--p-purple-500')
                    ],
                    hoverBackgroundColor: [
                        documentStyle.getPropertyValue('--p-green-400'),
                        documentStyle.getPropertyValue('--p-blue-400'),
                        documentStyle.getPropertyValue('--p-yellow-400'),
                        documentStyle.getPropertyValue('--p-purple-400')
                    ]
                }
            ]
        };

        this.polarOptions = {
            plugins: {
                legend: {
                    labels: {
                        color: textColor,
                        usePointStyle: true,
                        padding: 20
                    },
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context: any) {
                            const label = context.label || '';
                            const value = context.parsed || 0;
                            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
                            const percentage = Math.round((value / total) * 100);
                            return `${label}: ${value} proje (${percentage}%)`;
                        }
                    }
                }
            },
            scales: {
                r: {
                    grid: {
                        display: true,
                        color: documentStyle.getPropertyValue('--surface-border')
                    },
                    ticks: {
                        display: false
                    }
                }
            },
            maintainAspectRatio: false
        };
    }
}

{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-megamenu.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { CommonModule, isPlatformBrowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, inject, forwardRef, booleanAttribute, numberAttribute, ViewChild, Output, Input, ViewEncapsulation, Component, signal, effect, ContentChildren, ContentChild, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport { resolve, isNotEmpty, uuid, isEmpty, focus, findSingle, isPrintableCharacter, findLastIndex, isTouchDevice } from '@primeuix/utils';\nimport { SharedModule, PrimeTemplate } from 'primeng/api';\nimport * as i4 from 'primeng/badge';\nimport { BadgeModule } from 'primeng/badge';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { AngleDownIcon, AngleRightIcon, BarsIcon } from 'primeng/icons';\nimport { Ripple } from 'primeng/ripple';\nimport * as i3 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ZIndexUtils } from 'primeng/utils';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"menubar\"];\nconst _c1 = a0 => ({\n  \"max-height\": a0,\n  overflow: \"auto\"\n});\nconst _c2 = (a0, a1) => ({\n  \"p-megamenu-root-list\": a0,\n  \"p-megamenu-submenu\": a1\n});\nconst _c3 = a0 => ({\n  \"p-megamenu-item-link\": true,\n  \"p-disabled\": a0\n});\nconst _c4 = () => ({\n  exact: false\n});\nconst _c5 = a0 => ({\n  $implicit: a0\n});\nfunction MegaMenuSub_ul_0_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(ctx_r1.getItemProp(ctx_r1.submenu, \"style\"));\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getSubmenuHeaderClass(ctx_r1.submenu));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getItemLabel(ctx_r1.submenu), \" \");\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 11);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(ctx_r1.getItemProp(processedItem_r3, \"style\"));\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getSeparatorItemClass(processedItem_r3));\n    i0.ɵɵattribute(\"id\", ctx_r1.getItemId(processedItem_r3))(\"data-pc-section\", \"separator\");\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 22);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getItemProp(processedItem_r3, \"icon\"))(\"ngStyle\", ctx_r1.getItemProp(processedItem_r3, \"iconStyle\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\")(\"tabindex\", -1);\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getItemLabel(processedItem_r3), \" \");\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 24);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.getItemLabel(processedItem_r3), i0.ɵɵsanitizeHtml);\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_p_badge_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-badge\", 25);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"styleClass\", ctx_r1.getItemProp(processedItem_r3, \"badgeStyleClass\"))(\"value\", ctx_r1.getItemProp(processedItem_r3, \"badge\"));\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleDownIcon\", 27);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngClass\", \"p-megamenu-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_Conditional_2_AngleRightIcon_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleRightIcon\", 27);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngClass\", \"p-megamenu-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_Conditional_2_AngleRightIcon_0_Template, 1, 3, \"AngleRightIcon\", 28);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.orientation === \"vertical\");\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_Conditional_1_Template, 1, 3, \"AngleDownIcon\", 27)(2, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_Conditional_2_Template, 1, 1, \"AngleRightIcon\", 27);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(7);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.orientation === \"horizontal\" || ctx_r1.mobileActive ? 1 : 2);\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_2_ng_template_0_Template(rf, ctx) {}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_2_ng_template_0_Template, 0, 0, \"ng-template\", 29);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_Template, 3, 1, \"ng-container\", 14)(2, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_2_Template, 1, 2, null, 26);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.megaMenu.submenuIconTemplate && !ctx_r1.megaMenu._submenuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.megaMenu.submenuIconTemplate || ctx_r1.megaMenu._submenuIconTemplate);\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 18);\n    i0.ɵɵtemplate(1, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_span_1_Template, 1, 4, \"span\", 19)(2, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_span_2_Template, 2, 2, \"span\", 20)(3, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_template_3_Template, 1, 2, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(5, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_p_badge_5_Template, 1, 2, \"p-badge\", 21)(6, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_Template, 3, 2, \"ng-container\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlLabel_r5 = i0.ɵɵreference(4);\n    const processedItem_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"target\", ctx_r1.getItemProp(processedItem_r3, \"target\"))(\"ngClass\", i0.ɵɵpureFunction1(11, _c3, ctx_r1.getItemProp(processedItem_r3, \"disabled\")));\n    i0.ɵɵattribute(\"href\", ctx_r1.getItemProp(processedItem_r3, \"url\"), i0.ɵɵsanitizeUrl)(\"data-automationid\", ctx_r1.getItemProp(processedItem_r3, \"automationId\"))(\"data-pc-section\", \"action\")(\"tabindex\", -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getItemProp(processedItem_r3, \"icon\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getItemProp(processedItem_r3, \"escape\"))(\"ngIfElse\", htmlLabel_r5);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getItemProp(processedItem_r3, \"badge\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isItemGroup(processedItem_r3));\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 22);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getItemProp(processedItem_r3, \"icon\"))(\"ngStyle\", ctx_r1.getItemProp(processedItem_r3, \"iconStyle\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\")(\"tabindex\", -1);\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.getItemLabel(processedItem_r3));\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 24);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.getItemLabel(processedItem_r3), i0.ɵɵsanitizeHtml);\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_p_badge_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-badge\", 25);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"styleClass\", ctx_r1.getItemProp(processedItem_r3, \"badgeStyleClass\"))(\"value\", ctx_r1.getItemProp(processedItem_r3, \"badge\"));\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleDownIcon\", 32);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-megamenu-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleRightIcon\", 32);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-megamenu-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleDownIcon_1_Template, 1, 3, \"AngleDownIcon\", 31)(2, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleRightIcon_2_Template, 1, 3, \"AngleRightIcon\", 31);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(7);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.orientation === \"horizontal\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.orientation === \"vertical\");\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_container_6_2_ng_template_0_Template(rf, ctx) {}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_container_6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_container_6_2_ng_template_0_Template, 0, 0, \"ng-template\", 29);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_Template, 3, 2, \"ng-container\", 14)(2, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_container_6_2_Template, 1, 2, null, 26);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.megaMenu.submenuIconTemplate && !ctx_r1.megaMenu._submenuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.megaMenu.submenuIconTemplate || ctx_r1.megaMenu._submenuIconTemplate);\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 30);\n    i0.ɵɵtemplate(1, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_span_1_Template, 1, 4, \"span\", 19)(2, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_span_2_Template, 2, 1, \"span\", 20)(3, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_template_3_Template, 1, 2, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(5, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_p_badge_5_Template, 1, 2, \"p-badge\", 21)(6, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_container_6_Template, 3, 2, \"ng-container\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlRouteLabel_r6 = i0.ɵɵreference(4);\n    const processedItem_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"routerLink\", ctx_r1.getItemProp(processedItem_r3, \"routerLink\"))(\"queryParams\", ctx_r1.getItemProp(processedItem_r3, \"queryParams\"))(\"routerLinkActive\", \"p-megamenu-item-link-active\")(\"routerLinkActiveOptions\", ctx_r1.getItemProp(processedItem_r3, \"routerLinkActiveOptions\") || i0.ɵɵpureFunction0(20, _c4))(\"target\", ctx_r1.getItemProp(processedItem_r3, \"target\"))(\"ngClass\", i0.ɵɵpureFunction1(21, _c3, ctx_r1.getItemProp(processedItem_r3, \"disabled\")))(\"fragment\", ctx_r1.getItemProp(processedItem_r3, \"fragment\"))(\"queryParamsHandling\", ctx_r1.getItemProp(processedItem_r3, \"queryParamsHandling\"))(\"preserveFragment\", ctx_r1.getItemProp(processedItem_r3, \"preserveFragment\"))(\"skipLocationChange\", ctx_r1.getItemProp(processedItem_r3, \"skipLocationChange\"))(\"replaceUrl\", ctx_r1.getItemProp(processedItem_r3, \"replaceUrl\"))(\"state\", ctx_r1.getItemProp(processedItem_r3, \"state\"));\n    i0.ɵɵattribute(\"data-automationid\", ctx_r1.getItemProp(processedItem_r3, \"automationId\"))(\"tabindex\", -1)(\"data-pc-section\", \"action\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getItemProp(processedItem_r3, \"icon\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getItemProp(processedItem_r3, \"escape\"))(\"ngIfElse\", htmlRouteLabel_r6);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getItemProp(processedItem_r3, \"badge\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isItemGroup(processedItem_r3));\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_Template, 7, 13, \"a\", 16)(2, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_Template, 7, 23, \"a\", 17);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.getItemProp(processedItem_r3, \"routerLink\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getItemProp(processedItem_r3, \"routerLink\"));\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_4_1_ng_template_0_Template(rf, ctx) {}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_4_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_4_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_4_1_Template, 1, 0, null, 33);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c5, processedItem_r3.item));\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_div_5_div_2_p_megamenu_sub_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-megamenu-sub\", 38);\n    i0.ɵɵlistener(\"itemClick\", function MegaMenuSub_ul_0_ng_template_3_li_1_div_5_div_2_p_megamenu_sub_1_Template_p_megamenu_sub_itemClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(6);\n      return i0.ɵɵresetView(ctx_r1.itemClick.emit($event));\n    })(\"itemMouseEnter\", function MegaMenuSub_ul_0_ng_template_3_li_1_div_5_div_2_p_megamenu_sub_1_Template_p_megamenu_sub_itemMouseEnter_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(6);\n      return i0.ɵɵresetView(ctx_r1.onItemMouseEnter($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const submenu_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵproperty(\"id\", ctx_r1.getSubListId(submenu_r8))(\"submenu\", submenu_r8)(\"items\", submenu_r8.items)(\"itemTemplate\", ctx_r1.itemTemplate)(\"mobileActive\", ctx_r1.mobileActive)(\"menuId\", ctx_r1.menuId)(\"focusedItemId\", ctx_r1.focusedItemId)(\"level\", ctx_r1.level + 1)(\"root\", false);\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_div_5_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, MegaMenuSub_ul_0_ng_template_3_li_1_div_5_div_2_p_megamenu_sub_1_Template, 1, 9, \"p-megamenu-sub\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const col_r9 = ctx.$implicit;\n    const processedItem_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getColumnClass(processedItem_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", col_r9);\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35);\n    i0.ɵɵtemplate(2, MegaMenuSub_ul_0_ng_template_3_li_1_div_5_div_2_Template, 2, 2, \"div\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵattribute(\"data-pc-section\", \"panel\");\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"data-pc-section\", \"grid\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", processedItem_r3.items);\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 12, 1)(2, \"div\", 13);\n    i0.ɵɵlistener(\"click\", function MegaMenuSub_ul_0_ng_template_3_li_1_Template_div_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const processedItem_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onItemClick($event, processedItem_r3));\n    })(\"mouseenter\", function MegaMenuSub_ul_0_ng_template_3_li_1_Template_div_mouseenter_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const processedItem_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onItemMouseEnter({\n        $event: $event,\n        processedItem: processedItem_r3\n      }));\n    });\n    i0.ɵɵtemplate(3, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_Template, 3, 2, \"ng-container\", 14)(4, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_4_Template, 2, 4, \"ng-container\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, MegaMenuSub_ul_0_ng_template_3_li_1_div_5_Template, 3, 3, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    const processedItem_r3 = ctx_r9.$implicit;\n    const index_r11 = ctx_r9.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r1.getItemProp(processedItem_r3, \"styleClass\"));\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.getItemProp(processedItem_r3, \"style\"))(\"ngClass\", ctx_r1.getItemClass(processedItem_r3))(\"tooltipOptions\", ctx_r1.getItemProp(processedItem_r3, \"tooltipOptions\"));\n    i0.ɵɵattribute(\"id\", ctx_r1.getItemId(processedItem_r3))(\"data-pc-section\", \"menuitem\")(\"data-p-highlight\", ctx_r1.isItemActive(processedItem_r3))(\"data-p-focused\", ctx_r1.isItemFocused(processedItem_r3))(\"data-p-disabled\", ctx_r1.isItemDisabled(processedItem_r3))(\"aria-label\", ctx_r1.getItemLabel(processedItem_r3))(\"aria-disabled\", ctx_r1.isItemDisabled(processedItem_r3) || undefined)(\"aria-haspopup\", ctx_r1.isItemGroup(processedItem_r3) && !ctx_r1.getItemProp(processedItem_r3, \"to\") ? \"menu\" : undefined)(\"aria-expanded\", ctx_r1.isItemGroup(processedItem_r3) ? ctx_r1.isItemActive(processedItem_r3) : undefined)(\"aria-level\", ctx_r1.level + 1)(\"aria-setsize\", ctx_r1.getAriaSetSize())(\"aria-posinset\", ctx_r1.getAriaPosInset(index_r11));\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"content\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isItemVisible(processedItem_r3) && ctx_r1.isItemGroup(processedItem_r3));\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MegaMenuSub_ul_0_ng_template_3_li_0_Template, 1, 5, \"li\", 9)(1, MegaMenuSub_ul_0_ng_template_3_li_1_Template, 6, 21, \"li\", 10);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isItemVisible(processedItem_r3) && ctx_r1.getItemProp(processedItem_r3, \"separator\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isItemVisible(processedItem_r3) && !ctx_r1.getItemProp(processedItem_r3, \"separator\"));\n  }\n}\nfunction MegaMenuSub_ul_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ul\", 5, 0);\n    i0.ɵɵlistener(\"keydown\", function MegaMenuSub_ul_0_Template_ul_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.menuKeydown.emit($event));\n    })(\"focus\", function MegaMenuSub_ul_0_Template_ul_focus_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.menuFocus.emit($event));\n    })(\"blur\", function MegaMenuSub_ul_0_Template_ul_blur_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.menuBlur.emit($event));\n    });\n    i0.ɵɵtemplate(2, MegaMenuSub_ul_0_li_2_Template, 2, 4, \"li\", 6)(3, MegaMenuSub_ul_0_ng_template_3_Template, 2, 2, \"ng-template\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction1(11, _c1, ctx_r1.scrollHeight));\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(13, _c2, ctx_r1.root, !ctx_r1.root))(\"tabindex\", ctx_r1.tabindex);\n    i0.ɵɵattribute(\"role\", ctx_r1.root ? \"menubar\" : \"menu\")(\"id\", ctx_r1.id)(\"aria-orientation\", ctx_r1.orientation)(\"aria-activedescendant\", ctx_r1.focusedItemId)(\"data-pc-section\", ctx_r1.root ? \"root\" : \"submenu\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submenu);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.items);\n  }\n}\nconst _c6 = [\"start\"];\nconst _c7 = [\"end\"];\nconst _c8 = [\"menuicon\"];\nconst _c9 = [\"submenuicon\"];\nconst _c10 = [\"item\"];\nconst _c11 = [\"button\"];\nconst _c12 = [\"buttonicon\"];\nconst _c13 = [\"menubutton\"];\nconst _c14 = [\"rootmenu\"];\nconst _c15 = [\"container\"];\nconst _c16 = a0 => ({\n  flexDirection: a0\n});\nconst _c17 = (a0, a1, a2, a3) => ({\n  \"p-megamenu p-component\": true,\n  \"p-megamenu-mobile\": a0,\n  \"p-megamenu-mobile-active\": a1,\n  \"p-megamenu-horizontal\": a2,\n  \"p-megamenu-vertical\": a3\n});\nfunction MegaMenu_div_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction MegaMenu_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtemplate(1, MegaMenu_div_2_ng_container_1_Template, 1, 0, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.startTemplate || ctx_r1._startTemplate);\n  }\n}\nfunction MegaMenu_ng_container_3_a_1_BarsIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"BarsIcon\");\n  }\n}\nfunction MegaMenu_ng_container_3_a_1_3_ng_template_0_Template(rf, ctx) {}\nfunction MegaMenu_ng_container_3_a_1_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MegaMenu_ng_container_3_a_1_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction MegaMenu_ng_container_3_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 11, 2);\n    i0.ɵɵlistener(\"click\", function MegaMenu_ng_container_3_a_1_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.menuButtonClick($event));\n    })(\"keydown\", function MegaMenu_ng_container_3_a_1_Template_a_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.menuButtonKeydown($event));\n    });\n    i0.ɵɵtemplate(2, MegaMenu_ng_container_3_a_1_BarsIcon_2_Template, 1, 0, \"BarsIcon\", 5)(3, MegaMenu_ng_container_3_a_1_3_Template, 1, 0, null, 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"aria-haspopup\", ctx_r1.model.length && ctx_r1.model.length > 0 ? true : false)(\"aria-expanded\", ctx_r1.mobileActive)(\"aria-controls\", ctx_r1.id)(\"aria-label\", ctx_r1.config.translation.aria.navigation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.buttonIconTemplate && !ctx_r1._buttonIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.buttonIconTemplate || ctx_r1._buttonIconTemplate);\n  }\n}\nfunction MegaMenu_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MegaMenu_ng_container_3_a_1_Template, 4, 6, \"a\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.model && ctx_r1.model.length > 0);\n  }\n}\nfunction MegaMenu_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction MegaMenu_div_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction MegaMenu_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtemplate(1, MegaMenu_div_7_ng_container_1_Template, 1, 0, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.endTemplate || ctx_r1._endTemplate);\n  }\n}\nconst theme = ({\n  dt\n}) => `\n.p-megamenu {\n    position: relative;\n    display: flex;\n    align-items: center;\n    background: ${dt('megamenu.background')};\n    border: 1px solid ${dt('megamenu.border.color')};\n    border-radius: ${dt('megamenu.border.radius')};\n    color: ${dt('megamenu.color')};\n    gap: ${dt('megamenu.gap')};\n}\n\n.p-megamenu-start,\n.p-megamenu-end {\n    display: flex;\n    align-items: center;\n}\n\n.p-megamenu-root-list {\n    margin: 0;\n    padding: 0;\n    list-style: none;\n    outline: 0 none;\n    align-items: center;\n    display: flex;\n    flex-wrap: wrap;\n    gap: ${dt('megamenu.gap')};\n}\n\n.p-megamenu-root-list > .p-megamenu-item > .p-menumega-item-content {\n    border-radius: ${dt('megamenu.base.item.border.radius')};\n}\n\n.p-megamenu-root-list > .p-megamenu-item > .p-megamenu-item-content > .p-megamenu-item-link {\n    padding: ${dt('megamenu.base.item.padding')};\n}\n\n.p-megamenu-item-content {\n    transition: background ${dt('megamenu.transition.duration')}, color ${dt('megamenu.transition.duration')};\n    border-radius: ${dt('megamenu.item.border.radius')};\n    color: ${dt('megamenu.item.color')};\n}\n\n.p-megamenu-item-link {\n    cursor: pointer;\n    display: flex;\n    align-items: center;\n    text-decoration: none;\n    overflow: hidden;\n    position: relative;\n    color: inherit;\n    padding: ${dt('megamenu.item.padding')};\n    gap: ${dt('megamenu.item.gap')};\n    user-select: none;\n    outline: 0 none;\n}\n\n.p-megamenu-item-label {\n    line-height: 1;\n}\n\n.p-megamenu-item-icon {\n    color: ${dt('megamenu.item.icon.color')};\n}\n\n.p-megamenu-submenu-icon {\n    color: ${dt('megamenu.submenu.icon.color')};\n    font-size: ${dt('megamenu.submenu.icon.size')};\n    width: ${dt('megamenu.submenu.icon.size')};\n    height: ${dt('megamenu.submenu.icon.size')};\n}\n\n.p-megamenu-item.p-focus > .p-megamenu-item-content {\n    color: ${dt('megamenu.item.focus.color')};\n    background: ${dt('megamenu.item.focus.background')};\n}\n\n.p-megamenu-item.p-focus > .p-megamenu-item-content .p-megamenu-item-icon {\n    color: ${dt('megamenu.item.icon.focus.color')};\n}\n\n.p-megamenu-item.p-focus > .p-megamenu-item-content .p-megamenu-submenu-icon {\n    color: ${dt('megamenu.submenu.icon.focus.color')};\n}\n\n.p-megamenu-item:not(.p-disabled) > .p-megamenu-item-content:hover {\n    color: ${dt('megamenu.item.focus.color')};\n    background: ${dt('megamenu.item.focus.background')};\n}\n\n.p-megamenu-item:not(.p-disabled) > .p-megamenu-item-content:hover .p-megamenu-item-icon {\n    color: ${dt('megamenu.item.icon.focus.color')};\n}\n\n.p-megamenu-item:not(.p-disabled) > .p-megamenu-item-content:hover .p-megamenu-submenu-icon {\n    color: ${dt('megamenu.submenu.icon.focus.color')};\n}\n\n.p-megamenu-item-active > .p-megamenu-item-content {\n    color: ${dt('megamenu.item.active.color')};\n    background: ${dt('megamenu.item.active.background')};\n}\n\n.p-megamenu-item-active > .p-megamenu-item-content .p-megamenu-item-icon {\n    color: ${dt('megamenu.item.icon.active.color')};\n}\n\n.p-megamenu-item-active > .p-megamenu-item-content .p-megamenu-submenu-icon {\n    color: ${dt('megamenu.submenu.icon.active.color')};\n}\n\n.p-megamenu-overlay {\n    display: none;\n    position: absolute;\n    width: auto;\n    z-index: 1;\n    left: 0;\n    min-width: 100%;\n    padding: ${dt('megamenu.overlay.padding')};\n    background: ${dt('megamenu.overlay.background')};\n    color: ${dt('megamenu.overlay.color')};\n    border: 1px solid ${dt('megamenu.overlay.border.color')};\n    border-radius: ${dt('megamenu.overlay.border.radius')};\n    box-shadow: ${dt('megamenu.overlay.shadow')};\n}\n\n.p-megamenu-root-list > .p-megamenu-item-active > .p-megamenu-overlay {\n    display: block;\n}\n\n.p-megamenu-submenu {\n    margin: 0;\n    list-style: none;\n    padding: ${dt('megamenu.submenu.padding')};\n    min-width: 12.5rem;\n    display: flex;\n    flex-direction: column;\n    gap: ${dt('megamenu.submenu.gap')}\n}\n\n.p-megamenu-submenu-label {\n    padding: ${dt('megamenu.submenu.label.padding')};\n    color: ${dt('megamenu.submenu.label.color')};\n    font-weight: ${dt('megamenu.submenu.label.font.weight')};\n    background: ${dt('megamenu.submenu.label.background')};\n}\n\n.p-megamenu-separator {\n    border-top: 1px solid ${dt('megamenu.separator.border.color')};\n}\n\n.p-megamenu-horizontal {\n    align-items: center;\n    padding: ${dt('megamenu.horizontal.orientation.padding')};\n}\n\n.p-megamenu-horizontal .p-megamenu-root-list {\n    display: flex;\n    align-items: center;\n    flex-wrap: wrap;\n    gap: ${dt('megamenu.horizontal.orientation.gap')};\n}\n\n.p-megamenu-horizontal .p-megamenu-end {\n    margin-left: auto;\n    align-self: center;\n}\n\n.p-megamenu-vertical {\n    display: inline-flex;\n    min-width: 12.5rem;\n    flex-direction: column;\n    align-items: stretch;\n    padding: ${dt('megamenu.vertical.orientation.padding')};\n}\n\n.p-megamenu-vertical .p-megamenu-root-list {\n    align-items: stretch;\n    flex-direction: column;\n    gap: ${dt('megamenu.vertical.orientation.gap')};\n}\n\n.p-megamenu-vertical .p-megamenu-root-list > .p-megamenu-item-active > .p-megamenu-overlay {\n    left: 100%;\n    top: 0;\n}\n\n.p-megamenu-vertical .p-megamenu-root-list > .p-megamenu-item-active >.p-megamenu-overlay:dir(rtl) {\n    left: auto;\n    right: 100%;\n}\n\n.p-megamenu-vertical .p-megamenu-root-list > .p-megamenu-item > .p-megamenu-item-content .p-megamenu-submenu-icon {\n    margin-left: auto;\n}\n\n.p-megamenu-vertical .p-megamenu-root-list > .p-megamenu-item > .p-megamenu-item-content .p-megamenu-submenu-icon:dir(rtl) {\n    margin-left: 0;\n    margin-right: auto;\n    transform: rotate(180deg);\n}\n\n.p-megamenu-grid {\n    display: flex;\n}\n\n.p-megamenu-col-2,\n.p-megamenu-col-3,\n.p-megamenu-col-4,\n.p-megamenu-col-6,\n.p-megamenu-col-12 {\n    flex: 0 0 auto;\n    padding: ${dt('megamenu.overlay.gap')};\n}\n\n.p-megamenu-col-2 {\n    width: 16.6667%;\n}\n\n.p-megamenu-col-3 {\n    width: 25%;\n}\n\n.p-megamenu-col-4 {\n    width: 33.3333%;\n}\n\n.p-megamenu-col-6 {\n    width: 50%;\n}\n\n.p-megamenu-col-12 {\n    width: 100%;\n}\n\n.p-megamenu-button {\n    display: none;\n    justify-content: center;\n    align-items: center;\n    cursor: pointer;\n    width: ${dt('megamenu.mobile.button.size')};\n    height: ${dt('megamenu.mobile.button.size')};\n    position: relative;\n    color: ${dt('megamenu.mobile.button.color')};\n    border: 0 none;\n    background: transparent;\n    border-radius: ${dt('megamenu.mobile.button.border.radius')};\n    transition: background ${dt('megamenu.transition.duration')}, color ${dt('megamenu.transition.duration')}, outline-color ${dt('megamenu.transition.duration')}, ox-shadow ${dt('megamenu.transition.duration')};\n    outline-color: transparent;\n}\n\n.p-megamenu-button:hover {\n    color: ${dt('megamenu.mobile.button.hover.color')};\n    background: ${dt('megamenu.mobile.button.hover.background')};\n}\n\n.p-megamenu-button:focus-visible {\n    box-shadow: ${dt('megamenu.mobile.button.focus.ring.shadow')};\n    outline: ${dt('megamenu.mobile.button.focus.ring.width')} ${dt('megamenu.mobile.button.focus.ring.style')} ${dt('megamenu.mobile.button.focus.ring.color')};\n    outline-offset: ${dt('megamenu.mobile.button.focus.ring.offset')};\n}\n\n.p-megamenu-mobile {\n    display: flex;\n}\n\n.p-megamenu-mobile .p-megamenu-button {\n    display: flex;\n}\n\n.p-megamenu-mobile .p-megamenu-root-list {\n    position: absolute;\n    display: none;\n    flex-direction: column;\n    top: 100%;\n    left: 0;\n    z-index: 1;\n    width: 100%;\n    padding: ${dt('megamenu.submenu.padding')};\n    gap: ${dt('megamenu.submenu.gap')};\n    background: ${dt('megamenu.overlay.background')};\n    border: 1px solid ${dt('megamenu.overlay.border.color')};\n    box-shadow: ${dt('megamenu.overlay.shadow')};\n}\n\n.p-megamenu-mobile-active .p-megamenu-root-list {\n    display: block;\n}\n\n.p-megamenu-mobile .p-megamenu-root-list .p-megamenu-item {\n    width: 100%;\n    position: static;\n}\n\n.p-megamenu-mobile .p-megamenu-overlay {\n    position: static;\n    border: 0 none;\n    border-radius: 0;\n    box-shadow: none;\n}\n\n.p-megamenu-mobile .p-megamenu-grid {\n    flex-wrap: wrap;\n    overflow: auto;\n    max-height: 90%;\n}\n\n.p-megamenu-mobile .p-megamenu-root-list > .p-megamenu-item > .p-megamenu-item-content .p-megamenu-submenu-icon {\n    margin-left: auto;\n    transition: transform 0.2s;\n}\n\n.p-megamenu-mobile .p-megamenu-root-list > .p-megamenu-item > .p-megamenu-item-content .p-megamenu-submenu-icon:dir(rtl) {\n    margin-left: 0;\n    margin-right: auto;\n}\n\n.p-megamenu-mobile .p-megamenu-root-list > .p-megamenu-item-active > .p-megamenu-item-content .p-megamenu-submenu-icon {\n    transform: rotate(-180deg);\n}\n\n/* For PrimeNG */\n.p-megamenu-submenu-icon.p-iconwrapper {\n    margin-left: auto;\n}\n`;\nconst inlineStyles = {\n  rootList: ({\n    props\n  }) => ({\n    'max-height': props.scrollHeight,\n    overflow: 'auto'\n  })\n};\nconst classes = {\n  root: ({\n    instance\n  }) => ['p-megamenu p-component', {\n    'p-megamenu-mobile': instance.queryMatches,\n    'p-megamenu-mobile-active': instance.mobileActive,\n    'p-megamenu-horizontal': instance.horizontal,\n    'p-megamenu-vertical': instance.vertical\n  }],\n  start: 'p-megamenu-start',\n  button: 'p-megamenu-button',\n  rootList: 'p-megamenu-root-list',\n  submenuLabel: ({\n    instance,\n    processedItem\n  }) => ['p-megamenu-submenu-label', {\n    'p-disabled': instance.isItemDisabled(processedItem)\n  }],\n  item: ({\n    instance,\n    processedItem\n  }) => ['p-megamenu-item', {\n    'p-megamenu-item-active': instance.isItemActive(processedItem),\n    'p-focus': instance.isItemFocused(processedItem),\n    'p-disabled': instance.isItemDisabled(processedItem)\n  }],\n  itemContent: 'p-megamenu-item-content',\n  itemLink: 'p-megamenu-item-link',\n  itemIcon: 'p-megamenu-item-icon',\n  itemLabel: 'p-megamenu-item-label',\n  submenuIcon: 'p-megamenu-submenu-icon',\n  overlay: 'p-megamenu-overlay',\n  grid: 'p-megamenu-grid',\n  column: ({\n    instance,\n    processedItem\n  }) => {\n    let length = instance.isItemGroup(processedItem) ? processedItem.items.length : 0;\n    let columnClass;\n    if (instance.$parentInstance.queryMatches) columnClass = 'p-megamenu-col-12';else {\n      switch (length) {\n        case 2:\n          columnClass = 'p-megamenu-col-6';\n          break;\n        case 3:\n          columnClass = 'p-megamenu-col-4';\n          break;\n        case 4:\n          columnClass = 'p-megamenu-col-3';\n          break;\n        case 6:\n          columnClass = 'p-megamenu-col-2';\n          break;\n        default:\n          columnClass = 'p-megamenu-col-12';\n          break;\n      }\n    }\n    return columnClass;\n  },\n  submenu: 'p-megamenu-submenu',\n  separator: 'p-megamenu-separator',\n  end: 'p-megamenu-end'\n};\nclass MegaMenuStyle extends BaseStyle {\n  name = 'megamenu';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMegaMenuStyle_BaseFactory;\n    return function MegaMenuStyle_Factory(__ngFactoryType__) {\n      return (ɵMegaMenuStyle_BaseFactory || (ɵMegaMenuStyle_BaseFactory = i0.ɵɵgetInheritedFactory(MegaMenuStyle)))(__ngFactoryType__ || MegaMenuStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MegaMenuStyle,\n    factory: MegaMenuStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MegaMenuStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * MegaMenu is navigation component that displays submenus together.\n *\n * [Live Demo](https://www.primeng.org/megamenu/)\n *\n * @module megamenustyle\n *\n */\nvar MegaMenuClasses;\n(function (MegaMenuClasses) {\n  /**\n   * Class name of the root element\n   */\n  MegaMenuClasses[\"root\"] = \"p-megamenu\";\n  /**\n   * Class name of the start element\n   */\n  MegaMenuClasses[\"start\"] = \"p-megamenu-start\";\n  /**\n   * Class name of the button element\n   */\n  MegaMenuClasses[\"button\"] = \"p-megamenu-button\";\n  /**\n   * Class name of the root list element\n   */\n  MegaMenuClasses[\"rootList\"] = \"p-megamenu-root-list\";\n  /**\n   * Class name of the submenu item element\n   */\n  MegaMenuClasses[\"submenuItem\"] = \"p-megamenu-submenu-item\";\n  /**\n   * Class name of the item element\n   */\n  MegaMenuClasses[\"item\"] = \"p-megamenu-item\";\n  /**\n   * Class name of the item content element\n   */\n  MegaMenuClasses[\"itemContent\"] = \"p-megamenu-item-content\";\n  /**\n   * Class name of the item link element\n   */\n  MegaMenuClasses[\"itemLink\"] = \"p-megamenu-item-link\";\n  /**\n   * Class name of the item icon element\n   */\n  MegaMenuClasses[\"itemIcon\"] = \"p-megamenu-item-icon\";\n  /**\n   * Class name of the item label element\n   */\n  MegaMenuClasses[\"itemLabel\"] = \"p-megamenu-item-label\";\n  /**\n   * Class name of the submenu icon element\n   */\n  MegaMenuClasses[\"submenuIcon\"] = \"p-megamenu-submenu-icon\";\n  /**\n   * Class name of the panel element\n   */\n  MegaMenuClasses[\"panel\"] = \"p-megamenu-panel\";\n  /**\n   * Class name of the grid element\n   */\n  MegaMenuClasses[\"grid\"] = \"p-megamenu-grid\";\n  /**\n   * Class name of the submenu element\n   */\n  MegaMenuClasses[\"submenu\"] = \"p-megamenu-submenu\";\n  /**\n   * Class name of the submenu item label element\n   */\n  MegaMenuClasses[\"submenuItemLabel\"] = \"p-megamenu-submenu-item-label\";\n  /**\n   * Class name of the separator element\n   */\n  MegaMenuClasses[\"separator\"] = \"p-megamenu-separator\";\n  /**\n   * Class name of the end element\n   */\n  MegaMenuClasses[\"end\"] = \"p-megamenu-end\";\n})(MegaMenuClasses || (MegaMenuClasses = {}));\nclass MegaMenuSub extends BaseComponent {\n  id;\n  items;\n  itemTemplate;\n  menuId;\n  ariaLabel;\n  ariaLabelledBy;\n  level = 0;\n  focusedItemId;\n  disabled = false;\n  orientation;\n  activeItem;\n  submenu;\n  queryMatches = false;\n  mobileActive = false;\n  scrollHeight;\n  tabindex = 0;\n  root = false;\n  itemClick = new EventEmitter();\n  itemMouseEnter = new EventEmitter();\n  menuFocus = new EventEmitter();\n  menuBlur = new EventEmitter();\n  menuKeydown = new EventEmitter();\n  menubarViewChild;\n  megaMenu = inject(forwardRef(() => MegaMenu));\n  onItemClick(event, processedItem) {\n    this.getItemProp(processedItem, 'command', {\n      originalEvent: event,\n      item: processedItem.item\n    });\n    this.itemClick.emit({\n      originalEvent: event,\n      processedItem,\n      isFocus: true\n    });\n  }\n  getItemProp(processedItem, name, params = null) {\n    return processedItem && processedItem.item ? resolve(processedItem.item[name], params) : undefined;\n  }\n  getItemId(processedItem) {\n    return processedItem.item && processedItem.item?.id ? processedItem.item.id : `${this.menuId}_${processedItem.key}`;\n  }\n  getSubListId(processedItem) {\n    return `${this.getItemId(processedItem)}_list`;\n  }\n  getItemClass(processedItem) {\n    return {\n      ...this.getItemProp(processedItem, 'class'),\n      'p-megamenu-item': true,\n      'p-megamenu-item-active': this.isItemActive(processedItem),\n      'p-focus': this.isItemFocused(processedItem),\n      'p-disabled': this.isItemDisabled(processedItem)\n    };\n  }\n  getItemLabel(processedItem) {\n    return this.getItemProp(processedItem, 'label');\n  }\n  getSeparatorItemClass(processedItem) {\n    return {\n      ...this.getItemProp(processedItem, 'class'),\n      'p-megamenu-separator': true\n    };\n  }\n  getColumnClass(processedItem) {\n    let length = this.isItemGroup(processedItem) ? processedItem.items.length : 0;\n    let columnClass;\n    if (this.queryMatches) columnClass = 'p-megamenu-col-12';else {\n      switch (length) {\n        case 2:\n          columnClass = 'p-megamenu-col-6';\n          break;\n        case 3:\n          columnClass = 'p-megamenu-col-4';\n          break;\n        case 4:\n          columnClass = 'p-megamenu-col-3';\n          break;\n        case 6:\n          columnClass = 'p-megamenu-col-2';\n          break;\n        default:\n          columnClass = 'p-megamenu-col-12';\n          break;\n      }\n    }\n    return columnClass;\n  }\n  getSubmenuHeaderClass(processedItem) {\n    return {\n      'p-megamenu-submenu-label': true,\n      'p-disabled': this.isItemDisabled(processedItem),\n      ...this.getItemProp(processedItem, 'class')\n    };\n  }\n  isSubmenuVisible(submenu) {\n    if (this.submenu && !this.root) {\n      return this.isItemVisible(submenu);\n    } else {\n      return true;\n    }\n  }\n  isItemVisible(processedItem) {\n    return this.getItemProp(processedItem, 'visible') !== false;\n  }\n  isItemActive(processedItem) {\n    return isNotEmpty(this.activeItem) ? this.activeItem.key === processedItem.key : false;\n  }\n  isItemDisabled(processedItem) {\n    return this.getItemProp(processedItem, 'disabled');\n  }\n  isItemFocused(processedItem) {\n    return this.focusedItemId === this.getItemId(processedItem);\n  }\n  isItemGroup(processedItem) {\n    return isNotEmpty(processedItem.items);\n  }\n  getAriaSetSize() {\n    return this.items.filter(processedItem => this.isItemVisible(processedItem) && !this.getItemProp(processedItem, 'separator')).length;\n  }\n  getAriaPosInset(index) {\n    return index - this.items.slice(0, index).filter(processedItem => this.isItemVisible(processedItem) && this.getItemProp(processedItem, 'separator')).length + 1;\n  }\n  onItemMouseEnter(param) {\n    const {\n      event,\n      processedItem\n    } = param;\n    this.itemMouseEnter.emit({\n      originalEvent: event,\n      processedItem\n    });\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMegaMenuSub_BaseFactory;\n    return function MegaMenuSub_Factory(__ngFactoryType__) {\n      return (ɵMegaMenuSub_BaseFactory || (ɵMegaMenuSub_BaseFactory = i0.ɵɵgetInheritedFactory(MegaMenuSub)))(__ngFactoryType__ || MegaMenuSub);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MegaMenuSub,\n    selectors: [[\"p-megaMenuSub\"], [\"p-megamenu-sub\"]],\n    viewQuery: function MegaMenuSub_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menubarViewChild = _t.first);\n      }\n    },\n    inputs: {\n      id: \"id\",\n      items: \"items\",\n      itemTemplate: \"itemTemplate\",\n      menuId: \"menuId\",\n      ariaLabel: \"ariaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\",\n      level: [2, \"level\", \"level\", numberAttribute],\n      focusedItemId: \"focusedItemId\",\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      orientation: \"orientation\",\n      activeItem: \"activeItem\",\n      submenu: \"submenu\",\n      queryMatches: [2, \"queryMatches\", \"queryMatches\", booleanAttribute],\n      mobileActive: [2, \"mobileActive\", \"mobileActive\", booleanAttribute],\n      scrollHeight: \"scrollHeight\",\n      tabindex: [2, \"tabindex\", \"tabindex\", numberAttribute],\n      root: [2, \"root\", \"root\", booleanAttribute]\n    },\n    outputs: {\n      itemClick: \"itemClick\",\n      itemMouseEnter: \"itemMouseEnter\",\n      menuFocus: \"menuFocus\",\n      menuBlur: \"menuBlur\",\n      menuKeydown: \"menuKeydown\"\n    },\n    features: [i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature],\n    decls: 1,\n    vars: 1,\n    consts: [[\"menubar\", \"\"], [\"listItem\", \"\"], [\"htmlLabel\", \"\"], [\"htmlRouteLabel\", \"\"], [3, \"ngClass\", \"style\", \"tabindex\", \"keydown\", \"focus\", \"blur\", 4, \"ngIf\"], [3, \"keydown\", \"focus\", \"blur\", \"ngClass\", \"tabindex\"], [\"role\", \"presentation\", 3, \"ngClass\", \"style\", 4, \"ngIf\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"role\", \"presentation\", 3, \"ngClass\"], [\"role\", \"separator\", 3, \"style\", \"ngClass\", 4, \"ngIf\"], [\"role\", \"menuitem\", \"pTooltip\", \"\", 3, \"ngStyle\", \"ngClass\", \"class\", \"tooltipOptions\", 4, \"ngIf\"], [\"role\", \"separator\", 3, \"ngClass\"], [\"role\", \"menuitem\", \"pTooltip\", \"\", 3, \"ngStyle\", \"ngClass\", \"tooltipOptions\"], [1, \"p-megamenu-item-content\", 3, \"click\", \"mouseenter\"], [4, \"ngIf\"], [\"class\", \"p-megamenu-overlay\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"target\", \"ngClass\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"target\", \"ngClass\"], [\"class\", \"p-megamenu-item-icon\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-megamenu-item-label\", 4, \"ngIf\", \"ngIfElse\"], [3, \"styleClass\", \"value\", 4, \"ngIf\"], [1, \"p-megamenu-item-icon\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-megamenu-item-label\"], [1, \"p-megamenu-item-label\", 3, \"innerHTML\"], [3, \"styleClass\", \"value\"], [4, \"ngTemplateOutlet\"], [3, \"ngClass\"], [3, \"ngClass\", 4, \"ngIf\"], [3, \"data-pc-section\", \"aria-hidden\"], [\"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"styleClass\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"p-megamenu-overlay\"], [1, \"p-megamenu-grid\"], [3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [3, \"id\", \"submenu\", \"items\", \"itemTemplate\", \"mobileActive\", \"menuId\", \"focusedItemId\", \"level\", \"root\", \"itemClick\", \"itemMouseEnter\", 4, \"ngFor\", \"ngForOf\"], [3, \"itemClick\", \"itemMouseEnter\", \"id\", \"submenu\", \"items\", \"itemTemplate\", \"mobileActive\", \"menuId\", \"focusedItemId\", \"level\", \"root\"]],\n    template: function MegaMenuSub_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, MegaMenuSub_ul_0_Template, 4, 16, \"ul\", 4);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isSubmenuVisible(ctx.submenu));\n      }\n    },\n    dependencies: [MegaMenuSub, CommonModule, i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, RouterModule, i2.RouterLink, i2.RouterLinkActive, Ripple, TooltipModule, i3.Tooltip, AngleDownIcon, AngleRightIcon, BadgeModule, i4.Badge, SharedModule],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MegaMenuSub, [{\n    type: Component,\n    args: [{\n      selector: 'p-megaMenuSub, p-megamenu-sub',\n      standalone: true,\n      imports: [CommonModule, RouterModule, Ripple, TooltipModule, AngleDownIcon, AngleRightIcon, BadgeModule, SharedModule],\n      template: `\n        <ul\n            *ngIf=\"isSubmenuVisible(submenu)\"\n            #menubar\n            [ngClass]=\"{ 'p-megamenu-root-list': root, 'p-megamenu-submenu': !root }\"\n            [style]=\"{ 'max-height': scrollHeight, overflow: 'auto' }\"\n            [attr.role]=\"root ? 'menubar' : 'menu'\"\n            [attr.id]=\"id\"\n            [attr.aria-orientation]=\"orientation\"\n            [tabindex]=\"tabindex\"\n            [attr.aria-activedescendant]=\"focusedItemId\"\n            [attr.data-pc-section]=\"root ? 'root' : 'submenu'\"\n            (keydown)=\"menuKeydown.emit($event)\"\n            (focus)=\"menuFocus.emit($event)\"\n            (blur)=\"menuBlur.emit($event)\"\n        >\n            <li *ngIf=\"submenu\" [ngClass]=\"getSubmenuHeaderClass(submenu)\" [style]=\"getItemProp(submenu, 'style')\" role=\"presentation\">\n                {{ getItemLabel(submenu) }}\n            </li>\n            <ng-template ngFor let-processedItem [ngForOf]=\"items\" let-index=\"index\">\n                <li\n                    *ngIf=\"isItemVisible(processedItem) && getItemProp(processedItem, 'separator')\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [style]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getSeparatorItemClass(processedItem)\"\n                    role=\"separator\"\n                    [attr.data-pc-section]=\"'separator'\"\n                ></li>\n                <li\n                    #listItem\n                    *ngIf=\"isItemVisible(processedItem) && !getItemProp(processedItem, 'separator')\"\n                    role=\"menuitem\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [attr.data-pc-section]=\"'menuitem'\"\n                    [attr.data-p-highlight]=\"isItemActive(processedItem)\"\n                    [attr.data-p-focused]=\"isItemFocused(processedItem)\"\n                    [attr.data-p-disabled]=\"isItemDisabled(processedItem)\"\n                    [attr.aria-label]=\"getItemLabel(processedItem)\"\n                    [attr.aria-disabled]=\"isItemDisabled(processedItem) || undefined\"\n                    [attr.aria-haspopup]=\"isItemGroup(processedItem) && !getItemProp(processedItem, 'to') ? 'menu' : undefined\"\n                    [attr.aria-expanded]=\"isItemGroup(processedItem) ? isItemActive(processedItem) : undefined\"\n                    [attr.aria-level]=\"level + 1\"\n                    [attr.aria-setsize]=\"getAriaSetSize()\"\n                    [attr.aria-posinset]=\"getAriaPosInset(index)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getItemClass(processedItem)\"\n                    [class]=\"getItemProp(processedItem, 'styleClass')\"\n                    pTooltip\n                    [tooltipOptions]=\"getItemProp(processedItem, 'tooltipOptions')\"\n                >\n                    <div class=\"p-megamenu-item-content\" [attr.data-pc-section]=\"'content'\" (click)=\"onItemClick($event, processedItem)\" (mouseenter)=\"onItemMouseEnter({ $event, processedItem })\">\n                        <ng-container *ngIf=\"!itemTemplate\">\n                            <a\n                                *ngIf=\"!getItemProp(processedItem, 'routerLink')\"\n                                [attr.href]=\"getItemProp(processedItem, 'url')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-megamenu-item-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [attr.tabindex]=\"-1\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-megamenu-item-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-megamenu-item-label\" [attr.data-pc-section]=\"'label'\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-megamenu-item-label\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span>\n                                </ng-template>\n                                <p-badge *ngIf=\"getItemProp(processedItem, 'badge')\" [styleClass]=\"getItemProp(processedItem, 'badgeStyleClass')\" [value]=\"getItemProp(processedItem, 'badge')\" />\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!megaMenu.submenuIconTemplate && !megaMenu._submenuIconTemplate\">\n                                        @if (orientation === 'horizontal' || mobileActive) {\n                                            <AngleDownIcon [ngClass]=\"'p-megamenu-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                        } @else {\n                                            <AngleRightIcon [ngClass]=\"'p-megamenu-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" *ngIf=\"orientation === 'vertical'\" [attr.aria-hidden]=\"true\" />\n                                        }\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"megaMenu.submenuIconTemplate || megaMenu._submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                            <a\n                                *ngIf=\"getItemProp(processedItem, 'routerLink')\"\n                                [routerLink]=\"getItemProp(processedItem, 'routerLink')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.tabindex]=\"-1\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [queryParams]=\"getItemProp(processedItem, 'queryParams')\"\n                                [routerLinkActive]=\"'p-megamenu-item-link-active'\"\n                                [routerLinkActiveOptions]=\"getItemProp(processedItem, 'routerLinkActiveOptions') || { exact: false }\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-megamenu-item-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [fragment]=\"getItemProp(processedItem, 'fragment')\"\n                                [queryParamsHandling]=\"getItemProp(processedItem, 'queryParamsHandling')\"\n                                [preserveFragment]=\"getItemProp(processedItem, 'preserveFragment')\"\n                                [skipLocationChange]=\"getItemProp(processedItem, 'skipLocationChange')\"\n                                [replaceUrl]=\"getItemProp(processedItem, 'replaceUrl')\"\n                                [state]=\"getItemProp(processedItem, 'state')\"\n                                pRipple\n                            >\n                                <span\n                                    class=\"p-megamenu-item-icon\"\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.tabindex]=\"-1\"\n                                ></span>\n                                <span class=\"p-megamenu-item-label\" *ngIf=\"getItemProp(processedItem, 'escape'); else htmlRouteLabel\">{{ getItemLabel(processedItem) }}</span>\n                                <ng-template #htmlRouteLabel><span class=\"p-megamenu-item-label\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span></ng-template>\n                                <p-badge *ngIf=\"getItemProp(processedItem, 'badge')\" [styleClass]=\"getItemProp(processedItem, 'badgeStyleClass')\" [value]=\"getItemProp(processedItem, 'badge')\" />\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!megaMenu.submenuIconTemplate && !megaMenu._submenuIconTemplate\">\n                                        <AngleDownIcon [styleClass]=\"'p-megamenu-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" *ngIf=\"orientation === 'horizontal'\" [attr.aria-hidden]=\"true\" />\n                                        <AngleRightIcon [styleClass]=\"'p-megamenu-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" *ngIf=\"orientation === 'vertical'\" [attr.aria-hidden]=\"true\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"megaMenu.submenuIconTemplate || megaMenu._submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                        </ng-container>\n                        <ng-container *ngIf=\"itemTemplate\">\n                            <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: processedItem.item }\"></ng-template>\n                        </ng-container>\n                    </div>\n                    <div *ngIf=\"isItemVisible(processedItem) && isItemGroup(processedItem)\" class=\"p-megamenu-overlay\" [attr.data-pc-section]=\"'panel'\">\n                        <div class=\"p-megamenu-grid\" [attr.data-pc-section]=\"'grid'\">\n                            <div *ngFor=\"let col of processedItem.items\" [ngClass]=\"getColumnClass(processedItem)\">\n                                <p-megamenu-sub\n                                    *ngFor=\"let submenu of col\"\n                                    [id]=\"getSubListId(submenu)\"\n                                    [submenu]=\"submenu\"\n                                    [items]=\"submenu.items\"\n                                    [itemTemplate]=\"itemTemplate\"\n                                    [mobileActive]=\"mobileActive\"\n                                    [menuId]=\"menuId\"\n                                    [focusedItemId]=\"focusedItemId\"\n                                    [level]=\"level + 1\"\n                                    [root]=\"false\"\n                                    (itemClick)=\"itemClick.emit($event)\"\n                                    (itemMouseEnter)=\"onItemMouseEnter($event)\"\n                                >\n                                </p-megamenu-sub>\n                            </div>\n                        </div>\n                    </div>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n      encapsulation: ViewEncapsulation.None\n    }]\n  }], null, {\n    id: [{\n      type: Input\n    }],\n    items: [{\n      type: Input\n    }],\n    itemTemplate: [{\n      type: Input\n    }],\n    menuId: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    level: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    focusedItemId: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    orientation: [{\n      type: Input\n    }],\n    activeItem: [{\n      type: Input\n    }],\n    submenu: [{\n      type: Input\n    }],\n    queryMatches: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    mobileActive: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    scrollHeight: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    root: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    itemClick: [{\n      type: Output\n    }],\n    itemMouseEnter: [{\n      type: Output\n    }],\n    menuFocus: [{\n      type: Output\n    }],\n    menuBlur: [{\n      type: Output\n    }],\n    menuKeydown: [{\n      type: Output\n    }],\n    menubarViewChild: [{\n      type: ViewChild,\n      args: ['menubar', {\n        static: true\n      }]\n    }]\n  });\n})();\n/**\n * MegaMenu is navigation component that displays submenus together.\n * @group Components\n */\nclass MegaMenu extends BaseComponent {\n  /**\n   * An array of menuitems.\n   * @group Props\n   */\n  set model(value) {\n    this._model = value;\n    this._processedItems = this.createProcessedItems(this._model || []);\n  }\n  get model() {\n    return this._model;\n  }\n  /**\n   * Inline style of the element.\n   * @group Props\n   */\n  style;\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Defines the orientation.\n   * @group Props\n   */\n  orientation = 'horizontal';\n  /**\n   * Current id state as a string.\n   * @group Props\n   */\n  id;\n  /**\n   * Defines a string value that labels an interactive element.\n   * @group Props\n   */\n  ariaLabel;\n  /**\n   * Identifier of the underlying input element.\n   * @group Props\n   */\n  ariaLabelledBy;\n  /**\n   * The breakpoint to define the maximum width boundary.\n   * @group Props\n   */\n  breakpoint = '960px';\n  /**\n   * Height of the viewport, a scrollbar is defined if height of list exceeds this value.\n   * @group Props\n   */\n  scrollHeight = '20rem';\n  /**\n   * When present, it specifies that the component should be disabled.\n   * @group Props\n   */\n  disabled = false;\n  /**\n   * Index of the element in tabbing order.\n   * @group Props\n   */\n  tabindex = 0;\n  /**\n   * Defines template option for start.\n   * @group Templates\n   */\n  startTemplate;\n  /**\n   * Defines template option for end.\n   * @group Templates\n   */\n  endTemplate;\n  /**\n   * Defines template option for menu icon.\n   * @group Templates\n   */\n  menuIconTemplate;\n  /**\n   * Defines template option for submenu icon.\n   * @group Templates\n   */\n  submenuIconTemplate;\n  /**\n   * Defines template option for submenu icon.\n   * @group Templates\n   */\n  itemTemplate;\n  /**\n   * Custom menu button template on responsive mode.\n   * @group Templates\n   */\n  buttonTemplate;\n  /**\n   * Custom menu button icon template on responsive mode.\n   * @group Templates\n   */\n  buttonIconTemplate;\n  templates;\n  menubuttonViewChild;\n  rootmenu;\n  container;\n  _startTemplate;\n  _endTemplate;\n  _menuIconTemplate;\n  _submenuIconTemplate;\n  _itemTemplate;\n  _buttonTemplate;\n  _buttonIconTemplate;\n  outsideClickListener;\n  resizeListener;\n  dirty = false;\n  focused = false;\n  activeItem = signal(null);\n  focusedItemInfo = signal({\n    index: -1,\n    level: 0,\n    parentKey: '',\n    item: null\n  });\n  searchValue = '';\n  searchTimeout;\n  _processedItems;\n  _model;\n  _componentStyle = inject(MegaMenuStyle);\n  matchMediaListener;\n  query;\n  queryMatches = false;\n  mobileActive = false;\n  get visibleItems() {\n    const processedItem = isNotEmpty(this.activeItem()) ? this.activeItem() : null;\n    return processedItem ? processedItem.items.reduce((items, col) => {\n      col.forEach(submenu => {\n        submenu.items.forEach(a => {\n          items.push(a);\n        });\n      });\n      return items;\n    }, []) : this.processedItems;\n  }\n  get processedItems() {\n    if (!this._processedItems || !this._processedItems.length) {\n      this._processedItems = this.createProcessedItems(this.model || []);\n    }\n    return this._processedItems;\n  }\n  get focusedItemId() {\n    const focusedItem = this.focusedItemInfo();\n    return focusedItem?.item && focusedItem.item?.id ? focusedItem.item.id : isNotEmpty(focusedItem.key) ? `${this.id}_${focusedItem.key}` : null;\n  }\n  constructor() {\n    super();\n    effect(() => {\n      const activeItem = this.activeItem();\n      if (isNotEmpty(activeItem)) {\n        this.bindOutsideClickListener();\n        this.bindResizeListener();\n      } else {\n        this.unbindOutsideClickListener();\n        this.unbindResizeListener();\n      }\n    });\n  }\n  ngOnInit() {\n    super.ngOnInit();\n    this.bindMatchMediaListener();\n    this.id = this.id || uuid('pn_id_');\n  }\n  ngAfterContentInit() {\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'start':\n          this._startTemplate = item.template;\n          break;\n        case 'end':\n          this._endTemplate = item.template;\n          break;\n        case 'menuicon':\n          this._menuIconTemplate = item.template;\n          break;\n        case 'submenuicon':\n          this._submenuIconTemplate = item.template;\n          break;\n        case 'item':\n          this._itemTemplate = item.template;\n          break;\n        case 'button':\n          this._buttonTemplate = item.template;\n          break;\n        case 'buttonicon':\n          this._buttonIconTemplate = item.template;\n          break;\n        default:\n          this._itemTemplate = item.template;\n          break;\n      }\n    });\n  }\n  bindMatchMediaListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.matchMediaListener) {\n        const query = window.matchMedia(`(max-width: ${this.breakpoint})`);\n        this.query = query;\n        this.queryMatches = query.matches;\n        this.matchMediaListener = () => {\n          this.queryMatches = query.matches;\n          this.mobileActive = false;\n          this.cd.markForCheck();\n        };\n        query.addEventListener('change', this.matchMediaListener);\n      }\n    }\n  }\n  unbindMatchMediaListener() {\n    if (this.matchMediaListener) {\n      this.query.removeEventListener('change', this.matchMediaListener);\n      this.matchMediaListener = null;\n    }\n  }\n  createProcessedItems(items, level = 0, parent = {}, parentKey = '', columnIndex) {\n    const processedItems = [];\n    items && items.forEach((item, index) => {\n      const key = (parentKey !== '' ? parentKey + '_' : '') + (columnIndex !== undefined ? columnIndex + '_' : '') + index;\n      const newItem = {\n        item,\n        index,\n        level,\n        key,\n        parent,\n        parentKey,\n        columnIndex: columnIndex !== undefined ? columnIndex : parent.columnIndex !== undefined ? parent.columnIndex : index\n      };\n      newItem['items'] = level === 0 && item.items && item.items.length > 0 ? item.items.map((_items, _index) => this.createProcessedItems(_items, level + 1, newItem, key, _index)) : this.createProcessedItems(item.items, level + 1, newItem, key);\n      processedItems.push(newItem);\n    });\n    return processedItems;\n  }\n  getItemProp(item, name) {\n    return item ? resolve(item[name]) : undefined;\n  }\n  onItemClick(event) {\n    const {\n      originalEvent,\n      processedItem\n    } = event;\n    const grouped = this.isProcessedItemGroup(processedItem);\n    const root = isEmpty(processedItem.parent);\n    const selected = this.isSelected(processedItem);\n    if (selected) {\n      const {\n        index,\n        key,\n        parentKey,\n        item\n      } = processedItem;\n      this.activeItem.set(null);\n      this.focusedItemInfo.set({\n        index,\n        key,\n        parentKey,\n        item\n      });\n      this.dirty = !root;\n      if (!this.mobileActive) {\n        focus(this.rootmenu?.menubarViewChild?.nativeElement, {\n          preventScroll: true\n        });\n      }\n    } else {\n      if (grouped) {\n        this.onItemChange(event);\n      } else {\n        this.hide(originalEvent);\n      }\n    }\n  }\n  onItemMouseEnter(event) {\n    if (!this.mobileActive && this.dirty) {\n      this.onItemChange(event);\n    }\n  }\n  menuButtonClick(event) {\n    this.toggle(event);\n  }\n  menuButtonKeydown(event) {\n    (event.code === 'Enter' || event.code === 'NumpadEnter' || event.code === 'Space') && this.menuButtonClick(event);\n  }\n  toggle(event) {\n    if (this.mobileActive) {\n      this.mobileActive = false;\n      ZIndexUtils.clear(this.rootmenu.el.nativeElement);\n      this.hide();\n    } else {\n      this.mobileActive = true;\n      ZIndexUtils.set('menu', this.rootmenu.el.nativeElement, this.config.zIndex.menu);\n      setTimeout(() => {\n        this.show();\n      }, 0);\n    }\n    this.bindOutsideClickListener();\n    event.preventDefault();\n  }\n  show() {\n    this.focusedItemInfo.set({\n      index: this.findFirstFocusedItemIndex(),\n      level: 0,\n      parentKey: ''\n    });\n    focus(this.rootmenu?.el.nativeElement);\n  }\n  scrollInView(index = -1) {\n    const id = index !== -1 ? `${this.id}_${index}` : this.focusedItemId;\n    let element;\n    if (id === null && this.queryMatches) {\n      element = this.menubuttonViewChild.nativeElement;\n    } else {\n      element = findSingle(this.rootmenu?.menubarViewChild?.nativeElement, `li[id=\"${id}\"]`);\n    }\n    if (element) {\n      element.scrollIntoView && element.scrollIntoView({\n        block: 'nearest',\n        inline: 'nearest',\n        behavior: 'smooth'\n      });\n    }\n  }\n  onItemChange(event) {\n    const {\n      processedItem,\n      isFocus\n    } = event;\n    if (isEmpty(processedItem)) return;\n    const {\n      index,\n      key,\n      parentKey,\n      items,\n      item\n    } = processedItem;\n    const grouped = isNotEmpty(items);\n    if (grouped) {\n      this.activeItem.set(processedItem);\n    }\n    this.focusedItemInfo.set({\n      index,\n      key,\n      parentKey,\n      item\n    });\n    grouped && (this.dirty = true);\n    isFocus && focus(this.rootmenu?.menubarViewChild?.nativeElement);\n  }\n  hide(event, isFocus) {\n    if (this.mobileActive) {\n      this.mobileActive = false;\n      setTimeout(() => {\n        focus(this.menubuttonViewChild?.nativeElement);\n        this.scrollInView();\n      }, 100);\n    }\n    this.activeItem.set(null);\n    this.focusedItemInfo.set({\n      index: -1,\n      key: '',\n      parentKey: '',\n      item: null\n    });\n    isFocus && focus(this.rootmenu?.menubarViewChild?.nativeElement);\n    this.dirty = false;\n  }\n  onMenuFocus(event) {\n    this.focused = true;\n    if (this.focusedItemInfo().index === -1) {\n      const index = this.findFirstFocusedItemIndex();\n      const processedItem = this.findVisibleItem(index);\n      this.focusedItemInfo.set({\n        index,\n        key: processedItem.key,\n        parentKey: processedItem.parentKey,\n        item: processedItem.item\n      });\n    }\n  }\n  onMenuBlur(event) {\n    this.focused = false;\n    this.focusedItemInfo.set({\n      index: -1,\n      level: 0,\n      parentKey: '',\n      item: null\n    });\n    this.searchValue = '';\n    this.dirty = false;\n  }\n  onKeyDown(event) {\n    const metaKey = event.metaKey || event.ctrlKey;\n    switch (event.code) {\n      case 'ArrowDown':\n        this.onArrowDownKey(event);\n        break;\n      case 'ArrowUp':\n        this.onArrowUpKey(event);\n        break;\n      case 'ArrowLeft':\n        this.onArrowLeftKey(event);\n        break;\n      case 'ArrowRight':\n        this.onArrowRightKey(event);\n        break;\n      case 'Home':\n        this.onHomeKey(event);\n        break;\n      case 'End':\n        this.onEndKey(event);\n        break;\n      case 'Space':\n        this.onSpaceKey(event);\n        break;\n      case 'Enter':\n        this.onEnterKey(event);\n        break;\n      case 'Escape':\n        this.onEscapeKey(event);\n        break;\n      case 'Tab':\n        this.onTabKey(event);\n        break;\n      case 'PageDown':\n      case 'PageUp':\n      case 'Backspace':\n      case 'ShiftLeft':\n      case 'ShiftRight':\n        //NOOP\n        break;\n      default:\n        if (!metaKey && isPrintableCharacter(event.key)) {\n          this.searchItems(event, event.key);\n        }\n        break;\n    }\n  }\n  findFirstFocusedItemIndex() {\n    const selectedIndex = this.findSelectedItemIndex();\n    return selectedIndex < 0 ? this.findFirstItemIndex() : selectedIndex;\n  }\n  findFirstItemIndex() {\n    return this.visibleItems.findIndex(processedItem => this.isValidItem(processedItem));\n  }\n  findSelectedItemIndex() {\n    return this.visibleItems.findIndex(processedItem => this.isValidSelectedItem(processedItem));\n  }\n  isProcessedItemGroup(processedItem) {\n    return processedItem && isNotEmpty(processedItem.items);\n  }\n  isSelected(processedItem) {\n    return isNotEmpty(this.activeItem()) ? this.activeItem().key === processedItem.key : false;\n  }\n  isValidSelectedItem(processedItem) {\n    return this.isValidItem(processedItem) && this.isSelected(processedItem);\n  }\n  isValidItem(processedItem) {\n    return !!processedItem && !this.isItemDisabled(processedItem.item) && !this.isItemSeparator(processedItem.item);\n  }\n  isItemDisabled(item) {\n    return this.getItemProp(item, 'disabled');\n  }\n  isItemSeparator(item) {\n    return this.getItemProp(item, 'separator');\n  }\n  isItemMatched(processedItem) {\n    return this.isValidItem(processedItem) && this.getProccessedItemLabel(processedItem).toLocaleLowerCase().startsWith(this.searchValue.toLocaleLowerCase());\n  }\n  isProccessedItemGroup(processedItem) {\n    return processedItem && isNotEmpty(processedItem.items);\n  }\n  searchItems(event, char) {\n    this.searchValue = (this.searchValue || '') + char;\n    let itemIndex = -1;\n    let matched = false;\n    if (this.focusedItemInfo().index !== -1) {\n      itemIndex = this.visibleItems.slice(this.focusedItemInfo().index).findIndex(processedItem => this.isItemMatched(processedItem));\n      itemIndex = itemIndex === -1 ? this.visibleItems.slice(0, this.focusedItemInfo().index).findIndex(processedItem => this.isItemMatched(processedItem)) : itemIndex + this.focusedItemInfo().index;\n    } else {\n      itemIndex = this.visibleItems.findIndex(processedItem => this.isItemMatched(processedItem));\n    }\n    if (itemIndex !== -1) {\n      matched = true;\n    }\n    if (itemIndex === -1 && this.focusedItemInfo().index === -1) {\n      itemIndex = this.findFirstFocusedItemIndex();\n    }\n    if (itemIndex !== -1) {\n      this.changeFocusedItemInfo(event, itemIndex);\n    }\n    if (this.searchTimeout) {\n      clearTimeout(this.searchTimeout);\n    }\n    this.searchTimeout = setTimeout(() => {\n      this.searchValue = '';\n      this.searchTimeout = null;\n    }, 500);\n    return matched;\n  }\n  getProccessedItemLabel(processedItem) {\n    return processedItem ? this.getItemLabel(processedItem.item) : undefined;\n  }\n  getItemLabel(item) {\n    return this.getItemProp(item, 'label');\n  }\n  changeFocusedItemInfo(event, index) {\n    const processedItem = this.findVisibleItem(index);\n    if (isNotEmpty(processedItem)) {\n      const {\n        key,\n        parentKey,\n        item\n      } = processedItem;\n      this.focusedItemInfo.set({\n        index,\n        key: key ? key : '',\n        parentKey,\n        item\n      });\n    }\n    this.scrollInView();\n  }\n  onArrowDownKey(event) {\n    if (this.orientation === 'horizontal') {\n      if (isNotEmpty(this.activeItem()) && this.activeItem().key === this.focusedItemInfo().key) {\n        const {\n          key,\n          item\n        } = this.activeItem();\n        this.focusedItemInfo.set({\n          index: -1,\n          key: '',\n          parentKey: key,\n          item\n        });\n      } else {\n        const processedItem = this.findVisibleItem(this.focusedItemInfo().index);\n        const grouped = this.isProccessedItemGroup(processedItem);\n        if (grouped) {\n          const {\n            parentKey,\n            key,\n            item\n          } = processedItem;\n          this.onItemChange({\n            originalEvent: event,\n            processedItem\n          });\n          this.focusedItemInfo.set({\n            index: -1,\n            key: key,\n            parentKey: parentKey,\n            item: item\n          });\n          this.searchValue = '';\n        }\n      }\n    }\n    const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n    this.changeFocusedItemInfo(event, itemIndex);\n    event.preventDefault();\n  }\n  onArrowRightKey(event) {\n    const processedItem = this.findVisibleItem(this.focusedItemInfo().index);\n    const grouped = this.isProccessedItemGroup(processedItem);\n    if (grouped) {\n      if (this.orientation === 'vertical') {\n        if (isNotEmpty(this.activeItem()) && this.activeItem().key === processedItem.key) {\n          this.focusedItemInfo.set({\n            index: -1,\n            key: '',\n            parentKey: this.activeItem().key,\n            item: processedItem.item\n          });\n        } else {\n          const processedItem = this.findVisibleItem(this.focusedItemInfo().index);\n          const grouped = this.isProccessedItemGroup(processedItem);\n          if (grouped) {\n            this.onItemChange({\n              originalEvent: event,\n              processedItem\n            });\n            this.focusedItemInfo.set({\n              index: -1,\n              key: processedItem.key,\n              parentKey: processedItem.parentKey,\n              item: processedItem.item\n            });\n            this.searchValue = '';\n          }\n        }\n      }\n      const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n      this.changeFocusedItemInfo(event, itemIndex);\n    } else {\n      const columnIndex = processedItem.columnIndex + 1;\n      const itemIndex = this.visibleItems.findIndex(item => item.columnIndex === columnIndex);\n      itemIndex !== -1 && this.changeFocusedItemInfo(event, itemIndex);\n    }\n    event.preventDefault();\n  }\n  onArrowUpKey(event) {\n    if (event.altKey && this.orientation === 'horizontal') {\n      if (this.focusedItemInfo().index !== -1) {\n        const processedItem = this.findVisibleItem(this.focusedItemInfo().index);\n        const grouped = this.isProccessedItemGroup(processedItem);\n        if (!grouped && isNotEmpty(this.activeItem)) {\n          if (this.focusedItemInfo().index === 0) {\n            this.focusedItemInfo.set({\n              index: this.activeItem().index,\n              key: this.activeItem().key,\n              parentKey: this.activeItem().parentKey,\n              item: processedItem.item\n            });\n            this.activeItem.set(null);\n          } else {\n            this.changeFocusedItemInfo(event, this.findFirstItemIndex());\n          }\n        }\n      }\n      event.preventDefault();\n    } else {\n      const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n      this.changeFocusedItemInfo(event, itemIndex);\n      event.preventDefault();\n    }\n  }\n  onArrowLeftKey(event) {\n    const processedItem = this.findVisibleItem(this.focusedItemInfo().index);\n    const grouped = this.isProccessedItemGroup(processedItem);\n    if (grouped) {\n      if (this.orientation === 'horizontal') {\n        const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n        this.changeFocusedItemInfo(event, itemIndex);\n      }\n    } else {\n      if (this.orientation === 'vertical' && isNotEmpty(this.activeItem())) {\n        if (processedItem.columnIndex === 0) {\n          this.focusedItemInfo.set({\n            index: this.activeItem().index,\n            key: this.activeItem().key,\n            parentKey: this.activeItem().parentKey,\n            item: processedItem.item\n          });\n          this.activeItem.set(null);\n        }\n      }\n      const columnIndex = processedItem.columnIndex - 1;\n      const itemIndex = this.visibleItems.findIndex(item => item.columnIndex === columnIndex);\n      itemIndex !== -1 && this.changeFocusedItemInfo(event, itemIndex);\n    }\n    event.preventDefault();\n  }\n  onHomeKey(event) {\n    this.changeFocusedItemInfo(event, this.findFirstItemIndex());\n    event.preventDefault();\n  }\n  onEndKey(event) {\n    this.changeFocusedItemInfo(event, this.findLastItemIndex());\n    event.preventDefault();\n  }\n  onSpaceKey(event) {\n    this.onEnterKey(event);\n  }\n  onEscapeKey(event) {\n    if (isNotEmpty(this.activeItem())) {\n      this.focusedItemInfo.set({\n        index: this.activeItem().index,\n        key: this.activeItem().key,\n        item: this.activeItem().item\n      });\n      this.activeItem.set(null);\n    }\n    event.preventDefault();\n  }\n  onTabKey(event) {\n    if (this.focusedItemInfo().index !== -1) {\n      const processedItem = this.findVisibleItem(this.focusedItemInfo().index);\n      const grouped = this.isProccessedItemGroup(processedItem);\n      !grouped && this.onItemChange({\n        originalEvent: event,\n        processedItem\n      });\n    }\n    this.hide();\n  }\n  onEnterKey(event) {\n    if (this.focusedItemInfo().index !== -1) {\n      const element = findSingle(this.rootmenu?.el?.nativeElement, `li[id=\"${`${this.focusedItemId}`}\"]`);\n      const anchorElement = element && findSingle(element, 'a[data-pc-section=\"action\"]');\n      anchorElement ? anchorElement.click() : element && element.click();\n      const processedItem = this.visibleItems[this.focusedItemInfo().index];\n      const grouped = this.isProccessedItemGroup(processedItem);\n      !grouped && this.changeFocusedItemInfo(event, this.findFirstFocusedItemIndex());\n    }\n    event.preventDefault();\n  }\n  findVisibleItem(index) {\n    return isNotEmpty(this.visibleItems) ? this.visibleItems[index] : null;\n  }\n  findLastFocusedItemIndex() {\n    const selectedIndex = this.findSelectedItemIndex();\n    return selectedIndex < 0 ? this.findLastItemIndex() : selectedIndex;\n  }\n  findLastItemIndex() {\n    return findLastIndex(this.visibleItems, processedItem => this.isValidItem(processedItem));\n  }\n  findPrevItemIndex(index) {\n    const matchedItemIndex = index > 0 ? findLastIndex(this.visibleItems.slice(0, index), processedItem => this.isValidItem(processedItem)) : -1;\n    return matchedItemIndex > -1 ? matchedItemIndex : index;\n  }\n  findNextItemIndex(index) {\n    const matchedItemIndex = index < this.visibleItems.length - 1 ? this.visibleItems.slice(index + 1).findIndex(processedItem => this.isValidItem(processedItem)) : -1;\n    return matchedItemIndex > -1 ? matchedItemIndex + index + 1 : index;\n  }\n  bindResizeListener() {\n    if (!this.resizeListener) {\n      this.resizeListener = event => {\n        if (!isTouchDevice()) {\n          this.hide(event, true);\n        }\n        this.mobileActive = false;\n      };\n      window.addEventListener('resize', this.resizeListener);\n    }\n  }\n  bindOutsideClickListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.outsideClickListener) {\n        this.outsideClickListener = this.renderer.listen(this.document, 'click', event => {\n          const isOutsideContainer = this.container?.nativeElement !== event.target && !this.container?.nativeElement.contains(event.target);\n          if (isOutsideContainer) {\n            this.hide();\n          }\n        });\n      }\n    }\n  }\n  unbindOutsideClickListener() {\n    if (this.outsideClickListener) {\n      this.outsideClickListener();\n      this.outsideClickListener = null;\n    }\n  }\n  unbindResizeListener() {\n    if (this.resizeListener) {\n      window.removeEventListener('resize', this.resizeListener);\n      this.resizeListener = null;\n    }\n  }\n  ngOnDestroy() {\n    this.unbindOutsideClickListener();\n    this.unbindResizeListener();\n    this.unbindMatchMediaListener();\n    super.ngOnDestroy();\n  }\n  static ɵfac = function MegaMenu_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MegaMenu)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MegaMenu,\n    selectors: [[\"p-megaMenu\"], [\"p-megamenu\"], [\"p-mega-menu\"]],\n    contentQueries: function MegaMenu_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, _c6, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c7, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c8, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c9, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c10, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c11, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c12, 4);\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.startTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.endTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menuIconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.submenuIconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.itemTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.buttonTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.buttonIconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function MegaMenu_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c13, 5);\n        i0.ɵɵviewQuery(_c14, 5);\n        i0.ɵɵviewQuery(_c15, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menubuttonViewChild = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.rootmenu = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.container = _t.first);\n      }\n    },\n    inputs: {\n      model: \"model\",\n      style: \"style\",\n      styleClass: \"styleClass\",\n      orientation: \"orientation\",\n      id: \"id\",\n      ariaLabel: \"ariaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\",\n      breakpoint: \"breakpoint\",\n      scrollHeight: \"scrollHeight\",\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      tabindex: [2, \"tabindex\", \"tabindex\", numberAttribute]\n    },\n    features: [i0.ɵɵProvidersFeature([MegaMenuStyle]), i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature],\n    decls: 8,\n    vars: 36,\n    consts: [[\"container\", \"\"], [\"rootmenu\", \"\"], [\"menubutton\", \"\"], [3, \"ngClass\", \"ngStyle\"], [\"class\", \"p-megamenu-start\", 4, \"ngIf\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [3, \"itemClick\", \"menuFocus\", \"menuBlur\", \"menuKeydown\", \"itemMouseEnter\", \"itemTemplate\", \"items\", \"menuId\", \"root\", \"orientation\", \"ariaLabel\", \"disabled\", \"tabindex\", \"activeItem\", \"level\", \"ariaLabelledBy\", \"focusedItemId\", \"mobileActive\", \"queryMatches\", \"scrollHeight\"], [\"class\", \"p-megamenu-end\", 4, \"ngIf\"], [1, \"p-megamenu-start\"], [\"role\", \"button\", \"tabindex\", \"0\", \"class\", \"p-megamenu-button\", 3, \"click\", \"keydown\", 4, \"ngIf\"], [\"role\", \"button\", \"tabindex\", \"0\", 1, \"p-megamenu-button\", 3, \"click\", \"keydown\"], [1, \"p-megamenu-end\"]],\n    template: function MegaMenu_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 3, 0);\n        i0.ɵɵtemplate(2, MegaMenu_div_2_Template, 2, 1, \"div\", 4)(3, MegaMenu_ng_container_3_Template, 2, 1, \"ng-container\", 5)(4, MegaMenu_ng_container_4_Template, 1, 0, \"ng-container\", 6);\n        i0.ɵɵelementStart(5, \"p-megamenu-sub\", 7, 1);\n        i0.ɵɵlistener(\"itemClick\", function MegaMenu_Template_p_megamenu_sub_itemClick_5_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onItemClick($event));\n        })(\"menuFocus\", function MegaMenu_Template_p_megamenu_sub_menuFocus_5_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onMenuFocus($event));\n        })(\"menuBlur\", function MegaMenu_Template_p_megamenu_sub_menuBlur_5_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onMenuBlur($event));\n        })(\"menuKeydown\", function MegaMenu_Template_p_megamenu_sub_menuKeydown_5_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onKeyDown($event));\n        })(\"itemMouseEnter\", function MegaMenu_Template_p_megamenu_sub_itemMouseEnter_5_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onItemMouseEnter($event));\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(7, MegaMenu_div_7_Template, 2, 1, \"div\", 8);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵstyleMap(i0.ɵɵpureFunction1(29, _c16, ctx.orientation == \"vertical\" && ctx.queryMatches ? \"row\" : \"\"));\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(31, _c17, ctx.queryMatches, ctx.mobileActive, ctx.orientation == \"horizontal\", ctx.orientation == \"vertical\"))(\"ngStyle\", ctx.style);\n        i0.ɵɵattribute(\"data-pc-section\", \"root\")(\"data-pc-name\", \"megamenu\")(\"id\", ctx.id);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.startTemplate || ctx._startTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.buttonTemplate && !ctx._buttonTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.buttonTemplate || ctx._buttonTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"itemTemplate\", ctx.itemTemplate || ctx._itemTemplate)(\"items\", ctx.processedItems)(\"menuId\", ctx.id)(\"root\", true)(\"orientation\", ctx.orientation)(\"ariaLabel\", ctx.ariaLabel)(\"disabled\", ctx.disabled)(\"tabindex\", !ctx.disabled ? ctx.tabindex : -1)(\"activeItem\", ctx.activeItem())(\"level\", 0)(\"ariaLabelledBy\", ctx.ariaLabelledBy)(\"focusedItemId\", ctx.focused ? ctx.focusedItemId : undefined)(\"mobileActive\", ctx.mobileActive)(\"queryMatches\", ctx.queryMatches)(\"scrollHeight\", ctx.scrollHeight);\n        i0.ɵɵattribute(\"id\", ctx.id + \"_list\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.endTemplate || ctx._endTemplate);\n      }\n    },\n    dependencies: [CommonModule, i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, RouterModule, MegaMenuSub, TooltipModule, BarsIcon, BadgeModule, SharedModule],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MegaMenu, [{\n    type: Component,\n    args: [{\n      selector: 'p-megaMenu, p-megamenu, p-mega-menu',\n      standalone: true,\n      imports: [CommonModule, RouterModule, MegaMenuSub, TooltipModule, BarsIcon, BadgeModule, SharedModule],\n      template: `\n        <div\n            [ngClass]=\"{\n                'p-megamenu p-component': true,\n                'p-megamenu-mobile': queryMatches,\n                'p-megamenu-mobile-active': mobileActive,\n                'p-megamenu-horizontal': orientation == 'horizontal',\n                'p-megamenu-vertical': orientation == 'vertical'\n            }\"\n            [class]=\"styleClass\"\n            [style]=\"{ flexDirection: orientation == 'vertical' && queryMatches ? 'row' : '' }\"\n            [ngStyle]=\"style\"\n            [attr.data-pc-section]=\"'root'\"\n            [attr.data-pc-name]=\"'megamenu'\"\n            [attr.id]=\"id\"\n            #container\n        >\n            <div class=\"p-megamenu-start\" *ngIf=\"startTemplate || _startTemplate\">\n                <ng-container *ngTemplateOutlet=\"startTemplate || _startTemplate\"></ng-container>\n            </div>\n            <ng-container *ngIf=\"!buttonTemplate && !_buttonTemplate\">\n                <a\n                    *ngIf=\"model && model.length > 0\"\n                    #menubutton\n                    role=\"button\"\n                    tabindex=\"0\"\n                    class=\"p-megamenu-button\"\n                    [attr.aria-haspopup]=\"model.length && model.length > 0 ? true : false\"\n                    [attr.aria-expanded]=\"mobileActive\"\n                    [attr.aria-controls]=\"id\"\n                    [attr.aria-label]=\"config.translation.aria.navigation\"\n                    (click)=\"menuButtonClick($event)\"\n                    (keydown)=\"menuButtonKeydown($event)\"\n                >\n                    <BarsIcon *ngIf=\"!buttonIconTemplate && !_buttonIconTemplate\" />\n                    <ng-template *ngTemplateOutlet=\"buttonIconTemplate || _buttonIconTemplate\"></ng-template>\n                </a>\n            </ng-container>\n            <ng-container *ngTemplateOutlet=\"buttonTemplate || _buttonTemplate\"></ng-container>\n            <p-megamenu-sub\n                #rootmenu\n                [itemTemplate]=\"itemTemplate || _itemTemplate\"\n                [items]=\"processedItems\"\n                [attr.id]=\"id + '_list'\"\n                [menuId]=\"id\"\n                [root]=\"true\"\n                [orientation]=\"orientation\"\n                [ariaLabel]=\"ariaLabel\"\n                [disabled]=\"disabled\"\n                [tabindex]=\"!disabled ? tabindex : -1\"\n                [activeItem]=\"activeItem()\"\n                [level]=\"0\"\n                [ariaLabelledBy]=\"ariaLabelledBy\"\n                [focusedItemId]=\"focused ? focusedItemId : undefined\"\n                [mobileActive]=\"mobileActive\"\n                (itemClick)=\"onItemClick($event)\"\n                (menuFocus)=\"onMenuFocus($event)\"\n                (menuBlur)=\"onMenuBlur($event)\"\n                (menuKeydown)=\"onKeyDown($event)\"\n                (itemMouseEnter)=\"onItemMouseEnter($event)\"\n                [queryMatches]=\"queryMatches\"\n                [scrollHeight]=\"scrollHeight\"\n            ></p-megamenu-sub>\n            <div class=\"p-megamenu-end\" *ngIf=\"endTemplate || _endTemplate\">\n                <ng-container *ngTemplateOutlet=\"endTemplate || _endTemplate\"></ng-container>\n            </div>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [MegaMenuStyle]\n    }]\n  }], () => [], {\n    model: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    orientation: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    breakpoint: [{\n      type: Input\n    }],\n    scrollHeight: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tabindex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    startTemplate: [{\n      type: ContentChild,\n      args: ['start', {\n        descendants: false\n      }]\n    }],\n    endTemplate: [{\n      type: ContentChild,\n      args: ['end', {\n        descendants: false\n      }]\n    }],\n    menuIconTemplate: [{\n      type: ContentChild,\n      args: ['menuicon', {\n        descendants: false\n      }]\n    }],\n    submenuIconTemplate: [{\n      type: ContentChild,\n      args: ['submenuicon', {\n        descendants: false\n      }]\n    }],\n    itemTemplate: [{\n      type: ContentChild,\n      args: ['item', {\n        descendants: false\n      }]\n    }],\n    buttonTemplate: [{\n      type: ContentChild,\n      args: ['button', {\n        descendants: false\n      }]\n    }],\n    buttonIconTemplate: [{\n      type: ContentChild,\n      args: ['buttonicon', {\n        descendants: false\n      }]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    menubuttonViewChild: [{\n      type: ViewChild,\n      args: ['menubutton']\n    }],\n    rootmenu: [{\n      type: ViewChild,\n      args: ['rootmenu']\n    }],\n    container: [{\n      type: ViewChild,\n      args: ['container']\n    }]\n  });\n})();\nclass MegaMenuModule {\n  static ɵfac = function MegaMenuModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MegaMenuModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MegaMenuModule,\n    imports: [MegaMenu, SharedModule],\n    exports: [MegaMenu, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MegaMenu, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MegaMenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MegaMenu, SharedModule],\n      exports: [MegaMenu, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MegaMenu, MegaMenuClasses, MegaMenuModule, MegaMenuStyle, MegaMenuSub };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBA,IAAM,MAAM,CAAC,SAAS;AACtB,IAAM,MAAM,SAAO;AAAA,EACjB,cAAc;AAAA,EACd,UAAU;AACZ;AACA,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,wBAAwB;AAAA,EACxB,sBAAsB;AACxB;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,wBAAwB;AAAA,EACxB,cAAc;AAChB;AACA,IAAM,MAAM,OAAO;AAAA,EACjB,OAAO;AACT;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,YAAY,OAAO,SAAS,OAAO,CAAC;AACzD,IAAG,WAAW,WAAW,OAAO,sBAAsB,OAAO,OAAO,CAAC;AACrE,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,aAAa,OAAO,OAAO,GAAG,GAAG;AAAA,EACrE;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,MAAM,EAAE;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,EAAE;AAC5C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,YAAY,kBAAkB,OAAO,CAAC;AAC3D,IAAG,WAAW,WAAW,OAAO,sBAAsB,gBAAgB,CAAC;AACvE,IAAG,YAAY,MAAM,OAAO,UAAU,gBAAgB,CAAC,EAAE,mBAAmB,WAAW;AAAA,EACzF;AACF;AACA,SAAS,uEAAuE,IAAI,KAAK;AACvF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,YAAY,kBAAkB,MAAM,CAAC,EAAE,WAAW,OAAO,YAAY,kBAAkB,WAAW,CAAC;AACnI,IAAG,YAAY,mBAAmB,MAAM,EAAE,YAAY,EAAE;AAAA,EAC1D;AACF;AACA,SAAS,uEAAuE,IAAI,KAAK;AACvF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,mBAAmB,OAAO;AACzC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,aAAa,gBAAgB,GAAG,GAAG;AAAA,EACvE;AACF;AACA,SAAS,8EAA8E,IAAI,KAAK;AAC9F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,aAAa,OAAO,aAAa,gBAAgB,GAAM,cAAc;AACnF,IAAG,YAAY,mBAAmB,OAAO;AAAA,EAC3C;AACF;AACA,SAAS,0EAA0E,IAAI,KAAK;AAC1F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,EAAE;AAAA,EAC/B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,cAAc,OAAO,YAAY,kBAAkB,iBAAiB,CAAC,EAAE,SAAS,OAAO,YAAY,kBAAkB,OAAO,CAAC;AAAA,EAC7I;AACF;AACA,SAAS,4GAA4G,IAAI,KAAK;AAC5H,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,iBAAiB,EAAE;AAAA,EACrC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,WAAW,yBAAyB;AAClD,IAAG,YAAY,mBAAmB,aAAa,EAAE,eAAe,IAAI;AAAA,EACtE;AACF;AACA,SAAS,6HAA6H,IAAI,KAAK;AAC7I,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,kBAAkB,EAAE;AAAA,EACtC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,WAAW,yBAAyB;AAClD,IAAG,YAAY,mBAAmB,aAAa,EAAE,eAAe,IAAI;AAAA,EACtE;AACF;AACA,SAAS,4GAA4G,IAAI,KAAK;AAC5H,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,8HAA8H,GAAG,GAAG,kBAAkB,EAAE;AAAA,EAC3K;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,QAAQ,OAAO,gBAAgB,UAAU;AAAA,EACzD;AACF;AACA,SAAS,8FAA8F,IAAI,KAAK;AAC9G,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,6GAA6G,GAAG,GAAG,iBAAiB,EAAE,EAAE,GAAG,6GAA6G,GAAG,GAAG,kBAAkB,EAAE;AACnS,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,gBAAgB,gBAAgB,OAAO,eAAe,IAAI,CAAC;AAAA,EACrF;AACF;AACA,SAAS,+FAA+F,IAAI,KAAK;AAAC;AAClH,SAAS,iFAAiF,IAAI,KAAK;AACjG,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,gGAAgG,GAAG,GAAG,eAAe,EAAE;AAAA,EAC1I;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,mBAAmB,aAAa,EAAE,eAAe,IAAI;AAAA,EACrE;AACF;AACA,SAAS,+EAA+E,IAAI,KAAK;AAC/F,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,+FAA+F,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,kFAAkF,GAAG,GAAG,MAAM,EAAE;AAC7O,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,SAAS,uBAAuB,CAAC,OAAO,SAAS,oBAAoB;AACnG,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,SAAS,uBAAuB,OAAO,SAAS,oBAAoB;AAAA,EAC/G;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,GAAG,wEAAwE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,wEAAwE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,+EAA+E,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,2EAA2E,GAAG,GAAG,WAAW,EAAE,EAAE,GAAG,gFAAgF,GAAG,GAAG,gBAAgB,EAAE;AACjiB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAkB,YAAY,CAAC;AACrC,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,UAAU,OAAO,YAAY,kBAAkB,QAAQ,CAAC,EAAE,WAAc,gBAAgB,IAAI,KAAK,OAAO,YAAY,kBAAkB,UAAU,CAAC,CAAC;AAChK,IAAG,YAAY,QAAQ,OAAO,YAAY,kBAAkB,KAAK,GAAM,aAAa,EAAE,qBAAqB,OAAO,YAAY,kBAAkB,cAAc,CAAC,EAAE,mBAAmB,QAAQ,EAAE,YAAY,EAAE;AAC5M,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,MAAM,CAAC;AAClE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,QAAQ,CAAC,EAAE,YAAY,YAAY;AAC9F,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,OAAO,CAAC;AACnE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,gBAAgB,CAAC;AAAA,EAC5D;AACF;AACA,SAAS,uEAAuE,IAAI,KAAK;AACvF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,YAAY,kBAAkB,MAAM,CAAC,EAAE,WAAW,OAAO,YAAY,kBAAkB,WAAW,CAAC;AACnI,IAAG,YAAY,mBAAmB,MAAM,EAAE,YAAY,EAAE;AAAA,EAC1D;AACF;AACA,SAAS,uEAAuE,IAAI,KAAK;AACvF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,aAAa,gBAAgB,CAAC;AAAA,EAC5D;AACF;AACA,SAAS,8EAA8E,IAAI,KAAK;AAC9F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,aAAa,OAAO,aAAa,gBAAgB,GAAM,cAAc;AACnF,IAAG,YAAY,mBAAmB,OAAO;AAAA,EAC3C;AACF;AACA,SAAS,0EAA0E,IAAI,KAAK;AAC1F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,EAAE;AAAA,EAC/B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,cAAc,OAAO,YAAY,kBAAkB,iBAAiB,CAAC,EAAE,SAAS,OAAO,YAAY,kBAAkB,OAAO,CAAC;AAAA,EAC7I;AACF;AACA,SAAS,8GAA8G,IAAI,KAAK;AAC9H,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,iBAAiB,EAAE;AAAA,EACrC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,yBAAyB;AACrD,IAAG,YAAY,mBAAmB,aAAa,EAAE,eAAe,IAAI;AAAA,EACtE;AACF;AACA,SAAS,+GAA+G,IAAI,KAAK;AAC/H,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,kBAAkB,EAAE;AAAA,EACtC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,yBAAyB;AACrD,IAAG,YAAY,mBAAmB,aAAa,EAAE,eAAe,IAAI;AAAA,EACtE;AACF;AACA,SAAS,8FAA8F,IAAI,KAAK;AAC9G,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,+GAA+G,GAAG,GAAG,iBAAiB,EAAE,EAAE,GAAG,gHAAgH,GAAG,GAAG,kBAAkB,EAAE;AACxS,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,gBAAgB,YAAY;AACzD,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,gBAAgB,UAAU;AAAA,EACzD;AACF;AACA,SAAS,+FAA+F,IAAI,KAAK;AAAC;AAClH,SAAS,iFAAiF,IAAI,KAAK;AACjG,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,gGAAgG,GAAG,GAAG,eAAe,EAAE;AAAA,EAC1I;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,mBAAmB,aAAa,EAAE,eAAe,IAAI;AAAA,EACrE;AACF;AACA,SAAS,+EAA+E,IAAI,KAAK;AAC/F,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,+FAA+F,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,kFAAkF,GAAG,GAAG,MAAM,EAAE;AAC7O,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,SAAS,uBAAuB,CAAC,OAAO,SAAS,oBAAoB;AACnG,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,SAAS,uBAAuB,OAAO,SAAS,oBAAoB;AAAA,EAC/G;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,GAAG,wEAAwE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,wEAAwE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,+EAA+E,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,2EAA2E,GAAG,GAAG,WAAW,EAAE,EAAE,GAAG,gFAAgF,GAAG,GAAG,gBAAgB,EAAE;AACjiB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,oBAAuB,YAAY,CAAC;AAC1C,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,cAAc,OAAO,YAAY,kBAAkB,YAAY,CAAC,EAAE,eAAe,OAAO,YAAY,kBAAkB,aAAa,CAAC,EAAE,oBAAoB,6BAA6B,EAAE,2BAA2B,OAAO,YAAY,kBAAkB,yBAAyB,KAAQ,gBAAgB,IAAI,GAAG,CAAC,EAAE,UAAU,OAAO,YAAY,kBAAkB,QAAQ,CAAC,EAAE,WAAc,gBAAgB,IAAI,KAAK,OAAO,YAAY,kBAAkB,UAAU,CAAC,CAAC,EAAE,YAAY,OAAO,YAAY,kBAAkB,UAAU,CAAC,EAAE,uBAAuB,OAAO,YAAY,kBAAkB,qBAAqB,CAAC,EAAE,oBAAoB,OAAO,YAAY,kBAAkB,kBAAkB,CAAC,EAAE,sBAAsB,OAAO,YAAY,kBAAkB,oBAAoB,CAAC,EAAE,cAAc,OAAO,YAAY,kBAAkB,YAAY,CAAC,EAAE,SAAS,OAAO,YAAY,kBAAkB,OAAO,CAAC;AACh4B,IAAG,YAAY,qBAAqB,OAAO,YAAY,kBAAkB,cAAc,CAAC,EAAE,YAAY,EAAE,EAAE,mBAAmB,QAAQ;AACrI,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,MAAM,CAAC;AAClE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,QAAQ,CAAC,EAAE,YAAY,iBAAiB;AACnG,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,OAAO,CAAC;AACnE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,gBAAgB,CAAC;AAAA,EAC5D;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,iEAAiE,GAAG,IAAI,KAAK,EAAE,EAAE,GAAG,iEAAiE,GAAG,IAAI,KAAK,EAAE;AACpL,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,YAAY,kBAAkB,YAAY,CAAC;AACzE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,YAAY,CAAC;AAAA,EAC1E;AACF;AACA,SAAS,4EAA4E,IAAI,KAAK;AAAC;AAC/F,SAAS,8DAA8D,IAAI,KAAK;AAC9E,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,6EAA6E,GAAG,GAAG,aAAa;AAAA,EACnH;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,+DAA+D,GAAG,GAAG,MAAM,EAAE;AAC9F,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,YAAY,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,iBAAiB,IAAI,CAAC;AAAA,EACrI;AACF;AACA,SAAS,0EAA0E,IAAI,KAAK;AAC1F,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,kBAAkB,EAAE;AACzC,IAAG,WAAW,aAAa,SAAS,8GAA8G,QAAQ;AACxJ,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,UAAU,KAAK,MAAM,CAAC;AAAA,IACrD,CAAC,EAAE,kBAAkB,SAAS,mHAAmH,QAAQ;AACvJ,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAa,IAAI;AACvB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,MAAM,OAAO,aAAa,UAAU,CAAC,EAAE,WAAW,UAAU,EAAE,SAAS,WAAW,KAAK,EAAE,gBAAgB,OAAO,YAAY,EAAE,gBAAgB,OAAO,YAAY,EAAE,UAAU,OAAO,MAAM,EAAE,iBAAiB,OAAO,aAAa,EAAE,SAAS,OAAO,QAAQ,CAAC,EAAE,QAAQ,KAAK;AAAA,EAC3R;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,2EAA2E,GAAG,GAAG,kBAAkB,EAAE;AACtH,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAS,IAAI;AACnB,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,eAAe,gBAAgB,CAAC;AAChE,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,MAAM;AAAA,EACjC;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE;AAC5C,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,OAAO,EAAE;AAC1F,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,IAAG,YAAY,mBAAmB,OAAO;AACzC,IAAG,UAAU;AACb,IAAG,YAAY,mBAAmB,MAAM;AACxC,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,iBAAiB,KAAK;AAAA,EACjD;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,IAAI,CAAC,EAAE,GAAG,OAAO,EAAE;AAC9C,IAAG,WAAW,SAAS,SAAS,kEAAkE,QAAQ;AACxG,MAAG,cAAc,GAAG;AACpB,YAAM,mBAAsB,cAAc,EAAE;AAC5C,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,YAAY,QAAQ,gBAAgB,CAAC;AAAA,IACpE,CAAC,EAAE,cAAc,SAAS,uEAAuE,QAAQ;AACvG,MAAG,cAAc,GAAG;AACpB,YAAM,mBAAsB,cAAc,EAAE;AAC5C,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,iBAAiB;AAAA,QAC5C;AAAA,QACA,eAAe;AAAA,MACjB,CAAC,CAAC;AAAA,IACJ,CAAC;AACD,IAAG,WAAW,GAAG,6DAA6D,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,6DAA6D,GAAG,GAAG,gBAAgB,EAAE;AAChM,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,OAAO,EAAE;AACpF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,mBAAmB,OAAO;AAChC,UAAM,YAAY,OAAO;AACzB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,YAAY,kBAAkB,YAAY,CAAC;AAChE,IAAG,WAAW,WAAW,OAAO,YAAY,kBAAkB,OAAO,CAAC,EAAE,WAAW,OAAO,aAAa,gBAAgB,CAAC,EAAE,kBAAkB,OAAO,YAAY,kBAAkB,gBAAgB,CAAC;AAClM,IAAG,YAAY,MAAM,OAAO,UAAU,gBAAgB,CAAC,EAAE,mBAAmB,UAAU,EAAE,oBAAoB,OAAO,aAAa,gBAAgB,CAAC,EAAE,kBAAkB,OAAO,cAAc,gBAAgB,CAAC,EAAE,mBAAmB,OAAO,eAAe,gBAAgB,CAAC,EAAE,cAAc,OAAO,aAAa,gBAAgB,CAAC,EAAE,iBAAiB,OAAO,eAAe,gBAAgB,KAAK,MAAS,EAAE,iBAAiB,OAAO,YAAY,gBAAgB,KAAK,CAAC,OAAO,YAAY,kBAAkB,IAAI,IAAI,SAAS,MAAS,EAAE,iBAAiB,OAAO,YAAY,gBAAgB,IAAI,OAAO,aAAa,gBAAgB,IAAI,MAAS,EAAE,cAAc,OAAO,QAAQ,CAAC,EAAE,gBAAgB,OAAO,eAAe,CAAC,EAAE,iBAAiB,OAAO,gBAAgB,SAAS,CAAC;AACtuB,IAAG,UAAU,CAAC;AACd,IAAG,YAAY,mBAAmB,SAAS;AAC3C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,YAAY;AAC1C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY;AACzC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,cAAc,gBAAgB,KAAK,OAAO,YAAY,gBAAgB,CAAC;AAAA,EACtG;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,8CAA8C,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,8CAA8C,GAAG,IAAI,MAAM,EAAE;AAAA,EAChJ;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAmB,IAAI;AAC7B,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,QAAQ,OAAO,cAAc,gBAAgB,KAAK,OAAO,YAAY,kBAAkB,WAAW,CAAC;AACjH,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,cAAc,gBAAgB,KAAK,CAAC,OAAO,YAAY,kBAAkB,WAAW,CAAC;AAAA,EACpH;AACF;AACA,SAAS,0BAA0B,IAAI,KAAK;AAC1C,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,GAAG,CAAC;AAC/B,IAAG,WAAW,WAAW,SAAS,gDAAgD,QAAQ;AACxF,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,KAAK,MAAM,CAAC;AAAA,IACvD,CAAC,EAAE,SAAS,SAAS,8CAA8C,QAAQ;AACzE,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,UAAU,KAAK,MAAM,CAAC;AAAA,IACrD,CAAC,EAAE,QAAQ,SAAS,6CAA6C,QAAQ;AACvE,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,SAAS,KAAK,MAAM,CAAC;AAAA,IACpD,CAAC;AACD,IAAG,WAAW,GAAG,gCAAgC,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,yCAAyC,GAAG,GAAG,eAAe,CAAC;AAClI,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAc,gBAAgB,IAAI,KAAK,OAAO,YAAY,CAAC;AAC9D,IAAG,WAAW,WAAc,gBAAgB,IAAI,KAAK,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC,EAAE,YAAY,OAAO,QAAQ;AAC5G,IAAG,YAAY,QAAQ,OAAO,OAAO,YAAY,MAAM,EAAE,MAAM,OAAO,EAAE,EAAE,oBAAoB,OAAO,WAAW,EAAE,yBAAyB,OAAO,aAAa,EAAE,mBAAmB,OAAO,OAAO,SAAS,SAAS;AACpN,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,OAAO;AACpC,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,KAAK;AAAA,EACvC;AACF;AACA,IAAM,MAAM,CAAC,OAAO;AACpB,IAAM,MAAM,CAAC,KAAK;AAClB,IAAM,MAAM,CAAC,UAAU;AACvB,IAAM,MAAM,CAAC,aAAa;AAC1B,IAAM,OAAO,CAAC,MAAM;AACpB,IAAM,OAAO,CAAC,QAAQ;AACtB,IAAM,OAAO,CAAC,YAAY;AAC1B,IAAM,OAAO,CAAC,YAAY;AAC1B,IAAM,OAAO,CAAC,UAAU;AACxB,IAAM,OAAO,CAAC,WAAW;AACzB,IAAM,OAAO,SAAO;AAAA,EAClB,eAAe;AACjB;AACA,IAAM,OAAO,CAAC,IAAI,IAAI,IAAI,QAAQ;AAAA,EAChC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,4BAA4B;AAAA,EAC5B,yBAAyB;AAAA,EACzB,uBAAuB;AACzB;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,wBAAwB,IAAI,KAAK;AACxC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,wCAAwC,GAAG,GAAG,gBAAgB,CAAC;AAChF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,iBAAiB,OAAO,cAAc;AAAA,EACjF;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,UAAU;AAAA,EAC5B;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AAAC;AACxE,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,sDAAsD,GAAG,GAAG,aAAa;AAAA,EAC5F;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,KAAK,IAAI,CAAC;AAC/B,IAAG,WAAW,SAAS,SAAS,wDAAwD,QAAQ;AAC9F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,gBAAgB,MAAM,CAAC;AAAA,IACtD,CAAC,EAAE,WAAW,SAAS,0DAA0D,QAAQ;AACvF,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,kBAAkB,MAAM,CAAC;AAAA,IACxD,CAAC;AACD,IAAG,WAAW,GAAG,iDAAiD,GAAG,GAAG,YAAY,CAAC,EAAE,GAAG,wCAAwC,GAAG,GAAG,MAAM,CAAC;AAC/I,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,iBAAiB,OAAO,MAAM,UAAU,OAAO,MAAM,SAAS,IAAI,OAAO,KAAK,EAAE,iBAAiB,OAAO,YAAY,EAAE,iBAAiB,OAAO,EAAE,EAAE,cAAc,OAAO,OAAO,YAAY,KAAK,UAAU;AACxN,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,CAAC,OAAO,sBAAsB,CAAC,OAAO,mBAAmB;AAC/E,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,sBAAsB,OAAO,mBAAmB;AAAA,EAC3F;AACF;AACA,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,sCAAsC,GAAG,GAAG,KAAK,EAAE;AACpE,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,SAAS,OAAO,MAAM,SAAS,CAAC;AAAA,EAC/D;AACF;AACA,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,wBAAwB,IAAI,KAAK;AACxC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,wCAAwC,GAAG,GAAG,gBAAgB,CAAC;AAChF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,eAAe,OAAO,YAAY;AAAA,EAC7E;AACF;AACA,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,kBAKY,GAAG,qBAAqB,CAAC;AAAA,wBACnB,GAAG,uBAAuB,CAAC;AAAA,qBAC9B,GAAG,wBAAwB,CAAC;AAAA,aACpC,GAAG,gBAAgB,CAAC;AAAA,WACtB,GAAG,cAAc,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WAiBlB,GAAG,cAAc,CAAC;AAAA;AAAA;AAAA;AAAA,qBAIR,GAAG,kCAAkC,CAAC;AAAA;AAAA;AAAA;AAAA,eAI5C,GAAG,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA,6BAIlB,GAAG,8BAA8B,CAAC,WAAW,GAAG,8BAA8B,CAAC;AAAA,qBACvF,GAAG,6BAA6B,CAAC;AAAA,aACzC,GAAG,qBAAqB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAWvB,GAAG,uBAAuB,CAAC;AAAA,WAC/B,GAAG,mBAAmB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAUrB,GAAG,0BAA0B,CAAC;AAAA;AAAA;AAAA;AAAA,aAI9B,GAAG,6BAA6B,CAAC;AAAA,iBAC7B,GAAG,4BAA4B,CAAC;AAAA,aACpC,GAAG,4BAA4B,CAAC;AAAA,cAC/B,GAAG,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA,aAIjC,GAAG,2BAA2B,CAAC;AAAA,kBAC1B,GAAG,gCAAgC,CAAC;AAAA;AAAA;AAAA;AAAA,aAIzC,GAAG,gCAAgC,CAAC;AAAA;AAAA;AAAA;AAAA,aAIpC,GAAG,mCAAmC,CAAC;AAAA;AAAA;AAAA;AAAA,aAIvC,GAAG,2BAA2B,CAAC;AAAA,kBAC1B,GAAG,gCAAgC,CAAC;AAAA;AAAA;AAAA;AAAA,aAIzC,GAAG,gCAAgC,CAAC;AAAA;AAAA;AAAA;AAAA,aAIpC,GAAG,mCAAmC,CAAC;AAAA;AAAA;AAAA;AAAA,aAIvC,GAAG,4BAA4B,CAAC;AAAA,kBAC3B,GAAG,iCAAiC,CAAC;AAAA;AAAA;AAAA;AAAA,aAI1C,GAAG,iCAAiC,CAAC;AAAA;AAAA;AAAA;AAAA,aAIrC,GAAG,oCAAoC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAUtC,GAAG,0BAA0B,CAAC;AAAA,kBAC3B,GAAG,6BAA6B,CAAC;AAAA,aACtC,GAAG,wBAAwB,CAAC;AAAA,wBACjB,GAAG,+BAA+B,CAAC;AAAA,qBACtC,GAAG,gCAAgC,CAAC;AAAA,kBACvC,GAAG,yBAAyB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAUhC,GAAG,0BAA0B,CAAC;AAAA;AAAA;AAAA;AAAA,WAIlC,GAAG,sBAAsB,CAAC;AAAA;AAAA;AAAA;AAAA,eAItB,GAAG,gCAAgC,CAAC;AAAA,aACtC,GAAG,8BAA8B,CAAC;AAAA,mBAC5B,GAAG,oCAAoC,CAAC;AAAA,kBACzC,GAAG,mCAAmC,CAAC;AAAA;AAAA;AAAA;AAAA,4BAI7B,GAAG,iCAAiC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,eAKlD,GAAG,yCAAyC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WAOjD,GAAG,qCAAqC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAarC,GAAG,uCAAuC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WAM/C,GAAG,mCAAmC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAiCnC,GAAG,sBAAsB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aA4B5B,GAAG,6BAA6B,CAAC;AAAA,cAChC,GAAG,6BAA6B,CAAC;AAAA;AAAA,aAElC,GAAG,8BAA8B,CAAC;AAAA;AAAA;AAAA,qBAG1B,GAAG,sCAAsC,CAAC;AAAA,6BAClC,GAAG,8BAA8B,CAAC,WAAW,GAAG,8BAA8B,CAAC,mBAAmB,GAAG,8BAA8B,CAAC,eAAe,GAAG,8BAA8B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,aAKrM,GAAG,oCAAoC,CAAC;AAAA,kBACnC,GAAG,yCAAyC,CAAC;AAAA;AAAA;AAAA;AAAA,kBAI7C,GAAG,0CAA0C,CAAC;AAAA,eACjD,GAAG,yCAAyC,CAAC,IAAI,GAAG,yCAAyC,CAAC,IAAI,GAAG,yCAAyC,CAAC;AAAA,sBACxI,GAAG,0CAA0C,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAmBrD,GAAG,0BAA0B,CAAC;AAAA,WAClC,GAAG,sBAAsB,CAAC;AAAA,kBACnB,GAAG,6BAA6B,CAAC;AAAA,wBAC3B,GAAG,+BAA+B,CAAC;AAAA,kBACzC,GAAG,yBAAyB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoD/C,IAAM,UAAU;AAAA,EACd,MAAM,CAAC;AAAA,IACL;AAAA,EACF,MAAM,CAAC,0BAA0B;AAAA,IAC/B,qBAAqB,SAAS;AAAA,IAC9B,4BAA4B,SAAS;AAAA,IACrC,yBAAyB,SAAS;AAAA,IAClC,uBAAuB,SAAS;AAAA,EAClC,CAAC;AAAA,EACD,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,cAAc,CAAC;AAAA,IACb;AAAA,IACA;AAAA,EACF,MAAM,CAAC,4BAA4B;AAAA,IACjC,cAAc,SAAS,eAAe,aAAa;AAAA,EACrD,CAAC;AAAA,EACD,MAAM,CAAC;AAAA,IACL;AAAA,IACA;AAAA,EACF,MAAM,CAAC,mBAAmB;AAAA,IACxB,0BAA0B,SAAS,aAAa,aAAa;AAAA,IAC7D,WAAW,SAAS,cAAc,aAAa;AAAA,IAC/C,cAAc,SAAS,eAAe,aAAa;AAAA,EACrD,CAAC;AAAA,EACD,aAAa;AAAA,EACb,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS;AAAA,EACT,MAAM;AAAA,EACN,QAAQ,CAAC;AAAA,IACP;AAAA,IACA;AAAA,EACF,MAAM;AACJ,QAAI,SAAS,SAAS,YAAY,aAAa,IAAI,cAAc,MAAM,SAAS;AAChF,QAAI;AACJ,QAAI,SAAS,gBAAgB,aAAc,eAAc;AAAA,SAAyB;AAChF,cAAQ,QAAQ;AAAA,QACd,KAAK;AACH,wBAAc;AACd;AAAA,QACF,KAAK;AACH,wBAAc;AACd;AAAA,QACF,KAAK;AACH,wBAAc;AACd;AAAA,QACF,KAAK;AACH,wBAAc;AACd;AAAA,QACF;AACE,wBAAc;AACd;AAAA,MACJ;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,EACT,WAAW;AAAA,EACX,KAAK;AACP;AACA,IAAM,gBAAN,MAAM,uBAAsB,UAAU;AAAA,EACpC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,sBAAsB,mBAAmB;AACvD,cAAQ,+BAA+B,6BAAgC,sBAAsB,cAAa,IAAI,qBAAqB,cAAa;AAAA,IAClJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,eAAc;AAAA,EACzB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,kBAAiB;AAI1B,EAAAA,iBAAgB,MAAM,IAAI;AAI1B,EAAAA,iBAAgB,OAAO,IAAI;AAI3B,EAAAA,iBAAgB,QAAQ,IAAI;AAI5B,EAAAA,iBAAgB,UAAU,IAAI;AAI9B,EAAAA,iBAAgB,aAAa,IAAI;AAIjC,EAAAA,iBAAgB,MAAM,IAAI;AAI1B,EAAAA,iBAAgB,aAAa,IAAI;AAIjC,EAAAA,iBAAgB,UAAU,IAAI;AAI9B,EAAAA,iBAAgB,UAAU,IAAI;AAI9B,EAAAA,iBAAgB,WAAW,IAAI;AAI/B,EAAAA,iBAAgB,aAAa,IAAI;AAIjC,EAAAA,iBAAgB,OAAO,IAAI;AAI3B,EAAAA,iBAAgB,MAAM,IAAI;AAI1B,EAAAA,iBAAgB,SAAS,IAAI;AAI7B,EAAAA,iBAAgB,kBAAkB,IAAI;AAItC,EAAAA,iBAAgB,WAAW,IAAI;AAI/B,EAAAA,iBAAgB,KAAK,IAAI;AAC3B,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAC5C,IAAM,cAAN,MAAM,qBAAoB,cAAc;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAe;AAAA,EACf,eAAe;AAAA,EACf;AAAA,EACA,WAAW;AAAA,EACX,OAAO;AAAA,EACP,YAAY,IAAI,aAAa;AAAA,EAC7B,iBAAiB,IAAI,aAAa;AAAA,EAClC,YAAY,IAAI,aAAa;AAAA,EAC7B,WAAW,IAAI,aAAa;AAAA,EAC5B,cAAc,IAAI,aAAa;AAAA,EAC/B;AAAA,EACA,WAAW,OAAO,WAAW,MAAM,QAAQ,CAAC;AAAA,EAC5C,YAAY,OAAO,eAAe;AAChC,SAAK,YAAY,eAAe,WAAW;AAAA,MACzC,eAAe;AAAA,MACf,MAAM,cAAc;AAAA,IACtB,CAAC;AACD,SAAK,UAAU,KAAK;AAAA,MAClB,eAAe;AAAA,MACf;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAAA,EACA,YAAY,eAAe,MAAM,SAAS,MAAM;AAC9C,WAAO,iBAAiB,cAAc,OAAO,QAAQ,cAAc,KAAK,IAAI,GAAG,MAAM,IAAI;AAAA,EAC3F;AAAA,EACA,UAAU,eAAe;AACvB,WAAO,cAAc,QAAQ,cAAc,MAAM,KAAK,cAAc,KAAK,KAAK,GAAG,KAAK,MAAM,IAAI,cAAc,GAAG;AAAA,EACnH;AAAA,EACA,aAAa,eAAe;AAC1B,WAAO,GAAG,KAAK,UAAU,aAAa,CAAC;AAAA,EACzC;AAAA,EACA,aAAa,eAAe;AAC1B,WAAO,iCACF,KAAK,YAAY,eAAe,OAAO,IADrC;AAAA,MAEL,mBAAmB;AAAA,MACnB,0BAA0B,KAAK,aAAa,aAAa;AAAA,MACzD,WAAW,KAAK,cAAc,aAAa;AAAA,MAC3C,cAAc,KAAK,eAAe,aAAa;AAAA,IACjD;AAAA,EACF;AAAA,EACA,aAAa,eAAe;AAC1B,WAAO,KAAK,YAAY,eAAe,OAAO;AAAA,EAChD;AAAA,EACA,sBAAsB,eAAe;AACnC,WAAO,iCACF,KAAK,YAAY,eAAe,OAAO,IADrC;AAAA,MAEL,wBAAwB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,eAAe,eAAe;AAC5B,QAAI,SAAS,KAAK,YAAY,aAAa,IAAI,cAAc,MAAM,SAAS;AAC5E,QAAI;AACJ,QAAI,KAAK,aAAc,eAAc;AAAA,SAAyB;AAC5D,cAAQ,QAAQ;AAAA,QACd,KAAK;AACH,wBAAc;AACd;AAAA,QACF,KAAK;AACH,wBAAc;AACd;AAAA,QACF,KAAK;AACH,wBAAc;AACd;AAAA,QACF,KAAK;AACH,wBAAc;AACd;AAAA,QACF;AACE,wBAAc;AACd;AAAA,MACJ;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,sBAAsB,eAAe;AACnC,WAAO;AAAA,MACL,4BAA4B;AAAA,MAC5B,cAAc,KAAK,eAAe,aAAa;AAAA,OAC5C,KAAK,YAAY,eAAe,OAAO;AAAA,EAE9C;AAAA,EACA,iBAAiB,SAAS;AACxB,QAAI,KAAK,WAAW,CAAC,KAAK,MAAM;AAC9B,aAAO,KAAK,cAAc,OAAO;AAAA,IACnC,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,cAAc,eAAe;AAC3B,WAAO,KAAK,YAAY,eAAe,SAAS,MAAM;AAAA,EACxD;AAAA,EACA,aAAa,eAAe;AAC1B,WAAO,WAAW,KAAK,UAAU,IAAI,KAAK,WAAW,QAAQ,cAAc,MAAM;AAAA,EACnF;AAAA,EACA,eAAe,eAAe;AAC5B,WAAO,KAAK,YAAY,eAAe,UAAU;AAAA,EACnD;AAAA,EACA,cAAc,eAAe;AAC3B,WAAO,KAAK,kBAAkB,KAAK,UAAU,aAAa;AAAA,EAC5D;AAAA,EACA,YAAY,eAAe;AACzB,WAAO,WAAW,cAAc,KAAK;AAAA,EACvC;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,MAAM,OAAO,mBAAiB,KAAK,cAAc,aAAa,KAAK,CAAC,KAAK,YAAY,eAAe,WAAW,CAAC,EAAE;AAAA,EAChI;AAAA,EACA,gBAAgB,OAAO;AACrB,WAAO,QAAQ,KAAK,MAAM,MAAM,GAAG,KAAK,EAAE,OAAO,mBAAiB,KAAK,cAAc,aAAa,KAAK,KAAK,YAAY,eAAe,WAAW,CAAC,EAAE,SAAS;AAAA,EAChK;AAAA,EACA,iBAAiB,OAAO;AACtB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,SAAK,eAAe,KAAK;AAAA,MACvB,eAAe;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,oBAAoB,mBAAmB;AACrD,cAAQ,6BAA6B,2BAA8B,sBAAsB,YAAW,IAAI,qBAAqB,YAAW;AAAA,IAC1I;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,GAAG,CAAC,gBAAgB,CAAC;AAAA,IACjD,WAAW,SAAS,kBAAkB,IAAI,KAAK;AAC7C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AAAA,MACzE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,OAAO,CAAC,GAAG,SAAS,SAAS,eAAe;AAAA,MAC5C,eAAe;AAAA,MACf,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,cAAc,CAAC,GAAG,gBAAgB,gBAAgB,gBAAgB;AAAA,MAClE,cAAc,CAAC,GAAG,gBAAgB,gBAAgB,gBAAgB;AAAA,MAClE,cAAc;AAAA,MACd,UAAU,CAAC,GAAG,YAAY,YAAY,eAAe;AAAA,MACrD,MAAM,CAAC,GAAG,QAAQ,QAAQ,gBAAgB;AAAA,IAC5C;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,UAAU,CAAI,0BAA6B,0BAA0B;AAAA,IACrE,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC,kBAAkB,EAAE,GAAG,CAAC,GAAG,WAAW,SAAS,YAAY,WAAW,SAAS,QAAQ,GAAG,MAAM,GAAG,CAAC,GAAG,WAAW,SAAS,QAAQ,WAAW,UAAU,GAAG,CAAC,QAAQ,gBAAgB,GAAG,WAAW,SAAS,GAAG,MAAM,GAAG,CAAC,SAAS,IAAI,GAAG,SAAS,GAAG,CAAC,QAAQ,gBAAgB,GAAG,SAAS,GAAG,CAAC,QAAQ,aAAa,GAAG,SAAS,WAAW,GAAG,MAAM,GAAG,CAAC,QAAQ,YAAY,YAAY,IAAI,GAAG,WAAW,WAAW,SAAS,kBAAkB,GAAG,MAAM,GAAG,CAAC,QAAQ,aAAa,GAAG,SAAS,GAAG,CAAC,QAAQ,YAAY,YAAY,IAAI,GAAG,WAAW,WAAW,gBAAgB,GAAG,CAAC,GAAG,2BAA2B,GAAG,SAAS,YAAY,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,SAAS,sBAAsB,GAAG,MAAM,GAAG,CAAC,WAAW,IAAI,GAAG,UAAU,WAAW,GAAG,MAAM,GAAG,CAAC,WAAW,IAAI,GAAG,cAAc,eAAe,oBAAoB,2BAA2B,UAAU,WAAW,YAAY,uBAAuB,oBAAoB,sBAAsB,cAAc,SAAS,GAAG,MAAM,GAAG,CAAC,WAAW,IAAI,GAAG,UAAU,SAAS,GAAG,CAAC,SAAS,wBAAwB,GAAG,WAAW,WAAW,GAAG,MAAM,GAAG,CAAC,SAAS,yBAAyB,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,cAAc,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,wBAAwB,GAAG,WAAW,SAAS,GAAG,CAAC,GAAG,uBAAuB,GAAG,CAAC,GAAG,yBAAyB,GAAG,WAAW,GAAG,CAAC,GAAG,cAAc,OAAO,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,mBAAmB,aAAa,GAAG,CAAC,WAAW,IAAI,GAAG,cAAc,eAAe,oBAAoB,2BAA2B,UAAU,WAAW,YAAY,uBAAuB,oBAAoB,sBAAsB,cAAc,OAAO,GAAG,CAAC,GAAG,cAAc,GAAG,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,WAAW,GAAG,SAAS,SAAS,GAAG,CAAC,GAAG,MAAM,WAAW,SAAS,gBAAgB,gBAAgB,UAAU,iBAAiB,SAAS,QAAQ,aAAa,kBAAkB,GAAG,SAAS,SAAS,GAAG,CAAC,GAAG,aAAa,kBAAkB,MAAM,WAAW,SAAS,gBAAgB,gBAAgB,UAAU,iBAAiB,SAAS,MAAM,CAAC;AAAA,IACvpE,UAAU,SAAS,qBAAqB,IAAI,KAAK;AAC/C,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,2BAA2B,GAAG,IAAI,MAAM,CAAC;AAAA,MAC5D;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,QAAQ,IAAI,iBAAiB,IAAI,OAAO,CAAC;AAAA,MACzD;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAa,cAAiB,SAAY,SAAY,MAAS,kBAAqB,SAAS,cAAiB,YAAe,kBAAkB,QAAQ,eAAkB,SAAS,eAAe,gBAAgB,aAAgB,OAAO,YAAY;AAAA,IACnQ,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,cAAc,QAAQ,eAAe,eAAe,gBAAgB,aAAa,YAAY;AAAA,MACrH,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA6JV,eAAe,kBAAkB;AAAA,IACnC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,WAAN,MAAM,kBAAiB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnC,IAAI,MAAM,OAAO;AACf,SAAK,SAAS;AACd,SAAK,kBAAkB,KAAK,qBAAqB,KAAK,UAAU,CAAC,CAAC;AAAA,EACpE;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,aAAa,OAAO,IAAI;AAAA,EACxB,kBAAkB,OAAO;AAAA,IACvB,OAAO;AAAA,IACP,OAAO;AAAA,IACP,WAAW;AAAA,IACX,MAAM;AAAA,EACR,CAAC;AAAA,EACD,cAAc;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAkB,OAAO,aAAa;AAAA,EACtC;AAAA,EACA;AAAA,EACA,eAAe;AAAA,EACf,eAAe;AAAA,EACf,IAAI,eAAe;AACjB,UAAM,gBAAgB,WAAW,KAAK,WAAW,CAAC,IAAI,KAAK,WAAW,IAAI;AAC1E,WAAO,gBAAgB,cAAc,MAAM,OAAO,CAAC,OAAO,QAAQ;AAChE,UAAI,QAAQ,aAAW;AACrB,gBAAQ,MAAM,QAAQ,OAAK;AACzB,gBAAM,KAAK,CAAC;AAAA,QACd,CAAC;AAAA,MACH,CAAC;AACD,aAAO;AAAA,IACT,GAAG,CAAC,CAAC,IAAI,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,iBAAiB;AACnB,QAAI,CAAC,KAAK,mBAAmB,CAAC,KAAK,gBAAgB,QAAQ;AACzD,WAAK,kBAAkB,KAAK,qBAAqB,KAAK,SAAS,CAAC,CAAC;AAAA,IACnE;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB;AAClB,UAAM,cAAc,KAAK,gBAAgB;AACzC,WAAO,aAAa,QAAQ,YAAY,MAAM,KAAK,YAAY,KAAK,KAAK,WAAW,YAAY,GAAG,IAAI,GAAG,KAAK,EAAE,IAAI,YAAY,GAAG,KAAK;AAAA,EAC3I;AAAA,EACA,cAAc;AACZ,UAAM;AACN,WAAO,MAAM;AACX,YAAM,aAAa,KAAK,WAAW;AACnC,UAAI,WAAW,UAAU,GAAG;AAC1B,aAAK,yBAAyB;AAC9B,aAAK,mBAAmB;AAAA,MAC1B,OAAO;AACL,aAAK,2BAA2B;AAChC,aAAK,qBAAqB;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AACT,UAAM,SAAS;AACf,SAAK,uBAAuB;AAC5B,SAAK,KAAK,KAAK,MAAM,KAAK,QAAQ;AAAA,EACpC;AAAA,EACA,qBAAqB;AACnB,SAAK,WAAW,QAAQ,UAAQ;AAC9B,cAAQ,KAAK,QAAQ,GAAG;AAAA,QACtB,KAAK;AACH,eAAK,iBAAiB,KAAK;AAC3B;AAAA,QACF,KAAK;AACH,eAAK,eAAe,KAAK;AACzB;AAAA,QACF,KAAK;AACH,eAAK,oBAAoB,KAAK;AAC9B;AAAA,QACF,KAAK;AACH,eAAK,uBAAuB,KAAK;AACjC;AAAA,QACF,KAAK;AACH,eAAK,gBAAgB,KAAK;AAC1B;AAAA,QACF,KAAK;AACH,eAAK,kBAAkB,KAAK;AAC5B;AAAA,QACF,KAAK;AACH,eAAK,sBAAsB,KAAK;AAChC;AAAA,QACF;AACE,eAAK,gBAAgB,KAAK;AAC1B;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,yBAAyB;AACvB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,UAAI,CAAC,KAAK,oBAAoB;AAC5B,cAAM,QAAQ,OAAO,WAAW,eAAe,KAAK,UAAU,GAAG;AACjE,aAAK,QAAQ;AACb,aAAK,eAAe,MAAM;AAC1B,aAAK,qBAAqB,MAAM;AAC9B,eAAK,eAAe,MAAM;AAC1B,eAAK,eAAe;AACpB,eAAK,GAAG,aAAa;AAAA,QACvB;AACA,cAAM,iBAAiB,UAAU,KAAK,kBAAkB;AAAA,MAC1D;AAAA,IACF;AAAA,EACF;AAAA,EACA,2BAA2B;AACzB,QAAI,KAAK,oBAAoB;AAC3B,WAAK,MAAM,oBAAoB,UAAU,KAAK,kBAAkB;AAChE,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,qBAAqB,OAAO,QAAQ,GAAG,SAAS,CAAC,GAAG,YAAY,IAAI,aAAa;AAC/E,UAAM,iBAAiB,CAAC;AACxB,aAAS,MAAM,QAAQ,CAAC,MAAM,UAAU;AACtC,YAAM,OAAO,cAAc,KAAK,YAAY,MAAM,OAAO,gBAAgB,SAAY,cAAc,MAAM,MAAM;AAC/G,YAAM,UAAU;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,aAAa,gBAAgB,SAAY,cAAc,OAAO,gBAAgB,SAAY,OAAO,cAAc;AAAA,MACjH;AACA,cAAQ,OAAO,IAAI,UAAU,KAAK,KAAK,SAAS,KAAK,MAAM,SAAS,IAAI,KAAK,MAAM,IAAI,CAAC,QAAQ,WAAW,KAAK,qBAAqB,QAAQ,QAAQ,GAAG,SAAS,KAAK,MAAM,CAAC,IAAI,KAAK,qBAAqB,KAAK,OAAO,QAAQ,GAAG,SAAS,GAAG;AAC9O,qBAAe,KAAK,OAAO;AAAA,IAC7B,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,YAAY,MAAM,MAAM;AACtB,WAAO,OAAO,QAAQ,KAAK,IAAI,CAAC,IAAI;AAAA,EACtC;AAAA,EACA,YAAY,OAAO;AACjB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,UAAU,KAAK,qBAAqB,aAAa;AACvD,UAAM,OAAO,QAAQ,cAAc,MAAM;AACzC,UAAM,WAAW,KAAK,WAAW,aAAa;AAC9C,QAAI,UAAU;AACZ,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,WAAK,WAAW,IAAI,IAAI;AACxB,WAAK,gBAAgB,IAAI;AAAA,QACvB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,WAAK,QAAQ,CAAC;AACd,UAAI,CAAC,KAAK,cAAc;AACtB,cAAM,KAAK,UAAU,kBAAkB,eAAe;AAAA,UACpD,eAAe;AAAA,QACjB,CAAC;AAAA,MACH;AAAA,IACF,OAAO;AACL,UAAI,SAAS;AACX,aAAK,aAAa,KAAK;AAAA,MACzB,OAAO;AACL,aAAK,KAAK,aAAa;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO;AACtB,QAAI,CAAC,KAAK,gBAAgB,KAAK,OAAO;AACpC,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA,EACA,gBAAgB,OAAO;AACrB,SAAK,OAAO,KAAK;AAAA,EACnB;AAAA,EACA,kBAAkB,OAAO;AACvB,KAAC,MAAM,SAAS,WAAW,MAAM,SAAS,iBAAiB,MAAM,SAAS,YAAY,KAAK,gBAAgB,KAAK;AAAA,EAClH;AAAA,EACA,OAAO,OAAO;AACZ,QAAI,KAAK,cAAc;AACrB,WAAK,eAAe;AACpB,kBAAY,MAAM,KAAK,SAAS,GAAG,aAAa;AAChD,WAAK,KAAK;AAAA,IACZ,OAAO;AACL,WAAK,eAAe;AACpB,kBAAY,IAAI,QAAQ,KAAK,SAAS,GAAG,eAAe,KAAK,OAAO,OAAO,IAAI;AAC/E,iBAAW,MAAM;AACf,aAAK,KAAK;AAAA,MACZ,GAAG,CAAC;AAAA,IACN;AACA,SAAK,yBAAyB;AAC9B,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,OAAO;AACL,SAAK,gBAAgB,IAAI;AAAA,MACvB,OAAO,KAAK,0BAA0B;AAAA,MACtC,OAAO;AAAA,MACP,WAAW;AAAA,IACb,CAAC;AACD,UAAM,KAAK,UAAU,GAAG,aAAa;AAAA,EACvC;AAAA,EACA,aAAa,QAAQ,IAAI;AACvB,UAAM,KAAK,UAAU,KAAK,GAAG,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK;AACvD,QAAI;AACJ,QAAI,OAAO,QAAQ,KAAK,cAAc;AACpC,gBAAU,KAAK,oBAAoB;AAAA,IACrC,OAAO;AACL,gBAAU,WAAW,KAAK,UAAU,kBAAkB,eAAe,UAAU,EAAE,IAAI;AAAA,IACvF;AACA,QAAI,SAAS;AACX,cAAQ,kBAAkB,QAAQ,eAAe;AAAA,QAC/C,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,aAAa,OAAO;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,QAAQ,aAAa,EAAG;AAC5B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,UAAU,WAAW,KAAK;AAChC,QAAI,SAAS;AACX,WAAK,WAAW,IAAI,aAAa;AAAA,IACnC;AACA,SAAK,gBAAgB,IAAI;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,gBAAY,KAAK,QAAQ;AACzB,eAAW,MAAM,KAAK,UAAU,kBAAkB,aAAa;AAAA,EACjE;AAAA,EACA,KAAK,OAAO,SAAS;AACnB,QAAI,KAAK,cAAc;AACrB,WAAK,eAAe;AACpB,iBAAW,MAAM;AACf,cAAM,KAAK,qBAAqB,aAAa;AAC7C,aAAK,aAAa;AAAA,MACpB,GAAG,GAAG;AAAA,IACR;AACA,SAAK,WAAW,IAAI,IAAI;AACxB,SAAK,gBAAgB,IAAI;AAAA,MACvB,OAAO;AAAA,MACP,KAAK;AAAA,MACL,WAAW;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AACD,eAAW,MAAM,KAAK,UAAU,kBAAkB,aAAa;AAC/D,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,UAAU;AACf,QAAI,KAAK,gBAAgB,EAAE,UAAU,IAAI;AACvC,YAAM,QAAQ,KAAK,0BAA0B;AAC7C,YAAM,gBAAgB,KAAK,gBAAgB,KAAK;AAChD,WAAK,gBAAgB,IAAI;AAAA,QACvB;AAAA,QACA,KAAK,cAAc;AAAA,QACnB,WAAW,cAAc;AAAA,QACzB,MAAM,cAAc;AAAA,MACtB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,UAAU;AACf,SAAK,gBAAgB,IAAI;AAAA,MACvB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,WAAW;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AACD,SAAK,cAAc;AACnB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,UAAU,OAAO;AACf,UAAM,UAAU,MAAM,WAAW,MAAM;AACvC,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK;AACH,aAAK,eAAe,KAAK;AACzB;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK;AACvB;AAAA,MACF,KAAK;AACH,aAAK,eAAe,KAAK;AACzB;AAAA,MACF,KAAK;AACH,aAAK,gBAAgB,KAAK;AAC1B;AAAA,MACF,KAAK;AACH,aAAK,UAAU,KAAK;AACpB;AAAA,MACF,KAAK;AACH,aAAK,SAAS,KAAK;AACnB;AAAA,MACF,KAAK;AACH,aAAK,WAAW,KAAK;AACrB;AAAA,MACF,KAAK;AACH,aAAK,WAAW,KAAK;AACrB;AAAA,MACF,KAAK;AACH,aAAK,YAAY,KAAK;AACtB;AAAA,MACF,KAAK;AACH,aAAK,SAAS,KAAK;AACnB;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAEH;AAAA,MACF;AACE,YAAI,CAAC,WAAW,qBAAqB,MAAM,GAAG,GAAG;AAC/C,eAAK,YAAY,OAAO,MAAM,GAAG;AAAA,QACnC;AACA;AAAA,IACJ;AAAA,EACF;AAAA,EACA,4BAA4B;AAC1B,UAAM,gBAAgB,KAAK,sBAAsB;AACjD,WAAO,gBAAgB,IAAI,KAAK,mBAAmB,IAAI;AAAA,EACzD;AAAA,EACA,qBAAqB;AACnB,WAAO,KAAK,aAAa,UAAU,mBAAiB,KAAK,YAAY,aAAa,CAAC;AAAA,EACrF;AAAA,EACA,wBAAwB;AACtB,WAAO,KAAK,aAAa,UAAU,mBAAiB,KAAK,oBAAoB,aAAa,CAAC;AAAA,EAC7F;AAAA,EACA,qBAAqB,eAAe;AAClC,WAAO,iBAAiB,WAAW,cAAc,KAAK;AAAA,EACxD;AAAA,EACA,WAAW,eAAe;AACxB,WAAO,WAAW,KAAK,WAAW,CAAC,IAAI,KAAK,WAAW,EAAE,QAAQ,cAAc,MAAM;AAAA,EACvF;AAAA,EACA,oBAAoB,eAAe;AACjC,WAAO,KAAK,YAAY,aAAa,KAAK,KAAK,WAAW,aAAa;AAAA,EACzE;AAAA,EACA,YAAY,eAAe;AACzB,WAAO,CAAC,CAAC,iBAAiB,CAAC,KAAK,eAAe,cAAc,IAAI,KAAK,CAAC,KAAK,gBAAgB,cAAc,IAAI;AAAA,EAChH;AAAA,EACA,eAAe,MAAM;AACnB,WAAO,KAAK,YAAY,MAAM,UAAU;AAAA,EAC1C;AAAA,EACA,gBAAgB,MAAM;AACpB,WAAO,KAAK,YAAY,MAAM,WAAW;AAAA,EAC3C;AAAA,EACA,cAAc,eAAe;AAC3B,WAAO,KAAK,YAAY,aAAa,KAAK,KAAK,uBAAuB,aAAa,EAAE,kBAAkB,EAAE,WAAW,KAAK,YAAY,kBAAkB,CAAC;AAAA,EAC1J;AAAA,EACA,sBAAsB,eAAe;AACnC,WAAO,iBAAiB,WAAW,cAAc,KAAK;AAAA,EACxD;AAAA,EACA,YAAY,OAAO,MAAM;AACvB,SAAK,eAAe,KAAK,eAAe,MAAM;AAC9C,QAAI,YAAY;AAChB,QAAI,UAAU;AACd,QAAI,KAAK,gBAAgB,EAAE,UAAU,IAAI;AACvC,kBAAY,KAAK,aAAa,MAAM,KAAK,gBAAgB,EAAE,KAAK,EAAE,UAAU,mBAAiB,KAAK,cAAc,aAAa,CAAC;AAC9H,kBAAY,cAAc,KAAK,KAAK,aAAa,MAAM,GAAG,KAAK,gBAAgB,EAAE,KAAK,EAAE,UAAU,mBAAiB,KAAK,cAAc,aAAa,CAAC,IAAI,YAAY,KAAK,gBAAgB,EAAE;AAAA,IAC7L,OAAO;AACL,kBAAY,KAAK,aAAa,UAAU,mBAAiB,KAAK,cAAc,aAAa,CAAC;AAAA,IAC5F;AACA,QAAI,cAAc,IAAI;AACpB,gBAAU;AAAA,IACZ;AACA,QAAI,cAAc,MAAM,KAAK,gBAAgB,EAAE,UAAU,IAAI;AAC3D,kBAAY,KAAK,0BAA0B;AAAA,IAC7C;AACA,QAAI,cAAc,IAAI;AACpB,WAAK,sBAAsB,OAAO,SAAS;AAAA,IAC7C;AACA,QAAI,KAAK,eAAe;AACtB,mBAAa,KAAK,aAAa;AAAA,IACjC;AACA,SAAK,gBAAgB,WAAW,MAAM;AACpC,WAAK,cAAc;AACnB,WAAK,gBAAgB;AAAA,IACvB,GAAG,GAAG;AACN,WAAO;AAAA,EACT;AAAA,EACA,uBAAuB,eAAe;AACpC,WAAO,gBAAgB,KAAK,aAAa,cAAc,IAAI,IAAI;AAAA,EACjE;AAAA,EACA,aAAa,MAAM;AACjB,WAAO,KAAK,YAAY,MAAM,OAAO;AAAA,EACvC;AAAA,EACA,sBAAsB,OAAO,OAAO;AAClC,UAAM,gBAAgB,KAAK,gBAAgB,KAAK;AAChD,QAAI,WAAW,aAAa,GAAG;AAC7B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,WAAK,gBAAgB,IAAI;AAAA,QACvB;AAAA,QACA,KAAK,MAAM,MAAM;AAAA,QACjB;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AACA,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,eAAe,OAAO;AACpB,QAAI,KAAK,gBAAgB,cAAc;AACrC,UAAI,WAAW,KAAK,WAAW,CAAC,KAAK,KAAK,WAAW,EAAE,QAAQ,KAAK,gBAAgB,EAAE,KAAK;AACzF,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,KAAK,WAAW;AACpB,aAAK,gBAAgB,IAAI;AAAA,UACvB,OAAO;AAAA,UACP,KAAK;AAAA,UACL,WAAW;AAAA,UACX;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,cAAM,gBAAgB,KAAK,gBAAgB,KAAK,gBAAgB,EAAE,KAAK;AACvE,cAAM,UAAU,KAAK,sBAAsB,aAAa;AACxD,YAAI,SAAS;AACX,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,eAAK,aAAa;AAAA,YAChB,eAAe;AAAA,YACf;AAAA,UACF,CAAC;AACD,eAAK,gBAAgB,IAAI;AAAA,YACvB,OAAO;AAAA,YACP;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AACD,eAAK,cAAc;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AACA,UAAM,YAAY,KAAK,gBAAgB,EAAE,UAAU,KAAK,KAAK,kBAAkB,KAAK,gBAAgB,EAAE,KAAK,IAAI,KAAK,0BAA0B;AAC9I,SAAK,sBAAsB,OAAO,SAAS;AAC3C,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,gBAAgB,OAAO;AACrB,UAAM,gBAAgB,KAAK,gBAAgB,KAAK,gBAAgB,EAAE,KAAK;AACvE,UAAM,UAAU,KAAK,sBAAsB,aAAa;AACxD,QAAI,SAAS;AACX,UAAI,KAAK,gBAAgB,YAAY;AACnC,YAAI,WAAW,KAAK,WAAW,CAAC,KAAK,KAAK,WAAW,EAAE,QAAQ,cAAc,KAAK;AAChF,eAAK,gBAAgB,IAAI;AAAA,YACvB,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW,KAAK,WAAW,EAAE;AAAA,YAC7B,MAAM,cAAc;AAAA,UACtB,CAAC;AAAA,QACH,OAAO;AACL,gBAAMC,iBAAgB,KAAK,gBAAgB,KAAK,gBAAgB,EAAE,KAAK;AACvE,gBAAMC,WAAU,KAAK,sBAAsBD,cAAa;AACxD,cAAIC,UAAS;AACX,iBAAK,aAAa;AAAA,cAChB,eAAe;AAAA,cACf,eAAAD;AAAA,YACF,CAAC;AACD,iBAAK,gBAAgB,IAAI;AAAA,cACvB,OAAO;AAAA,cACP,KAAKA,eAAc;AAAA,cACnB,WAAWA,eAAc;AAAA,cACzB,MAAMA,eAAc;AAAA,YACtB,CAAC;AACD,iBAAK,cAAc;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AACA,YAAM,YAAY,KAAK,gBAAgB,EAAE,UAAU,KAAK,KAAK,kBAAkB,KAAK,gBAAgB,EAAE,KAAK,IAAI,KAAK,0BAA0B;AAC9I,WAAK,sBAAsB,OAAO,SAAS;AAAA,IAC7C,OAAO;AACL,YAAM,cAAc,cAAc,cAAc;AAChD,YAAM,YAAY,KAAK,aAAa,UAAU,UAAQ,KAAK,gBAAgB,WAAW;AACtF,oBAAc,MAAM,KAAK,sBAAsB,OAAO,SAAS;AAAA,IACjE;AACA,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,aAAa,OAAO;AAClB,QAAI,MAAM,UAAU,KAAK,gBAAgB,cAAc;AACrD,UAAI,KAAK,gBAAgB,EAAE,UAAU,IAAI;AACvC,cAAM,gBAAgB,KAAK,gBAAgB,KAAK,gBAAgB,EAAE,KAAK;AACvE,cAAM,UAAU,KAAK,sBAAsB,aAAa;AACxD,YAAI,CAAC,WAAW,WAAW,KAAK,UAAU,GAAG;AAC3C,cAAI,KAAK,gBAAgB,EAAE,UAAU,GAAG;AACtC,iBAAK,gBAAgB,IAAI;AAAA,cACvB,OAAO,KAAK,WAAW,EAAE;AAAA,cACzB,KAAK,KAAK,WAAW,EAAE;AAAA,cACvB,WAAW,KAAK,WAAW,EAAE;AAAA,cAC7B,MAAM,cAAc;AAAA,YACtB,CAAC;AACD,iBAAK,WAAW,IAAI,IAAI;AAAA,UAC1B,OAAO;AACL,iBAAK,sBAAsB,OAAO,KAAK,mBAAmB,CAAC;AAAA,UAC7D;AAAA,QACF;AAAA,MACF;AACA,YAAM,eAAe;AAAA,IACvB,OAAO;AACL,YAAM,YAAY,KAAK,gBAAgB,EAAE,UAAU,KAAK,KAAK,kBAAkB,KAAK,gBAAgB,EAAE,KAAK,IAAI,KAAK,yBAAyB;AAC7I,WAAK,sBAAsB,OAAO,SAAS;AAC3C,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA,EACA,eAAe,OAAO;AACpB,UAAM,gBAAgB,KAAK,gBAAgB,KAAK,gBAAgB,EAAE,KAAK;AACvE,UAAM,UAAU,KAAK,sBAAsB,aAAa;AACxD,QAAI,SAAS;AACX,UAAI,KAAK,gBAAgB,cAAc;AACrC,cAAM,YAAY,KAAK,gBAAgB,EAAE,UAAU,KAAK,KAAK,kBAAkB,KAAK,gBAAgB,EAAE,KAAK,IAAI,KAAK,yBAAyB;AAC7I,aAAK,sBAAsB,OAAO,SAAS;AAAA,MAC7C;AAAA,IACF,OAAO;AACL,UAAI,KAAK,gBAAgB,cAAc,WAAW,KAAK,WAAW,CAAC,GAAG;AACpE,YAAI,cAAc,gBAAgB,GAAG;AACnC,eAAK,gBAAgB,IAAI;AAAA,YACvB,OAAO,KAAK,WAAW,EAAE;AAAA,YACzB,KAAK,KAAK,WAAW,EAAE;AAAA,YACvB,WAAW,KAAK,WAAW,EAAE;AAAA,YAC7B,MAAM,cAAc;AAAA,UACtB,CAAC;AACD,eAAK,WAAW,IAAI,IAAI;AAAA,QAC1B;AAAA,MACF;AACA,YAAM,cAAc,cAAc,cAAc;AAChD,YAAM,YAAY,KAAK,aAAa,UAAU,UAAQ,KAAK,gBAAgB,WAAW;AACtF,oBAAc,MAAM,KAAK,sBAAsB,OAAO,SAAS;AAAA,IACjE;AACA,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,UAAU,OAAO;AACf,SAAK,sBAAsB,OAAO,KAAK,mBAAmB,CAAC;AAC3D,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,SAAS,OAAO;AACd,SAAK,sBAAsB,OAAO,KAAK,kBAAkB,CAAC;AAC1D,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,WAAW,KAAK;AAAA,EACvB;AAAA,EACA,YAAY,OAAO;AACjB,QAAI,WAAW,KAAK,WAAW,CAAC,GAAG;AACjC,WAAK,gBAAgB,IAAI;AAAA,QACvB,OAAO,KAAK,WAAW,EAAE;AAAA,QACzB,KAAK,KAAK,WAAW,EAAE;AAAA,QACvB,MAAM,KAAK,WAAW,EAAE;AAAA,MAC1B,CAAC;AACD,WAAK,WAAW,IAAI,IAAI;AAAA,IAC1B;AACA,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,SAAS,OAAO;AACd,QAAI,KAAK,gBAAgB,EAAE,UAAU,IAAI;AACvC,YAAM,gBAAgB,KAAK,gBAAgB,KAAK,gBAAgB,EAAE,KAAK;AACvE,YAAM,UAAU,KAAK,sBAAsB,aAAa;AACxD,OAAC,WAAW,KAAK,aAAa;AAAA,QAC5B,eAAe;AAAA,QACf;AAAA,MACF,CAAC;AAAA,IACH;AACA,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,WAAW,OAAO;AAChB,QAAI,KAAK,gBAAgB,EAAE,UAAU,IAAI;AACvC,YAAM,UAAU,WAAW,KAAK,UAAU,IAAI,eAAe,UAAU,GAAG,KAAK,aAAa,EAAE,IAAI;AAClG,YAAM,gBAAgB,WAAW,WAAW,SAAS,6BAA6B;AAClF,sBAAgB,cAAc,MAAM,IAAI,WAAW,QAAQ,MAAM;AACjE,YAAM,gBAAgB,KAAK,aAAa,KAAK,gBAAgB,EAAE,KAAK;AACpE,YAAM,UAAU,KAAK,sBAAsB,aAAa;AACxD,OAAC,WAAW,KAAK,sBAAsB,OAAO,KAAK,0BAA0B,CAAC;AAAA,IAChF;AACA,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,gBAAgB,OAAO;AACrB,WAAO,WAAW,KAAK,YAAY,IAAI,KAAK,aAAa,KAAK,IAAI;AAAA,EACpE;AAAA,EACA,2BAA2B;AACzB,UAAM,gBAAgB,KAAK,sBAAsB;AACjD,WAAO,gBAAgB,IAAI,KAAK,kBAAkB,IAAI;AAAA,EACxD;AAAA,EACA,oBAAoB;AAClB,WAAO,cAAc,KAAK,cAAc,mBAAiB,KAAK,YAAY,aAAa,CAAC;AAAA,EAC1F;AAAA,EACA,kBAAkB,OAAO;AACvB,UAAM,mBAAmB,QAAQ,IAAI,cAAc,KAAK,aAAa,MAAM,GAAG,KAAK,GAAG,mBAAiB,KAAK,YAAY,aAAa,CAAC,IAAI;AAC1I,WAAO,mBAAmB,KAAK,mBAAmB;AAAA,EACpD;AAAA,EACA,kBAAkB,OAAO;AACvB,UAAM,mBAAmB,QAAQ,KAAK,aAAa,SAAS,IAAI,KAAK,aAAa,MAAM,QAAQ,CAAC,EAAE,UAAU,mBAAiB,KAAK,YAAY,aAAa,CAAC,IAAI;AACjK,WAAO,mBAAmB,KAAK,mBAAmB,QAAQ,IAAI;AAAA,EAChE;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,gBAAgB;AACxB,WAAK,iBAAiB,WAAS;AAC7B,YAAI,CAAC,cAAc,GAAG;AACpB,eAAK,KAAK,OAAO,IAAI;AAAA,QACvB;AACA,aAAK,eAAe;AAAA,MACtB;AACA,aAAO,iBAAiB,UAAU,KAAK,cAAc;AAAA,IACvD;AAAA,EACF;AAAA,EACA,2BAA2B;AACzB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,UAAI,CAAC,KAAK,sBAAsB;AAC9B,aAAK,uBAAuB,KAAK,SAAS,OAAO,KAAK,UAAU,SAAS,WAAS;AAChF,gBAAM,qBAAqB,KAAK,WAAW,kBAAkB,MAAM,UAAU,CAAC,KAAK,WAAW,cAAc,SAAS,MAAM,MAAM;AACjI,cAAI,oBAAoB;AACtB,iBAAK,KAAK;AAAA,UACZ;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,6BAA6B;AAC3B,QAAI,KAAK,sBAAsB;AAC7B,WAAK,qBAAqB;AAC1B,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,gBAAgB;AACvB,aAAO,oBAAoB,UAAU,KAAK,cAAc;AACxD,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,2BAA2B;AAChC,SAAK,qBAAqB;AAC1B,SAAK,yBAAyB;AAC9B,UAAM,YAAY;AAAA,EACpB;AAAA,EACA,OAAO,OAAO,SAAS,iBAAiB,mBAAmB;AACzD,WAAO,KAAK,qBAAqB,WAAU;AAAA,EAC7C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,GAAG,CAAC,YAAY,GAAG,CAAC,aAAa,CAAC;AAAA,IAC3D,gBAAgB,SAAS,wBAAwB,IAAI,KAAK,UAAU;AAClE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,MAAM,CAAC;AACnC,QAAG,eAAe,UAAU,MAAM,CAAC;AACnC,QAAG,eAAe,UAAU,MAAM,CAAC;AACnC,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AACpE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAClE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AACvE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,sBAAsB,GAAG;AAC1E,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AACnE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AACrE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB,GAAG;AACzE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW,SAAS,eAAe,IAAI,KAAK;AAC1C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,MAAM,CAAC;AACtB,QAAG,YAAY,MAAM,CAAC;AACtB,QAAG,YAAY,MAAM,CAAC;AAAA,MACxB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,sBAAsB,GAAG;AAC1E,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAC/D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY,GAAG;AAAA,MAClE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,UAAU,CAAC,GAAG,YAAY,YAAY,eAAe;AAAA,IACvD;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,aAAa,CAAC,GAAM,0BAA6B,0BAA0B;AAAA,IAC7G,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,aAAa,EAAE,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,cAAc,EAAE,GAAG,CAAC,GAAG,WAAW,SAAS,GAAG,CAAC,SAAS,oBAAoB,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,aAAa,aAAa,YAAY,eAAe,kBAAkB,gBAAgB,SAAS,UAAU,QAAQ,eAAe,aAAa,YAAY,YAAY,cAAc,SAAS,kBAAkB,iBAAiB,gBAAgB,gBAAgB,cAAc,GAAG,CAAC,SAAS,kBAAkB,GAAG,MAAM,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,QAAQ,UAAU,YAAY,KAAK,SAAS,qBAAqB,GAAG,SAAS,WAAW,GAAG,MAAM,GAAG,CAAC,QAAQ,UAAU,YAAY,KAAK,GAAG,qBAAqB,GAAG,SAAS,SAAS,GAAG,CAAC,GAAG,gBAAgB,CAAC;AAAA,IACjtB,UAAU,SAAS,kBAAkB,IAAI,KAAK;AAC5C,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,WAAW,GAAG,yBAAyB,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,kCAAkC,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,kCAAkC,GAAG,GAAG,gBAAgB,CAAC;AACpL,QAAG,eAAe,GAAG,kBAAkB,GAAG,CAAC;AAC3C,QAAG,WAAW,aAAa,SAAS,sDAAsD,QAAQ;AAChG,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,YAAY,MAAM,CAAC;AAAA,QAC/C,CAAC,EAAE,aAAa,SAAS,sDAAsD,QAAQ;AACrF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,YAAY,MAAM,CAAC;AAAA,QAC/C,CAAC,EAAE,YAAY,SAAS,qDAAqD,QAAQ;AACnF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,WAAW,MAAM,CAAC;AAAA,QAC9C,CAAC,EAAE,eAAe,SAAS,wDAAwD,QAAQ;AACzF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,UAAU,MAAM,CAAC;AAAA,QAC7C,CAAC,EAAE,kBAAkB,SAAS,2DAA2D,QAAQ;AAC/F,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,iBAAiB,MAAM,CAAC;AAAA,QACpD,CAAC;AACD,QAAG,aAAa;AAChB,QAAG,WAAW,GAAG,yBAAyB,GAAG,GAAG,OAAO,CAAC;AACxD,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAc,gBAAgB,IAAI,MAAM,IAAI,eAAe,cAAc,IAAI,eAAe,QAAQ,EAAE,CAAC;AAC1G,QAAG,WAAW,IAAI,UAAU;AAC5B,QAAG,WAAW,WAAc,gBAAgB,IAAI,MAAM,IAAI,cAAc,IAAI,cAAc,IAAI,eAAe,cAAc,IAAI,eAAe,UAAU,CAAC,EAAE,WAAW,IAAI,KAAK;AAC/K,QAAG,YAAY,mBAAmB,MAAM,EAAE,gBAAgB,UAAU,EAAE,MAAM,IAAI,EAAE;AAClF,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,QAAQ,IAAI,iBAAiB,IAAI,cAAc;AAC7D,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,CAAC,IAAI,kBAAkB,CAAC,IAAI,eAAe;AACjE,QAAG,UAAU;AACb,QAAG,WAAW,oBAAoB,IAAI,kBAAkB,IAAI,eAAe;AAC3E,QAAG,UAAU;AACb,QAAG,WAAW,gBAAgB,IAAI,gBAAgB,IAAI,aAAa,EAAE,SAAS,IAAI,cAAc,EAAE,UAAU,IAAI,EAAE,EAAE,QAAQ,IAAI,EAAE,eAAe,IAAI,WAAW,EAAE,aAAa,IAAI,SAAS,EAAE,YAAY,IAAI,QAAQ,EAAE,YAAY,CAAC,IAAI,WAAW,IAAI,WAAW,EAAE,EAAE,cAAc,IAAI,WAAW,CAAC,EAAE,SAAS,CAAC,EAAE,kBAAkB,IAAI,cAAc,EAAE,iBAAiB,IAAI,UAAU,IAAI,gBAAgB,MAAS,EAAE,gBAAgB,IAAI,YAAY,EAAE,gBAAgB,IAAI,YAAY,EAAE,gBAAgB,IAAI,YAAY;AAC3f,QAAG,YAAY,MAAM,IAAI,KAAK,OAAO;AACrC,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,QAAQ,IAAI,eAAe,IAAI,YAAY;AAAA,MAC3D;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAiB,SAAY,MAAS,kBAAqB,SAAS,cAAc,aAAa,eAAe,UAAU,aAAa,YAAY;AAAA,IAChK,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,cAAc,aAAa,eAAe,UAAU,aAAa,YAAY;AAAA,MACrG,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAoEV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,aAAa;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,QACd,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,QACZ,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,QACjB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,QACpB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,QACb,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,QACf,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,QACnB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,UAAU,YAAY;AAAA,IAChC,SAAS,CAAC,UAAU,YAAY;AAAA,EAClC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,UAAU,cAAc,YAAY;AAAA,EAChD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,UAAU,YAAY;AAAA,MAChC,SAAS,CAAC,UAAU,YAAY;AAAA,IAClC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["MegaMenuClasses", "processedItem", "grouped"]}
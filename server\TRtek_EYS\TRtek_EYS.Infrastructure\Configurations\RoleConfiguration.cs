﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TRtek_EYS.Domain.Entities;

namespace TRtek_EYS.Infrastructure.Configurations
{
    public class RoleConfiguration : IEntityTypeConfiguration<Role>
    {
        public void Configure(EntityTypeBuilder<Role> builder)
        {
            builder.ToTable("Roles");

            builder.<PERSON><PERSON>ey(r => r.Id);

            builder.Property(r => r.Name)
                   .IsRequired()
                   .HasMaxLength(100);

            builder.Property(r => r.Description)
                   .HasMaxLength(250);

            builder.Property(r => r.CreatedAt)
                   .IsRequired();

            builder.HasOne(r => r.Team)
                   .WithMany(t => t.Roles)
                   .HasForeignKey(r => r.TeamId)
                   .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(r => r.UserRoles)
                   .WithOne(ur => ur.Role)
                   .HasForeignKey(ur => ur.RoleId);

            builder.HasMany(r => r.ProjectUserRoles)
                   .WithOne(pur => pur.Role)
                   .HasForeignKey(pur => pur.RoleId);
        }
    }
}

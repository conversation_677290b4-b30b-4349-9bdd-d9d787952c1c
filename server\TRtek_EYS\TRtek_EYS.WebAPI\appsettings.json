{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",

  "ConnectionStrings": {
    "DefaultConnection": "Host=dpg-d1rl1kmmcj7s73cdkc4g-a.frankfurt-postgres.render.com;Database=eys_db;Username=eys_db_user;Password=********************************;SSL Mode=Prefer;"
  },

  "JwtSettings": {
    "Key": "supersecure_jwt_key_for_trtek_eys_2025!", // 38 karakter
    "Issuer": "TRtekEYS.API",
    "Audience": "TRtekEYS.Client",
    "AccessTokenExpiresInMinutes": 60,
    "RefreshTokenExpiresInDays": 7
  }
}

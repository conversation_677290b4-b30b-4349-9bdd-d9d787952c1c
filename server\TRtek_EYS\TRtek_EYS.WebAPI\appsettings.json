{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=dpg-d1rl1kmmcj7s73cdkc4g-a.frankfurt-postgres.render.com;Port=5432;Database=eys_db;Username=eys_db_user;Password=********************************;SSL Mode=Require"}, "JwtSettings": {"Key": "supersecure_32_char_min_key", "Issuer": "TRtekEYS.API", "Audience": "TRtekEYS.Client", "AccessTokenExpiresInMinutes": 60, "RefreshTokenExpiresInDays": 7}}
import { Component } from '@angular/core';
import { RippleModule } from 'primeng/ripple';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { CommonModule } from '@angular/common';
import { TagModule } from 'primeng/tag';

interface Project {
    name: string;
    customer: string;
    status: string;
    technologies: string[];
    lastUpdate: Date;
}

@Component({
    standalone: true,
    selector: 'app-recent-projects-widget',
    imports: [CommonModule, TableModule, ButtonModule, RippleModule, TagModule],
    template: `<div class="card !mb-8">
        <div class="font-semibold text-xl mb-4">Son <PERSON>jeler</div>
        <p-table [value]="projects" [paginator]="true" [rows]="5" responsiveLayout="scroll">
            <ng-template pTemplate="header">
                <tr>
                    <th pSortableColumn="name">Proje <p-sortIcon field="name"></p-sortIcon></th>
                    <th pSortableColumn="customer">Müşteri <p-sortIcon field="customer"></p-sortIcon></th>
                    <th pSortableColumn="status">Durum <p-sortIcon field="status"></p-sortIcon></th>
                    <th>Teknolojiler</th>
                    <th pSortableColumn="lastUpdate">Son Güncelleme <p-sortIcon field="lastUpdate"></p-sortIcon></th>
                    <th>İşlemler</th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-project>
                <tr>
                    <td style="min-width: 10rem;">{{ project.name }}</td>
                    <td style="min-width: 8rem;">{{ project.customer }}</td>
                    <td style="min-width: 8rem;">
                        <p-tag [severity]="getStatusSeverity(project.status)" [value]="project.status"></p-tag>
                    </td>
                    <td style="min-width: 12rem;">
                        <div class="flex flex-wrap gap-2">
                            <p-tag *ngFor="let tech of project.technologies" severity="info" [value]="tech"></p-tag>
                        </div>
                    </td>
                    <td style="min-width: 10rem;">{{ project.lastUpdate | date:'medium' }}</td>
                    <td>
                        <button pButton pRipple type="button" icon="pi pi-eye" 
                            class="p-button p-component p-button-text p-button-icon-only"
                            pTooltip="Detayları Görüntüle"></button>
                    </td>
                </tr>
            </ng-template>
        </p-table>
    </div>`
})
export class RecentProjectsWidget {
    projects: Project[] = [
        {
            name: 'E-Ticaret Platformu',
            customer: 'TechCorp A.Ş.',
            status: 'Aktif',
            technologies: ['Angular', 'Node.js', 'MongoDB'],
            lastUpdate: new Date('2025-07-15')
        },
        {
            name: 'Mobil Uygulama',
            customer: 'HealthCare Ltd.',
            status: 'Planlama',
            technologies: ['React Native', 'Firebase'],
            lastUpdate: new Date('2025-07-14')
        },
        {
            name: 'CRM Sistemi',
            customer: 'Sales Pro',
            status: 'Tamamlandı',
            technologies: ['.NET', 'SQL Server', 'Angular'],
            lastUpdate: new Date('2025-07-13')
        },
        {
            name: 'Analitik Dashboard',
            customer: 'DataViz Ltd',
            status: 'Aktif',
            technologies: ['Vue.js', 'Python', 'PostgreSQL'],
            lastUpdate: new Date('2025-07-12')
        },
        {
            name: 'Envanter Sistemi',
            customer: 'Logistics Co',
            status: 'Beklemede',
            technologies: ['Java', 'Spring', 'Oracle'],
            lastUpdate: new Date('2025-07-11')
        }
    ];

    getStatusSeverity(status: string): string {
        switch (status) {
            case 'Aktif':
                return 'success';
            case 'Planlama':
                return 'info';
            case 'Tamamlandı':
                return 'primary';
            case 'Beklemede':
                return 'warning';
            default:
                return 'info';
        }
    }
}

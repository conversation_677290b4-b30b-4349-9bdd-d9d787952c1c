{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-floatlabel.mjs"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, Input, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { SharedModule } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"*\"];\nconst theme = ({\n  dt\n}) => `\n.p-floatlabel {\n    display: block;\n    position: relative;\n}\n\n.p-floatlabel label {\n    position: absolute;\n    pointer-events: none;\n    top: 50%;\n    transform: translateY(-50%);\n    transition-property: all;\n    transition-timing-function: ease;\n    line-height: 1;\n    font-weight: ${dt('floatlabel.font.weight')};\n    inset-inline-start: ${dt('floatlabel.position.x')};\n    color: ${dt('floatlabel.color')};\n    transition-duration: ${dt('floatlabel.transition.duration')};\n}\n\n.p-floatlabel:has(.p-textarea) label {\n    top: ${dt('floatlabel.position.y')};\n    transform: translateY(0);\n}\n\n.p-floatlabel:has(.p-inputicon:first-child) label {\n    inset-inline-start: calc((${dt('form.field.padding.x')} * 2) + ${dt('icon.size')});\n}\n\n.p-floatlabel:has(.ng-invalid.ng-dirty) label {\n    color: ${dt('floatlabel.invalid.color')};\n}\n\n.p-floatlabel:has(input:focus) label,\n.p-floatlabel:has(input.p-filled) label,\n.p-floatlabel:has(input:-webkit-autofill) label,\n.p-floatlabel:has(textarea:focus) label,\n.p-floatlabel:has(textarea.p-filled) label,\n.p-floatlabel:has(.p-inputwrapper-focus) label,\n.p-floatlabel:has(.p-inputwrapper-filled) label {\n    top: ${dt('floatlabel.over.active.top')};\n    transform: translateY(0);\n    font-size: ${dt('floatlabel.active.font.size')};\n    font-weight: ${dt('floatlabel.label.active.font.weight')};\n}\n\n.p-floatlabel:has(input.p-filled) label,\n.p-floatlabel:has(textarea.p-filled) label,\n.p-floatlabel:has(.p-inputwrapper-filled) label {\n    color: ${dt('floatlabel.active.color')};\n}\n\n.p-floatlabel:has(input:focus) label,\n.p-floatlabel:has(input:-webkit-autofill) label,\n.p-floatlabel:has(textarea:focus) label,\n.p-floatlabel:has(.p-inputwrapper-focus) label {\n    color: ${dt('floatlabel.focus.color')};\n}\n\n.p-floatlabel-in .p-inputtext,\n.p-floatlabel-in .p-textarea,\n.p-floatlabel-in .p-select-label,\n.p-floatlabel-in .p-multiselect-label-container,\n.p-floatlabel-in .p-autocomplete-input-multiple,\n.p-floatlabel-in .p-cascadeselect-label,\n.p-floatlabel-in .p-treeselect-label {\n    padding-top: ${dt('floatlabel.in.input.padding.top')};\n}\n\n.p-floatlabel-in:has(input:focus) label,\n.p-floatlabel-in:has(input.p-filled) label,\n.p-floatlabel-in:has(input:-webkit-autofill) label,\n.p-floatlabel-in:has(textarea:focus) label,\n.p-floatlabel-in:has(textarea.p-filled) label,\n.p-floatlabel-in:has(.p-inputwrapper-focus) label,\n.p-floatlabel-in:has(.p-inputwrapper-filled) label {\n    top: ${dt('floatlabel.in.active.top')};\n}\n\n.p-floatlabel-on:has(input:focus) label,\n.p-floatlabel-on:has(input.p-filled) label,\n.p-floatlabel-on:has(input:-webkit-autofill) label,\n.p-floatlabel-on:has(textarea:focus) label,\n.p-floatlabel-on:has(textarea.p-filled) label,\n.p-floatlabel-on:has(.p-inputwrapper-focus) label,\n.p-floatlabel-on:has(.p-inputwrapper-filled) label {\n    top: 0;\n    transform: translateY(-50%);\n    border-radius: ${dt('floatlabel.on.border.radius')};\n    background: ${dt('floatlabel.on.active.background')};\n    padding: ${dt('floatlabel.on.active.padding')};\n}\n`;\nconst classes = {\n  root: ({\n    instance,\n    props\n  }) => ['p-floatlabel', {\n    'p-floatlabel-over': props.variant === 'over',\n    'p-floatlabel-on': props.variant === 'on',\n    'p-floatlabel-in': props.variant === 'in'\n  }]\n};\nclass FloatLabelStyle extends BaseStyle {\n  name = 'floatlabel';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵFloatLabelStyle_BaseFactory;\n    return function FloatLabelStyle_Factory(__ngFactoryType__) {\n      return (ɵFloatLabelStyle_BaseFactory || (ɵFloatLabelStyle_BaseFactory = i0.ɵɵgetInheritedFactory(FloatLabelStyle)))(__ngFactoryType__ || FloatLabelStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FloatLabelStyle,\n    factory: FloatLabelStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FloatLabelStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * FloatLabel visually integrates a label with its form element.\n *\n * [Live Demo](https://www.primeng.org/floatlabel/)\n *\n * @module floatlabelstyle\n *\n */\nvar FloatLabelClasses;\n(function (FloatLabelClasses) {\n  /**\n   * Class name of the root element\n   */\n  FloatLabelClasses[\"root\"] = \"p-floatlabel\";\n})(FloatLabelClasses || (FloatLabelClasses = {}));\n\n/**\n * FloatLabel appears on top of the input field when focused.\n * @group Components\n */\nclass FloatLabel extends BaseComponent {\n  _componentStyle = inject(FloatLabelStyle);\n  /**\n   * Defines the positioning of the label relative to the input.\n   * @group Props\n   */\n  variant = 'over';\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵFloatLabel_BaseFactory;\n    return function FloatLabel_Factory(__ngFactoryType__) {\n      return (ɵFloatLabel_BaseFactory || (ɵFloatLabel_BaseFactory = i0.ɵɵgetInheritedFactory(FloatLabel)))(__ngFactoryType__ || FloatLabel);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: FloatLabel,\n    selectors: [[\"p-floatlabel\"], [\"p-floatLabel\"], [\"p-float-label\"]],\n    hostVars: 8,\n    hostBindings: function FloatLabel_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"p-floatlabel\", true)(\"p-floatlabel-over\", ctx.variant === \"over\")(\"p-floatlabel-on\", ctx.variant === \"on\")(\"p-floatlabel-in\", ctx.variant === \"in\");\n      }\n    },\n    inputs: {\n      variant: \"variant\"\n    },\n    features: [i0.ɵɵProvidersFeature([FloatLabelStyle]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function FloatLabel_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    dependencies: [CommonModule, SharedModule],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FloatLabel, [{\n    type: Component,\n    args: [{\n      selector: 'p-floatlabel, p-floatLabel, p-float-label',\n      standalone: true,\n      imports: [CommonModule, SharedModule],\n      template: ` <ng-content></ng-content> `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [FloatLabelStyle],\n      host: {\n        '[class.p-floatlabel]': 'true',\n        '[class.p-floatlabel-over]': \"variant === 'over'\",\n        '[class.p-floatlabel-on]': \"variant === 'on'\",\n        '[class.p-floatlabel-in]': \"variant === 'in'\"\n      }\n    }]\n  }], null, {\n    variant: [{\n      type: Input\n    }]\n  });\n})();\nclass FloatLabelModule {\n  static ɵfac = function FloatLabelModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FloatLabelModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: FloatLabelModule,\n    imports: [FloatLabel, SharedModule],\n    exports: [FloatLabel, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [FloatLabel, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FloatLabelModule, [{\n    type: NgModule,\n    args: [{\n      imports: [FloatLabel, SharedModule],\n      exports: [FloatLabel, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FloatLabel, FloatLabelClasses, FloatLabelModule, FloatLabelStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAca,GAAG,wBAAwB,CAAC;AAAA,0BACrB,GAAG,uBAAuB,CAAC;AAAA,aACxC,GAAG,kBAAkB,CAAC;AAAA,2BACR,GAAG,gCAAgC,CAAC;AAAA;AAAA;AAAA;AAAA,WAIpD,GAAG,uBAAuB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,gCAKN,GAAG,sBAAsB,CAAC,WAAW,GAAG,WAAW,CAAC;AAAA;AAAA;AAAA;AAAA,aAIvE,GAAG,0BAA0B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WAUhC,GAAG,4BAA4B,CAAC;AAAA;AAAA,iBAE1B,GAAG,6BAA6B,CAAC;AAAA,mBAC/B,GAAG,qCAAqC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAM/C,GAAG,yBAAyB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAO7B,GAAG,wBAAwB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAUtB,GAAG,iCAAiC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WAU7C,GAAG,0BAA0B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAYpB,GAAG,6BAA6B,CAAC;AAAA,kBACpC,GAAG,iCAAiC,CAAC;AAAA,eACxC,GAAG,8BAA8B,CAAC;AAAA;AAAA;AAGjD,IAAM,UAAU;AAAA,EACd,MAAM,CAAC;AAAA,IACL;AAAA,IACA;AAAA,EACF,MAAM,CAAC,gBAAgB;AAAA,IACrB,qBAAqB,MAAM,YAAY;AAAA,IACvC,mBAAmB,MAAM,YAAY;AAAA,IACrC,mBAAmB,MAAM,YAAY;AAAA,EACvC,CAAC;AACH;AACA,IAAM,kBAAN,MAAM,yBAAwB,UAAU;AAAA,EACtC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,wBAAwB,mBAAmB;AACzD,cAAQ,iCAAiC,+BAAkC,sBAAsB,gBAAe,IAAI,qBAAqB,gBAAe;AAAA,IAC1J;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,iBAAgB;AAAA,EAC3B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,oBAAmB;AAI5B,EAAAA,mBAAkB,MAAM,IAAI;AAC9B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAMhD,IAAM,aAAN,MAAM,oBAAmB,cAAc;AAAA,EACrC,kBAAkB,OAAO,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxC,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,mBAAmB,mBAAmB;AACpD,cAAQ,4BAA4B,0BAA6B,sBAAsB,WAAU,IAAI,qBAAqB,WAAU;AAAA,IACtI;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,GAAG,CAAC,cAAc,GAAG,CAAC,eAAe,CAAC;AAAA,IACjE,UAAU;AAAA,IACV,cAAc,SAAS,wBAAwB,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,gBAAgB,IAAI,EAAE,qBAAqB,IAAI,YAAY,MAAM,EAAE,mBAAmB,IAAI,YAAY,IAAI,EAAE,mBAAmB,IAAI,YAAY,IAAI;AAAA,MACpK;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,eAAe,CAAC,GAAM,0BAA0B;AAAA,IAClF,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,oBAAoB,IAAI,KAAK;AAC9C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAc,YAAY;AAAA,IACzC,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,YAAY;AAAA,MACpC,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,eAAe;AAAA,MAC3B,MAAM;AAAA,QACJ,wBAAwB;AAAA,QACxB,6BAA6B;AAAA,QAC7B,2BAA2B;AAAA,QAC3B,2BAA2B;AAAA,MAC7B;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,YAAY,YAAY;AAAA,IAClC,SAAS,CAAC,YAAY,YAAY;AAAA,EACpC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,YAAY,cAAc,YAAY;AAAA,EAClD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY,YAAY;AAAA,MAClC,SAAS,CAAC,YAAY,YAAY;AAAA,IACpC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["FloatLabelClasses"]}
{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-splitter.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { CommonModule, isPlatformBrowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, contentChild, forwardRef, computed, ChangeDetectionStrategy, Component, EventEmitter, inject, numberAttribute, ContentChildren, ViewChild, Output, Input, ViewEncapsulation, NgModule } from '@angular/core';\nimport { hasClass, getWidth, getHeight, getOuterWidth, getOuterHeight, addClass, isRTL, removeClass } from '@primeuix/utils';\nimport { SharedModule, PrimeTemplate } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"*\"];\nconst _c1 = [\"panel\"];\nconst _c2 = [\"container\"];\nconst _c3 = a0 => ({\n  display: \"flex\",\n  \"flex-wrap\": \"nowrap\",\n  \"flex-direction\": a0\n});\nfunction Splitter_ng_template_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Splitter_ng_template_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵlistener(\"mousedown\", function Splitter_ng_template_2_div_2_Template_div_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const i_r2 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onGutterMouseDown($event, i_r2));\n    })(\"touchstart\", function Splitter_ng_template_2_div_2_Template_div_touchstart_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const i_r2 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onGutterTouchStart($event, i_r2));\n    })(\"touchmove\", function Splitter_ng_template_2_div_2_Template_div_touchmove_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onGutterTouchMove($event));\n    })(\"touchend\", function Splitter_ng_template_2_div_2_Template_div_touchend_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onGutterTouchEnd($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 7);\n    i0.ɵɵlistener(\"keyup\", function Splitter_ng_template_2_div_2_Template_div_keyup_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onGutterKeyUp($event));\n    })(\"keydown\", function Splitter_ng_template_2_div_2_Template_div_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const i_r2 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onGutterKeyDown($event, i_r2));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-p-gutter-resizing\", false)(\"data-pc-section\", \"gutter\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", ctx_r2.gutterStyle());\n    i0.ɵɵattribute(\"aria-orientation\", ctx_r2.layout)(\"aria-valuenow\", ctx_r2.prevSize)(\"data-pc-section\", \"gutterhandle\");\n  }\n}\nfunction Splitter_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵtemplate(1, Splitter_ng_template_2_ng_container_1_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, Splitter_ng_template_2_div_2_Template, 2, 6, \"div\", 5);\n  }\n  if (rf & 2) {\n    const panel_r4 = ctx.$implicit;\n    const i_r2 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r2.panelStyleClass);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.panelContainerClass())(\"ngStyle\", ctx_r2.panelStyle);\n    i0.ɵɵattribute(\"data-pc-name\", \"splitter\")(\"data-pc-section\", \"root\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", panel_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r2 !== ctx_r2.panels.length - 1);\n  }\n}\nconst theme = ({\n  dt\n}) => `\n.p-splitter {\n    display: flex;\n    flex-wrap: nowrap;\n    border: 1px solid ${dt('splitter.border.color')};\n    background: ${dt('splitter.background')};\n    border-radius: ${dt('border.radius.md')};\n    color: ${dt('splitter.color')};\n}\n\n.p-splitter-vertical {\n    flex-direction: column;\n}\n\n.p-splitter-gutter {\n    flex-grow: 0;\n    flex-shrink: 0;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    z-index: 1;\n    background: ${dt('splitter.gutter.background')};\n}\n\n.p-splitter-gutter-handle {\n    border-radius: ${dt('splitter.handle.border.radius')};\n    background: ${dt('splitter.handle.background')};\n    transition: outline-color ${dt('splitter.transition.duration')}, box-shadow ${dt('splitter.transition.duration')};\n    outline-color: transparent;\n}\n\n.p-splitter-gutter-handle:focus-visible {\n    box-shadow: ${dt('splitter.handle.focus.ring.shadow')};\n    outline: ${dt('splitter.handle.focus.ring.width')} ${dt('splitter.handle.focus.ring.style')} ${dt('splitter.handle.focus.ring.color')};\n    outline-offset: ${dt('splitter.handle.focus.ring.offset')};\n}\n\n.p-splitter-horizontal.p-splitter-resizing {\n    cursor: col-resize;\n    user-select: none;\n}\n\n.p-splitter-vertical.p-splitter-resizing {\n    cursor: row-resize;\n    user-select: none;\n}\n\n.p-splitter-horizontal > .p-splitter-gutter > .p-splitter-gutter-handle {\n    height: ${dt('splitter.handle.size')};\n    width: 100%;\n}\n\n.p-splitter-vertical > .p-splitter-gutter > .p-splitter-gutter-handle {\n    width: ${dt('splitter.handle.size')};\n    height: 100%;\n}\n\n.p-splitter-horizontal > .p-splitter-gutter {\n    cursor: col-resize;\n}\n\n.p-splitter-vertical > .p-splitter-gutter {\n    cursor: row-resize;\n}\n\n.p-splitterpanel {\n    flex-grow: 1;\n    overflow: hidden;\n}\n\n.p-splitterpanel-nested {\n    display: flex;\n    flex-grow: 1;\n    justify-content: center;\n}\n\n.p-splitterpanel .p-splitter {\n    flex-grow: 1;\n    border: 0 none;\n}\n`;\nconst classes = {\n  root: ({\n    props\n  }) => ['p-splitter p-component', 'p-splitter-' + props.layout],\n  gutter: 'p-splitter-gutter',\n  gutterHandle: 'p-splitter-gutter-handle'\n};\n/*const inlineStyles = {\n    root: ({ props }) => [{ display: 'flex', 'flex-wrap': 'nowrap' }, props.layout === 'vertical' ? { 'flex-direction': 'column' } : '']\n};*/\nclass SplitterStyle extends BaseStyle {\n  name = 'splitter';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵSplitterStyle_BaseFactory;\n    return function SplitterStyle_Factory(__ngFactoryType__) {\n      return (ɵSplitterStyle_BaseFactory || (ɵSplitterStyle_BaseFactory = i0.ɵɵgetInheritedFactory(SplitterStyle)))(__ngFactoryType__ || SplitterStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: SplitterStyle,\n    factory: SplitterStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SplitterStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * Splitter is utilized to separate and resize panels.\n *\n * [Live Demo](https://www.primeng.org/splitter/)\n *\n * @module splitterstyle\n *\n */\nvar SplitterClasses;\n(function (SplitterClasses) {\n  /**\n   * Class name of the root element\n   */\n  SplitterClasses[\"root\"] = \"p-splitter\";\n  /**\n   * Class name of the gutter element\n   */\n  SplitterClasses[\"gutter\"] = \"p-splitter-gutter\";\n  /**\n   * Class name of the gutter handle element\n   */\n  SplitterClasses[\"gutterHandle\"] = \"p-splitter-gutter-handle\";\n})(SplitterClasses || (SplitterClasses = {}));\nclass SplitterPanel extends BaseComponent {\n  splitter = contentChild(forwardRef(() => Splitter));\n  nestedState = computed(() => this.splitter());\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵSplitterPanel_BaseFactory;\n    return function SplitterPanel_Factory(__ngFactoryType__) {\n      return (ɵSplitterPanel_BaseFactory || (ɵSplitterPanel_BaseFactory = i0.ɵɵgetInheritedFactory(SplitterPanel)))(__ngFactoryType__ || SplitterPanel);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: SplitterPanel,\n    selectors: [[\"p-splitter-panel\"]],\n    contentQueries: function SplitterPanel_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuerySignal(dirIndex, ctx.splitter, Splitter, 5);\n      }\n      if (rf & 2) {\n        i0.ɵɵqueryAdvance();\n      }\n    },\n    hostAttrs: [1, \"p-splitterpanel\"],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function SplitterPanel_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    dependencies: [CommonModule],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SplitterPanel, [{\n    type: Component,\n    args: [{\n      selector: 'p-splitter-panel',\n      standalone: true,\n      imports: [CommonModule],\n      template: `<ng-content></ng-content>`,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'p-splitterpanel'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Splitter is utilized to separate and resize panels.\n * @group Components\n */\nclass Splitter extends BaseComponent {\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Style class of the panel.\n   * @group Props\n   */\n  panelStyleClass;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Inline style of the panel.\n   * @group Props\n   */\n  panelStyle;\n  /**\n   * Defines where a stateful splitter keeps its state, valid values are 'session' for sessionStorage and 'local' for localStorage.\n   * @group Props\n   */\n  stateStorage = 'session';\n  /**\n   * Storage identifier of a stateful Splitter.\n   * @group Props\n   */\n  stateKey = null;\n  /**\n   * Orientation of the panels. Valid values are 'horizontal' and 'vertical'.\n   * @group Props\n   */\n  layout = 'horizontal';\n  /**\n   * Size of the divider in pixels.\n   * @group Props\n   */\n  gutterSize = 4;\n  /**\n   * Step factor to increment/decrement the size of the panels while pressing the arrow keys.\n   * @group Props\n   */\n  step = 5;\n  /**\n   * Minimum size of the elements relative to 100%.\n   * @group Props\n   */\n  minSizes = [];\n  /**\n   * Size of the elements relative to 100%.\n   * @group Props\n   */\n  get panelSizes() {\n    return this._panelSizes;\n  }\n  set panelSizes(val) {\n    this._panelSizes = val;\n    if (this.el && this.el.nativeElement && this.panels.length > 0) {\n      let children = [...this.el.nativeElement.children[0].children].filter(child => hasClass(child, 'p-splitterpanel'));\n      let _panelSizes = [];\n      this.panels.map((panel, i) => {\n        let panelInitialSize = this.panelSizes.length - 1 >= i ? this.panelSizes[i] : null;\n        let panelSize = panelInitialSize || 100 / this.panels.length;\n        _panelSizes[i] = panelSize;\n        children[i].style.flexBasis = 'calc(' + panelSize + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n      });\n    }\n  }\n  /**\n   * Callback to invoke when resize ends.\n   * @param {SplitterResizeEndEvent} event - Custom panel resize end event\n   * @group Emits\n   */\n  onResizeEnd = new EventEmitter();\n  /**\n   * Callback to invoke when resize starts.\n   * @param {SplitterResizeStartEvent} event - Custom panel resize start event\n   * @group Emits\n   */\n  onResizeStart = new EventEmitter();\n  containerViewChild;\n  templates;\n  panelChildren;\n  nested = false;\n  panels = [];\n  dragging = false;\n  mouseMoveListener;\n  mouseUpListener;\n  touchMoveListener;\n  touchEndListener;\n  size;\n  gutterElement;\n  startPos;\n  prevPanelElement;\n  nextPanelElement;\n  nextPanelSize;\n  prevPanelSize;\n  _panelSizes = [];\n  prevPanelIndex;\n  timer;\n  prevSize;\n  _componentStyle = inject(SplitterStyle);\n  ngOnInit() {\n    super.ngOnInit();\n    this.nested = this.isNested();\n  }\n  ngAfterContentInit() {\n    if (this.templates && this.templates.toArray().length > 0) {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'panel':\n            this.panels.push(item.template);\n            break;\n          default:\n            this.panels.push(item.template);\n            break;\n        }\n      });\n    }\n    if (this.panelChildren && this.panelChildren.toArray().length > 0) {\n      this.panelChildren.forEach(item => {\n        this.panels.push(item);\n      });\n    }\n  }\n  ngAfterViewInit() {\n    super.ngAfterViewInit();\n    if (isPlatformBrowser(this.platformId)) {\n      if (this.panels && this.panels.length) {\n        let initialized = false;\n        if (this.isStateful()) {\n          initialized = this.restoreState();\n        }\n        if (!initialized) {\n          let children = [...this.el.nativeElement.children[0].children].filter(child => hasClass(child, 'p-splitterpanel'));\n          let _panelSizes = [];\n          this.panels.map((panel, i) => {\n            let panelInitialSize = this.panelSizes.length - 1 >= i ? this.panelSizes[i] : null;\n            let panelSize = panelInitialSize || 100 / this.panels.length;\n            _panelSizes[i] = panelSize;\n            children[i].style.flexBasis = 'calc(' + panelSize + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n          });\n          this._panelSizes = _panelSizes;\n          this.prevSize = parseFloat(_panelSizes[0]).toFixed(4);\n        }\n      }\n    }\n  }\n  resizeStart(event, index, isKeyDown) {\n    this.gutterElement = event.currentTarget || event.target.parentElement;\n    this.size = this.horizontal() ? getWidth(this.containerViewChild.nativeElement) : getHeight(this.containerViewChild.nativeElement);\n    if (!isKeyDown) {\n      this.dragging = true;\n      this.startPos = this.horizontal() ? event instanceof MouseEvent ? event.pageX : event.changedTouches[0].pageX : event instanceof MouseEvent ? event.pageY : event.changedTouches[0].pageY;\n    }\n    this.prevPanelElement = this.gutterElement.previousElementSibling;\n    this.nextPanelElement = this.gutterElement.nextElementSibling;\n    if (isKeyDown) {\n      this.prevPanelSize = this.horizontal() ? getOuterWidth(this.prevPanelElement, true) : getOuterHeight(this.prevPanelElement, true);\n      this.nextPanelSize = this.horizontal() ? getOuterWidth(this.nextPanelElement, true) : getOuterHeight(this.nextPanelElement, true);\n    } else {\n      this.prevPanelSize = 100 * (this.horizontal() ? getOuterWidth(this.prevPanelElement, true) : getOuterHeight(this.prevPanelElement, true)) / this.size;\n      this.nextPanelSize = 100 * (this.horizontal() ? getOuterWidth(this.nextPanelElement, true) : getOuterHeight(this.nextPanelElement, true)) / this.size;\n    }\n    this.prevPanelIndex = index;\n    addClass(this.gutterElement, 'p-splitter-gutter-resizing');\n    this.gutterElement.setAttribute('data-p-gutter-resizing', 'true');\n    addClass(this.containerViewChild.nativeElement, 'p-splitter-resizing');\n    this.containerViewChild.nativeElement.setAttribute('data-p-resizing', 'true');\n    this.onResizeStart.emit({\n      originalEvent: event,\n      sizes: this._panelSizes\n    });\n  }\n  onResize(event, step, isKeyDown) {\n    let newPos, newPrevPanelSize, newNextPanelSize;\n    if (isKeyDown) {\n      if (this.horizontal()) {\n        newPrevPanelSize = 100 * (this.prevPanelSize + step) / this.size;\n        newNextPanelSize = 100 * (this.nextPanelSize - step) / this.size;\n      } else {\n        newPrevPanelSize = 100 * (this.prevPanelSize - step) / this.size;\n        newNextPanelSize = 100 * (this.nextPanelSize + step) / this.size;\n      }\n    } else {\n      if (this.horizontal()) {\n        if (isRTL(this.el.nativeElement)) {\n          newPos = (this.startPos - event.pageX) * 100 / this.size;\n        } else {\n          newPos = (event.pageX - this.startPos) * 100 / this.size;\n        }\n      } else {\n        newPos = (event.pageY - this.startPos) * 100 / this.size;\n      }\n      newPrevPanelSize = this.prevPanelSize + newPos;\n      newNextPanelSize = this.nextPanelSize - newPos;\n    }\n    this.prevSize = parseFloat(newPrevPanelSize).toFixed(4);\n    if (this.validateResize(newPrevPanelSize, newNextPanelSize)) {\n      this.prevPanelElement.style.flexBasis = 'calc(' + newPrevPanelSize + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n      this.nextPanelElement.style.flexBasis = 'calc(' + newNextPanelSize + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n      this._panelSizes[this.prevPanelIndex] = newPrevPanelSize;\n      this._panelSizes[this.prevPanelIndex + 1] = newNextPanelSize;\n    }\n  }\n  resizeEnd(event) {\n    if (this.isStateful()) {\n      this.saveState();\n    }\n    this.onResizeEnd.emit({\n      originalEvent: event,\n      sizes: this._panelSizes\n    });\n    removeClass(this.gutterElement, 'p-splitter-gutter-resizing');\n    removeClass(this.containerViewChild.nativeElement, 'p-splitter-resizing');\n    this.clear();\n  }\n  onGutterMouseDown(event, index) {\n    this.resizeStart(event, index);\n    this.bindMouseListeners();\n  }\n  onGutterTouchStart(event, index) {\n    if (event.cancelable) {\n      this.resizeStart(event, index);\n      this.bindTouchListeners();\n      event.preventDefault();\n    }\n  }\n  onGutterTouchMove(event) {\n    this.onResize(event);\n    event.preventDefault();\n  }\n  onGutterTouchEnd(event) {\n    this.resizeEnd(event);\n    this.unbindTouchListeners();\n    if (event.cancelable) event.preventDefault();\n  }\n  repeat(event, index, step) {\n    this.resizeStart(event, index, true);\n    this.onResize(event, step, true);\n  }\n  setTimer(event, index, step) {\n    this.clearTimer();\n    this.timer = setTimeout(() => {\n      this.repeat(event, index, step);\n    }, 40);\n  }\n  clearTimer() {\n    if (this.timer) {\n      clearTimeout(this.timer);\n    }\n  }\n  onGutterKeyUp(event) {\n    this.clearTimer();\n    this.resizeEnd(event);\n  }\n  onGutterKeyDown(event, index) {\n    switch (event.code) {\n      case 'ArrowLeft':\n        {\n          if (this.layout === 'horizontal') {\n            this.setTimer(event, index, this.step * -1);\n          }\n          event.preventDefault();\n          break;\n        }\n      case 'ArrowRight':\n        {\n          if (this.layout === 'horizontal') {\n            this.setTimer(event, index, this.step);\n          }\n          event.preventDefault();\n          break;\n        }\n      case 'ArrowDown':\n        {\n          if (this.layout === 'vertical') {\n            this.setTimer(event, index, this.step * -1);\n          }\n          event.preventDefault();\n          break;\n        }\n      case 'ArrowUp':\n        {\n          if (this.layout === 'vertical') {\n            this.setTimer(event, index, this.step);\n          }\n          event.preventDefault();\n          break;\n        }\n      default:\n        //no op\n        break;\n    }\n  }\n  validateResize(newPrevPanelSize, newNextPanelSize) {\n    if (this.minSizes.length >= 1 && this.minSizes[0] && this.minSizes[0] > newPrevPanelSize) {\n      return false;\n    }\n    if (this.minSizes.length > 1 && this.minSizes[1] && this.minSizes[1] > newNextPanelSize) {\n      return false;\n    }\n    return true;\n  }\n  bindMouseListeners() {\n    if (!this.mouseMoveListener) {\n      this.mouseMoveListener = this.renderer.listen(this.document, 'mousemove', event => {\n        this.onResize(event);\n      });\n    }\n    if (!this.mouseUpListener) {\n      this.mouseUpListener = this.renderer.listen(this.document, 'mouseup', event => {\n        this.resizeEnd(event);\n        this.unbindMouseListeners();\n      });\n    }\n  }\n  bindTouchListeners() {\n    if (!this.touchMoveListener) {\n      this.touchMoveListener = this.renderer.listen(this.document, 'touchmove', event => {\n        this.onResize(event.changedTouches[0]);\n      });\n    }\n    if (!this.touchEndListener) {\n      this.touchEndListener = this.renderer.listen(this.document, 'touchend', event => {\n        this.resizeEnd(event);\n        this.unbindTouchListeners();\n      });\n    }\n  }\n  unbindMouseListeners() {\n    if (this.mouseMoveListener) {\n      this.mouseMoveListener();\n      this.mouseMoveListener = null;\n    }\n    if (this.mouseUpListener) {\n      this.mouseUpListener();\n      this.mouseUpListener = null;\n    }\n  }\n  unbindTouchListeners() {\n    if (this.touchMoveListener) {\n      this.touchMoveListener();\n      this.touchMoveListener = null;\n    }\n    if (this.touchEndListener) {\n      this.touchEndListener();\n      this.touchEndListener = null;\n    }\n  }\n  clear() {\n    this.dragging = false;\n    this.size = null;\n    this.startPos = null;\n    this.prevPanelElement = null;\n    this.nextPanelElement = null;\n    this.prevPanelSize = null;\n    this.nextPanelSize = null;\n    this.gutterElement = null;\n    this.prevPanelIndex = null;\n  }\n  isNested() {\n    if (this.el.nativeElement) {\n      let parent = this.el.nativeElement.parentElement;\n      while (parent && !hasClass(parent, 'p-splitter')) {\n        parent = parent.parentElement;\n      }\n      return parent !== null;\n    } else {\n      return false;\n    }\n  }\n  isStateful() {\n    return this.stateKey != null;\n  }\n  getStorage() {\n    if (isPlatformBrowser(this.platformId)) {\n      switch (this.stateStorage) {\n        case 'local':\n          return this.document.defaultView.localStorage;\n        case 'session':\n          return this.document.defaultView.sessionStorage;\n        default:\n          throw new Error(this.stateStorage + ' is not a valid value for the state storage, supported values are \"local\" and \"session\".');\n      }\n    } else {\n      throw new Error('Storage is not a available by default on the server.');\n    }\n  }\n  saveState() {\n    this.getStorage().setItem(this.stateKey, JSON.stringify(this._panelSizes));\n  }\n  restoreState() {\n    const storage = this.getStorage();\n    const stateString = storage.getItem(this.stateKey);\n    if (stateString) {\n      this._panelSizes = JSON.parse(stateString);\n      let children = [...this.containerViewChild.nativeElement.children].filter(child => hasClass(child, 'p-splitterpanel'));\n      children.forEach((child, i) => {\n        child.style.flexBasis = 'calc(' + this._panelSizes[i] + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n      });\n      return true;\n    }\n    return false;\n  }\n  containerClass() {\n    return {\n      'p-splitter p-component': true,\n      'p-splitter-horizontal': this.layout === 'horizontal',\n      'p-splitter-vertical': this.layout === 'vertical'\n    };\n  }\n  panelContainerClass() {\n    return {\n      'p-splitterpanel': true,\n      'p-splitterpanel-nested': true\n    };\n  }\n  gutterStyle() {\n    if (this.horizontal()) return {\n      width: this.gutterSize + 'px'\n    };else return {\n      height: this.gutterSize + 'px'\n    };\n  }\n  horizontal() {\n    return this.layout === 'horizontal';\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵSplitter_BaseFactory;\n    return function Splitter_Factory(__ngFactoryType__) {\n      return (ɵSplitter_BaseFactory || (ɵSplitter_BaseFactory = i0.ɵɵgetInheritedFactory(Splitter)))(__ngFactoryType__ || Splitter);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Splitter,\n    selectors: [[\"p-splitter\"]],\n    contentQueries: function Splitter_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c1, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.panelChildren = _t);\n      }\n    },\n    viewQuery: function Splitter_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c2, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);\n      }\n    },\n    hostVars: 2,\n    hostBindings: function Splitter_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"p-splitterpanel-nested\", ctx.nested);\n      }\n    },\n    inputs: {\n      styleClass: \"styleClass\",\n      panelStyleClass: \"panelStyleClass\",\n      style: \"style\",\n      panelStyle: \"panelStyle\",\n      stateStorage: \"stateStorage\",\n      stateKey: \"stateKey\",\n      layout: \"layout\",\n      gutterSize: [2, \"gutterSize\", \"gutterSize\", numberAttribute],\n      step: [2, \"step\", \"step\", numberAttribute],\n      minSizes: \"minSizes\",\n      panelSizes: \"panelSizes\"\n    },\n    outputs: {\n      onResizeEnd: \"onResizeEnd\",\n      onResizeStart: \"onResizeStart\"\n    },\n    features: [i0.ɵɵProvidersFeature([SplitterStyle]), i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature],\n    decls: 3,\n    vars: 12,\n    consts: [[\"container\", \"\"], [3, \"ngClass\", \"ngStyle\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"tabindex\", \"-1\", 3, \"ngClass\", \"ngStyle\"], [4, \"ngTemplateOutlet\"], [\"class\", \"p-splitter-gutter\", \"role\", \"separator\", \"tabindex\", \"-1\", 3, \"mousedown\", \"touchstart\", \"touchmove\", \"touchend\", 4, \"ngIf\"], [\"role\", \"separator\", \"tabindex\", \"-1\", 1, \"p-splitter-gutter\", 3, \"mousedown\", \"touchstart\", \"touchmove\", \"touchend\"], [\"tabindex\", \"0\", 1, \"p-splitter-gutter-handle\", 3, \"keyup\", \"keydown\", \"ngStyle\"]],\n    template: function Splitter_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 1, 0);\n        i0.ɵɵtemplate(2, Splitter_ng_template_2_Template, 3, 8, \"ng-template\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵstyleMap(i0.ɵɵpureFunction1(10, _c3, ctx.layout === \"vertical\" ? \"column\" : \"\"));\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", ctx.containerClass())(\"ngStyle\", ctx.style);\n        i0.ɵɵattribute(\"data-pc-name\", \"splitter\")(\"data-p-gutter-resizing\", false)(\"data-pc-section\", \"root\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.panels);\n      }\n    },\n    dependencies: [CommonModule, i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, SharedModule],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Splitter, [{\n    type: Component,\n    args: [{\n      selector: 'p-splitter',\n      standalone: true,\n      imports: [CommonModule, SharedModule],\n      template: `\n        <div\n            #container\n            [ngClass]=\"containerClass()\"\n            [class]=\"styleClass\"\n            [ngStyle]=\"style\"\n            [style]=\"{ display: 'flex', 'flex-wrap': 'nowrap', 'flex-direction': layout === 'vertical' ? 'column' : '' }\"\n            [attr.data-pc-name]=\"'splitter'\"\n            [attr.data-p-gutter-resizing]=\"false\"\n            [attr.data-pc-section]=\"'root'\"\n        >\n            <ng-template ngFor let-panel [ngForOf]=\"panels\" let-i=\"index\">\n                <div [ngClass]=\"panelContainerClass()\" [class]=\"panelStyleClass\" [ngStyle]=\"panelStyle\" tabindex=\"-1\" [attr.data-pc-name]=\"'splitter'\" [attr.data-pc-section]=\"'root'\">\n                    <ng-container *ngTemplateOutlet=\"panel\"></ng-container>\n                </div>\n                <div\n                    *ngIf=\"i !== panels.length - 1\"\n                    class=\"p-splitter-gutter\"\n                    role=\"separator\"\n                    tabindex=\"-1\"\n                    (mousedown)=\"onGutterMouseDown($event, i)\"\n                    (touchstart)=\"onGutterTouchStart($event, i)\"\n                    (touchmove)=\"onGutterTouchMove($event)\"\n                    (touchend)=\"onGutterTouchEnd($event)\"\n                    [attr.data-p-gutter-resizing]=\"false\"\n                    [attr.data-pc-section]=\"'gutter'\"\n                >\n                    <div\n                        class=\"p-splitter-gutter-handle\"\n                        tabindex=\"0\"\n                        [ngStyle]=\"gutterStyle()\"\n                        [attr.aria-orientation]=\"layout\"\n                        [attr.aria-valuenow]=\"prevSize\"\n                        [attr.data-pc-section]=\"'gutterhandle'\"\n                        (keyup)=\"onGutterKeyUp($event)\"\n                        (keydown)=\"onGutterKeyDown($event, i)\"\n                    ></div>\n                </div>\n            </ng-template>\n        </div>\n    `,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[class.p-splitterpanel-nested]': 'nested'\n      },\n      providers: [SplitterStyle]\n    }]\n  }], null, {\n    styleClass: [{\n      type: Input\n    }],\n    panelStyleClass: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    panelStyle: [{\n      type: Input\n    }],\n    stateStorage: [{\n      type: Input\n    }],\n    stateKey: [{\n      type: Input\n    }],\n    layout: [{\n      type: Input\n    }],\n    gutterSize: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    step: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    minSizes: [{\n      type: Input\n    }],\n    panelSizes: [{\n      type: Input\n    }],\n    onResizeEnd: [{\n      type: Output\n    }],\n    onResizeStart: [{\n      type: Output\n    }],\n    containerViewChild: [{\n      type: ViewChild,\n      args: ['container', {\n        static: false\n      }]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    panelChildren: [{\n      type: ContentChildren,\n      args: ['panel', {\n        descendants: false\n      }]\n    }]\n  });\n})();\nclass SplitterModule {\n  static ɵfac = function SplitterModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || SplitterModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: SplitterModule,\n    imports: [Splitter, SplitterPanel, SharedModule],\n    exports: [Splitter, SplitterPanel, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [Splitter, SplitterPanel, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SplitterModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Splitter, SplitterPanel, SharedModule],\n      exports: [Splitter, SplitterPanel, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Splitter, SplitterClasses, SplitterModule, SplitterPanel, SplitterStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,CAAC,OAAO;AACpB,IAAM,MAAM,CAAC,WAAW;AACxB,IAAM,MAAM,SAAO;AAAA,EACjB,SAAS;AAAA,EACT,aAAa;AAAA,EACb,kBAAkB;AACpB;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,sCAAsC,IAAI,KAAK;AACtD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,aAAa,SAAS,+DAA+D,QAAQ;AACzG,MAAG,cAAc,GAAG;AACpB,YAAM,OAAU,cAAc,EAAE;AAChC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,kBAAkB,QAAQ,IAAI,CAAC;AAAA,IAC9D,CAAC,EAAE,cAAc,SAAS,gEAAgE,QAAQ;AAChG,MAAG,cAAc,GAAG;AACpB,YAAM,OAAU,cAAc,EAAE;AAChC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,mBAAmB,QAAQ,IAAI,CAAC;AAAA,IAC/D,CAAC,EAAE,aAAa,SAAS,+DAA+D,QAAQ;AAC9F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,kBAAkB,MAAM,CAAC;AAAA,IACxD,CAAC,EAAE,YAAY,SAAS,8DAA8D,QAAQ;AAC5F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC;AACD,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,SAAS,SAAS,2DAA2D,QAAQ;AACjG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,cAAc,MAAM,CAAC;AAAA,IACpD,CAAC,EAAE,WAAW,SAAS,6DAA6D,QAAQ;AAC1F,MAAG,cAAc,GAAG;AACpB,YAAM,OAAU,cAAc,EAAE;AAChC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,gBAAgB,QAAQ,IAAI,CAAC;AAAA,IAC5D,CAAC;AACD,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,0BAA0B,KAAK,EAAE,mBAAmB,QAAQ;AAC3E,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,YAAY,CAAC;AAC7C,IAAG,YAAY,oBAAoB,OAAO,MAAM,EAAE,iBAAiB,OAAO,QAAQ,EAAE,mBAAmB,cAAc;AAAA,EACvH;AACF;AACA,SAAS,gCAAgC,IAAI,KAAK;AAChD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,gBAAgB,CAAC;AACxF,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,uCAAuC,GAAG,GAAG,OAAO,CAAC;AAAA,EACxE;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,UAAM,OAAO,IAAI;AACjB,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,eAAe;AACpC,IAAG,WAAW,WAAW,OAAO,oBAAoB,CAAC,EAAE,WAAW,OAAO,UAAU;AACnF,IAAG,YAAY,gBAAgB,UAAU,EAAE,mBAAmB,MAAM;AACpE,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,QAAQ;AAC1C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,SAAS,OAAO,OAAO,SAAS,CAAC;AAAA,EACzD;AACF;AACA,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA;AAAA;AAAA,wBAIkB,GAAG,uBAAuB,CAAC;AAAA,kBACjC,GAAG,qBAAqB,CAAC;AAAA,qBACtB,GAAG,kBAAkB,CAAC;AAAA,aAC9B,GAAG,gBAAgB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAcf,GAAG,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA,qBAI7B,GAAG,+BAA+B,CAAC;AAAA,kBACtC,GAAG,4BAA4B,CAAC;AAAA,gCAClB,GAAG,8BAA8B,CAAC,gBAAgB,GAAG,8BAA8B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,kBAKlG,GAAG,mCAAmC,CAAC;AAAA,eAC1C,GAAG,kCAAkC,CAAC,IAAI,GAAG,kCAAkC,CAAC,IAAI,GAAG,kCAAkC,CAAC;AAAA,sBACnH,GAAG,mCAAmC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAc/C,GAAG,sBAAsB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,aAK3B,GAAG,sBAAsB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4BvC,IAAM,UAAU;AAAA,EACd,MAAM,CAAC;AAAA,IACL;AAAA,EACF,MAAM,CAAC,0BAA0B,gBAAgB,MAAM,MAAM;AAAA,EAC7D,QAAQ;AAAA,EACR,cAAc;AAChB;AAIA,IAAM,gBAAN,MAAM,uBAAsB,UAAU;AAAA,EACpC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,sBAAsB,mBAAmB;AACvD,cAAQ,+BAA+B,6BAAgC,sBAAsB,cAAa,IAAI,qBAAqB,cAAa;AAAA,IAClJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,eAAc;AAAA,EACzB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,kBAAiB;AAI1B,EAAAA,iBAAgB,MAAM,IAAI;AAI1B,EAAAA,iBAAgB,QAAQ,IAAI;AAI5B,EAAAA,iBAAgB,cAAc,IAAI;AACpC,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAC5C,IAAM,gBAAN,MAAM,uBAAsB,cAAc;AAAA,EACxC,WAAW,aAAa,WAAW,MAAM,QAAQ,CAAC;AAAA,EAClD,cAAc,SAAS,MAAM,KAAK,SAAS,CAAC;AAAA,EAC5C,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,sBAAsB,mBAAmB;AACvD,cAAQ,+BAA+B,6BAAgC,sBAAsB,cAAa,IAAI,qBAAqB,cAAa;AAAA,IAClJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,IAChC,gBAAgB,SAAS,6BAA6B,IAAI,KAAK,UAAU;AACvE,UAAI,KAAK,GAAG;AACV,QAAG,qBAAqB,UAAU,IAAI,UAAU,UAAU,CAAC;AAAA,MAC7D;AACA,UAAI,KAAK,GAAG;AACV,QAAG,eAAe;AAAA,MACpB;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,iBAAiB;AAAA,IAChC,UAAU,CAAI,0BAA0B;AAAA,IACxC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,uBAAuB,IAAI,KAAK;AACjD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,cAAc,CAAC,YAAY;AAAA,IAC3B,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,YAAY;AAAA,MACtB,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,WAAN,MAAM,kBAAiB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAKT,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKP,WAAW,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKZ,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,KAAK;AAClB,SAAK,cAAc;AACnB,QAAI,KAAK,MAAM,KAAK,GAAG,iBAAiB,KAAK,OAAO,SAAS,GAAG;AAC9D,UAAI,WAAW,CAAC,GAAG,KAAK,GAAG,cAAc,SAAS,CAAC,EAAE,QAAQ,EAAE,OAAO,WAAS,SAAS,OAAO,iBAAiB,CAAC;AACjH,UAAI,cAAc,CAAC;AACnB,WAAK,OAAO,IAAI,CAAC,OAAO,MAAM;AAC5B,YAAI,mBAAmB,KAAK,WAAW,SAAS,KAAK,IAAI,KAAK,WAAW,CAAC,IAAI;AAC9E,YAAI,YAAY,oBAAoB,MAAM,KAAK,OAAO;AACtD,oBAAY,CAAC,IAAI;AACjB,iBAAS,CAAC,EAAE,MAAM,YAAY,UAAU,YAAY,UAAU,KAAK,OAAO,SAAS,KAAK,KAAK,aAAa;AAAA,MAC5G,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM/B,gBAAgB,IAAI,aAAa;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT,SAAS,CAAC;AAAA,EACV,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAc,CAAC;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAkB,OAAO,aAAa;AAAA,EACtC,WAAW;AACT,UAAM,SAAS;AACf,SAAK,SAAS,KAAK,SAAS;AAAA,EAC9B;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,aAAa,KAAK,UAAU,QAAQ,EAAE,SAAS,GAAG;AACzD,WAAK,UAAU,QAAQ,UAAQ;AAC7B,gBAAQ,KAAK,QAAQ,GAAG;AAAA,UACtB,KAAK;AACH,iBAAK,OAAO,KAAK,KAAK,QAAQ;AAC9B;AAAA,UACF;AACE,iBAAK,OAAO,KAAK,KAAK,QAAQ;AAC9B;AAAA,QACJ;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,KAAK,iBAAiB,KAAK,cAAc,QAAQ,EAAE,SAAS,GAAG;AACjE,WAAK,cAAc,QAAQ,UAAQ;AACjC,aAAK,OAAO,KAAK,IAAI;AAAA,MACvB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,UAAM,gBAAgB;AACtB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,UAAI,KAAK,UAAU,KAAK,OAAO,QAAQ;AACrC,YAAI,cAAc;AAClB,YAAI,KAAK,WAAW,GAAG;AACrB,wBAAc,KAAK,aAAa;AAAA,QAClC;AACA,YAAI,CAAC,aAAa;AAChB,cAAI,WAAW,CAAC,GAAG,KAAK,GAAG,cAAc,SAAS,CAAC,EAAE,QAAQ,EAAE,OAAO,WAAS,SAAS,OAAO,iBAAiB,CAAC;AACjH,cAAI,cAAc,CAAC;AACnB,eAAK,OAAO,IAAI,CAAC,OAAO,MAAM;AAC5B,gBAAI,mBAAmB,KAAK,WAAW,SAAS,KAAK,IAAI,KAAK,WAAW,CAAC,IAAI;AAC9E,gBAAI,YAAY,oBAAoB,MAAM,KAAK,OAAO;AACtD,wBAAY,CAAC,IAAI;AACjB,qBAAS,CAAC,EAAE,MAAM,YAAY,UAAU,YAAY,UAAU,KAAK,OAAO,SAAS,KAAK,KAAK,aAAa;AAAA,UAC5G,CAAC;AACD,eAAK,cAAc;AACnB,eAAK,WAAW,WAAW,YAAY,CAAC,CAAC,EAAE,QAAQ,CAAC;AAAA,QACtD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,OAAO,OAAO,WAAW;AACnC,SAAK,gBAAgB,MAAM,iBAAiB,MAAM,OAAO;AACzD,SAAK,OAAO,KAAK,WAAW,IAAI,SAAS,KAAK,mBAAmB,aAAa,IAAI,UAAU,KAAK,mBAAmB,aAAa;AACjI,QAAI,CAAC,WAAW;AACd,WAAK,WAAW;AAChB,WAAK,WAAW,KAAK,WAAW,IAAI,iBAAiB,aAAa,MAAM,QAAQ,MAAM,eAAe,CAAC,EAAE,QAAQ,iBAAiB,aAAa,MAAM,QAAQ,MAAM,eAAe,CAAC,EAAE;AAAA,IACtL;AACA,SAAK,mBAAmB,KAAK,cAAc;AAC3C,SAAK,mBAAmB,KAAK,cAAc;AAC3C,QAAI,WAAW;AACb,WAAK,gBAAgB,KAAK,WAAW,IAAI,cAAc,KAAK,kBAAkB,IAAI,IAAI,eAAe,KAAK,kBAAkB,IAAI;AAChI,WAAK,gBAAgB,KAAK,WAAW,IAAI,cAAc,KAAK,kBAAkB,IAAI,IAAI,eAAe,KAAK,kBAAkB,IAAI;AAAA,IAClI,OAAO;AACL,WAAK,gBAAgB,OAAO,KAAK,WAAW,IAAI,cAAc,KAAK,kBAAkB,IAAI,IAAI,eAAe,KAAK,kBAAkB,IAAI,KAAK,KAAK;AACjJ,WAAK,gBAAgB,OAAO,KAAK,WAAW,IAAI,cAAc,KAAK,kBAAkB,IAAI,IAAI,eAAe,KAAK,kBAAkB,IAAI,KAAK,KAAK;AAAA,IACnJ;AACA,SAAK,iBAAiB;AACtB,aAAS,KAAK,eAAe,4BAA4B;AACzD,SAAK,cAAc,aAAa,0BAA0B,MAAM;AAChE,aAAS,KAAK,mBAAmB,eAAe,qBAAqB;AACrE,SAAK,mBAAmB,cAAc,aAAa,mBAAmB,MAAM;AAC5E,SAAK,cAAc,KAAK;AAAA,MACtB,eAAe;AAAA,MACf,OAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EACA,SAAS,OAAO,MAAM,WAAW;AAC/B,QAAI,QAAQ,kBAAkB;AAC9B,QAAI,WAAW;AACb,UAAI,KAAK,WAAW,GAAG;AACrB,2BAAmB,OAAO,KAAK,gBAAgB,QAAQ,KAAK;AAC5D,2BAAmB,OAAO,KAAK,gBAAgB,QAAQ,KAAK;AAAA,MAC9D,OAAO;AACL,2BAAmB,OAAO,KAAK,gBAAgB,QAAQ,KAAK;AAC5D,2BAAmB,OAAO,KAAK,gBAAgB,QAAQ,KAAK;AAAA,MAC9D;AAAA,IACF,OAAO;AACL,UAAI,KAAK,WAAW,GAAG;AACrB,YAAI,MAAM,KAAK,GAAG,aAAa,GAAG;AAChC,oBAAU,KAAK,WAAW,MAAM,SAAS,MAAM,KAAK;AAAA,QACtD,OAAO;AACL,oBAAU,MAAM,QAAQ,KAAK,YAAY,MAAM,KAAK;AAAA,QACtD;AAAA,MACF,OAAO;AACL,kBAAU,MAAM,QAAQ,KAAK,YAAY,MAAM,KAAK;AAAA,MACtD;AACA,yBAAmB,KAAK,gBAAgB;AACxC,yBAAmB,KAAK,gBAAgB;AAAA,IAC1C;AACA,SAAK,WAAW,WAAW,gBAAgB,EAAE,QAAQ,CAAC;AACtD,QAAI,KAAK,eAAe,kBAAkB,gBAAgB,GAAG;AAC3D,WAAK,iBAAiB,MAAM,YAAY,UAAU,mBAAmB,UAAU,KAAK,OAAO,SAAS,KAAK,KAAK,aAAa;AAC3H,WAAK,iBAAiB,MAAM,YAAY,UAAU,mBAAmB,UAAU,KAAK,OAAO,SAAS,KAAK,KAAK,aAAa;AAC3H,WAAK,YAAY,KAAK,cAAc,IAAI;AACxC,WAAK,YAAY,KAAK,iBAAiB,CAAC,IAAI;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,UAAU,OAAO;AACf,QAAI,KAAK,WAAW,GAAG;AACrB,WAAK,UAAU;AAAA,IACjB;AACA,SAAK,YAAY,KAAK;AAAA,MACpB,eAAe;AAAA,MACf,OAAO,KAAK;AAAA,IACd,CAAC;AACD,gBAAY,KAAK,eAAe,4BAA4B;AAC5D,gBAAY,KAAK,mBAAmB,eAAe,qBAAqB;AACxE,SAAK,MAAM;AAAA,EACb;AAAA,EACA,kBAAkB,OAAO,OAAO;AAC9B,SAAK,YAAY,OAAO,KAAK;AAC7B,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,mBAAmB,OAAO,OAAO;AAC/B,QAAI,MAAM,YAAY;AACpB,WAAK,YAAY,OAAO,KAAK;AAC7B,WAAK,mBAAmB;AACxB,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA,EACA,kBAAkB,OAAO;AACvB,SAAK,SAAS,KAAK;AACnB,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,iBAAiB,OAAO;AACtB,SAAK,UAAU,KAAK;AACpB,SAAK,qBAAqB;AAC1B,QAAI,MAAM,WAAY,OAAM,eAAe;AAAA,EAC7C;AAAA,EACA,OAAO,OAAO,OAAO,MAAM;AACzB,SAAK,YAAY,OAAO,OAAO,IAAI;AACnC,SAAK,SAAS,OAAO,MAAM,IAAI;AAAA,EACjC;AAAA,EACA,SAAS,OAAO,OAAO,MAAM;AAC3B,SAAK,WAAW;AAChB,SAAK,QAAQ,WAAW,MAAM;AAC5B,WAAK,OAAO,OAAO,OAAO,IAAI;AAAA,IAChC,GAAG,EAAE;AAAA,EACP;AAAA,EACA,aAAa;AACX,QAAI,KAAK,OAAO;AACd,mBAAa,KAAK,KAAK;AAAA,IACzB;AAAA,EACF;AAAA,EACA,cAAc,OAAO;AACnB,SAAK,WAAW;AAChB,SAAK,UAAU,KAAK;AAAA,EACtB;AAAA,EACA,gBAAgB,OAAO,OAAO;AAC5B,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK,aACH;AACE,YAAI,KAAK,WAAW,cAAc;AAChC,eAAK,SAAS,OAAO,OAAO,KAAK,OAAO,EAAE;AAAA,QAC5C;AACA,cAAM,eAAe;AACrB;AAAA,MACF;AAAA,MACF,KAAK,cACH;AACE,YAAI,KAAK,WAAW,cAAc;AAChC,eAAK,SAAS,OAAO,OAAO,KAAK,IAAI;AAAA,QACvC;AACA,cAAM,eAAe;AACrB;AAAA,MACF;AAAA,MACF,KAAK,aACH;AACE,YAAI,KAAK,WAAW,YAAY;AAC9B,eAAK,SAAS,OAAO,OAAO,KAAK,OAAO,EAAE;AAAA,QAC5C;AACA,cAAM,eAAe;AACrB;AAAA,MACF;AAAA,MACF,KAAK,WACH;AACE,YAAI,KAAK,WAAW,YAAY;AAC9B,eAAK,SAAS,OAAO,OAAO,KAAK,IAAI;AAAA,QACvC;AACA,cAAM,eAAe;AACrB;AAAA,MACF;AAAA,MACF;AAEE;AAAA,IACJ;AAAA,EACF;AAAA,EACA,eAAe,kBAAkB,kBAAkB;AACjD,QAAI,KAAK,SAAS,UAAU,KAAK,KAAK,SAAS,CAAC,KAAK,KAAK,SAAS,CAAC,IAAI,kBAAkB;AACxF,aAAO;AAAA,IACT;AACA,QAAI,KAAK,SAAS,SAAS,KAAK,KAAK,SAAS,CAAC,KAAK,KAAK,SAAS,CAAC,IAAI,kBAAkB;AACvF,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,mBAAmB;AAC3B,WAAK,oBAAoB,KAAK,SAAS,OAAO,KAAK,UAAU,aAAa,WAAS;AACjF,aAAK,SAAS,KAAK;AAAA,MACrB,CAAC;AAAA,IACH;AACA,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkB,KAAK,SAAS,OAAO,KAAK,UAAU,WAAW,WAAS;AAC7E,aAAK,UAAU,KAAK;AACpB,aAAK,qBAAqB;AAAA,MAC5B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,mBAAmB;AAC3B,WAAK,oBAAoB,KAAK,SAAS,OAAO,KAAK,UAAU,aAAa,WAAS;AACjF,aAAK,SAAS,MAAM,eAAe,CAAC,CAAC;AAAA,MACvC,CAAC;AAAA,IACH;AACA,QAAI,CAAC,KAAK,kBAAkB;AAC1B,WAAK,mBAAmB,KAAK,SAAS,OAAO,KAAK,UAAU,YAAY,WAAS;AAC/E,aAAK,UAAU,KAAK;AACpB,aAAK,qBAAqB;AAAA,MAC5B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,mBAAmB;AAC1B,WAAK,kBAAkB;AACvB,WAAK,oBAAoB;AAAA,IAC3B;AACA,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB;AACrB,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,mBAAmB;AAC1B,WAAK,kBAAkB;AACvB,WAAK,oBAAoB;AAAA,IAC3B;AACA,QAAI,KAAK,kBAAkB;AACzB,WAAK,iBAAiB;AACtB,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,QAAQ;AACN,SAAK,WAAW;AAChB,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,mBAAmB;AACxB,SAAK,mBAAmB;AACxB,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AACrB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,WAAW;AACT,QAAI,KAAK,GAAG,eAAe;AACzB,UAAI,SAAS,KAAK,GAAG,cAAc;AACnC,aAAO,UAAU,CAAC,SAAS,QAAQ,YAAY,GAAG;AAChD,iBAAS,OAAO;AAAA,MAClB;AACA,aAAO,WAAW;AAAA,IACpB,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,aAAa;AACX,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA,EACA,aAAa;AACX,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,cAAQ,KAAK,cAAc;AAAA,QACzB,KAAK;AACH,iBAAO,KAAK,SAAS,YAAY;AAAA,QACnC,KAAK;AACH,iBAAO,KAAK,SAAS,YAAY;AAAA,QACnC;AACE,gBAAM,IAAI,MAAM,KAAK,eAAe,0FAA0F;AAAA,MAClI;AAAA,IACF,OAAO;AACL,YAAM,IAAI,MAAM,sDAAsD;AAAA,IACxE;AAAA,EACF;AAAA,EACA,YAAY;AACV,SAAK,WAAW,EAAE,QAAQ,KAAK,UAAU,KAAK,UAAU,KAAK,WAAW,CAAC;AAAA,EAC3E;AAAA,EACA,eAAe;AACb,UAAM,UAAU,KAAK,WAAW;AAChC,UAAM,cAAc,QAAQ,QAAQ,KAAK,QAAQ;AACjD,QAAI,aAAa;AACf,WAAK,cAAc,KAAK,MAAM,WAAW;AACzC,UAAI,WAAW,CAAC,GAAG,KAAK,mBAAmB,cAAc,QAAQ,EAAE,OAAO,WAAS,SAAS,OAAO,iBAAiB,CAAC;AACrH,eAAS,QAAQ,CAAC,OAAO,MAAM;AAC7B,cAAM,MAAM,YAAY,UAAU,KAAK,YAAY,CAAC,IAAI,UAAU,KAAK,OAAO,SAAS,KAAK,KAAK,aAAa;AAAA,MAChH,CAAC;AACD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AACf,WAAO;AAAA,MACL,0BAA0B;AAAA,MAC1B,yBAAyB,KAAK,WAAW;AAAA,MACzC,uBAAuB,KAAK,WAAW;AAAA,IACzC;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,WAAO;AAAA,MACL,mBAAmB;AAAA,MACnB,0BAA0B;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,WAAW,EAAG,QAAO;AAAA,MAC5B,OAAO,KAAK,aAAa;AAAA,IAC3B;AAAA,QAAO,QAAO;AAAA,MACZ,QAAQ,KAAK,aAAa;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,aAAa;AACX,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,iBAAiB,mBAAmB;AAClD,cAAQ,0BAA0B,wBAA2B,sBAAsB,SAAQ,IAAI,qBAAqB,SAAQ;AAAA,IAC9H;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,IAC1B,gBAAgB,SAAS,wBAAwB,IAAI,KAAK,UAAU;AAClE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,eAAe,CAAC;AAC5C,QAAG,eAAe,UAAU,KAAK,CAAC;AAAA,MACpC;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAC7D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB;AAAA,MACnE;AAAA,IACF;AAAA,IACA,WAAW,SAAS,eAAe,IAAI,KAAK;AAC1C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB,GAAG;AAAA,MAC3E;AAAA,IACF;AAAA,IACA,UAAU;AAAA,IACV,cAAc,SAAS,sBAAsB,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,0BAA0B,IAAI,MAAM;AAAA,MACrD;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,YAAY,CAAC,GAAG,cAAc,cAAc,eAAe;AAAA,MAC3D,MAAM,CAAC,GAAG,QAAQ,QAAQ,eAAe;AAAA,MACzC,UAAU;AAAA,MACV,YAAY;AAAA,IACd;AAAA,IACA,SAAS;AAAA,MACP,aAAa;AAAA,MACb,eAAe;AAAA,IACjB;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,aAAa,CAAC,GAAM,0BAA6B,0BAA0B;AAAA,IAC7G,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,aAAa,EAAE,GAAG,CAAC,GAAG,WAAW,SAAS,GAAG,CAAC,SAAS,IAAI,GAAG,SAAS,GAAG,CAAC,YAAY,MAAM,GAAG,WAAW,SAAS,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,SAAS,qBAAqB,QAAQ,aAAa,YAAY,MAAM,GAAG,aAAa,cAAc,aAAa,YAAY,GAAG,MAAM,GAAG,CAAC,QAAQ,aAAa,YAAY,MAAM,GAAG,qBAAqB,GAAG,aAAa,cAAc,aAAa,UAAU,GAAG,CAAC,YAAY,KAAK,GAAG,4BAA4B,GAAG,SAAS,WAAW,SAAS,CAAC;AAAA,IAC7e,UAAU,SAAS,kBAAkB,IAAI,KAAK;AAC5C,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,WAAW,GAAG,iCAAiC,GAAG,GAAG,eAAe,CAAC;AACxE,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAc,gBAAgB,IAAI,KAAK,IAAI,WAAW,aAAa,WAAW,EAAE,CAAC;AACpF,QAAG,WAAW,IAAI,UAAU;AAC5B,QAAG,WAAW,WAAW,IAAI,eAAe,CAAC,EAAE,WAAW,IAAI,KAAK;AACnE,QAAG,YAAY,gBAAgB,UAAU,EAAE,0BAA0B,KAAK,EAAE,mBAAmB,MAAM;AACrG,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,WAAW,IAAI,MAAM;AAAA,MACrC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAiB,SAAY,SAAY,MAAS,kBAAqB,SAAS,YAAY;AAAA,IAC3G,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,YAAY;AAAA,MACpC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAyCV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,kCAAkC;AAAA,MACpC;AAAA,MACA,WAAW,CAAC,aAAa;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,QACd,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,UAAU,eAAe,YAAY;AAAA,IAC/C,SAAS,CAAC,UAAU,eAAe,YAAY;AAAA,EACjD,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,UAAU,eAAe,cAAc,YAAY;AAAA,EAC/D,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,UAAU,eAAe,YAAY;AAAA,MAC/C,SAAS,CAAC,UAAU,eAAe,YAAY;AAAA,IACjD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["SplitterClasses"]}
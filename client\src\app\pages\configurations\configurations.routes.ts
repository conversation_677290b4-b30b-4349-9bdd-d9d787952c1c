import { Routes } from '@angular/router';

export default [
    {
        path: '',
        loadComponent: () => import('./components/configurations-list/configurations-list.component').then(m => m.ConfigurationsListComponent)
    },
    {
        path: 'new',
        loadComponent: () => import('./components/configuration-form/configuration-form.component').then(m => m.ConfigurationFormComponent)
    },
    {
        path: ':id',
        loadComponent: () => import('./components/configuration-detail/configuration-detail.component').then(m => m.ConfigurationDetailComponent)
    },
    {
        path: ':id/edit',
        loadComponent: () => import('./components/configuration-form/configuration-form.component').then(m => m.ConfigurationFormComponent)
    }
] as Routes;

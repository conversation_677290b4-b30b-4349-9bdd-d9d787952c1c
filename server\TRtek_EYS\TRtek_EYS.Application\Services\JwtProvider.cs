﻿using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using TRtek_EYS.Application.Common.Interfaces;
using TRtek_EYS.Application.Features.Auth.Login;
using TRtek_EYS.Domain.Entities;

namespace TRtek_EYS.Infrastructure.Services
{
    public class JwtProvider : IJwtProvider
    {
        private readonly IConfiguration _configuration;

        public JwtProvider(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public Task<LoginCommandResponse> CreateToken(User user)
        {
            var jwtSection = _configuration.GetSection("JwtSettings");
            var key = Encoding.UTF8.GetBytes(jwtSection["Key"]!);
            var issuer = jwtSection["Issuer"];
            var audience = jwtSection["Audience"];
            var expiresInMinutes = int.Parse(jwtSection["AccessTokenExpiresInMinutes"] ?? "15");
            var accessTokenExpires = DateTime.UtcNow.AddMinutes(expiresInMinutes);

            var role = user.UserRoles.FirstOrDefault()?.Role?.Name ?? "User";

            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Name, user.Username),
                new Claim(ClaimTypes.Role, role),
                new Claim("FullName", user.FullName ?? string.Empty)
            };

            var signingCredentials = new SigningCredentials(
                new SymmetricSecurityKey(key),
                SecurityAlgorithms.HmacSha256);

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = accessTokenExpires,
                Issuer = issuer,
                Audience = audience,
                SigningCredentials = signingCredentials
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            var token = tokenHandler.CreateToken(tokenDescriptor);
            var accessToken = tokenHandler.WriteToken(token);

            var refreshToken = GenerateRefreshToken();
            var refreshTokenExpires = DateTime.UtcNow.AddDays(int.Parse(jwtSection["RefreshTokenExpiresInDays"] ?? "7"));

            return Task.FromResult(new LoginCommandResponse
            {
                UserId = user.Id,
                Username = user.Username,
                FullName = user.FullName,
                Token = accessToken,
                RefreshToken = refreshToken,
                RefreshTokenExpires = refreshTokenExpires
            });
        }

        public string GenerateRefreshToken()
        {
            var randomNumber = new byte[64];
            using var rng = RandomNumberGenerator.Create();
            rng.GetBytes(randomNumber);
            return Convert.ToBase64String(randomNumber);
        }
    }
}

import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { Textarea } from 'primeng/inputtextarea';
import { Select } from 'primeng/select';
import { MultiSelect } from 'primeng/multiselect';
import { DatePicker } from 'primeng/datepicker';
import { InputNumber } from 'primeng/inputnumber';
import { ToastModule } from 'primeng/toast';
import { MessageService } from 'primeng/api';
import { ProjectStatus, Platform, Priority } from '../../models/project.model';

@Component({
    selector: 'app-project-form',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        ButtonModule,
        InputTextModule,
        Textarea,
        Select,
        MultiSelect,
        DatePicker,
        InputNumber,
        ToastModule
    ],
    providers: [MessageService],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <h5>{{ isEditMode ? 'Proje Düzenle' : 'Yeni Proje' }}</h5>
                        <p-button 
                            label="Geri" 
                            icon="pi pi-arrow-left" 
                            class="p-button-secondary"
                            (onClick)="goBack()">
                        </p-button>
                    </div>

                    <form [formGroup]="projectForm" (ngSubmit)="onSubmit()">
                        <div class="grid">
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="projectCode" class="font-medium">Proje Kodu *</label>
                                    <input 
                                        id="projectCode"
                                        type="text" 
                                        pInputText 
                                        formControlName="projectCode"
                                        [readonly]="isEditMode"
                                        class="w-full"
                                        placeholder="Otomatik oluşturulacak" />
                                    <small class="text-500">Benzersiz proje kodu otomatik oluşturulur</small>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="name" class="font-medium">Proje Adı *</label>
                                    <input 
                                        id="name"
                                        type="text" 
                                        pInputText 
                                        formControlName="name"
                                        class="w-full"
                                        placeholder="Proje adını girin" />
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field">
                                    <label for="description" class="font-medium">Açıklama *</label>
                                    <p-textarea
                                        id="description"
                                        formControlName="description"
                                        rows="3"
                                        class="w-full"
                                        placeholder="Proje açıklamasını girin">
                                    </p-textarea>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="status" class="font-medium">Durum *</label>
                                    <p-select
                                        id="status"
                                        formControlName="status"
                                        [options]="statusOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Durum seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="priority" class="font-medium">Öncelik *</label>
                                    <p-select
                                        id="priority"
                                        formControlName="priority"
                                        [options]="priorityOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Öncelik seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="platforms" class="font-medium">Platformlar *</label>
                                    <p-multiselect
                                        id="platforms"
                                        formControlName="platforms"
                                        [options]="platformOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Platform seçin"
                                        class="w-full">
                                    </p-multiselect>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="currentVersion" class="font-medium">Mevcut Versiyon</label>
                                    <input 
                                        id="currentVersion"
                                        type="text" 
                                        pInputText 
                                        formControlName="currentVersion"
                                        class="w-full"
                                        placeholder="v1.0.0" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="projectManager" class="font-medium">Proje Yöneticisi</label>
                                    <input 
                                        id="projectManager"
                                        type="text" 
                                        pInputText 
                                        formControlName="projectManager"
                                        class="w-full"
                                        placeholder="Proje yöneticisi adı" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="repositoryUrl" class="font-medium">Repository URL</label>
                                    <input 
                                        id="repositoryUrl"
                                        type="url" 
                                        pInputText 
                                        formControlName="repositoryUrl"
                                        class="w-full"
                                        placeholder="https://github.com/..." />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="startDate" class="font-medium">Başlangıç Tarihi</label>
                                    <p-datepicker
                                        id="startDate"
                                        formControlName="startDate"
                                        dateFormat="dd/mm/yy"
                                        placeholder="Tarih seçin"
                                        class="w-full">
                                    </p-datepicker>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="endDate" class="font-medium">Bitiş Tarihi</label>
                                    <p-datepicker
                                        id="endDate"
                                        formControlName="endDate"
                                        dateFormat="dd/mm/yy"
                                        placeholder="Tarih seçin"
                                        class="w-full">
                                    </p-datepicker>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="estimatedHours" class="font-medium">Tahmini Saat</label>
                                    <p-inputnumber
                                        id="estimatedHours"
                                        formControlName="estimatedHours"
                                        mode="decimal"
                                        [min]="0"
                                        placeholder="0"
                                        class="w-full">
                                    </p-inputnumber>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="actualHours" class="font-medium">Gerçekleşen Saat</label>
                                    <p-inputnumber
                                        id="actualHours"
                                        formControlName="actualHours"
                                        mode="decimal"
                                        [min]="0"
                                        placeholder="0"
                                        class="w-full">
                                    </p-inputnumber>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field">
                                    <label for="notes" class="font-medium">Notlar</label>
                                    <p-textarea
                                        id="notes"
                                        formControlName="notes"
                                        rows="3"
                                        class="w-full"
                                        placeholder="Ek notlar...">
                                    </p-textarea>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-content-end gap-2 mt-4">
                            <p-button 
                                label="İptal" 
                                icon="pi pi-times" 
                                class="p-button-secondary"
                                type="button"
                                (onClick)="goBack()">
                            </p-button>
                            <p-button 
                                icon="pi pi-check" 
                                class="p-button-secondary"
                                type="submit"
                                [disabled]="projectForm.invalid">
                            </p-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <p-toast></p-toast>
    `
})
export class ProjectFormComponent implements OnInit {
    projectForm!: FormGroup;
    isEditMode = false;
    projectId: string = '';

    statusOptions = [
        { label: 'Planlama', value: ProjectStatus.PLANNING },
        { label: 'Geliştirme', value: ProjectStatus.DEVELOPMENT },
        { label: 'Test', value: ProjectStatus.TESTING },
        { label: 'Dağıtım', value: ProjectStatus.DEPLOYMENT },
        { label: 'Bakım', value: ProjectStatus.MAINTENANCE },
        { label: 'Tamamlandı', value: ProjectStatus.COMPLETED },
        { label: 'Beklemede', value: ProjectStatus.ON_HOLD }
    ];

    priorityOptions = [
        { label: 'Düşük', value: Priority.LOW },
        { label: 'Orta', value: Priority.MEDIUM },
        { label: 'Yüksek', value: Priority.HIGH },
        { label: 'Kritik', value: Priority.CRITICAL }
    ];

    platformOptions = [
        { label: 'Web', value: Platform.WEB },
        { label: 'Mobile', value: Platform.MOBILE },
        { label: 'Desktop', value: Platform.DESKTOP },
        { label: 'API', value: Platform.API }
    ];

    constructor(
        private fb: FormBuilder,
        private route: ActivatedRoute,
        private router: Router,
        private messageService: MessageService
    ) {}

    ngOnInit() {
        this.initForm();
        this.checkEditMode();
    }

    initForm() {
        this.projectForm = this.fb.group({
            projectCode: ['', Validators.required],
            name: ['', Validators.required],
            description: ['', Validators.required],
            status: [ProjectStatus.PLANNING, Validators.required],
            priority: [Priority.MEDIUM, Validators.required],
            platforms: [[], Validators.required],
            currentVersion: ['v1.0.0'],
            projectManager: [''],
            repositoryUrl: [''],
            startDate: [null],
            endDate: [null],
            estimatedHours: [null],
            actualHours: [null],
            notes: ['']
        });

        if (!this.isEditMode) {
            this.generateProjectCode();
        }
    }

    checkEditMode() {
        this.projectId = this.route.snapshot.params['id'];
        this.isEditMode = !!this.projectId && this.router.url.includes('edit');
        
        if (this.isEditMode) {
            this.loadProject();
        }
    }

    generateProjectCode() {
        const year = new Date().getFullYear();
        const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        const projectCode = `PRJ-${year}-${randomNum}`;
        this.projectForm.patchValue({ projectCode });
    }

    loadProject() {
        const dummyProject = {
            projectCode: 'PRJ-2024-001',
            name: 'E-Ticaret Platformu',
            description: 'Kapsamlı e-ticaret çözümü',
            status: ProjectStatus.DEVELOPMENT,
            priority: Priority.HIGH,
            platforms: [Platform.WEB, Platform.MOBILE],
            currentVersion: 'v2.1.0',
            projectManager: 'Mehmet Özkan',
            repositoryUrl: 'https://github.com/company/ecommerce-platform',
            startDate: new Date('2024-01-01'),
            endDate: null,
            estimatedHours: 1000,
            actualHours: 750,
            notes: 'Proje başarıyla ilerliyor'
        };

        this.projectForm.patchValue(dummyProject);
    }

    onSubmit() {
        if (this.projectForm.valid) {
            this.messageService.add({
                severity: 'success',
                summary: 'Başarılı',
                detail: this.isEditMode ? 'Proje başarıyla güncellendi' : 'Proje başarıyla oluşturuldu'
            });

            setTimeout(() => {
                this.router.navigate(['/pages/projects']);
            }, 1500);
        }
    }

    goBack() {
        this.router.navigate(['/pages/projects']);
    }
}

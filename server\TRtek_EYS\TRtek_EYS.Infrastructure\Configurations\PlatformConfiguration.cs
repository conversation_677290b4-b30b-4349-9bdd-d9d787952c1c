﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TRtek_EYS.Domain.Entities;

namespace TRtek_EYS.Infrastructure.Configurations
{
    public class PlatformConfiguration : IEntityTypeConfiguration<Platform>
    {
        public void Configure(EntityTypeBuilder<Platform> builder)
        {
            builder.ToTable("Platforms");

            builder.HasKey(p => p.Id);

            builder.Property(p => p.Type)
                   .IsRequired();

            // YENİ ALANLAR İÇİN CONFIGURATION
            builder.Property(p => p.Name)
                   .HasMaxLength(200);

            builder.Property(p => p.PlatformCode)
                   .HasMaxLength(50);

            builder.Property(p => p.Description)
                   .HasMaxLength(1000);

            builder.Property(p => p.Version)
                   .HasMaxLength(50);

            builder.Property(p => p.Vendor)
                   .HasMaxLength(200);

            builder.Property(p => p.Website)
                   .HasMaxLength(500);

            builder.Property(p => p.Documentation)
                   .HasMaxLength(500);

            builder.Property(p => p.Currency)
                   .HasMaxLength(10);

            builder.Property(p => p.Tags)
                   .HasMaxLength(2000);

            builder.Property(p => p.Notes)
                   .HasMaxLength(2000);

            builder.Property(p => p.Cost)
                   .HasPrecision(10, 2);

            builder.Property(p => p.CreatedAt)
                   .IsRequired();

            builder.HasOne(p => p.Project)
                   .WithMany()
                   .HasForeignKey(p => p.ProjectId)
                   .OnDelete(DeleteBehavior.Cascade);
        }
    }
}

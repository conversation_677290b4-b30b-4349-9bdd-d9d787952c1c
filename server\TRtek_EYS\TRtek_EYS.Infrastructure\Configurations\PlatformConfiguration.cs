﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TRtek_EYS.Domain.Entities;

namespace TRtek_EYS.Infrastructure.Configurations
{
    public class PlatformConfiguration : IEntityTypeConfiguration<Platform>
    {
        public void Configure(EntityTypeBuilder<Platform> builder)
        {
            builder.ToTable("Platforms");

            builder.HasKey(p => p.Id);

            builder.Property(p => p.Type)
                   .IsRequired(); // Enum: PlatformType

            builder.Property(p => p.CreatedAt)
                   .IsRequired();

            // İlişki: her platform bir projeye aittir
            builder.HasOne(p => p.Project)
                   .WithMany()
                   .HasForeignKey(p => p.ProjectId)
                   .OnDelete(DeleteBehavior.Cascade);
        }
    }
}

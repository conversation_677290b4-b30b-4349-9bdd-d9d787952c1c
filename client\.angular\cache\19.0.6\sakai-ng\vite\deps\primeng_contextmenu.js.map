{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-contextmenu.mjs"], "sourcesContent": ["import { trigger, transition, style, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule, isPlatformBrowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, forwardRef, numberAttribute, booleanAttribute, ViewChild, Output, Input, Inject, ViewEncapsulation, Component, signal, inject, effect, ContentChildren, ContentChild, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport { resolve, isNotEmpty, getOffset, getViewport, getHiddenElementOuterWidth, getOuterWidth, calculateScrollbarWidth, uuid, isIOS, isAndroid, focus, isPrintableCharacter, isEmpty, findSingle, appendChild, getHiddenElementOuterHeight, findLastIndex, removeChild } from '@primeuix/utils';\nimport * as i5 from 'primeng/api';\nimport { SharedModule, PrimeTemplate } from 'primeng/api';\nimport * as i4 from 'primeng/badge';\nimport { BadgeModule } from 'primeng/badge';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { AngleRightIcon } from 'primeng/icons';\nimport { Ripple } from 'primeng/ripple';\nimport * as i3 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ZIndexUtils } from 'primeng/utils';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"sublist\"];\nconst _c1 = (a0, a1) => ({\n  \"p-contextmenu-submenu\": a0,\n  \"p-contextmenu-root-list\": a1\n});\nconst _c2 = () => ({\n  \"p-contextmenu-item-link\": true\n});\nconst _c3 = () => ({\n  class: \"p-contextmenu-submenu-icon\"\n});\nconst _c4 = () => ({\n  exact: false\n});\nconst _c5 = a0 => ({\n  \"p-contextmenu-item-link\": true,\n  \"p-disabled\": a0\n});\nconst _c6 = a0 => ({\n  $implicit: a0\n});\nfunction ContextMenuSub_ul_0_ng_template_2_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 8);\n  }\n  if (rf & 2) {\n    const processedItem_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(ctx_r2.getItemProp(processedItem_r4, \"style\"));\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getSeparatorItemClass(processedItem_r4));\n    i0.ɵɵattribute(\"id\", ctx_r2.getItemId(processedItem_r4))(\"data-pc-section\", \"separator\");\n  }\n}\nfunction ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 19);\n  }\n  if (rf & 2) {\n    const processedItem_r4 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getItemProp(processedItem_r4, \"icon\"))(\"ngStyle\", ctx_r2.getItemProp(processedItem_r4, \"iconStyle\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\")(\"aria-hidden\", true)(\"tabindex\", -1);\n  }\n}\nfunction ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r4 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getItemLabel(processedItem_r4), \" \");\n  }\n}\nfunction ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 21);\n  }\n  if (rf & 2) {\n    const processedItem_r4 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.getItemLabel(processedItem_r4), i0.ɵɵsanitizeHtml);\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n  }\n}\nfunction ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_1_p_badge_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-badge\", 22);\n  }\n  if (rf & 2) {\n    const processedItem_r4 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"styleClass\", ctx_r2.getItemProp(processedItem_r4, \"badgeStyleClass\"))(\"value\", ctx_r2.getItemProp(processedItem_r4, \"badge\"));\n  }\n}\nfunction ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_AngleRightIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleRightIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵclassMap(\"p-contextmenu-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_ng_template_0_Template(rf, ctx) {}\nfunction ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_ng_template_0_Template, 0, 0, \"ng-template\", 25);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_AngleRightIcon_1_Template, 1, 4, \"AngleRightIcon\", 23)(2, ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_Template, 1, 2, null, 24);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.contextMenu.submenuIconTemplate && !ctx_r2.contextMenu._submenuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.contextMenu.submenuIconTemplate || ctx_r2.contextMenu._submenuIconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction0(3, _c3));\n  }\n}\nfunction ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 15);\n    i0.ɵɵtemplate(1, ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_1_span_1_Template, 1, 5, \"span\", 16)(2, ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_1_span_2_Template, 2, 2, \"span\", 17)(3, ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_1_ng_template_3_Template, 1, 2, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(5, ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_1_p_badge_5_Template, 1, 2, \"p-badge\", 18)(6, ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_Template, 3, 4, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlLabel_r6 = i0.ɵɵreference(4);\n    const processedItem_r4 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"target\", ctx_r2.getItemProp(processedItem_r4, \"target\"))(\"ngClass\", i0.ɵɵpureFunction0(12, _c2));\n    i0.ɵɵattribute(\"href\", ctx_r2.getItemProp(processedItem_r4, \"url\"), i0.ɵɵsanitizeUrl)(\"aria-hidden\", true)(\"data-automationid\", ctx_r2.getItemProp(processedItem_r4, \"automationId\"))(\"data-pc-section\", \"action\")(\"tabindex\", -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r4, \"icon\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r4, \"escape\"))(\"ngIfElse\", htmlLabel_r6);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r4, \"badge\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemGroup(processedItem_r4));\n  }\n}\nfunction ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 19);\n  }\n  if (rf & 2) {\n    const processedItem_r4 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getItemProp(processedItem_r4, \"icon\"))(\"ngStyle\", ctx_r2.getItemProp(processedItem_r4, \"iconStyle\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\")(\"aria-hidden\", true)(\"tabindex\", -1);\n  }\n}\nfunction ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r4 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getItemLabel(processedItem_r4), \" \");\n  }\n}\nfunction ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 21);\n  }\n  if (rf & 2) {\n    const processedItem_r4 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.getItemLabel(processedItem_r4), i0.ɵɵsanitizeHtml);\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n  }\n}\nfunction ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_2_p_badge_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-badge\", 22);\n  }\n  if (rf & 2) {\n    const processedItem_r4 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"styleClass\", ctx_r2.getItemProp(processedItem_r4, \"badgeStyleClass\"))(\"value\", ctx_r2.getItemProp(processedItem_r4, \"badge\"));\n  }\n}\nfunction ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_AngleRightIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleRightIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵclassMap(\"p-contextmenu-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_ng_template_0_Template(rf, ctx) {}\nfunction ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_ng_template_0_Template, 0, 0, \"ng-template\", 25);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_AngleRightIcon_1_Template, 1, 4, \"AngleRightIcon\", 23)(2, ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_Template, 1, 2, null, 24);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.contextMenu.submenuIconTemplate && !ctx_r2.contextMenu._submenuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", !ctx_r2.contextMenu.submenuIconTemplate || !ctx_r2.contextMenu._submenuIconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction0(3, _c3));\n  }\n}\nfunction ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 26);\n    i0.ɵɵtemplate(1, ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_2_span_1_Template, 1, 5, \"span\", 16)(2, ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_2_span_2_Template, 2, 2, \"span\", 17)(3, ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_2_ng_template_3_Template, 1, 2, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(5, ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_2_p_badge_5_Template, 1, 2, \"p-badge\", 18)(6, ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_Template, 3, 4, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlLabel_r7 = i0.ɵɵreference(4);\n    const processedItem_r4 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"routerLink\", ctx_r2.getItemProp(processedItem_r4, \"routerLink\"))(\"queryParams\", ctx_r2.getItemProp(processedItem_r4, \"queryParams\"))(\"routerLinkActiveOptions\", ctx_r2.getItemProp(processedItem_r4, \"routerLinkActiveOptions\") || i0.ɵɵpureFunction0(20, _c4))(\"target\", ctx_r2.getItemProp(processedItem_r4, \"target\"))(\"ngClass\", i0.ɵɵpureFunction1(21, _c5, ctx_r2.getItemProp(processedItem_r4, \"disabled\")))(\"fragment\", ctx_r2.getItemProp(processedItem_r4, \"fragment\"))(\"queryParamsHandling\", ctx_r2.getItemProp(processedItem_r4, \"queryParamsHandling\"))(\"preserveFragment\", ctx_r2.getItemProp(processedItem_r4, \"preserveFragment\"))(\"skipLocationChange\", ctx_r2.getItemProp(processedItem_r4, \"skipLocationChange\"))(\"replaceUrl\", ctx_r2.getItemProp(processedItem_r4, \"replaceUrl\"))(\"state\", ctx_r2.getItemProp(processedItem_r4, \"state\"));\n    i0.ɵɵattribute(\"data-automationid\", ctx_r2.getItemProp(processedItem_r4, \"automationId\"))(\"tabindex\", -1)(\"aria-hidden\", true)(\"data-pc-section\", \"action\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r4, \"icon\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r4, \"escape\"))(\"ngIfElse\", htmlLabel_r7);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r4, \"badge\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemGroup(processedItem_r4));\n  }\n}\nfunction ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_1_Template, 7, 13, \"a\", 13)(2, ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_a_2_Template, 7, 23, \"a\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r4 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.getItemProp(processedItem_r4, \"routerLink\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r4, \"routerLink\"));\n  }\n}\nfunction ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_4_1_ng_template_0_Template(rf, ctx) {}\nfunction ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_4_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_4_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_4_1_Template, 1, 0, null, 24);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r4 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c6, processedItem_r4.item));\n  }\n}\nfunction ContextMenuSub_ul_0_ng_template_2_li_1_p_contextmenu_sub_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-contextmenu-sub\", 27);\n    i0.ɵɵlistener(\"itemClick\", function ContextMenuSub_ul_0_ng_template_2_li_1_p_contextmenu_sub_5_Template_p_contextmenu_sub_itemClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.itemClick.emit($event));\n    })(\"itemMouseEnter\", function ContextMenuSub_ul_0_ng_template_2_li_1_p_contextmenu_sub_5_Template_p_contextmenu_sub_itemMouseEnter_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onItemMouseEnter($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r4 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"items\", processedItem_r4.items)(\"itemTemplate\", ctx_r2.itemTemplate)(\"menuId\", ctx_r2.menuId)(\"visible\", ctx_r2.isItemActive(processedItem_r4) && ctx_r2.isItemGroup(processedItem_r4))(\"activeItemPath\", ctx_r2.activeItemPath)(\"focusedItemId\", ctx_r2.focusedItemId)(\"level\", ctx_r2.level + 1);\n  }\n}\nfunction ContextMenuSub_ul_0_ng_template_2_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 9, 1)(2, \"div\", 10);\n    i0.ɵɵlistener(\"click\", function ContextMenuSub_ul_0_ng_template_2_li_1_Template_div_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const processedItem_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onItemClick($event, processedItem_r4));\n    })(\"mouseenter\", function ContextMenuSub_ul_0_ng_template_2_li_1_Template_div_mouseenter_2_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const processedItem_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onItemMouseEnter({\n        $event: $event,\n        processedItem: processedItem_r4\n      }));\n    });\n    i0.ɵɵtemplate(3, ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_3_Template, 3, 2, \"ng-container\", 11)(4, ContextMenuSub_ul_0_ng_template_2_li_1_ng_container_4_Template, 2, 4, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, ContextMenuSub_ul_0_ng_template_2_li_1_p_contextmenu_sub_5_Template, 1, 7, \"p-contextmenu-sub\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    const processedItem_r4 = ctx_r8.$implicit;\n    const index_r10 = ctx_r8.index;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r2.getItemProp(processedItem_r4, \"styleClass\"));\n    i0.ɵɵproperty(\"ngStyle\", ctx_r2.getItemProp(processedItem_r4, \"style\"))(\"ngClass\", ctx_r2.getItemClass(processedItem_r4))(\"tooltipOptions\", ctx_r2.getItemProp(processedItem_r4, \"tooltipOptions\"));\n    i0.ɵɵattribute(\"id\", ctx_r2.getItemId(processedItem_r4))(\"data-pc-section\", \"menuitem\")(\"data-p-highlight\", ctx_r2.isItemActive(processedItem_r4))(\"data-p-focused\", ctx_r2.isItemFocused(processedItem_r4))(\"data-p-disabled\", ctx_r2.isItemDisabled(processedItem_r4))(\"aria-label\", ctx_r2.getItemLabel(processedItem_r4))(\"aria-disabled\", ctx_r2.isItemDisabled(processedItem_r4) || undefined)(\"aria-haspopup\", ctx_r2.isItemGroup(processedItem_r4) && !ctx_r2.getItemProp(processedItem_r4, \"to\") ? \"menu\" : undefined)(\"aria-expanded\", ctx_r2.isItemGroup(processedItem_r4) ? ctx_r2.isItemActive(processedItem_r4) : undefined)(\"aria-level\", ctx_r2.level + 1)(\"aria-setsize\", ctx_r2.getAriaSetSize())(\"aria-posinset\", ctx_r2.getAriaPosInset(index_r10));\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"content\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemVisible(processedItem_r4) && ctx_r2.isItemGroup(processedItem_r4));\n  }\n}\nfunction ContextMenuSub_ul_0_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ContextMenuSub_ul_0_ng_template_2_li_0_Template, 1, 5, \"li\", 6)(1, ContextMenuSub_ul_0_ng_template_2_li_1_Template, 6, 21, \"li\", 7);\n  }\n  if (rf & 2) {\n    const processedItem_r4 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemVisible(processedItem_r4) && ctx_r2.getItemProp(processedItem_r4, \"separator\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemVisible(processedItem_r4) && !ctx_r2.getItemProp(processedItem_r4, \"separator\"));\n  }\n}\nfunction ContextMenuSub_ul_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ul\", 4, 0);\n    i0.ɵɵlistener(\"@overlayAnimation.start\", function ContextMenuSub_ul_0_Template_ul_animation_overlayAnimation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const sublist_r2 = i0.ɵɵreference(1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onEnter($event, sublist_r2));\n    })(\"keydown\", function ContextMenuSub_ul_0_Template_ul_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.menuKeydown.emit($event));\n    })(\"focus\", function ContextMenuSub_ul_0_Template_ul_focus_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.menuFocus.emit($event));\n    })(\"blur\", function ContextMenuSub_ul_0_Template_ul_blur_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.menuBlur.emit($event));\n    });\n    i0.ɵɵtemplate(2, ContextMenuSub_ul_0_ng_template_2_Template, 2, 2, \"ng-template\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(10, _c1, !ctx_r2.root, ctx_r2.root))(\"@overlayAnimation\", ctx_r2.visible)(\"tabindex\", ctx_r2.tabindex);\n    i0.ɵɵattribute(\"id\", ctx_r2.menuId + \"_list\")(\"aria-label\", ctx_r2.ariaLabel)(\"aria-labelledBy\", ctx_r2.ariaLabelledBy)(\"aria-activedescendant\", ctx_r2.focusedItemId)(\"aria-orientation\", \"vertical\")(\"data-pc-section\", \"menu\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.items);\n  }\n}\nconst _c7 = [\"item\"];\nconst _c8 = [\"submenuicon\"];\nconst _c9 = [\"rootmenu\"];\nconst _c10 = [\"container\"];\nconst _c11 = a0 => ({\n  \"p-contextmenu p-component\": true,\n  \"p-contextmenu-mobile\": a0\n});\nconst _c12 = () => ({\n  value: \"visible\"\n});\nfunction ContextMenu_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3, 0);\n    i0.ɵɵlistener(\"@overlayAnimation.start\", function ContextMenu_div_0_Template_div_animation_overlayAnimation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onOverlayAnimationStart($event));\n    })(\"@overlayAnimation.done\", function ContextMenu_div_0_Template_div_animation_overlayAnimation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onOverlayAnimationEnd($event));\n    });\n    i0.ɵɵelementStart(2, \"p-contextmenu-sub\", 4, 1);\n    i0.ɵɵlistener(\"itemClick\", function ContextMenu_div_0_Template_p_contextmenu_sub_itemClick_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onItemClick($event));\n    })(\"menuFocus\", function ContextMenu_div_0_Template_p_contextmenu_sub_menuFocus_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onMenuFocus($event));\n    })(\"menuBlur\", function ContextMenu_div_0_Template_p_contextmenu_sub_menuBlur_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onMenuBlur($event));\n    })(\"menuKeydown\", function ContextMenu_div_0_Template_p_contextmenu_sub_menuKeydown_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onKeyDown($event));\n    })(\"itemMouseEnter\", function ContextMenu_div_0_Template_p_contextmenu_sub_itemMouseEnter_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onItemMouseEnter($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.styleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(20, _c11, ctx_r1.queryMatches))(\"ngStyle\", ctx_r1.style)(\"@overlayAnimation\", i0.ɵɵpureFunction0(22, _c12));\n    i0.ɵɵattribute(\"data-pc-section\", \"root\")(\"data-pc-name\", \"contextmenu\")(\"id\", ctx_r1.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"root\", true)(\"items\", ctx_r1.processedItems)(\"itemTemplate\", ctx_r1.itemTemplate || ctx_r1._itemTemplate)(\"menuId\", ctx_r1.id)(\"tabindex\", !ctx_r1.disabled ? ctx_r1.tabindex : -1)(\"ariaLabel\", ctx_r1.ariaLabel)(\"ariaLabelledBy\", ctx_r1.ariaLabelledBy)(\"baseZIndex\", ctx_r1.baseZIndex)(\"autoZIndex\", ctx_r1.autoZIndex)(\"visible\", ctx_r1.submenuVisible())(\"focusedItemId\", ctx_r1.focused ? ctx_r1.focusedItemId : undefined)(\"activeItemPath\", ctx_r1.activeItemPath());\n  }\n}\nconst theme = ({\n  dt\n}) => `\n.p-contextmenu {\n    position: absolute;\n    background: ${dt('contextmenu.background')};\n    color: ${dt('contextmenu.color')};\n    border: 1px solid ${dt('contextmenu.border.color')};\n    border-radius: ${dt('contextmenu.border.radius')};\n    box-shadow: ${dt('contextmenu.shadow')};\n    min-width: 12.5rem;\n}\n\n.p-contextmenu-root-list,\n.p-contextmenu-submenu {\n    margin: 0;\n    padding: ${dt('contextmenu.list.padding')};\n    list-style: none;\n    outline: 0 none;\n    display: flex;\n    flex-direction: column;\n    gap: ${dt('contextmenu.list.gap')};\n}\n\n.p-contextmenu-submenu {\n    position: absolute;\n    display: flex;\n    flex-direction: column;\n    min-width: 100%;\n    z-index: 1;\n    background: ${dt('contextmenu.background')};\n    color: ${dt('contextmenu.color')};\n    border: 1px solid ${dt('contextmenu.border.color')};\n    border-radius: ${dt('contextmenu.border.radius')};\n    box-shadow: ${dt('contextmenu.shadow')};\n}\n\n.p-contextmenu-item {\n    position: relative;\n}\n\n.p-contextmenu-item-content {\n    transition: background ${dt('contextmenu.transition.duration')}, color ${dt('contextmenu.transition.duration')};\n    border-radius: ${dt('contextmenu.item.border.radius')};\n    color: ${dt('contextmenu.item.color')};\n}\n\n.p-contextmenu-item-link {\n    cursor: pointer;\n    display: flex;\n    align-items: center;\n    text-decoration: none;\n    overflow: hidden;\n    position: relative;\n    color: inherit;\n    padding: ${dt('contextmenu.item.padding')};\n    gap: ${dt('contextmenu.item.gap')};\n    user-select: none;\n}\n\n.p-contextmenu-item-label {\n    line-height: 1;\n}\n\n.p-contextmenu-item-icon {\n    color: ${dt('contextmenu.item.icon.color')};\n}\n\n.p-contextmenu-submenu-icon {\n    color: ${dt('contextmenu.submenu.icon.color')};\n    margin-left: auto;\n    font-size: ${dt('contextmenu.submenu.icon.size')};\n    width: ${dt('contextmenu.submenu.icon.size')};\n    height: ${dt('contextmenu.submenu.icon.size')};\n}\n\n.p-contextmenu-submenu-icon:dir(rtl) {\n    margin-left: 0;\n    margin-right: auto;\n}\n\n.p-contextmenu-item.p-focus > .p-contextmenu-item-content {\n    color: ${dt('contextmenu.item.focus.color')};\n    background: ${dt('contextmenu.item.focus.background')};\n}\n\n.p-contextmenu-item.p-focus > .p-contextmenu-item-content .p-contextmenu-item-icon {\n    color: ${dt('contextmenu.item.icon.focus.color')};\n}\n\n.p-contextmenu-item.p-focus > .p-contextmenu-item-content .p-contextmenu-submenu-icon {\n    color: ${dt('contextmenu.submenu.icon.focus.color')};\n}\n\n.p-contextmenu-item:not(.p-disabled) > .p-contextmenu-item-content:hover {\n    color: ${dt('contextmenu.item.focus.color')};\n    background: ${dt('contextmenu.item.focus.background')};\n}\n\n.p-contextmenu-item:not(.p-disabled) > .p-contextmenu-item-content:hover .p-contextmenu-item-icon {\n    color: ${dt('contextmenu.item.icon.focus.color')};\n}\n\n.p-contextmenu-item:not(.p-disabled) > .p-contextmenu-item-content:hover .p-contextmenu-submenu-icon {\n    color: ${dt('contextmenu.submenu.icon.focus.color')};\n}\n\n.p-contextmenu-item-active > .p-contextmenu-item-content {\n    color: ${dt('contextmenu.item.active.color')};\n    background: ${dt('contextmenu.item.active.background')};\n}\n\n.p-contextmenu-item-active > .p-contextmenu-item-content .p-contextmenu-item-icon {\n    color: ${dt('contextmenu.item.icon.active.color')};\n}\n\n.p-contextmenu-item-active > .p-contextmenu-item-content .p-contextmenu-submenu-icon {\n    color: ${dt('contextmenu.submenu.icon.active.color')};\n}\n\n.p-contextmenu-separator {\n    border-top: 1px solid  ${dt('contextmenu.separator.border.color')};\n}\n\n.p-contextmenu-enter-from,\n.p-contextmenu-leave-active {\n    opacity: 0;\n}\n\n.p-contextmenu-enter-active {\n    transition: opacity 250ms;\n}\n\n.p-contextmenu-mobile .p-contextmenu-submenu {\n    position: static;\n    box-shadow: none;\n    border: 0 none;\n    padding-left: ${dt('tieredmenu.submenu.mobile.indent')};\n    padding-right: 0;\n}\n\n.p-contextmenu-mobile .p-contextmenu-submenu-icon {\n    transition: transform 0.2s;\n    transform: rotate(90deg);\n}\n\n.p-contextmenu-mobile .p-contextmenu-item-active > .p-contextmenu-item-content .p-contextmenu-submenu-icon {\n    transform: rotate(-90deg);\n}\n\n/* For PrimeNG */\n.p-contextmenu-submenu-icon.p-iconwrapper {\n    margin-left: auto;\n}\n\n.p-contextmenu-submenu-icon.p-iconwrapper:dir(rtl) {\n    margin-left: 0;\n    margin-right: auto;\n}\n`;\nconst classes = {\n  root: 'p-contextmenu p-component',\n  rootList: 'p-contextmenu-root-list',\n  item: ({\n    instance,\n    processedItem\n  }) => ['p-contextmenu-item', {\n    'p-contextmenu-item-active': instance.isItemActive(processedItem),\n    'p-focus': instance.isItemFocused(processedItem),\n    'p-disabled': instance.isItemDisabled(processedItem)\n  }],\n  itemContent: 'p-contextmenu-item-content',\n  itemLink: 'p-contextmenu-item-link',\n  itemIcon: 'p-contextmenu-item-icon',\n  itemLabel: 'p-contextmenu-item-label',\n  submenuIcon: 'p-contextmenu-submenu-icon',\n  submenu: 'p-contextmenu-submenu',\n  separator: 'p-contextmenu-separator'\n};\nclass ContextMenuStyle extends BaseStyle {\n  name = 'contextmenu';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵContextMenuStyle_BaseFactory;\n    return function ContextMenuStyle_Factory(__ngFactoryType__) {\n      return (ɵContextMenuStyle_BaseFactory || (ɵContextMenuStyle_BaseFactory = i0.ɵɵgetInheritedFactory(ContextMenuStyle)))(__ngFactoryType__ || ContextMenuStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ContextMenuStyle,\n    factory: ContextMenuStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ContextMenuStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * ContextMenu displays an overlay menu on right click of its target. Note that components like DataTable has special integration with ContextMenu.\n * Refer to documentation of the individual documentation of the with context menu support.\n *\n * [Live Demo](https://www.primeng.org/contextmenu/)\n *\n * @module contextmenustyle\n *\n */\nvar ContextMenuClasses;\n(function (ContextMenuClasses) {\n  /**\n   * Class name of the root element\n   */\n  ContextMenuClasses[\"root\"] = \"p-contextmenu\";\n  /**\n   * Class name of the root list element\n   */\n  ContextMenuClasses[\"rootList\"] = \"p-contextmenu-root-list\";\n  /**\n   * Class name of the item element\n   */\n  ContextMenuClasses[\"item\"] = \"p-contextmenu-item\";\n  /**\n   * Class name of the item content element\n   */\n  ContextMenuClasses[\"itemContent\"] = \"p-contextmenu-item-content\";\n  /**\n   * Class name of the item link element\n   */\n  ContextMenuClasses[\"itemLink\"] = \"p-contextmenu-item-link\";\n  /**\n   * Class name of the item icon element\n   */\n  ContextMenuClasses[\"itemIcon\"] = \"p-contextmenu-item-icon\";\n  /**\n   * Class name of the item label element\n   */\n  ContextMenuClasses[\"itemLabel\"] = \"p-contextmenu-item-label\";\n  /**\n   * Class name of the submenu icon element\n   */\n  ContextMenuClasses[\"submenuIcon\"] = \"p-contextmenu-submenu-icon\";\n  /**\n   * Class name of the submenu element\n   */\n  ContextMenuClasses[\"submenu\"] = \"p-contextmenu-submenu\";\n  /**\n   * Class name of the separator element\n   */\n  ContextMenuClasses[\"separator\"] = \"p-contextmenu-separator\";\n})(ContextMenuClasses || (ContextMenuClasses = {}));\nclass ContextMenuSub extends BaseComponent {\n  contextMenu;\n  visible = false;\n  items;\n  itemTemplate;\n  root = false;\n  autoZIndex = true;\n  baseZIndex = 0;\n  popup;\n  menuId;\n  ariaLabel;\n  ariaLabelledBy;\n  level = 0;\n  focusedItemId;\n  activeItemPath;\n  tabindex = 0;\n  itemClick = new EventEmitter();\n  itemMouseEnter = new EventEmitter();\n  menuFocus = new EventEmitter();\n  menuBlur = new EventEmitter();\n  menuKeydown = new EventEmitter();\n  sublistViewChild;\n  constructor(contextMenu) {\n    super();\n    this.contextMenu = contextMenu;\n  }\n  getItemProp(processedItem, name, params = null) {\n    return processedItem && processedItem.item ? resolve(processedItem.item[name], params) : undefined;\n  }\n  getItemId(processedItem) {\n    return processedItem.item && processedItem.item?.id ? processedItem.item.id : `${this.menuId}_${processedItem.key}`;\n  }\n  getItemKey(processedItem) {\n    return this.getItemId(processedItem);\n  }\n  getItemClass(processedItem) {\n    return {\n      ...this.getItemProp(processedItem, 'class'),\n      'p-contextmenu-item': true,\n      'p-contextmenu-item-active': this.isItemActive(processedItem),\n      'p-focus': this.isItemFocused(processedItem),\n      'p-disabled': this.isItemDisabled(processedItem)\n    };\n  }\n  getItemLabel(processedItem) {\n    return this.getItemProp(processedItem, 'label');\n  }\n  getSeparatorItemClass(processedItem) {\n    return {\n      ...this.getItemProp(processedItem, 'class'),\n      'p-contextmenu-separator': true\n    };\n  }\n  getAriaSetSize() {\n    return this.items.filter(processedItem => this.isItemVisible(processedItem) && !this.getItemProp(processedItem, 'separator')).length;\n  }\n  getAriaPosInset(index) {\n    return index - this.items.slice(0, index).filter(processedItem => this.isItemVisible(processedItem) && this.getItemProp(processedItem, 'separator')).length + 1;\n  }\n  isItemVisible(processedItem) {\n    return this.getItemProp(processedItem, 'visible') !== false;\n  }\n  isItemActive(processedItem) {\n    if (this.activeItemPath) {\n      return this.activeItemPath.some(path => path.key === processedItem.key);\n    }\n  }\n  isItemDisabled(processedItem) {\n    return this.getItemProp(processedItem, 'disabled');\n  }\n  isItemFocused(processedItem) {\n    return this.focusedItemId === this.getItemId(processedItem);\n  }\n  isItemGroup(processedItem) {\n    return isNotEmpty(processedItem.items);\n  }\n  onItemMouseEnter(param) {\n    const {\n      event,\n      processedItem\n    } = param;\n    this.itemMouseEnter.emit({\n      originalEvent: event,\n      processedItem\n    });\n  }\n  onItemClick(event, processedItem) {\n    this.getItemProp(processedItem, 'command', {\n      originalEvent: event,\n      item: processedItem.item\n    });\n    this.itemClick.emit({\n      originalEvent: event,\n      processedItem,\n      isFocus: true\n    });\n  }\n  onEnter(event, sublist) {\n    if (event.fromState === 'void' && event.toState) {\n      const sublist = event.element;\n      this.position(sublist);\n    }\n  }\n  position(sublist) {\n    const parentItem = sublist.parentElement.parentElement;\n    const containerOffset = getOffset(sublist.parentElement.parentElement);\n    const viewport = getViewport();\n    const sublistWidth = sublist.offsetParent ? sublist.offsetWidth : getHiddenElementOuterWidth(sublist);\n    const itemOuterWidth = getOuterWidth(parentItem.children[0]);\n    sublist.style.top = '0px';\n    if (parseInt(containerOffset.left, 10) + itemOuterWidth + sublistWidth > viewport.width - calculateScrollbarWidth()) {\n      sublist.style.left = -1 * sublistWidth + 'px';\n    } else {\n      sublist.style.left = itemOuterWidth + 'px';\n    }\n  }\n  static ɵfac = function ContextMenuSub_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ContextMenuSub)(i0.ɵɵdirectiveInject(forwardRef(() => ContextMenu)));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: ContextMenuSub,\n    selectors: [[\"p-contextMenuSub\"], [\"p-contextmenu-sub\"]],\n    viewQuery: function ContextMenuSub_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sublistViewChild = _t.first);\n      }\n    },\n    inputs: {\n      visible: [2, \"visible\", \"visible\", booleanAttribute],\n      items: \"items\",\n      itemTemplate: \"itemTemplate\",\n      root: [2, \"root\", \"root\", booleanAttribute],\n      autoZIndex: [2, \"autoZIndex\", \"autoZIndex\", booleanAttribute],\n      baseZIndex: [2, \"baseZIndex\", \"baseZIndex\", numberAttribute],\n      popup: [2, \"popup\", \"popup\", booleanAttribute],\n      menuId: \"menuId\",\n      ariaLabel: \"ariaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\",\n      level: [2, \"level\", \"level\", numberAttribute],\n      focusedItemId: \"focusedItemId\",\n      activeItemPath: \"activeItemPath\",\n      tabindex: [2, \"tabindex\", \"tabindex\", numberAttribute]\n    },\n    outputs: {\n      itemClick: \"itemClick\",\n      itemMouseEnter: \"itemMouseEnter\",\n      menuFocus: \"menuFocus\",\n      menuBlur: \"menuBlur\",\n      menuKeydown: \"menuKeydown\"\n    },\n    features: [i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature],\n    decls: 1,\n    vars: 1,\n    consts: [[\"sublist\", \"\"], [\"listItem\", \"\"], [\"htmlLabel\", \"\"], [\"role\", \"menu\", 3, \"ngClass\", \"tabindex\", \"keydown\", \"focus\", \"blur\", 4, \"ngIf\"], [\"role\", \"menu\", 3, \"keydown\", \"focus\", \"blur\", \"ngClass\", \"tabindex\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"role\", \"separator\", 3, \"style\", \"ngClass\", 4, \"ngIf\"], [\"role\", \"menuitem\", \"pTooltip\", \"\", 3, \"ngStyle\", \"ngClass\", \"class\", \"tooltipOptions\", 4, \"ngIf\"], [\"role\", \"separator\", 3, \"ngClass\"], [\"role\", \"menuitem\", \"pTooltip\", \"\", 3, \"ngStyle\", \"ngClass\", \"tooltipOptions\"], [1, \"p-contextmenu-item-content\", 3, \"click\", \"mouseenter\"], [4, \"ngIf\"], [3, \"items\", \"itemTemplate\", \"menuId\", \"visible\", \"activeItemPath\", \"focusedItemId\", \"level\", \"itemClick\", \"itemMouseEnter\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"target\", \"ngClass\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"target\", \"ngClass\"], [\"class\", \"p-contextmenu-item-icon\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-contextmenu-item-label\", 4, \"ngIf\", \"ngIfElse\"], [3, \"styleClass\", \"value\", 4, \"ngIf\"], [1, \"p-contextmenu-item-icon\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-contextmenu-item-label\"], [1, \"p-contextmenu-item-label\", 3, \"innerHTML\"], [3, \"styleClass\", \"value\"], [3, \"class\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"data-pc-section\", \"aria-hidden\"], [\"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\"], [3, \"itemClick\", \"itemMouseEnter\", \"items\", \"itemTemplate\", \"menuId\", \"visible\", \"activeItemPath\", \"focusedItemId\", \"level\"]],\n    template: function ContextMenuSub_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, ContextMenuSub_ul_0_Template, 3, 13, \"ul\", 3);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.root ? true : ctx.visible);\n      }\n    },\n    dependencies: [ContextMenuSub, CommonModule, i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, RouterModule, i2.RouterLink, Ripple, TooltipModule, i3.Tooltip, AngleRightIcon, BadgeModule, i4.Badge, SharedModule],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('overlayAnimation', [transition(':enter', [style({\n        opacity: 0\n      })]), transition(':leave', [style({\n        opacity: 0\n      })])])]\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ContextMenuSub, [{\n    type: Component,\n    args: [{\n      selector: 'p-contextMenuSub, p-contextmenu-sub',\n      standalone: true,\n      imports: [CommonModule, RouterModule, Ripple, TooltipModule, AngleRightIcon, BadgeModule, SharedModule],\n      template: `\n        <ul\n            *ngIf=\"root ? true : visible\"\n            #sublist\n            role=\"menu\"\n            [ngClass]=\"{ 'p-contextmenu-submenu': !root, 'p-contextmenu-root-list': root }\"\n            [@overlayAnimation]=\"visible\"\n            (@overlayAnimation.start)=\"onEnter($event, sublist)\"\n            [attr.id]=\"menuId + '_list'\"\n            [tabindex]=\"tabindex\"\n            [attr.aria-label]=\"ariaLabel\"\n            [attr.aria-labelledBy]=\"ariaLabelledBy\"\n            [attr.aria-activedescendant]=\"focusedItemId\"\n            [attr.aria-orientation]=\"'vertical'\"\n            [attr.data-pc-section]=\"'menu'\"\n            (keydown)=\"menuKeydown.emit($event)\"\n            (focus)=\"menuFocus.emit($event)\"\n            (blur)=\"menuBlur.emit($event)\"\n        >\n            <ng-template ngFor let-processedItem [ngForOf]=\"items\" let-index=\"index\">\n                <li\n                    *ngIf=\"isItemVisible(processedItem) && getItemProp(processedItem, 'separator')\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [style]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getSeparatorItemClass(processedItem)\"\n                    role=\"separator\"\n                    [attr.data-pc-section]=\"'separator'\"\n                ></li>\n                <li\n                    #listItem\n                    *ngIf=\"isItemVisible(processedItem) && !getItemProp(processedItem, 'separator')\"\n                    role=\"menuitem\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [attr.data-pc-section]=\"'menuitem'\"\n                    [attr.data-p-highlight]=\"isItemActive(processedItem)\"\n                    [attr.data-p-focused]=\"isItemFocused(processedItem)\"\n                    [attr.data-p-disabled]=\"isItemDisabled(processedItem)\"\n                    [attr.aria-label]=\"getItemLabel(processedItem)\"\n                    [attr.aria-disabled]=\"isItemDisabled(processedItem) || undefined\"\n                    [attr.aria-haspopup]=\"isItemGroup(processedItem) && !getItemProp(processedItem, 'to') ? 'menu' : undefined\"\n                    [attr.aria-expanded]=\"isItemGroup(processedItem) ? isItemActive(processedItem) : undefined\"\n                    [attr.aria-level]=\"level + 1\"\n                    [attr.aria-setsize]=\"getAriaSetSize()\"\n                    [attr.aria-posinset]=\"getAriaPosInset(index)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getItemClass(processedItem)\"\n                    [class]=\"getItemProp(processedItem, 'styleClass')\"\n                    pTooltip\n                    [tooltipOptions]=\"getItemProp(processedItem, 'tooltipOptions')\"\n                >\n                    <div [attr.data-pc-section]=\"'content'\" class=\"p-contextmenu-item-content\" (click)=\"onItemClick($event, processedItem)\" (mouseenter)=\"onItemMouseEnter({ $event, processedItem })\">\n                        <ng-container *ngIf=\"!itemTemplate\">\n                            <a\n                                *ngIf=\"!getItemProp(processedItem, 'routerLink')\"\n                                [attr.href]=\"getItemProp(processedItem, 'url')\"\n                                [attr.aria-hidden]=\"true\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-contextmenu-item-link': true }\"\n                                [attr.tabindex]=\"-1\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-contextmenu-item-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.aria-hidden]=\"true\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-contextmenu-item-label\" [attr.data-pc-section]=\"'label'\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-contextmenu-item-label\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span>\n                                </ng-template>\n                                <p-badge *ngIf=\"getItemProp(processedItem, 'badge')\" [styleClass]=\"getItemProp(processedItem, 'badgeStyleClass')\" [value]=\"getItemProp(processedItem, 'badge')\" />\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <AngleRightIcon *ngIf=\"!contextMenu.submenuIconTemplate && !contextMenu._submenuIconTemplate\" [class]=\"'p-contextmenu-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                    <ng-template\n                                        *ngTemplateOutlet=\"contextMenu.submenuIconTemplate || contextMenu._submenuIconTemplate; context: { class: 'p-contextmenu-submenu-icon' }\"\n                                        [attr.data-pc-section]=\"'submenuicon'\"\n                                        [attr.aria-hidden]=\"true\"\n                                    ></ng-template>\n                                </ng-container>\n                            </a>\n                            <a\n                                *ngIf=\"getItemProp(processedItem, 'routerLink')\"\n                                [routerLink]=\"getItemProp(processedItem, 'routerLink')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.tabindex]=\"-1\"\n                                [attr.aria-hidden]=\"true\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [queryParams]=\"getItemProp(processedItem, 'queryParams')\"\n                                [routerLinkActiveOptions]=\"getItemProp(processedItem, 'routerLinkActiveOptions') || { exact: false }\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-contextmenu-item-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [fragment]=\"getItemProp(processedItem, 'fragment')\"\n                                [queryParamsHandling]=\"getItemProp(processedItem, 'queryParamsHandling')\"\n                                [preserveFragment]=\"getItemProp(processedItem, 'preserveFragment')\"\n                                [skipLocationChange]=\"getItemProp(processedItem, 'skipLocationChange')\"\n                                [replaceUrl]=\"getItemProp(processedItem, 'replaceUrl')\"\n                                [state]=\"getItemProp(processedItem, 'state')\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-contextmenu-item-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.aria-hidden]=\"true\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-contextmenu-item-label\" [attr.data-pc-section]=\"'label'\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-contextmenu-item-label\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span>\n                                </ng-template>\n                                <p-badge *ngIf=\"getItemProp(processedItem, 'badge')\" [styleClass]=\"getItemProp(processedItem, 'badgeStyleClass')\" [value]=\"getItemProp(processedItem, 'badge')\" />\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <AngleRightIcon *ngIf=\"!contextMenu.submenuIconTemplate && !contextMenu._submenuIconTemplate\" [class]=\"'p-contextmenu-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                    <ng-template\n                                        *ngTemplateOutlet=\"!contextMenu.submenuIconTemplate || !contextMenu._submenuIconTemplate; context: { class: 'p-contextmenu-submenu-icon' }\"\n                                        [attr.data-pc-section]=\"'submenuicon'\"\n                                        [attr.aria-hidden]=\"true\"\n                                    ></ng-template>\n                                </ng-container>\n                            </a>\n                        </ng-container>\n                        <ng-container *ngIf=\"itemTemplate\">\n                            <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: processedItem.item }\"></ng-template>\n                        </ng-container>\n                    </div>\n\n                    <p-contextmenu-sub\n                        *ngIf=\"isItemVisible(processedItem) && isItemGroup(processedItem)\"\n                        [items]=\"processedItem.items\"\n                        [itemTemplate]=\"itemTemplate\"\n                        [menuId]=\"menuId\"\n                        [visible]=\"isItemActive(processedItem) && isItemGroup(processedItem)\"\n                        [activeItemPath]=\"activeItemPath\"\n                        [focusedItemId]=\"focusedItemId\"\n                        [level]=\"level + 1\"\n                        (itemClick)=\"itemClick.emit($event)\"\n                        (itemMouseEnter)=\"onItemMouseEnter($event)\"\n                    />\n                </li>\n            </ng-template>\n        </ul>\n    `,\n      animations: [trigger('overlayAnimation', [transition(':enter', [style({\n        opacity: 0\n      })]), transition(':leave', [style({\n        opacity: 0\n      })])])],\n      encapsulation: ViewEncapsulation.None\n    }]\n  }], () => [{\n    type: ContextMenu,\n    decorators: [{\n      type: Inject,\n      args: [forwardRef(() => ContextMenu)]\n    }]\n  }], {\n    visible: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    items: [{\n      type: Input\n    }],\n    itemTemplate: [{\n      type: Input\n    }],\n    root: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    autoZIndex: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    baseZIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    popup: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    menuId: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    level: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    focusedItemId: [{\n      type: Input\n    }],\n    activeItemPath: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    itemClick: [{\n      type: Output\n    }],\n    itemMouseEnter: [{\n      type: Output\n    }],\n    menuFocus: [{\n      type: Output\n    }],\n    menuBlur: [{\n      type: Output\n    }],\n    menuKeydown: [{\n      type: Output\n    }],\n    sublistViewChild: [{\n      type: ViewChild,\n      args: ['sublist']\n    }]\n  });\n})();\n/**\n * ContextMenu displays an overlay menu on right click of its target. Note that components like Table has special integration with ContextMenu.\n * @group Components\n */\nclass ContextMenu extends BaseComponent {\n  overlayService;\n  /**\n   * An array of menuitems.\n   * @group Props\n   */\n  set model(value) {\n    this._model = value;\n    this._processedItems = this.createProcessedItems(this._model || []);\n  }\n  get model() {\n    return this._model;\n  }\n  /**\n   * Event for which the menu must be displayed.\n   * @group Props\n   */\n  triggerEvent = 'contextmenu';\n  /**\n   * Local template variable name of the element to attach the context menu.\n   * @group Props\n   */\n  target;\n  /**\n   * Attaches the menu to document instead of a particular item.\n   * @group Props\n   */\n  global;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Target element to attach the overlay, valid values are \"body\" or a local ng-template variable of another element.\n   * @group Props\n   */\n  appendTo;\n  /**\n   * Whether to automatically manage layering.\n   * @group Props\n   */\n  autoZIndex = true;\n  /**\n   * Base zIndex value to use in layering.\n   * @group Props\n   */\n  baseZIndex = 0;\n  /**\n   * Current id state as a string.\n   * @group Props\n   */\n  id;\n  /**\n   * The breakpoint to define the maximum width boundary.\n   * @group Props\n   */\n  breakpoint = '960px';\n  /**\n   * Defines a string value that labels an interactive element.\n   * @group Props\n   */\n  ariaLabel;\n  /**\n   * Identifier of the underlying input element.\n   * @group Props\n   */\n  ariaLabelledBy;\n  /**\n   * Press delay in touch devices as miliseconds.\n   * @group Props\n   */\n  pressDelay = 500;\n  /**\n   * Callback to invoke when overlay menu is shown.\n   * @group Emits\n   */\n  onShow = new EventEmitter();\n  /**\n   * Callback to invoke when overlay menu is hidden.\n   * @group Emits\n   */\n  onHide = new EventEmitter();\n  rootmenu;\n  containerViewChild;\n  container;\n  outsideClickListener;\n  resizeListener;\n  triggerEventListener;\n  documentClickListener;\n  documentTriggerListener;\n  touchEndListener;\n  pageX;\n  pageY;\n  visible = signal(false);\n  relativeAlign;\n  window;\n  focused = false;\n  activeItemPath = signal([]);\n  focusedItemInfo = signal({\n    index: -1,\n    level: 0,\n    parentKey: '',\n    item: null\n  });\n  submenuVisible = signal(false);\n  searchValue = '';\n  searchTimeout;\n  _processedItems;\n  _model;\n  pressTimer;\n  matchMediaListener;\n  query;\n  queryMatches;\n  _componentStyle = inject(ContextMenuStyle);\n  get visibleItems() {\n    const processedItem = this.activeItemPath().find(p => p.key === this.focusedItemInfo().parentKey);\n    return processedItem ? processedItem.items : this.processedItems;\n  }\n  get processedItems() {\n    if (!this._processedItems || !this._processedItems.length) {\n      this._processedItems = this.createProcessedItems(this.model || []);\n    }\n    return this._processedItems;\n  }\n  get focusedItemId() {\n    const focusedItem = this.focusedItemInfo();\n    return focusedItem.item && focusedItem.item?.id ? focusedItem.item.id : focusedItem.index !== -1 ? `${this.id}${isNotEmpty(focusedItem.parentKey) ? '_' + focusedItem.parentKey : ''}_${focusedItem.index}` : null;\n  }\n  constructor(overlayService) {\n    super();\n    this.overlayService = overlayService;\n    effect(() => {\n      const path = this.activeItemPath();\n      if (isNotEmpty(path)) {\n        this.bindGlobalListeners();\n      } else if (!this.visible()) {\n        this.unbindGlobalListeners();\n      }\n    });\n  }\n  ngOnInit() {\n    super.ngOnInit();\n    this.id = this.id || uuid('pn_id_');\n    this.bindMatchMediaListener();\n    this.bindTriggerEventListener();\n  }\n  isMobile() {\n    return isIOS() || isAndroid();\n  }\n  bindTriggerEventListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.triggerEventListener) {\n        if (!this.isMobile()) {\n          if (this.global) {\n            this.triggerEventListener = this.renderer.listen(this.document, this.triggerEvent, event => {\n              this.show(event);\n            });\n          } else if (this.target) {\n            this.triggerEventListener = this.renderer.listen(this.target, this.triggerEvent, event => {\n              this.show(event);\n            });\n          }\n        } else {\n          if (this.global) {\n            this.triggerEventListener = this.renderer.listen(this.document, 'touchstart', this.onTouchStart.bind(this));\n            this.touchEndListener = this.renderer.listen(this.document, 'touchend', this.onTouchEnd.bind(this));\n          } else if (this.target) {\n            this.triggerEventListener = this.renderer.listen(this.target, 'touchstart', this.onTouchStart.bind(this));\n            this.touchEndListener = this.renderer.listen(this.target, 'touchend', this.onTouchEnd.bind(this));\n          }\n        }\n      }\n    }\n  }\n  bindGlobalListeners() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.documentClickListener) {\n        const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n        this.documentClickListener = this.renderer.listen(documentTarget, 'click', event => {\n          if (this.containerViewChild.nativeElement.offsetParent && this.isOutsideClicked(event) && !event.ctrlKey && event.button !== 2 && this.triggerEvent !== 'click') {\n            this.hide();\n          }\n        });\n        this.documentTriggerListener = this.renderer.listen(documentTarget, this.triggerEvent, event => {\n          if (this.containerViewChild.nativeElement.offsetParent && this.isOutsideClicked(event)) {\n            this.hide();\n          }\n        });\n      }\n      if (!this.resizeListener) {\n        this.resizeListener = this.renderer.listen(this.document.defaultView, 'resize', event => {\n          this.hide();\n        });\n      }\n    }\n  }\n  /**\n   * Defines template option for item.\n   * @group Templates\n   */\n  itemTemplate;\n  /**\n   * Defines template option for submenuIcon.\n   * @group Templates\n   */\n  submenuIconTemplate;\n  templates;\n  _submenuIconTemplate;\n  _itemTemplate;\n  ngAfterContentInit() {\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'submenuicon':\n          this._submenuIconTemplate = item.template;\n          break;\n        case 'item':\n          this._itemTemplate = item.template;\n          break;\n        default:\n          this._itemTemplate = item.template;\n          break;\n      }\n    });\n  }\n  createProcessedItems(items, level = 0, parent = {}, parentKey = '') {\n    const processedItems = [];\n    items && items.forEach((item, index) => {\n      const key = (parentKey !== '' ? parentKey + '_' : '') + index;\n      const newItem = {\n        item,\n        index,\n        level,\n        key,\n        parent,\n        parentKey\n      };\n      newItem['items'] = this.createProcessedItems(item.items, level + 1, newItem, key);\n      processedItems.push(newItem);\n    });\n    return processedItems;\n  }\n  bindMatchMediaListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.matchMediaListener) {\n        const query = window.matchMedia(`(max-width: ${this.breakpoint})`);\n        this.query = query;\n        this.queryMatches = query.matches;\n        this.matchMediaListener = () => {\n          this.queryMatches = query.matches;\n        };\n        query.addEventListener('change', this.matchMediaListener);\n      }\n    }\n  }\n  unbindMatchMediaListener() {\n    if (this.matchMediaListener) {\n      this.query.removeEventListener('change', this.matchMediaListener);\n      this.matchMediaListener = null;\n    }\n  }\n  getItemProp(item, name) {\n    return item ? resolve(item[name]) : undefined;\n  }\n  getProccessedItemLabel(processedItem) {\n    return processedItem ? this.getItemLabel(processedItem.item) : undefined;\n  }\n  getItemLabel(item) {\n    return this.getItemProp(item, 'label');\n  }\n  isProcessedItemGroup(processedItem) {\n    return processedItem && isNotEmpty(processedItem.items);\n  }\n  isSelected(processedItem) {\n    return this.activeItemPath().some(p => p.key === processedItem.key);\n  }\n  isValidSelectedItem(processedItem) {\n    return this.isValidItem(processedItem) && this.isSelected(processedItem);\n  }\n  isValidItem(processedItem) {\n    return !!processedItem && !this.isItemDisabled(processedItem.item) && !this.isItemSeparator(processedItem.item);\n  }\n  isItemDisabled(item) {\n    return this.getItemProp(item, 'disabled');\n  }\n  isItemSeparator(item) {\n    return this.getItemProp(item, 'separator');\n  }\n  isItemMatched(processedItem) {\n    return this.isValidItem(processedItem) && this.getProccessedItemLabel(processedItem).toLocaleLowerCase().startsWith(this.searchValue.toLocaleLowerCase());\n  }\n  isProccessedItemGroup(processedItem) {\n    return processedItem && isNotEmpty(processedItem.items);\n  }\n  onItemClick(event) {\n    const {\n      processedItem\n    } = event;\n    const grouped = this.isProcessedItemGroup(processedItem);\n    const selected = this.isSelected(processedItem);\n    if (selected) {\n      const {\n        index,\n        key,\n        level,\n        parentKey,\n        item\n      } = processedItem;\n      this.activeItemPath.set(this.activeItemPath().filter(p => key !== p.key && key.startsWith(p.key)));\n      this.focusedItemInfo.set({\n        index,\n        level,\n        parentKey,\n        item\n      });\n      focus(this.rootmenu.sublistViewChild.nativeElement);\n    } else {\n      grouped ? this.onItemChange(event) : this.hide();\n    }\n  }\n  onItemMouseEnter(event) {\n    this.onItemChange(event, 'hover');\n  }\n  onKeyDown(event) {\n    const metaKey = event.metaKey || event.ctrlKey;\n    switch (event.code) {\n      case 'ArrowDown':\n        this.onArrowDownKey(event);\n        break;\n      case 'ArrowUp':\n        this.onArrowUpKey(event);\n        break;\n      case 'ArrowLeft':\n        this.onArrowLeftKey(event);\n        break;\n      case 'ArrowRight':\n        this.onArrowRightKey(event);\n        break;\n      case 'Home':\n        this.onHomeKey(event);\n        break;\n      case 'End':\n        this.onEndKey(event);\n        break;\n      case 'Space':\n        this.onSpaceKey(event);\n        break;\n      case 'Enter':\n        this.onEnterKey(event);\n        break;\n      case 'Escape':\n        this.onEscapeKey(event);\n        break;\n      case 'Tab':\n        this.onTabKey(event);\n        break;\n      case 'PageDown':\n      case 'PageUp':\n      case 'Backspace':\n      case 'ShiftLeft':\n      case 'ShiftRight':\n        //NOOP\n        break;\n      default:\n        if (!metaKey && isPrintableCharacter(event.key)) {\n          this.searchItems(event, event.key);\n        }\n        break;\n    }\n  }\n  onArrowDownKey(event) {\n    const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n    this.changeFocusedItemIndex(event, itemIndex);\n    event.preventDefault();\n  }\n  onArrowRightKey(event) {\n    const processedItem = this.visibleItems[this.focusedItemInfo().index];\n    const grouped = this.isProccessedItemGroup(processedItem);\n    if (grouped) {\n      this.onItemChange({\n        originalEvent: event,\n        processedItem\n      });\n      this.focusedItemInfo.set({\n        index: -1,\n        parentKey: processedItem.key,\n        item: processedItem.item\n      });\n      this.searchValue = '';\n      this.onArrowDownKey(event);\n    }\n    event.preventDefault();\n  }\n  onArrowUpKey(event) {\n    if (event.altKey) {\n      if (this.focusedItemInfo().index !== -1) {\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const grouped = this.isProccessedItemGroup(processedItem);\n        !grouped && this.onItemChange({\n          originalEvent: event,\n          processedItem\n        });\n      }\n      this.hide();\n      event.preventDefault();\n    } else {\n      const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n      this.changeFocusedItemIndex(event, itemIndex);\n      event.preventDefault();\n    }\n  }\n  onArrowLeftKey(event) {\n    const processedItem = this.visibleItems[this.focusedItemInfo().index];\n    const parentItem = this.activeItemPath().find(p => p.key === processedItem.parentKey);\n    const root = isEmpty(processedItem.parent);\n    if (!root) {\n      this.focusedItemInfo.set({\n        index: -1,\n        parentKey: parentItem ? parentItem.parentKey : '',\n        item: processedItem.item\n      });\n      this.searchValue = '';\n      this.onArrowDownKey(event);\n    }\n    const activeItemPath = this.activeItemPath().filter(p => p.parentKey !== this.focusedItemInfo().parentKey);\n    this.activeItemPath.set(activeItemPath);\n    event.preventDefault();\n  }\n  onHomeKey(event) {\n    this.changeFocusedItemIndex(event, this.findFirstItemIndex());\n    event.preventDefault();\n  }\n  onEndKey(event) {\n    this.changeFocusedItemIndex(event, this.findLastItemIndex());\n    event.preventDefault();\n  }\n  onSpaceKey(event) {\n    this.onEnterKey(event);\n  }\n  onEscapeKey(event) {\n    this.hide();\n    const processedItem = this.findVisibleItem(this.findFirstFocusedItemIndex());\n    const focusedItemInfo = this.focusedItemInfo();\n    this.focusedItemInfo.set({\n      ...focusedItemInfo,\n      index: this.findFirstFocusedItemIndex(),\n      item: processedItem.item\n    });\n    event.preventDefault();\n  }\n  onTabKey(event) {\n    if (this.focusedItemInfo().index !== -1) {\n      const processedItem = this.visibleItems[this.focusedItemInfo().index];\n      const grouped = this.isProccessedItemGroup(processedItem);\n      !grouped && this.onItemChange({\n        originalEvent: event,\n        processedItem\n      });\n    }\n    this.hide();\n  }\n  onEnterKey(event) {\n    if (this.focusedItemInfo().index !== -1) {\n      const element = findSingle(this.rootmenu.el.nativeElement, `li[id=\"${`${this.focusedItemId}`}\"]`);\n      const anchorElement = element && findSingle(element, 'a[data-pc-section=\"action\"]');\n      anchorElement ? anchorElement.click() : element && element.click();\n      const processedItem = this.visibleItems[this.focusedItemInfo().index];\n      const grouped = this.isProccessedItemGroup(processedItem);\n      if (!grouped) {\n        const focusedItemInfo = this.focusedItemInfo();\n        this.focusedItemInfo.set({\n          ...focusedItemInfo,\n          index: this.findFirstFocusedItemIndex()\n        });\n      }\n    }\n    event.preventDefault();\n  }\n  onItemChange(event, type) {\n    const {\n      processedItem,\n      isFocus\n    } = event;\n    if (isEmpty(processedItem)) return;\n    const {\n      index,\n      key,\n      level,\n      parentKey,\n      items\n    } = processedItem;\n    const grouped = isNotEmpty(items);\n    const activeItemPath = this.activeItemPath().filter(p => p.parentKey !== parentKey && p.parentKey !== key);\n    if (grouped) {\n      activeItemPath.push(processedItem);\n      this.submenuVisible.set(true);\n    }\n    this.focusedItemInfo.set({\n      index,\n      level,\n      parentKey,\n      item: processedItem.item\n    });\n    isFocus && focus(this.rootmenu.sublistViewChild.nativeElement);\n    if (type === 'hover' && this.queryMatches) {\n      return;\n    }\n    this.activeItemPath.set(activeItemPath);\n  }\n  onMenuFocus(event) {\n    this.focused = true;\n    const focusedItemInfo = this.focusedItemInfo().index !== -1 ? this.focusedItemInfo() : {\n      index: -1,\n      level: 0,\n      parentKey: '',\n      item: null\n    };\n    this.focusedItemInfo.set(focusedItemInfo);\n  }\n  onMenuBlur(event) {\n    this.focused = false;\n    this.focusedItemInfo.set({\n      index: -1,\n      level: 0,\n      parentKey: '',\n      item: null\n    });\n    this.searchValue = '';\n  }\n  onOverlayAnimationStart(event) {\n    switch (event.toState) {\n      case 'visible':\n        this.container = event.element;\n        this.position();\n        this.moveOnTop();\n        this.appendOverlay();\n        this.bindGlobalListeners();\n        focus(this.rootmenu.sublistViewChild.nativeElement);\n        break;\n    }\n  }\n  onOverlayAnimationEnd(event) {\n    switch (event.toState) {\n      case 'void':\n        this.onOverlayHide();\n        break;\n    }\n  }\n  appendOverlay() {\n    if (this.appendTo) {\n      if (this.appendTo === 'body') this.renderer.appendChild(this.document.body, this.containerViewChild.nativeElement);else appendChild(this.appendTo, this.containerViewChild.nativeElement);\n    }\n  }\n  moveOnTop() {\n    if (this.autoZIndex && this.containerViewChild) {\n      ZIndexUtils.set('menu', this.containerViewChild.nativeElement, this.baseZIndex + this.config.zIndex.menu);\n    }\n  }\n  onOverlayHide() {\n    this.unbindGlobalListeners();\n    if (!this.cd.destroyed) {\n      this.target = null;\n    }\n    if (this.container && this.autoZIndex) {\n      ZIndexUtils.clear(this.container);\n    }\n    this.container = null;\n  }\n  onTouchStart(event) {\n    this.pressTimer = setTimeout(() => {\n      this.show(event);\n    }, this.pressDelay);\n  }\n  onTouchEnd() {\n    clearTimeout(this.pressTimer);\n  }\n  hide() {\n    this.visible.set(false);\n    this.onHide.emit();\n    this.activeItemPath.set([]);\n    this.focusedItemInfo.set({\n      index: -1,\n      level: 0,\n      parentKey: '',\n      item: null\n    });\n  }\n  toggle(event) {\n    this.visible() ? this.hide() : this.show(event);\n  }\n  show(event) {\n    this.activeItemPath.set([]);\n    this.focusedItemInfo.set({\n      index: -1,\n      level: 0,\n      parentKey: '',\n      item: null\n    });\n    this.pageX = event.pageX;\n    this.pageY = event.pageY;\n    this.onShow.emit();\n    this.visible() ? this.position() : this.visible.set(true);\n    event.stopPropagation();\n    event.preventDefault();\n  }\n  position() {\n    let left = this.pageX + 1;\n    let top = this.pageY + 1;\n    let width = this.containerViewChild.nativeElement.offsetParent ? this.containerViewChild.nativeElement.offsetWidth : getHiddenElementOuterWidth(this.containerViewChild.nativeElement);\n    let height = this.containerViewChild.nativeElement.offsetParent ? this.containerViewChild.nativeElement.offsetHeight : getHiddenElementOuterHeight(this.containerViewChild.nativeElement);\n    let viewport = getViewport();\n    //flip\n    if (left + width - this.document.scrollingElement.scrollLeft > viewport.width) {\n      left -= width;\n    }\n    //flip\n    if (top + height - this.document.scrollingElement.scrollTop > viewport.height) {\n      top -= height;\n    }\n    //fit\n    if (left < this.document.scrollingElement.scrollLeft) {\n      left = this.document.scrollingElement.scrollLeft;\n    }\n    //fit\n    if (top < this.document.scrollingElement.scrollTop) {\n      top = this.document.scrollingElement.scrollTop;\n    }\n    this.containerViewChild.nativeElement.style.left = left + 'px';\n    this.containerViewChild.nativeElement.style.top = top + 'px';\n  }\n  searchItems(event, char) {\n    this.searchValue = (this.searchValue || '') + char;\n    let itemIndex = -1;\n    let matched = false;\n    if (this.focusedItemInfo().index !== -1) {\n      itemIndex = this.visibleItems.slice(this.focusedItemInfo().index).findIndex(processedItem => this.isItemMatched(processedItem));\n      itemIndex = itemIndex === -1 ? this.visibleItems.slice(0, this.focusedItemInfo().index).findIndex(processedItem => this.isItemMatched(processedItem)) : itemIndex + this.focusedItemInfo().index;\n    } else {\n      itemIndex = this.visibleItems.findIndex(processedItem => this.isItemMatched(processedItem));\n    }\n    if (itemIndex !== -1) {\n      matched = true;\n    }\n    if (itemIndex === -1 && this.focusedItemInfo().index === -1) {\n      itemIndex = this.findFirstFocusedItemIndex();\n    }\n    if (itemIndex !== -1) {\n      this.changeFocusedItemIndex(event, itemIndex);\n    }\n    if (this.searchTimeout) {\n      clearTimeout(this.searchTimeout);\n    }\n    this.searchTimeout = setTimeout(() => {\n      this.searchValue = '';\n      this.searchTimeout = null;\n    }, 500);\n    return matched;\n  }\n  findVisibleItem(index) {\n    return isNotEmpty(this.visibleItems) ? this.visibleItems[index] : null;\n  }\n  findLastFocusedItemIndex() {\n    const selectedIndex = this.findSelectedItemIndex();\n    return selectedIndex < 0 ? this.findLastItemIndex() : selectedIndex;\n  }\n  findLastItemIndex() {\n    return findLastIndex(this.visibleItems, processedItem => this.isValidItem(processedItem));\n  }\n  findPrevItemIndex(index) {\n    const matchedItemIndex = index > 0 ? findLastIndex(this.visibleItems.slice(0, index), processedItem => this.isValidItem(processedItem)) : -1;\n    return matchedItemIndex > -1 ? matchedItemIndex : index;\n  }\n  findNextItemIndex(index) {\n    const matchedItemIndex = index < this.visibleItems.length - 1 ? this.visibleItems.slice(index + 1).findIndex(processedItem => this.isValidItem(processedItem)) : -1;\n    return matchedItemIndex > -1 ? matchedItemIndex + index + 1 : index;\n  }\n  findFirstFocusedItemIndex() {\n    const selectedIndex = this.findSelectedItemIndex();\n    return selectedIndex < 0 ? this.findFirstItemIndex() : selectedIndex;\n  }\n  findFirstItemIndex() {\n    return this.visibleItems.findIndex(processedItem => this.isValidItem(processedItem));\n  }\n  findSelectedItemIndex() {\n    return this.visibleItems.findIndex(processedItem => this.isValidSelectedItem(processedItem));\n  }\n  changeFocusedItemIndex(event, index) {\n    const processedItem = this.findVisibleItem(index);\n    const focusedItemInfo = this.focusedItemInfo();\n    if (focusedItemInfo.index !== index) {\n      this.focusedItemInfo.set({\n        ...focusedItemInfo,\n        index,\n        item: processedItem.item\n      });\n      this.scrollInView();\n    }\n  }\n  scrollInView(index = -1) {\n    const id = index !== -1 ? `${this.id}_${index}` : this.focusedItemId;\n    const element = findSingle(this.rootmenu.el.nativeElement, `li[id=\"${id}\"]`);\n    if (element) {\n      element.scrollIntoView && element.scrollIntoView({\n        block: 'nearest',\n        inline: 'nearest'\n      });\n    }\n  }\n  bindResizeListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.resizeListener) {\n        this.resizeListener = this.renderer.listen(this.document.defaultView, 'resize', event => {\n          this.hide();\n        });\n      }\n    }\n  }\n  isOutsideClicked(event) {\n    return !(this.containerViewChild.nativeElement.isSameNode(event.target) || this.containerViewChild.nativeElement.contains(event.target));\n  }\n  unbindResizeListener() {\n    if (this.resizeListener) {\n      this.resizeListener();\n      this.resizeListener = null;\n    }\n  }\n  unbindGlobalListeners() {\n    if (this.documentClickListener) {\n      this.documentClickListener();\n      this.documentClickListener = null;\n    }\n    if (this.documentTriggerListener) {\n      this.documentTriggerListener();\n      this.documentTriggerListener = null;\n    }\n    if (this.resizeListener) {\n      this.resizeListener();\n      this.resizeListener = null;\n    }\n    if (this.touchEndListener) {\n      this.touchEndListener();\n      this.touchEndListener = null;\n    }\n  }\n  unbindTriggerEventListener() {\n    if (this.triggerEventListener) {\n      this.triggerEventListener();\n      this.triggerEventListener = null;\n    }\n  }\n  removeAppendedElements() {\n    if (this.appendTo && this.containerViewChild) {\n      if (this.appendTo === 'body') {\n        this.renderer.removeChild(this.document.body, this.containerViewChild.nativeElement);\n      } else {\n        removeChild(this.containerViewChild.nativeElement, this.appendTo);\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.unbindGlobalListeners();\n    this.unbindTriggerEventListener();\n    this.unbindMatchMediaListener();\n    this.removeAppendedElements();\n    super.ngOnDestroy();\n  }\n  static ɵfac = function ContextMenu_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ContextMenu)(i0.ɵɵdirectiveInject(i5.OverlayService));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: ContextMenu,\n    selectors: [[\"p-contextMenu\"], [\"p-contextmenu\"], [\"p-context-menu\"]],\n    contentQueries: function ContextMenu_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, _c7, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c8, 4);\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.itemTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.submenuIconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function ContextMenu_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c9, 5);\n        i0.ɵɵviewQuery(_c10, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.rootmenu = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);\n      }\n    },\n    inputs: {\n      model: \"model\",\n      triggerEvent: \"triggerEvent\",\n      target: \"target\",\n      global: [2, \"global\", \"global\", booleanAttribute],\n      style: \"style\",\n      styleClass: \"styleClass\",\n      appendTo: \"appendTo\",\n      autoZIndex: [2, \"autoZIndex\", \"autoZIndex\", booleanAttribute],\n      baseZIndex: [2, \"baseZIndex\", \"baseZIndex\", numberAttribute],\n      id: \"id\",\n      breakpoint: \"breakpoint\",\n      ariaLabel: \"ariaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\",\n      pressDelay: [2, \"pressDelay\", \"pressDelay\", numberAttribute]\n    },\n    outputs: {\n      onShow: \"onShow\",\n      onHide: \"onHide\"\n    },\n    features: [i0.ɵɵProvidersFeature([ContextMenuStyle]), i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature],\n    decls: 1,\n    vars: 1,\n    consts: [[\"container\", \"\"], [\"rootmenu\", \"\"], [3, \"ngClass\", \"class\", \"ngStyle\", 4, \"ngIf\"], [3, \"ngClass\", \"ngStyle\"], [3, \"itemClick\", \"menuFocus\", \"menuBlur\", \"menuKeydown\", \"itemMouseEnter\", \"root\", \"items\", \"itemTemplate\", \"menuId\", \"tabindex\", \"ariaLabel\", \"ariaLabelledBy\", \"baseZIndex\", \"autoZIndex\", \"visible\", \"focusedItemId\", \"activeItemPath\"]],\n    template: function ContextMenu_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, ContextMenu_div_0_Template, 4, 23, \"div\", 2);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.visible());\n      }\n    },\n    dependencies: [CommonModule, i1.NgClass, i1.NgIf, i1.NgStyle, ContextMenuSub, RouterModule, TooltipModule, BadgeModule, SharedModule],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('overlayAnimation', [transition(':enter', [style({\n        opacity: 0\n      }), animate('250ms')]), transition(':leave', [animate('.1s linear', style({\n        opacity: 0\n      }))])])]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ContextMenu, [{\n    type: Component,\n    args: [{\n      selector: 'p-contextMenu, p-contextmenu, p-context-menu',\n      standalone: true,\n      imports: [CommonModule, ContextMenuSub, RouterModule, TooltipModule, BadgeModule, SharedModule],\n      template: `\n        <div\n            #container\n            [attr.data-pc-section]=\"'root'\"\n            [attr.data-pc-name]=\"'contextmenu'\"\n            [attr.id]=\"id\"\n            [ngClass]=\"{ 'p-contextmenu p-component': true, 'p-contextmenu-mobile': queryMatches }\"\n            [class]=\"styleClass\"\n            [ngStyle]=\"style\"\n            [@overlayAnimation]=\"{ value: 'visible' }\"\n            (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\"\n            (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\"\n            *ngIf=\"visible()\"\n        >\n            <p-contextmenu-sub\n                #rootmenu\n                [root]=\"true\"\n                [items]=\"processedItems\"\n                [itemTemplate]=\"itemTemplate || _itemTemplate\"\n                [menuId]=\"id\"\n                [tabindex]=\"!disabled ? tabindex : -1\"\n                [ariaLabel]=\"ariaLabel\"\n                [ariaLabelledBy]=\"ariaLabelledBy\"\n                [baseZIndex]=\"baseZIndex\"\n                [autoZIndex]=\"autoZIndex\"\n                [visible]=\"submenuVisible()\"\n                [focusedItemId]=\"focused ? focusedItemId : undefined\"\n                [activeItemPath]=\"activeItemPath()\"\n                (itemClick)=\"onItemClick($event)\"\n                (menuFocus)=\"onMenuFocus($event)\"\n                (menuBlur)=\"onMenuBlur($event)\"\n                (menuKeydown)=\"onKeyDown($event)\"\n                (itemMouseEnter)=\"onItemMouseEnter($event)\"\n            />\n        </div>\n    `,\n      animations: [trigger('overlayAnimation', [transition(':enter', [style({\n        opacity: 0\n      }), animate('250ms')]), transition(':leave', [animate('.1s linear', style({\n        opacity: 0\n      }))])])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [ContextMenuStyle]\n    }]\n  }], () => [{\n    type: i5.OverlayService\n  }], {\n    model: [{\n      type: Input\n    }],\n    triggerEvent: [{\n      type: Input\n    }],\n    target: [{\n      type: Input\n    }],\n    global: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    baseZIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    id: [{\n      type: Input\n    }],\n    breakpoint: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    pressDelay: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    onShow: [{\n      type: Output\n    }],\n    onHide: [{\n      type: Output\n    }],\n    rootmenu: [{\n      type: ViewChild,\n      args: ['rootmenu']\n    }],\n    containerViewChild: [{\n      type: ViewChild,\n      args: ['container']\n    }],\n    itemTemplate: [{\n      type: ContentChild,\n      args: ['item', {\n        descendants: false\n      }]\n    }],\n    submenuIconTemplate: [{\n      type: ContentChild,\n      args: ['submenuicon', {\n        descendants: false\n      }]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass ContextMenuModule {\n  static ɵfac = function ContextMenuModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ContextMenuModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ContextMenuModule,\n    imports: [ContextMenu, SharedModule],\n    exports: [ContextMenu, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [ContextMenu, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ContextMenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [ContextMenu, SharedModule],\n      exports: [ContextMenu, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ContextMenu, ContextMenuClasses, ContextMenuModule, ContextMenuStyle, ContextMenuSub };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBA,IAAM,MAAM,CAAC,SAAS;AACtB,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,yBAAyB;AAAA,EACzB,2BAA2B;AAC7B;AACA,IAAM,MAAM,OAAO;AAAA,EACjB,2BAA2B;AAC7B;AACA,IAAM,MAAM,OAAO;AAAA,EACjB,OAAO;AACT;AACA,IAAM,MAAM,OAAO;AAAA,EACjB,OAAO;AACT;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,2BAA2B;AAAA,EAC3B,cAAc;AAChB;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,MAAM,CAAC;AAAA,EACzB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,EAAE;AAC5C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,YAAY,kBAAkB,OAAO,CAAC;AAC3D,IAAG,WAAW,WAAW,OAAO,sBAAsB,gBAAgB,CAAC;AACvE,IAAG,YAAY,MAAM,OAAO,UAAU,gBAAgB,CAAC,EAAE,mBAAmB,WAAW;AAAA,EACzF;AACF;AACA,SAAS,0EAA0E,IAAI,KAAK;AAC1F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,YAAY,kBAAkB,MAAM,CAAC,EAAE,WAAW,OAAO,YAAY,kBAAkB,WAAW,CAAC;AACnI,IAAG,YAAY,mBAAmB,MAAM,EAAE,eAAe,IAAI,EAAE,YAAY,EAAE;AAAA,EAC/E;AACF;AACA,SAAS,0EAA0E,IAAI,KAAK;AAC1F,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,mBAAmB,OAAO;AACzC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,aAAa,gBAAgB,GAAG,GAAG;AAAA,EACvE;AACF;AACA,SAAS,iFAAiF,IAAI,KAAK;AACjG,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,aAAa,OAAO,aAAa,gBAAgB,GAAM,cAAc;AACnF,IAAG,YAAY,mBAAmB,OAAO;AAAA,EAC3C;AACF;AACA,SAAS,6EAA6E,IAAI,KAAK;AAC7F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,EAAE;AAAA,EAC/B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,cAAc,OAAO,YAAY,kBAAkB,iBAAiB,CAAC,EAAE,SAAS,OAAO,YAAY,kBAAkB,OAAO,CAAC;AAAA,EAC7I;AACF;AACA,SAAS,mGAAmG,IAAI,KAAK;AACnH,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,gBAAgB;AAAA,EAClC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,4BAA4B;AAC1C,IAAG,YAAY,mBAAmB,aAAa,EAAE,eAAe,IAAI;AAAA,EACtE;AACF;AACA,SAAS,kGAAkG,IAAI,KAAK;AAAC;AACrH,SAAS,oFAAoF,IAAI,KAAK;AACpG,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,mGAAmG,GAAG,GAAG,eAAe,EAAE;AAAA,EAC7I;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,mBAAmB,aAAa,EAAE,eAAe,IAAI;AAAA,EACrE;AACF;AACA,SAAS,kFAAkF,IAAI,KAAK;AAClG,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,oGAAoG,GAAG,GAAG,kBAAkB,EAAE,EAAE,GAAG,qFAAqF,GAAG,GAAG,MAAM,EAAE;AACvP,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,YAAY,uBAAuB,CAAC,OAAO,YAAY,oBAAoB;AACzG,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,YAAY,uBAAuB,OAAO,YAAY,oBAAoB,EAAE,2BAA8B,gBAAgB,GAAG,GAAG,CAAC;AAAA,EAC5K;AACF;AACA,SAAS,mEAAmE,IAAI,KAAK;AACnF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,GAAG,2EAA2E,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,2EAA2E,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,kFAAkF,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,8EAA8E,GAAG,GAAG,WAAW,EAAE,EAAE,GAAG,mFAAmF,GAAG,GAAG,gBAAgB,EAAE;AAChjB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAkB,YAAY,CAAC;AACrC,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,UAAU,OAAO,YAAY,kBAAkB,QAAQ,CAAC,EAAE,WAAc,gBAAgB,IAAI,GAAG,CAAC;AAC9G,IAAG,YAAY,QAAQ,OAAO,YAAY,kBAAkB,KAAK,GAAM,aAAa,EAAE,eAAe,IAAI,EAAE,qBAAqB,OAAO,YAAY,kBAAkB,cAAc,CAAC,EAAE,mBAAmB,QAAQ,EAAE,YAAY,EAAE;AACjO,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,MAAM,CAAC;AAClE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,QAAQ,CAAC,EAAE,YAAY,YAAY;AAC9F,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,OAAO,CAAC;AACnE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,gBAAgB,CAAC;AAAA,EAC5D;AACF;AACA,SAAS,0EAA0E,IAAI,KAAK;AAC1F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,YAAY,kBAAkB,MAAM,CAAC,EAAE,WAAW,OAAO,YAAY,kBAAkB,WAAW,CAAC;AACnI,IAAG,YAAY,mBAAmB,MAAM,EAAE,eAAe,IAAI,EAAE,YAAY,EAAE;AAAA,EAC/E;AACF;AACA,SAAS,0EAA0E,IAAI,KAAK;AAC1F,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,mBAAmB,OAAO;AACzC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,aAAa,gBAAgB,GAAG,GAAG;AAAA,EACvE;AACF;AACA,SAAS,iFAAiF,IAAI,KAAK;AACjG,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,aAAa,OAAO,aAAa,gBAAgB,GAAM,cAAc;AACnF,IAAG,YAAY,mBAAmB,OAAO;AAAA,EAC3C;AACF;AACA,SAAS,6EAA6E,IAAI,KAAK;AAC7F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,EAAE;AAAA,EAC/B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,cAAc,OAAO,YAAY,kBAAkB,iBAAiB,CAAC,EAAE,SAAS,OAAO,YAAY,kBAAkB,OAAO,CAAC;AAAA,EAC7I;AACF;AACA,SAAS,mGAAmG,IAAI,KAAK;AACnH,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,gBAAgB;AAAA,EAClC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,4BAA4B;AAC1C,IAAG,YAAY,mBAAmB,aAAa,EAAE,eAAe,IAAI;AAAA,EACtE;AACF;AACA,SAAS,kGAAkG,IAAI,KAAK;AAAC;AACrH,SAAS,oFAAoF,IAAI,KAAK;AACpG,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,mGAAmG,GAAG,GAAG,eAAe,EAAE;AAAA,EAC7I;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,mBAAmB,aAAa,EAAE,eAAe,IAAI;AAAA,EACrE;AACF;AACA,SAAS,kFAAkF,IAAI,KAAK;AAClG,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,oGAAoG,GAAG,GAAG,kBAAkB,EAAE,EAAE,GAAG,qFAAqF,GAAG,GAAG,MAAM,EAAE;AACvP,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,YAAY,uBAAuB,CAAC,OAAO,YAAY,oBAAoB;AACzG,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,CAAC,OAAO,YAAY,uBAAuB,CAAC,OAAO,YAAY,oBAAoB,EAAE,2BAA8B,gBAAgB,GAAG,GAAG,CAAC;AAAA,EAC9K;AACF;AACA,SAAS,mEAAmE,IAAI,KAAK;AACnF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,GAAG,2EAA2E,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,2EAA2E,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,kFAAkF,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,8EAA8E,GAAG,GAAG,WAAW,EAAE,EAAE,GAAG,mFAAmF,GAAG,GAAG,gBAAgB,EAAE;AAChjB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAkB,YAAY,CAAC;AACrC,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,cAAc,OAAO,YAAY,kBAAkB,YAAY,CAAC,EAAE,eAAe,OAAO,YAAY,kBAAkB,aAAa,CAAC,EAAE,2BAA2B,OAAO,YAAY,kBAAkB,yBAAyB,KAAQ,gBAAgB,IAAI,GAAG,CAAC,EAAE,UAAU,OAAO,YAAY,kBAAkB,QAAQ,CAAC,EAAE,WAAc,gBAAgB,IAAI,KAAK,OAAO,YAAY,kBAAkB,UAAU,CAAC,CAAC,EAAE,YAAY,OAAO,YAAY,kBAAkB,UAAU,CAAC,EAAE,uBAAuB,OAAO,YAAY,kBAAkB,qBAAqB,CAAC,EAAE,oBAAoB,OAAO,YAAY,kBAAkB,kBAAkB,CAAC,EAAE,sBAAsB,OAAO,YAAY,kBAAkB,oBAAoB,CAAC,EAAE,cAAc,OAAO,YAAY,kBAAkB,YAAY,CAAC,EAAE,SAAS,OAAO,YAAY,kBAAkB,OAAO,CAAC;AAC70B,IAAG,YAAY,qBAAqB,OAAO,YAAY,kBAAkB,cAAc,CAAC,EAAE,YAAY,EAAE,EAAE,eAAe,IAAI,EAAE,mBAAmB,QAAQ;AAC1J,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,MAAM,CAAC;AAClE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,QAAQ,CAAC,EAAE,YAAY,YAAY;AAC9F,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,OAAO,CAAC;AACnE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,gBAAgB,CAAC;AAAA,EAC5D;AACF;AACA,SAAS,+DAA+D,IAAI,KAAK;AAC/E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,oEAAoE,GAAG,IAAI,KAAK,EAAE,EAAE,GAAG,oEAAoE,GAAG,IAAI,KAAK,EAAE;AAC1L,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,YAAY,kBAAkB,YAAY,CAAC;AACzE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,YAAY,CAAC;AAAA,EAC1E;AACF;AACA,SAAS,+EAA+E,IAAI,KAAK;AAAC;AAClG,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,gFAAgF,GAAG,GAAG,aAAa;AAAA,EACtH;AACF;AACA,SAAS,+DAA+D,IAAI,KAAK;AAC/E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,kEAAkE,GAAG,GAAG,MAAM,EAAE;AACjG,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,YAAY,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,iBAAiB,IAAI,CAAC;AAAA,EACrI;AACF;AACA,SAAS,oEAAoE,IAAI,KAAK;AACpF,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,qBAAqB,EAAE;AAC5C,IAAG,WAAW,aAAa,SAAS,2GAA2G,QAAQ;AACrJ,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,UAAU,KAAK,MAAM,CAAC;AAAA,IACrD,CAAC,EAAE,kBAAkB,SAAS,gHAAgH,QAAQ;AACpJ,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,SAAS,iBAAiB,KAAK,EAAE,gBAAgB,OAAO,YAAY,EAAE,UAAU,OAAO,MAAM,EAAE,WAAW,OAAO,aAAa,gBAAgB,KAAK,OAAO,YAAY,gBAAgB,CAAC,EAAE,kBAAkB,OAAO,cAAc,EAAE,iBAAiB,OAAO,aAAa,EAAE,SAAS,OAAO,QAAQ,CAAC;AAAA,EAClT;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,GAAG,CAAC,EAAE,GAAG,OAAO,EAAE;AAC7C,IAAG,WAAW,SAAS,SAAS,qEAAqE,QAAQ;AAC3G,MAAG,cAAc,GAAG;AACpB,YAAM,mBAAsB,cAAc,EAAE;AAC5C,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,YAAY,QAAQ,gBAAgB,CAAC;AAAA,IACpE,CAAC,EAAE,cAAc,SAAS,0EAA0E,QAAQ;AAC1G,MAAG,cAAc,GAAG;AACpB,YAAM,mBAAsB,cAAc,EAAE;AAC5C,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,iBAAiB;AAAA,QAC5C;AAAA,QACA,eAAe;AAAA,MACjB,CAAC,CAAC;AAAA,IACJ,CAAC;AACD,IAAG,WAAW,GAAG,gEAAgE,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,gEAAgE,GAAG,GAAG,gBAAgB,EAAE;AACtM,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,qEAAqE,GAAG,GAAG,qBAAqB,EAAE;AACnH,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,mBAAmB,OAAO;AAChC,UAAM,YAAY,OAAO;AACzB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,YAAY,kBAAkB,YAAY,CAAC;AAChE,IAAG,WAAW,WAAW,OAAO,YAAY,kBAAkB,OAAO,CAAC,EAAE,WAAW,OAAO,aAAa,gBAAgB,CAAC,EAAE,kBAAkB,OAAO,YAAY,kBAAkB,gBAAgB,CAAC;AAClM,IAAG,YAAY,MAAM,OAAO,UAAU,gBAAgB,CAAC,EAAE,mBAAmB,UAAU,EAAE,oBAAoB,OAAO,aAAa,gBAAgB,CAAC,EAAE,kBAAkB,OAAO,cAAc,gBAAgB,CAAC,EAAE,mBAAmB,OAAO,eAAe,gBAAgB,CAAC,EAAE,cAAc,OAAO,aAAa,gBAAgB,CAAC,EAAE,iBAAiB,OAAO,eAAe,gBAAgB,KAAK,MAAS,EAAE,iBAAiB,OAAO,YAAY,gBAAgB,KAAK,CAAC,OAAO,YAAY,kBAAkB,IAAI,IAAI,SAAS,MAAS,EAAE,iBAAiB,OAAO,YAAY,gBAAgB,IAAI,OAAO,aAAa,gBAAgB,IAAI,MAAS,EAAE,cAAc,OAAO,QAAQ,CAAC,EAAE,gBAAgB,OAAO,eAAe,CAAC,EAAE,iBAAiB,OAAO,gBAAgB,SAAS,CAAC;AACtuB,IAAG,UAAU,CAAC;AACd,IAAG,YAAY,mBAAmB,SAAS;AAC3C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,YAAY;AAC1C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY;AACzC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,cAAc,gBAAgB,KAAK,OAAO,YAAY,gBAAgB,CAAC;AAAA,EACtG;AACF;AACA,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,iDAAiD,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,iDAAiD,GAAG,IAAI,MAAM,CAAC;AAAA,EACrJ;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAmB,IAAI;AAC7B,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,QAAQ,OAAO,cAAc,gBAAgB,KAAK,OAAO,YAAY,kBAAkB,WAAW,CAAC;AACjH,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,cAAc,gBAAgB,KAAK,CAAC,OAAO,YAAY,kBAAkB,WAAW,CAAC;AAAA,EACpH;AACF;AACA,SAAS,6BAA6B,IAAI,KAAK;AAC7C,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,GAAG,CAAC;AAC/B,IAAG,WAAW,2BAA2B,SAAS,4EAA4E,QAAQ;AACpI,MAAG,cAAc,GAAG;AACpB,YAAM,aAAgB,YAAY,CAAC;AACnC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,QAAQ,QAAQ,UAAU,CAAC;AAAA,IAC1D,CAAC,EAAE,WAAW,SAAS,mDAAmD,QAAQ;AAChF,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,KAAK,MAAM,CAAC;AAAA,IACvD,CAAC,EAAE,SAAS,SAAS,iDAAiD,QAAQ;AAC5E,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,UAAU,KAAK,MAAM,CAAC;AAAA,IACrD,CAAC,EAAE,QAAQ,SAAS,gDAAgD,QAAQ;AAC1E,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,SAAS,KAAK,MAAM,CAAC;AAAA,IACpD,CAAC;AACD,IAAG,WAAW,GAAG,4CAA4C,GAAG,GAAG,eAAe,CAAC;AACnF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAc,gBAAgB,IAAI,KAAK,CAAC,OAAO,MAAM,OAAO,IAAI,CAAC,EAAE,qBAAqB,OAAO,OAAO,EAAE,YAAY,OAAO,QAAQ;AACjJ,IAAG,YAAY,MAAM,OAAO,SAAS,OAAO,EAAE,cAAc,OAAO,SAAS,EAAE,mBAAmB,OAAO,cAAc,EAAE,yBAAyB,OAAO,aAAa,EAAE,oBAAoB,UAAU,EAAE,mBAAmB,MAAM;AAChO,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,OAAO,KAAK;AAAA,EACvC;AACF;AACA,IAAM,MAAM,CAAC,MAAM;AACnB,IAAM,MAAM,CAAC,aAAa;AAC1B,IAAM,MAAM,CAAC,UAAU;AACvB,IAAM,OAAO,CAAC,WAAW;AACzB,IAAM,OAAO,SAAO;AAAA,EAClB,6BAA6B;AAAA,EAC7B,wBAAwB;AAC1B;AACA,IAAM,OAAO,OAAO;AAAA,EAClB,OAAO;AACT;AACA,SAAS,2BAA2B,IAAI,KAAK;AAC3C,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,IAAG,WAAW,2BAA2B,SAAS,2EAA2E,QAAQ;AACnI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,wBAAwB,MAAM,CAAC;AAAA,IAC9D,CAAC,EAAE,0BAA0B,SAAS,0EAA0E,QAAQ;AACtH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,sBAAsB,MAAM,CAAC;AAAA,IAC5D,CAAC;AACD,IAAG,eAAe,GAAG,qBAAqB,GAAG,CAAC;AAC9C,IAAG,WAAW,aAAa,SAAS,kEAAkE,QAAQ;AAC5G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,MAAM,CAAC;AAAA,IAClD,CAAC,EAAE,aAAa,SAAS,kEAAkE,QAAQ;AACjG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,MAAM,CAAC;AAAA,IAClD,CAAC,EAAE,YAAY,SAAS,iEAAiE,QAAQ;AAC/F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,WAAW,MAAM,CAAC;AAAA,IACjD,CAAC,EAAE,eAAe,SAAS,oEAAoE,QAAQ;AACrG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,UAAU,MAAM,CAAC;AAAA,IAChD,CAAC,EAAE,kBAAkB,SAAS,uEAAuE,QAAQ;AAC3G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC;AACD,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,UAAU;AAC/B,IAAG,WAAW,WAAc,gBAAgB,IAAI,MAAM,OAAO,YAAY,CAAC,EAAE,WAAW,OAAO,KAAK,EAAE,qBAAwB,gBAAgB,IAAI,IAAI,CAAC;AACtJ,IAAG,YAAY,mBAAmB,MAAM,EAAE,gBAAgB,aAAa,EAAE,MAAM,OAAO,EAAE;AACxF,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,IAAI,EAAE,SAAS,OAAO,cAAc,EAAE,gBAAgB,OAAO,gBAAgB,OAAO,aAAa,EAAE,UAAU,OAAO,EAAE,EAAE,YAAY,CAAC,OAAO,WAAW,OAAO,WAAW,EAAE,EAAE,aAAa,OAAO,SAAS,EAAE,kBAAkB,OAAO,cAAc,EAAE,cAAc,OAAO,UAAU,EAAE,cAAc,OAAO,UAAU,EAAE,WAAW,OAAO,eAAe,CAAC,EAAE,iBAAiB,OAAO,UAAU,OAAO,gBAAgB,MAAS,EAAE,kBAAkB,OAAO,eAAe,CAAC;AAAA,EAChe;AACF;AACA,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA;AAAA,kBAGY,GAAG,wBAAwB,CAAC;AAAA,aACjC,GAAG,mBAAmB,CAAC;AAAA,wBACZ,GAAG,0BAA0B,CAAC;AAAA,qBACjC,GAAG,2BAA2B,CAAC;AAAA,kBAClC,GAAG,oBAAoB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAO3B,GAAG,0BAA0B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,WAKlC,GAAG,sBAAsB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBASnB,GAAG,wBAAwB,CAAC;AAAA,aACjC,GAAG,mBAAmB,CAAC;AAAA,wBACZ,GAAG,0BAA0B,CAAC;AAAA,qBACjC,GAAG,2BAA2B,CAAC;AAAA,kBAClC,GAAG,oBAAoB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,6BAQb,GAAG,iCAAiC,CAAC,WAAW,GAAG,iCAAiC,CAAC;AAAA,qBAC7F,GAAG,gCAAgC,CAAC;AAAA,aAC5C,GAAG,wBAAwB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAW1B,GAAG,0BAA0B,CAAC;AAAA,WAClC,GAAG,sBAAsB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aASxB,GAAG,6BAA6B,CAAC;AAAA;AAAA;AAAA;AAAA,aAIjC,GAAG,gCAAgC,CAAC;AAAA;AAAA,iBAEhC,GAAG,+BAA+B,CAAC;AAAA,aACvC,GAAG,+BAA+B,CAAC;AAAA,cAClC,GAAG,+BAA+B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aASpC,GAAG,8BAA8B,CAAC;AAAA,kBAC7B,GAAG,mCAAmC,CAAC;AAAA;AAAA;AAAA;AAAA,aAI5C,GAAG,mCAAmC,CAAC;AAAA;AAAA;AAAA;AAAA,aAIvC,GAAG,sCAAsC,CAAC;AAAA;AAAA;AAAA;AAAA,aAI1C,GAAG,8BAA8B,CAAC;AAAA,kBAC7B,GAAG,mCAAmC,CAAC;AAAA;AAAA;AAAA;AAAA,aAI5C,GAAG,mCAAmC,CAAC;AAAA;AAAA;AAAA;AAAA,aAIvC,GAAG,sCAAsC,CAAC;AAAA;AAAA;AAAA;AAAA,aAI1C,GAAG,+BAA+B,CAAC;AAAA,kBAC9B,GAAG,oCAAoC,CAAC;AAAA;AAAA;AAAA;AAAA,aAI7C,GAAG,oCAAoC,CAAC;AAAA;AAAA;AAAA;AAAA,aAIxC,GAAG,uCAAuC,CAAC;AAAA;AAAA;AAAA;AAAA,6BAI3B,GAAG,oCAAoC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAgBjD,GAAG,kCAAkC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuB1D,IAAM,UAAU;AAAA,EACd,MAAM;AAAA,EACN,UAAU;AAAA,EACV,MAAM,CAAC;AAAA,IACL;AAAA,IACA;AAAA,EACF,MAAM,CAAC,sBAAsB;AAAA,IAC3B,6BAA6B,SAAS,aAAa,aAAa;AAAA,IAChE,WAAW,SAAS,cAAc,aAAa;AAAA,IAC/C,cAAc,SAAS,eAAe,aAAa;AAAA,EACrD,CAAC;AAAA,EACD,aAAa;AAAA,EACb,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS;AAAA,EACT,WAAW;AACb;AACA,IAAM,mBAAN,MAAM,0BAAyB,UAAU;AAAA,EACvC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,yBAAyB,mBAAmB;AAC1D,cAAQ,kCAAkC,gCAAmC,sBAAsB,iBAAgB,IAAI,qBAAqB,iBAAgB;AAAA,IAC9J;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,kBAAiB;AAAA,EAC5B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAWH,IAAI;AAAA,CACH,SAAUA,qBAAoB;AAI7B,EAAAA,oBAAmB,MAAM,IAAI;AAI7B,EAAAA,oBAAmB,UAAU,IAAI;AAIjC,EAAAA,oBAAmB,MAAM,IAAI;AAI7B,EAAAA,oBAAmB,aAAa,IAAI;AAIpC,EAAAA,oBAAmB,UAAU,IAAI;AAIjC,EAAAA,oBAAmB,UAAU,IAAI;AAIjC,EAAAA,oBAAmB,WAAW,IAAI;AAIlC,EAAAA,oBAAmB,aAAa,IAAI;AAIpC,EAAAA,oBAAmB,SAAS,IAAI;AAIhC,EAAAA,oBAAmB,WAAW,IAAI;AACpC,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAClD,IAAM,iBAAN,MAAM,wBAAuB,cAAc;AAAA,EACzC;AAAA,EACA,UAAU;AAAA,EACV;AAAA,EACA;AAAA,EACA,OAAO;AAAA,EACP,aAAa;AAAA,EACb,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA,WAAW;AAAA,EACX,YAAY,IAAI,aAAa;AAAA,EAC7B,iBAAiB,IAAI,aAAa;AAAA,EAClC,YAAY,IAAI,aAAa;AAAA,EAC7B,WAAW,IAAI,aAAa;AAAA,EAC5B,cAAc,IAAI,aAAa;AAAA,EAC/B;AAAA,EACA,YAAY,aAAa;AACvB,UAAM;AACN,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,YAAY,eAAe,MAAM,SAAS,MAAM;AAC9C,WAAO,iBAAiB,cAAc,OAAO,QAAQ,cAAc,KAAK,IAAI,GAAG,MAAM,IAAI;AAAA,EAC3F;AAAA,EACA,UAAU,eAAe;AACvB,WAAO,cAAc,QAAQ,cAAc,MAAM,KAAK,cAAc,KAAK,KAAK,GAAG,KAAK,MAAM,IAAI,cAAc,GAAG;AAAA,EACnH;AAAA,EACA,WAAW,eAAe;AACxB,WAAO,KAAK,UAAU,aAAa;AAAA,EACrC;AAAA,EACA,aAAa,eAAe;AAC1B,WAAO,iCACF,KAAK,YAAY,eAAe,OAAO,IADrC;AAAA,MAEL,sBAAsB;AAAA,MACtB,6BAA6B,KAAK,aAAa,aAAa;AAAA,MAC5D,WAAW,KAAK,cAAc,aAAa;AAAA,MAC3C,cAAc,KAAK,eAAe,aAAa;AAAA,IACjD;AAAA,EACF;AAAA,EACA,aAAa,eAAe;AAC1B,WAAO,KAAK,YAAY,eAAe,OAAO;AAAA,EAChD;AAAA,EACA,sBAAsB,eAAe;AACnC,WAAO,iCACF,KAAK,YAAY,eAAe,OAAO,IADrC;AAAA,MAEL,2BAA2B;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,MAAM,OAAO,mBAAiB,KAAK,cAAc,aAAa,KAAK,CAAC,KAAK,YAAY,eAAe,WAAW,CAAC,EAAE;AAAA,EAChI;AAAA,EACA,gBAAgB,OAAO;AACrB,WAAO,QAAQ,KAAK,MAAM,MAAM,GAAG,KAAK,EAAE,OAAO,mBAAiB,KAAK,cAAc,aAAa,KAAK,KAAK,YAAY,eAAe,WAAW,CAAC,EAAE,SAAS;AAAA,EAChK;AAAA,EACA,cAAc,eAAe;AAC3B,WAAO,KAAK,YAAY,eAAe,SAAS,MAAM;AAAA,EACxD;AAAA,EACA,aAAa,eAAe;AAC1B,QAAI,KAAK,gBAAgB;AACvB,aAAO,KAAK,eAAe,KAAK,UAAQ,KAAK,QAAQ,cAAc,GAAG;AAAA,IACxE;AAAA,EACF;AAAA,EACA,eAAe,eAAe;AAC5B,WAAO,KAAK,YAAY,eAAe,UAAU;AAAA,EACnD;AAAA,EACA,cAAc,eAAe;AAC3B,WAAO,KAAK,kBAAkB,KAAK,UAAU,aAAa;AAAA,EAC5D;AAAA,EACA,YAAY,eAAe;AACzB,WAAO,WAAW,cAAc,KAAK;AAAA,EACvC;AAAA,EACA,iBAAiB,OAAO;AACtB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,SAAK,eAAe,KAAK;AAAA,MACvB,eAAe;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,YAAY,OAAO,eAAe;AAChC,SAAK,YAAY,eAAe,WAAW;AAAA,MACzC,eAAe;AAAA,MACf,MAAM,cAAc;AAAA,IACtB,CAAC;AACD,SAAK,UAAU,KAAK;AAAA,MAClB,eAAe;AAAA,MACf;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAAA,EACA,QAAQ,OAAO,SAAS;AACtB,QAAI,MAAM,cAAc,UAAU,MAAM,SAAS;AAC/C,YAAMC,WAAU,MAAM;AACtB,WAAK,SAASA,QAAO;AAAA,IACvB;AAAA,EACF;AAAA,EACA,SAAS,SAAS;AAChB,UAAM,aAAa,QAAQ,cAAc;AACzC,UAAM,kBAAkB,UAAU,QAAQ,cAAc,aAAa;AACrE,UAAM,WAAW,YAAY;AAC7B,UAAM,eAAe,QAAQ,eAAe,QAAQ,cAAc,2BAA2B,OAAO;AACpG,UAAM,iBAAiB,cAAc,WAAW,SAAS,CAAC,CAAC;AAC3D,YAAQ,MAAM,MAAM;AACpB,QAAI,SAAS,gBAAgB,MAAM,EAAE,IAAI,iBAAiB,eAAe,SAAS,QAAQ,wBAAwB,GAAG;AACnH,cAAQ,MAAM,OAAO,KAAK,eAAe;AAAA,IAC3C,OAAO;AACL,cAAQ,MAAM,OAAO,iBAAiB;AAAA,IACxC;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAmB,kBAAkB,WAAW,MAAM,WAAW,CAAC,CAAC;AAAA,EACtG;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,kBAAkB,GAAG,CAAC,mBAAmB,CAAC;AAAA,IACvD,WAAW,SAAS,qBAAqB,IAAI,KAAK;AAChD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AAAA,MACzE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS,CAAC,GAAG,WAAW,WAAW,gBAAgB;AAAA,MACnD,OAAO;AAAA,MACP,cAAc;AAAA,MACd,MAAM,CAAC,GAAG,QAAQ,QAAQ,gBAAgB;AAAA,MAC1C,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,YAAY,CAAC,GAAG,cAAc,cAAc,eAAe;AAAA,MAC3D,OAAO,CAAC,GAAG,SAAS,SAAS,gBAAgB;AAAA,MAC7C,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,OAAO,CAAC,GAAG,SAAS,SAAS,eAAe;AAAA,MAC5C,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,UAAU,CAAC,GAAG,YAAY,YAAY,eAAe;AAAA,IACvD;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,UAAU,CAAI,0BAA6B,0BAA0B;AAAA,IACrE,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC,QAAQ,QAAQ,GAAG,WAAW,YAAY,WAAW,SAAS,QAAQ,GAAG,MAAM,GAAG,CAAC,QAAQ,QAAQ,GAAG,WAAW,SAAS,QAAQ,WAAW,UAAU,GAAG,CAAC,SAAS,IAAI,GAAG,SAAS,GAAG,CAAC,QAAQ,aAAa,GAAG,SAAS,WAAW,GAAG,MAAM,GAAG,CAAC,QAAQ,YAAY,YAAY,IAAI,GAAG,WAAW,WAAW,SAAS,kBAAkB,GAAG,MAAM,GAAG,CAAC,QAAQ,aAAa,GAAG,SAAS,GAAG,CAAC,QAAQ,YAAY,YAAY,IAAI,GAAG,WAAW,WAAW,gBAAgB,GAAG,CAAC,GAAG,8BAA8B,GAAG,SAAS,YAAY,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,SAAS,gBAAgB,UAAU,WAAW,kBAAkB,iBAAiB,SAAS,aAAa,kBAAkB,GAAG,MAAM,GAAG,CAAC,WAAW,IAAI,GAAG,UAAU,WAAW,GAAG,MAAM,GAAG,CAAC,WAAW,IAAI,GAAG,cAAc,eAAe,2BAA2B,UAAU,WAAW,YAAY,uBAAuB,oBAAoB,sBAAsB,cAAc,SAAS,GAAG,MAAM,GAAG,CAAC,WAAW,IAAI,GAAG,UAAU,SAAS,GAAG,CAAC,SAAS,2BAA2B,GAAG,WAAW,WAAW,GAAG,MAAM,GAAG,CAAC,SAAS,4BAA4B,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,cAAc,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,2BAA2B,GAAG,WAAW,SAAS,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,GAAG,4BAA4B,GAAG,WAAW,GAAG,CAAC,GAAG,cAAc,OAAO,GAAG,CAAC,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,mBAAmB,aAAa,GAAG,CAAC,WAAW,IAAI,GAAG,cAAc,eAAe,2BAA2B,UAAU,WAAW,YAAY,uBAAuB,oBAAoB,sBAAsB,cAAc,OAAO,GAAG,CAAC,GAAG,aAAa,kBAAkB,SAAS,gBAAgB,UAAU,WAAW,kBAAkB,iBAAiB,OAAO,CAAC;AAAA,IACtxD,UAAU,SAAS,wBAAwB,IAAI,KAAK;AAClD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,8BAA8B,GAAG,IAAI,MAAM,CAAC;AAAA,MAC/D;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,QAAQ,IAAI,OAAO,OAAO,IAAI,OAAO;AAAA,MACrD;AAAA,IACF;AAAA,IACA,cAAc,CAAC,iBAAgB,cAAiB,SAAY,SAAY,MAAS,kBAAqB,SAAS,cAAiB,YAAY,QAAQ,eAAkB,SAAS,gBAAgB,aAAgB,OAAO,YAAY;AAAA,IAClO,eAAe;AAAA,IACf,MAAM;AAAA,MACJ,WAAW,CAAC,QAAQ,oBAAoB,CAAC,WAAW,UAAU,CAAC,MAAM;AAAA,QACnE,SAAS;AAAA,MACX,CAAC,CAAC,CAAC,GAAG,WAAW,UAAU,CAAC,MAAM;AAAA,QAChC,SAAS;AAAA,MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,IACR;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,cAAc,QAAQ,eAAe,gBAAgB,aAAa,YAAY;AAAA,MACtG,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA4JV,YAAY,CAAC,QAAQ,oBAAoB,CAAC,WAAW,UAAU,CAAC,MAAM;AAAA,QACpE,SAAS;AAAA,MACX,CAAC,CAAC,CAAC,GAAG,WAAW,UAAU,CAAC,MAAM;AAAA,QAChC,SAAS;AAAA,MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,MACN,eAAe,kBAAkB;AAAA,IACnC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW,MAAM,WAAW,CAAC;AAAA,IACtC,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,cAAN,MAAM,qBAAoB,cAAc;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,MAAM,OAAO;AACf,SAAK,SAAS;AACd,SAAK,kBAAkB,KAAK,qBAAqB,KAAK,UAAU,CAAC,CAAC;AAAA,EACpE;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,SAAS,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1B,SAAS,IAAI,aAAa;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAU,OAAO,KAAK;AAAA,EACtB;AAAA,EACA;AAAA,EACA,UAAU;AAAA,EACV,iBAAiB,OAAO,CAAC,CAAC;AAAA,EAC1B,kBAAkB,OAAO;AAAA,IACvB,OAAO;AAAA,IACP,OAAO;AAAA,IACP,WAAW;AAAA,IACX,MAAM;AAAA,EACR,CAAC;AAAA,EACD,iBAAiB,OAAO,KAAK;AAAA,EAC7B,cAAc;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAkB,OAAO,gBAAgB;AAAA,EACzC,IAAI,eAAe;AACjB,UAAM,gBAAgB,KAAK,eAAe,EAAE,KAAK,OAAK,EAAE,QAAQ,KAAK,gBAAgB,EAAE,SAAS;AAChG,WAAO,gBAAgB,cAAc,QAAQ,KAAK;AAAA,EACpD;AAAA,EACA,IAAI,iBAAiB;AACnB,QAAI,CAAC,KAAK,mBAAmB,CAAC,KAAK,gBAAgB,QAAQ;AACzD,WAAK,kBAAkB,KAAK,qBAAqB,KAAK,SAAS,CAAC,CAAC;AAAA,IACnE;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB;AAClB,UAAM,cAAc,KAAK,gBAAgB;AACzC,WAAO,YAAY,QAAQ,YAAY,MAAM,KAAK,YAAY,KAAK,KAAK,YAAY,UAAU,KAAK,GAAG,KAAK,EAAE,GAAG,WAAW,YAAY,SAAS,IAAI,MAAM,YAAY,YAAY,EAAE,IAAI,YAAY,KAAK,KAAK;AAAA,EAChN;AAAA,EACA,YAAY,gBAAgB;AAC1B,UAAM;AACN,SAAK,iBAAiB;AACtB,WAAO,MAAM;AACX,YAAM,OAAO,KAAK,eAAe;AACjC,UAAI,WAAW,IAAI,GAAG;AACpB,aAAK,oBAAoB;AAAA,MAC3B,WAAW,CAAC,KAAK,QAAQ,GAAG;AAC1B,aAAK,sBAAsB;AAAA,MAC7B;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AACT,UAAM,SAAS;AACf,SAAK,KAAK,KAAK,MAAM,KAAK,QAAQ;AAClC,SAAK,uBAAuB;AAC5B,SAAK,yBAAyB;AAAA,EAChC;AAAA,EACA,WAAW;AACT,WAAO,MAAM,KAAK,UAAU;AAAA,EAC9B;AAAA,EACA,2BAA2B;AACzB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,UAAI,CAAC,KAAK,sBAAsB;AAC9B,YAAI,CAAC,KAAK,SAAS,GAAG;AACpB,cAAI,KAAK,QAAQ;AACf,iBAAK,uBAAuB,KAAK,SAAS,OAAO,KAAK,UAAU,KAAK,cAAc,WAAS;AAC1F,mBAAK,KAAK,KAAK;AAAA,YACjB,CAAC;AAAA,UACH,WAAW,KAAK,QAAQ;AACtB,iBAAK,uBAAuB,KAAK,SAAS,OAAO,KAAK,QAAQ,KAAK,cAAc,WAAS;AACxF,mBAAK,KAAK,KAAK;AAAA,YACjB,CAAC;AAAA,UACH;AAAA,QACF,OAAO;AACL,cAAI,KAAK,QAAQ;AACf,iBAAK,uBAAuB,KAAK,SAAS,OAAO,KAAK,UAAU,cAAc,KAAK,aAAa,KAAK,IAAI,CAAC;AAC1G,iBAAK,mBAAmB,KAAK,SAAS,OAAO,KAAK,UAAU,YAAY,KAAK,WAAW,KAAK,IAAI,CAAC;AAAA,UACpG,WAAW,KAAK,QAAQ;AACtB,iBAAK,uBAAuB,KAAK,SAAS,OAAO,KAAK,QAAQ,cAAc,KAAK,aAAa,KAAK,IAAI,CAAC;AACxG,iBAAK,mBAAmB,KAAK,SAAS,OAAO,KAAK,QAAQ,YAAY,KAAK,WAAW,KAAK,IAAI,CAAC;AAAA,UAClG;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,UAAI,CAAC,KAAK,uBAAuB;AAC/B,cAAM,iBAAiB,KAAK,KAAK,KAAK,GAAG,cAAc,gBAAgB;AACvE,aAAK,wBAAwB,KAAK,SAAS,OAAO,gBAAgB,SAAS,WAAS;AAClF,cAAI,KAAK,mBAAmB,cAAc,gBAAgB,KAAK,iBAAiB,KAAK,KAAK,CAAC,MAAM,WAAW,MAAM,WAAW,KAAK,KAAK,iBAAiB,SAAS;AAC/J,iBAAK,KAAK;AAAA,UACZ;AAAA,QACF,CAAC;AACD,aAAK,0BAA0B,KAAK,SAAS,OAAO,gBAAgB,KAAK,cAAc,WAAS;AAC9F,cAAI,KAAK,mBAAmB,cAAc,gBAAgB,KAAK,iBAAiB,KAAK,GAAG;AACtF,iBAAK,KAAK;AAAA,UACZ;AAAA,QACF,CAAC;AAAA,MACH;AACA,UAAI,CAAC,KAAK,gBAAgB;AACxB,aAAK,iBAAiB,KAAK,SAAS,OAAO,KAAK,SAAS,aAAa,UAAU,WAAS;AACvF,eAAK,KAAK;AAAA,QACZ,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,qBAAqB;AACnB,SAAK,WAAW,QAAQ,UAAQ;AAC9B,cAAQ,KAAK,QAAQ,GAAG;AAAA,QACtB,KAAK;AACH,eAAK,uBAAuB,KAAK;AACjC;AAAA,QACF,KAAK;AACH,eAAK,gBAAgB,KAAK;AAC1B;AAAA,QACF;AACE,eAAK,gBAAgB,KAAK;AAC1B;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB,OAAO,QAAQ,GAAG,SAAS,CAAC,GAAG,YAAY,IAAI;AAClE,UAAM,iBAAiB,CAAC;AACxB,aAAS,MAAM,QAAQ,CAAC,MAAM,UAAU;AACtC,YAAM,OAAO,cAAc,KAAK,YAAY,MAAM,MAAM;AACxD,YAAM,UAAU;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,cAAQ,OAAO,IAAI,KAAK,qBAAqB,KAAK,OAAO,QAAQ,GAAG,SAAS,GAAG;AAChF,qBAAe,KAAK,OAAO;AAAA,IAC7B,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,yBAAyB;AACvB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,UAAI,CAAC,KAAK,oBAAoB;AAC5B,cAAM,QAAQ,OAAO,WAAW,eAAe,KAAK,UAAU,GAAG;AACjE,aAAK,QAAQ;AACb,aAAK,eAAe,MAAM;AAC1B,aAAK,qBAAqB,MAAM;AAC9B,eAAK,eAAe,MAAM;AAAA,QAC5B;AACA,cAAM,iBAAiB,UAAU,KAAK,kBAAkB;AAAA,MAC1D;AAAA,IACF;AAAA,EACF;AAAA,EACA,2BAA2B;AACzB,QAAI,KAAK,oBAAoB;AAC3B,WAAK,MAAM,oBAAoB,UAAU,KAAK,kBAAkB;AAChE,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,YAAY,MAAM,MAAM;AACtB,WAAO,OAAO,QAAQ,KAAK,IAAI,CAAC,IAAI;AAAA,EACtC;AAAA,EACA,uBAAuB,eAAe;AACpC,WAAO,gBAAgB,KAAK,aAAa,cAAc,IAAI,IAAI;AAAA,EACjE;AAAA,EACA,aAAa,MAAM;AACjB,WAAO,KAAK,YAAY,MAAM,OAAO;AAAA,EACvC;AAAA,EACA,qBAAqB,eAAe;AAClC,WAAO,iBAAiB,WAAW,cAAc,KAAK;AAAA,EACxD;AAAA,EACA,WAAW,eAAe;AACxB,WAAO,KAAK,eAAe,EAAE,KAAK,OAAK,EAAE,QAAQ,cAAc,GAAG;AAAA,EACpE;AAAA,EACA,oBAAoB,eAAe;AACjC,WAAO,KAAK,YAAY,aAAa,KAAK,KAAK,WAAW,aAAa;AAAA,EACzE;AAAA,EACA,YAAY,eAAe;AACzB,WAAO,CAAC,CAAC,iBAAiB,CAAC,KAAK,eAAe,cAAc,IAAI,KAAK,CAAC,KAAK,gBAAgB,cAAc,IAAI;AAAA,EAChH;AAAA,EACA,eAAe,MAAM;AACnB,WAAO,KAAK,YAAY,MAAM,UAAU;AAAA,EAC1C;AAAA,EACA,gBAAgB,MAAM;AACpB,WAAO,KAAK,YAAY,MAAM,WAAW;AAAA,EAC3C;AAAA,EACA,cAAc,eAAe;AAC3B,WAAO,KAAK,YAAY,aAAa,KAAK,KAAK,uBAAuB,aAAa,EAAE,kBAAkB,EAAE,WAAW,KAAK,YAAY,kBAAkB,CAAC;AAAA,EAC1J;AAAA,EACA,sBAAsB,eAAe;AACnC,WAAO,iBAAiB,WAAW,cAAc,KAAK;AAAA,EACxD;AAAA,EACA,YAAY,OAAO;AACjB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,UAAU,KAAK,qBAAqB,aAAa;AACvD,UAAM,WAAW,KAAK,WAAW,aAAa;AAC9C,QAAI,UAAU;AACZ,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,WAAK,eAAe,IAAI,KAAK,eAAe,EAAE,OAAO,OAAK,QAAQ,EAAE,OAAO,IAAI,WAAW,EAAE,GAAG,CAAC,CAAC;AACjG,WAAK,gBAAgB,IAAI;AAAA,QACvB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,YAAM,KAAK,SAAS,iBAAiB,aAAa;AAAA,IACpD,OAAO;AACL,gBAAU,KAAK,aAAa,KAAK,IAAI,KAAK,KAAK;AAAA,IACjD;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO;AACtB,SAAK,aAAa,OAAO,OAAO;AAAA,EAClC;AAAA,EACA,UAAU,OAAO;AACf,UAAM,UAAU,MAAM,WAAW,MAAM;AACvC,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK;AACH,aAAK,eAAe,KAAK;AACzB;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK;AACvB;AAAA,MACF,KAAK;AACH,aAAK,eAAe,KAAK;AACzB;AAAA,MACF,KAAK;AACH,aAAK,gBAAgB,KAAK;AAC1B;AAAA,MACF,KAAK;AACH,aAAK,UAAU,KAAK;AACpB;AAAA,MACF,KAAK;AACH,aAAK,SAAS,KAAK;AACnB;AAAA,MACF,KAAK;AACH,aAAK,WAAW,KAAK;AACrB;AAAA,MACF,KAAK;AACH,aAAK,WAAW,KAAK;AACrB;AAAA,MACF,KAAK;AACH,aAAK,YAAY,KAAK;AACtB;AAAA,MACF,KAAK;AACH,aAAK,SAAS,KAAK;AACnB;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAEH;AAAA,MACF;AACE,YAAI,CAAC,WAAW,qBAAqB,MAAM,GAAG,GAAG;AAC/C,eAAK,YAAY,OAAO,MAAM,GAAG;AAAA,QACnC;AACA;AAAA,IACJ;AAAA,EACF;AAAA,EACA,eAAe,OAAO;AACpB,UAAM,YAAY,KAAK,gBAAgB,EAAE,UAAU,KAAK,KAAK,kBAAkB,KAAK,gBAAgB,EAAE,KAAK,IAAI,KAAK,0BAA0B;AAC9I,SAAK,uBAAuB,OAAO,SAAS;AAC5C,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,gBAAgB,OAAO;AACrB,UAAM,gBAAgB,KAAK,aAAa,KAAK,gBAAgB,EAAE,KAAK;AACpE,UAAM,UAAU,KAAK,sBAAsB,aAAa;AACxD,QAAI,SAAS;AACX,WAAK,aAAa;AAAA,QAChB,eAAe;AAAA,QACf;AAAA,MACF,CAAC;AACD,WAAK,gBAAgB,IAAI;AAAA,QACvB,OAAO;AAAA,QACP,WAAW,cAAc;AAAA,QACzB,MAAM,cAAc;AAAA,MACtB,CAAC;AACD,WAAK,cAAc;AACnB,WAAK,eAAe,KAAK;AAAA,IAC3B;AACA,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,aAAa,OAAO;AAClB,QAAI,MAAM,QAAQ;AAChB,UAAI,KAAK,gBAAgB,EAAE,UAAU,IAAI;AACvC,cAAM,gBAAgB,KAAK,aAAa,KAAK,gBAAgB,EAAE,KAAK;AACpE,cAAM,UAAU,KAAK,sBAAsB,aAAa;AACxD,SAAC,WAAW,KAAK,aAAa;AAAA,UAC5B,eAAe;AAAA,UACf;AAAA,QACF,CAAC;AAAA,MACH;AACA,WAAK,KAAK;AACV,YAAM,eAAe;AAAA,IACvB,OAAO;AACL,YAAM,YAAY,KAAK,gBAAgB,EAAE,UAAU,KAAK,KAAK,kBAAkB,KAAK,gBAAgB,EAAE,KAAK,IAAI,KAAK,yBAAyB;AAC7I,WAAK,uBAAuB,OAAO,SAAS;AAC5C,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA,EACA,eAAe,OAAO;AACpB,UAAM,gBAAgB,KAAK,aAAa,KAAK,gBAAgB,EAAE,KAAK;AACpE,UAAM,aAAa,KAAK,eAAe,EAAE,KAAK,OAAK,EAAE,QAAQ,cAAc,SAAS;AACpF,UAAM,OAAO,QAAQ,cAAc,MAAM;AACzC,QAAI,CAAC,MAAM;AACT,WAAK,gBAAgB,IAAI;AAAA,QACvB,OAAO;AAAA,QACP,WAAW,aAAa,WAAW,YAAY;AAAA,QAC/C,MAAM,cAAc;AAAA,MACtB,CAAC;AACD,WAAK,cAAc;AACnB,WAAK,eAAe,KAAK;AAAA,IAC3B;AACA,UAAM,iBAAiB,KAAK,eAAe,EAAE,OAAO,OAAK,EAAE,cAAc,KAAK,gBAAgB,EAAE,SAAS;AACzG,SAAK,eAAe,IAAI,cAAc;AACtC,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,UAAU,OAAO;AACf,SAAK,uBAAuB,OAAO,KAAK,mBAAmB,CAAC;AAC5D,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,SAAS,OAAO;AACd,SAAK,uBAAuB,OAAO,KAAK,kBAAkB,CAAC;AAC3D,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,WAAW,KAAK;AAAA,EACvB;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,KAAK;AACV,UAAM,gBAAgB,KAAK,gBAAgB,KAAK,0BAA0B,CAAC;AAC3E,UAAM,kBAAkB,KAAK,gBAAgB;AAC7C,SAAK,gBAAgB,IAAI,iCACpB,kBADoB;AAAA,MAEvB,OAAO,KAAK,0BAA0B;AAAA,MACtC,MAAM,cAAc;AAAA,IACtB,EAAC;AACD,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,SAAS,OAAO;AACd,QAAI,KAAK,gBAAgB,EAAE,UAAU,IAAI;AACvC,YAAM,gBAAgB,KAAK,aAAa,KAAK,gBAAgB,EAAE,KAAK;AACpE,YAAM,UAAU,KAAK,sBAAsB,aAAa;AACxD,OAAC,WAAW,KAAK,aAAa;AAAA,QAC5B,eAAe;AAAA,QACf;AAAA,MACF,CAAC;AAAA,IACH;AACA,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,WAAW,OAAO;AAChB,QAAI,KAAK,gBAAgB,EAAE,UAAU,IAAI;AACvC,YAAM,UAAU,WAAW,KAAK,SAAS,GAAG,eAAe,UAAU,GAAG,KAAK,aAAa,EAAE,IAAI;AAChG,YAAM,gBAAgB,WAAW,WAAW,SAAS,6BAA6B;AAClF,sBAAgB,cAAc,MAAM,IAAI,WAAW,QAAQ,MAAM;AACjE,YAAM,gBAAgB,KAAK,aAAa,KAAK,gBAAgB,EAAE,KAAK;AACpE,YAAM,UAAU,KAAK,sBAAsB,aAAa;AACxD,UAAI,CAAC,SAAS;AACZ,cAAM,kBAAkB,KAAK,gBAAgB;AAC7C,aAAK,gBAAgB,IAAI,iCACpB,kBADoB;AAAA,UAEvB,OAAO,KAAK,0BAA0B;AAAA,QACxC,EAAC;AAAA,MACH;AAAA,IACF;AACA,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,aAAa,OAAO,MAAM;AACxB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,QAAQ,aAAa,EAAG;AAC5B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,UAAU,WAAW,KAAK;AAChC,UAAM,iBAAiB,KAAK,eAAe,EAAE,OAAO,OAAK,EAAE,cAAc,aAAa,EAAE,cAAc,GAAG;AACzG,QAAI,SAAS;AACX,qBAAe,KAAK,aAAa;AACjC,WAAK,eAAe,IAAI,IAAI;AAAA,IAC9B;AACA,SAAK,gBAAgB,IAAI;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,cAAc;AAAA,IACtB,CAAC;AACD,eAAW,MAAM,KAAK,SAAS,iBAAiB,aAAa;AAC7D,QAAI,SAAS,WAAW,KAAK,cAAc;AACzC;AAAA,IACF;AACA,SAAK,eAAe,IAAI,cAAc;AAAA,EACxC;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,UAAU;AACf,UAAM,kBAAkB,KAAK,gBAAgB,EAAE,UAAU,KAAK,KAAK,gBAAgB,IAAI;AAAA,MACrF,OAAO;AAAA,MACP,OAAO;AAAA,MACP,WAAW;AAAA,MACX,MAAM;AAAA,IACR;AACA,SAAK,gBAAgB,IAAI,eAAe;AAAA,EAC1C;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,UAAU;AACf,SAAK,gBAAgB,IAAI;AAAA,MACvB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,WAAW;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AACD,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,wBAAwB,OAAO;AAC7B,YAAQ,MAAM,SAAS;AAAA,MACrB,KAAK;AACH,aAAK,YAAY,MAAM;AACvB,aAAK,SAAS;AACd,aAAK,UAAU;AACf,aAAK,cAAc;AACnB,aAAK,oBAAoB;AACzB,cAAM,KAAK,SAAS,iBAAiB,aAAa;AAClD;AAAA,IACJ;AAAA,EACF;AAAA,EACA,sBAAsB,OAAO;AAC3B,YAAQ,MAAM,SAAS;AAAA,MACrB,KAAK;AACH,aAAK,cAAc;AACnB;AAAA,IACJ;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,QAAI,KAAK,UAAU;AACjB,UAAI,KAAK,aAAa,OAAQ,MAAK,SAAS,YAAY,KAAK,SAAS,MAAM,KAAK,mBAAmB,aAAa;AAAA,UAAO,aAAY,KAAK,UAAU,KAAK,mBAAmB,aAAa;AAAA,IAC1L;AAAA,EACF;AAAA,EACA,YAAY;AACV,QAAI,KAAK,cAAc,KAAK,oBAAoB;AAC9C,kBAAY,IAAI,QAAQ,KAAK,mBAAmB,eAAe,KAAK,aAAa,KAAK,OAAO,OAAO,IAAI;AAAA,IAC1G;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,SAAK,sBAAsB;AAC3B,QAAI,CAAC,KAAK,GAAG,WAAW;AACtB,WAAK,SAAS;AAAA,IAChB;AACA,QAAI,KAAK,aAAa,KAAK,YAAY;AACrC,kBAAY,MAAM,KAAK,SAAS;AAAA,IAClC;AACA,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,aAAa,OAAO;AAClB,SAAK,aAAa,WAAW,MAAM;AACjC,WAAK,KAAK,KAAK;AAAA,IACjB,GAAG,KAAK,UAAU;AAAA,EACpB;AAAA,EACA,aAAa;AACX,iBAAa,KAAK,UAAU;AAAA,EAC9B;AAAA,EACA,OAAO;AACL,SAAK,QAAQ,IAAI,KAAK;AACtB,SAAK,OAAO,KAAK;AACjB,SAAK,eAAe,IAAI,CAAC,CAAC;AAC1B,SAAK,gBAAgB,IAAI;AAAA,MACvB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,WAAW;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO;AACZ,SAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK;AAAA,EAChD;AAAA,EACA,KAAK,OAAO;AACV,SAAK,eAAe,IAAI,CAAC,CAAC;AAC1B,SAAK,gBAAgB,IAAI;AAAA,MACvB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,WAAW;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AACD,SAAK,QAAQ,MAAM;AACnB,SAAK,QAAQ,MAAM;AACnB,SAAK,OAAO,KAAK;AACjB,SAAK,QAAQ,IAAI,KAAK,SAAS,IAAI,KAAK,QAAQ,IAAI,IAAI;AACxD,UAAM,gBAAgB;AACtB,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,WAAW;AACT,QAAI,OAAO,KAAK,QAAQ;AACxB,QAAI,MAAM,KAAK,QAAQ;AACvB,QAAI,QAAQ,KAAK,mBAAmB,cAAc,eAAe,KAAK,mBAAmB,cAAc,cAAc,2BAA2B,KAAK,mBAAmB,aAAa;AACrL,QAAI,SAAS,KAAK,mBAAmB,cAAc,eAAe,KAAK,mBAAmB,cAAc,eAAe,4BAA4B,KAAK,mBAAmB,aAAa;AACxL,QAAI,WAAW,YAAY;AAE3B,QAAI,OAAO,QAAQ,KAAK,SAAS,iBAAiB,aAAa,SAAS,OAAO;AAC7E,cAAQ;AAAA,IACV;AAEA,QAAI,MAAM,SAAS,KAAK,SAAS,iBAAiB,YAAY,SAAS,QAAQ;AAC7E,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,KAAK,SAAS,iBAAiB,YAAY;AACpD,aAAO,KAAK,SAAS,iBAAiB;AAAA,IACxC;AAEA,QAAI,MAAM,KAAK,SAAS,iBAAiB,WAAW;AAClD,YAAM,KAAK,SAAS,iBAAiB;AAAA,IACvC;AACA,SAAK,mBAAmB,cAAc,MAAM,OAAO,OAAO;AAC1D,SAAK,mBAAmB,cAAc,MAAM,MAAM,MAAM;AAAA,EAC1D;AAAA,EACA,YAAY,OAAO,MAAM;AACvB,SAAK,eAAe,KAAK,eAAe,MAAM;AAC9C,QAAI,YAAY;AAChB,QAAI,UAAU;AACd,QAAI,KAAK,gBAAgB,EAAE,UAAU,IAAI;AACvC,kBAAY,KAAK,aAAa,MAAM,KAAK,gBAAgB,EAAE,KAAK,EAAE,UAAU,mBAAiB,KAAK,cAAc,aAAa,CAAC;AAC9H,kBAAY,cAAc,KAAK,KAAK,aAAa,MAAM,GAAG,KAAK,gBAAgB,EAAE,KAAK,EAAE,UAAU,mBAAiB,KAAK,cAAc,aAAa,CAAC,IAAI,YAAY,KAAK,gBAAgB,EAAE;AAAA,IAC7L,OAAO;AACL,kBAAY,KAAK,aAAa,UAAU,mBAAiB,KAAK,cAAc,aAAa,CAAC;AAAA,IAC5F;AACA,QAAI,cAAc,IAAI;AACpB,gBAAU;AAAA,IACZ;AACA,QAAI,cAAc,MAAM,KAAK,gBAAgB,EAAE,UAAU,IAAI;AAC3D,kBAAY,KAAK,0BAA0B;AAAA,IAC7C;AACA,QAAI,cAAc,IAAI;AACpB,WAAK,uBAAuB,OAAO,SAAS;AAAA,IAC9C;AACA,QAAI,KAAK,eAAe;AACtB,mBAAa,KAAK,aAAa;AAAA,IACjC;AACA,SAAK,gBAAgB,WAAW,MAAM;AACpC,WAAK,cAAc;AACnB,WAAK,gBAAgB;AAAA,IACvB,GAAG,GAAG;AACN,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,OAAO;AACrB,WAAO,WAAW,KAAK,YAAY,IAAI,KAAK,aAAa,KAAK,IAAI;AAAA,EACpE;AAAA,EACA,2BAA2B;AACzB,UAAM,gBAAgB,KAAK,sBAAsB;AACjD,WAAO,gBAAgB,IAAI,KAAK,kBAAkB,IAAI;AAAA,EACxD;AAAA,EACA,oBAAoB;AAClB,WAAO,cAAc,KAAK,cAAc,mBAAiB,KAAK,YAAY,aAAa,CAAC;AAAA,EAC1F;AAAA,EACA,kBAAkB,OAAO;AACvB,UAAM,mBAAmB,QAAQ,IAAI,cAAc,KAAK,aAAa,MAAM,GAAG,KAAK,GAAG,mBAAiB,KAAK,YAAY,aAAa,CAAC,IAAI;AAC1I,WAAO,mBAAmB,KAAK,mBAAmB;AAAA,EACpD;AAAA,EACA,kBAAkB,OAAO;AACvB,UAAM,mBAAmB,QAAQ,KAAK,aAAa,SAAS,IAAI,KAAK,aAAa,MAAM,QAAQ,CAAC,EAAE,UAAU,mBAAiB,KAAK,YAAY,aAAa,CAAC,IAAI;AACjK,WAAO,mBAAmB,KAAK,mBAAmB,QAAQ,IAAI;AAAA,EAChE;AAAA,EACA,4BAA4B;AAC1B,UAAM,gBAAgB,KAAK,sBAAsB;AACjD,WAAO,gBAAgB,IAAI,KAAK,mBAAmB,IAAI;AAAA,EACzD;AAAA,EACA,qBAAqB;AACnB,WAAO,KAAK,aAAa,UAAU,mBAAiB,KAAK,YAAY,aAAa,CAAC;AAAA,EACrF;AAAA,EACA,wBAAwB;AACtB,WAAO,KAAK,aAAa,UAAU,mBAAiB,KAAK,oBAAoB,aAAa,CAAC;AAAA,EAC7F;AAAA,EACA,uBAAuB,OAAO,OAAO;AACnC,UAAM,gBAAgB,KAAK,gBAAgB,KAAK;AAChD,UAAM,kBAAkB,KAAK,gBAAgB;AAC7C,QAAI,gBAAgB,UAAU,OAAO;AACnC,WAAK,gBAAgB,IAAI,iCACpB,kBADoB;AAAA,QAEvB;AAAA,QACA,MAAM,cAAc;AAAA,MACtB,EAAC;AACD,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,aAAa,QAAQ,IAAI;AACvB,UAAM,KAAK,UAAU,KAAK,GAAG,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK;AACvD,UAAM,UAAU,WAAW,KAAK,SAAS,GAAG,eAAe,UAAU,EAAE,IAAI;AAC3E,QAAI,SAAS;AACX,cAAQ,kBAAkB,QAAQ,eAAe;AAAA,QAC/C,OAAO;AAAA,QACP,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,UAAI,CAAC,KAAK,gBAAgB;AACxB,aAAK,iBAAiB,KAAK,SAAS,OAAO,KAAK,SAAS,aAAa,UAAU,WAAS;AACvF,eAAK,KAAK;AAAA,QACZ,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO;AACtB,WAAO,EAAE,KAAK,mBAAmB,cAAc,WAAW,MAAM,MAAM,KAAK,KAAK,mBAAmB,cAAc,SAAS,MAAM,MAAM;AAAA,EACxI;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,gBAAgB;AACvB,WAAK,eAAe;AACpB,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,wBAAwB;AACtB,QAAI,KAAK,uBAAuB;AAC9B,WAAK,sBAAsB;AAC3B,WAAK,wBAAwB;AAAA,IAC/B;AACA,QAAI,KAAK,yBAAyB;AAChC,WAAK,wBAAwB;AAC7B,WAAK,0BAA0B;AAAA,IACjC;AACA,QAAI,KAAK,gBAAgB;AACvB,WAAK,eAAe;AACpB,WAAK,iBAAiB;AAAA,IACxB;AACA,QAAI,KAAK,kBAAkB;AACzB,WAAK,iBAAiB;AACtB,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,6BAA6B;AAC3B,QAAI,KAAK,sBAAsB;AAC7B,WAAK,qBAAqB;AAC1B,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,yBAAyB;AACvB,QAAI,KAAK,YAAY,KAAK,oBAAoB;AAC5C,UAAI,KAAK,aAAa,QAAQ;AAC5B,aAAK,SAAS,YAAY,KAAK,SAAS,MAAM,KAAK,mBAAmB,aAAa;AAAA,MACrF,OAAO;AACL,oBAAY,KAAK,mBAAmB,eAAe,KAAK,QAAQ;AAAA,MAClE;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,sBAAsB;AAC3B,SAAK,2BAA2B;AAChC,SAAK,yBAAyB;AAC9B,SAAK,uBAAuB;AAC5B,UAAM,YAAY;AAAA,EACpB;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAgB,kBAAqB,cAAc,CAAC;AAAA,EACvF;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,GAAG,CAAC,eAAe,GAAG,CAAC,gBAAgB,CAAC;AAAA,IACpE,gBAAgB,SAAS,2BAA2B,IAAI,KAAK,UAAU;AACrE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AACnE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,sBAAsB,GAAG;AAC1E,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW,SAAS,kBAAkB,IAAI,KAAK;AAC7C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,MAAM,CAAC;AAAA,MACxB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAC/D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB,GAAG;AAAA,MAC3E;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,QAAQ,CAAC,GAAG,UAAU,UAAU,gBAAgB;AAAA,MAChD,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,YAAY,CAAC,GAAG,cAAc,cAAc,eAAe;AAAA,MAC3D,IAAI;AAAA,MACJ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,YAAY,CAAC,GAAG,cAAc,cAAc,eAAe;AAAA,IAC7D;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,gBAAgB,CAAC,GAAM,0BAA6B,0BAA0B;AAAA,IAChH,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,aAAa,EAAE,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,GAAG,WAAW,SAAS,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,WAAW,SAAS,GAAG,CAAC,GAAG,aAAa,aAAa,YAAY,eAAe,kBAAkB,QAAQ,SAAS,gBAAgB,UAAU,YAAY,aAAa,kBAAkB,cAAc,cAAc,WAAW,iBAAiB,gBAAgB,CAAC;AAAA,IAClW,UAAU,SAAS,qBAAqB,IAAI,KAAK;AAC/C,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,4BAA4B,GAAG,IAAI,OAAO,CAAC;AAAA,MAC9D;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,QAAQ,IAAI,QAAQ,CAAC;AAAA,MACrC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAiB,SAAY,MAAS,SAAS,gBAAgB,cAAc,eAAe,aAAa,YAAY;AAAA,IACpI,eAAe;AAAA,IACf,MAAM;AAAA,MACJ,WAAW,CAAC,QAAQ,oBAAoB,CAAC,WAAW,UAAU,CAAC,MAAM;AAAA,QACnE,SAAS;AAAA,MACX,CAAC,GAAG,QAAQ,OAAO,CAAC,CAAC,GAAG,WAAW,UAAU,CAAC,QAAQ,cAAc,MAAM;AAAA,QACxE,SAAS;AAAA,MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,gBAAgB,cAAc,eAAe,aAAa,YAAY;AAAA,MAC9F,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAoCV,YAAY,CAAC,QAAQ,oBAAoB,CAAC,WAAW,UAAU,CAAC,MAAM;AAAA,QACpE,SAAS;AAAA,MACX,CAAC,GAAG,QAAQ,OAAO,CAAC,CAAC,GAAG,WAAW,UAAU,CAAC,QAAQ,cAAc,MAAM;AAAA,QACxE,SAAS;AAAA,MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,MACP,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,gBAAgB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,QACb,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,QACpB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,aAAa,YAAY;AAAA,IACnC,SAAS,CAAC,aAAa,YAAY;AAAA,EACrC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,aAAa,cAAc,YAAY;AAAA,EACnD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,aAAa,YAAY;AAAA,MACnC,SAAS,CAAC,aAAa,YAAY;AAAA,IACrC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["ContextMenuClasses", "sublist"]}
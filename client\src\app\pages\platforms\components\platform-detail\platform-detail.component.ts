import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { TabViewModule } from 'primeng/tabview';
import { TagModule } from 'primeng/tag';
import { CardModule } from 'primeng/card';
import { Platform, SupportLevel, LicenseType } from '../../models/platform.model';
import { PlatformService } from '../../services/platform.service';

@Component({
    selector: 'app-platform-detail',
    standalone: true,
    imports: [
        CommonModule,
        ButtonModule,
        TabViewModule,
        TagModule,
        CardModule
    ],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <div>
                            <h5 class="m-0">Platform Detayı</h5>
                            <p class="text-500 mt-1">{{ platform?.platformCode }}</p>
                        </div>
                        <div class="flex gap-2">
                            <p-button 
                                label="Düzenle" 
                                icon="pi pi-pencil" 
                                class="p-button-warning"
                                (onClick)="editPlatform()">
                            </p-button>
                            <p-button 
                                label="Geri" 
                                icon="pi pi-arrow-left" 
                                class="p-button-secondary"
                                (onClick)="goBack()">
                            </p-button>
                        </div>
                    </div>

                    <p-tabView *ngIf="platform">
                        <p-tabPanel header="Platform Bilgileri" leftIcon="pi pi-server">
                            <div class="grid">
                                <div class="col-12 md:col-6">
                                    <p-card header="Temel Bilgiler">
                                        <div class="field">
                                            <label class="font-medium">Platform Adı:</label>
                                            <div class="mt-1">{{ platform.name }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Açıklama:</label>
                                            <div class="mt-1">{{ platform.description }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Sağlayıcı:</label>
                                            <div class="mt-1">{{ platform.vendor }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Versiyon:</label>
                                            <div class="mt-1">
                                                <span class="font-bold text-primary">{{ platform.version }}</span>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Platform Tipi:</label>
                                            <div class="mt-1">
                                                <p-tag [value]="platform.type" severity="info"></p-tag>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Kategori:</label>
                                            <div class="mt-1">
                                                <p-tag [value]="platform.category" severity="secondary"></p-tag>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                                <div class="col-12 md:col-6">
                                    <p-card header="Durum ve Destek">
                                        <div class="field">
                                            <label class="font-medium">Durum:</label>
                                            <div class="mt-1">
                                                <p-tag 
                                                    [value]="platform.isActive ? 'Aktif' : 'Pasif'" 
                                                    [severity]="platform.isActive ? 'success' : 'secondary'">
                                                </p-tag>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Destek Seviyesi:</label>
                                            <div class="mt-1">
                                                <p-tag 
                                                    [value]="platform.supportLevel" 
                                                    [severity]="getSupportSeverity(platform.supportLevel)">
                                                </p-tag>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Lisans Tipi:</label>
                                            <div class="mt-1">
                                                <p-tag 
                                                    [value]="platform.licenseType" 
                                                    [severity]="getLicenseSeverity(platform.licenseType)">
                                                </p-tag>
                                            </div>
                                        </div>
                                        <div class="field" *ngIf="platform.cost">
                                            <label class="font-medium">Maliyet:</label>
                                            <div class="mt-1">
                                                <span class="font-bold text-green-600">
                                                    {{ platform.cost | currency:platform.currency:'symbol':'1.0-0' }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Proje Sayısı:</label>
                                            <div class="mt-1">
                                                <span class="font-bold text-primary text-xl">{{ platform.projectCount }}</span>
                                                <span class="text-500 ml-1">proje</span>
                                            </div>
                                        </div>
                                        <div class="field" *ngIf="platform.releaseDate">
                                            <label class="font-medium">Çıkış Tarihi:</label>
                                            <div class="mt-1">{{ platform.releaseDate | date:'dd/MM/yyyy' }}</div>
                                        </div>
                                    </p-card>
                                </div>
                            </div>
                        </p-tabPanel>

                        <p-tabPanel header="Özellikler" leftIcon="pi pi-star">
                            <div class="grid">
                                <div class="col-12">
                                    <p-card>
                                        <div *ngIf="platform.features.length === 0" class="text-center py-4">
                                            <i class="pi pi-info-circle text-4xl text-500 mb-3"></i>
                                            <div class="text-500 font-medium">Bu platform için henüz özellik tanımlanmamış</div>
                                        </div>
                                        <div *ngIf="platform.features.length > 0" class="grid">
                                            <div *ngFor="let feature of platform.features" class="col-12 md:col-6 lg:col-4">
                                                <div class="border-1 border-200 border-round p-3 mb-2">
                                                    <div class="flex align-items-start gap-2">
                                                        <i class="pi pi-check text-green-500 mt-1" *ngIf="feature.isAvailable"></i>
                                                        <i class="pi pi-times text-red-500 mt-1" *ngIf="!feature.isAvailable"></i>
                                                        <div class="flex-1">
                                                            <div class="font-medium">{{ feature.name }}</div>
                                                            <div class="text-sm text-500 mt-1">{{ feature.description }}</div>
                                                            <div class="mt-2" *ngIf="feature.version">
                                                                <p-tag [value]="'v' + feature.version" severity="info" class="text-xs"></p-tag>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                            </div>
                        </p-tabPanel>

                        <p-tabPanel header="Sistem Gereksinimleri" leftIcon="pi pi-cog">
                            <div class="grid">
                                <div class="col-12">
                                    <p-card>
                                        <div *ngIf="platform.requirements.length === 0" class="text-center py-4">
                                            <i class="pi pi-info-circle text-4xl text-500 mb-3"></i>
                                            <div class="text-500 font-medium">Bu platform için henüz sistem gereksinimi tanımlanmamış</div>
                                        </div>
                                        <div *ngIf="platform.requirements.length > 0" class="grid">
                                            <div *ngFor="let requirement of platform.requirements" class="col-12 md:col-6">
                                                <div class="border-1 border-200 border-round p-3 mb-3">
                                                    <div class="flex align-items-start gap-2">
                                                        <i class="pi pi-desktop text-blue-500 mt-1"></i>
                                                        <div class="flex-1">
                                                            <div class="font-medium">{{ requirement.name }}</div>
                                                            <div class="text-sm text-500 mt-1">{{ requirement.description }}</div>
                                                            <div class="mt-2">
                                                                <div class="text-sm">
                                                                    <span class="font-medium">Minimum:</span> {{ requirement.minimumVersion }}
                                                                </div>
                                                                <div class="text-sm" *ngIf="requirement.recommendedVersion">
                                                                    <span class="font-medium">Önerilen:</span> {{ requirement.recommendedVersion }}
                                                                </div>
                                                            </div>
                                                            <div class="mt-2">
                                                                <p-tag [value]="requirement.type" severity="secondary" class="text-xs"></p-tag>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                            </div>
                        </p-tabPanel>

                        <p-tabPanel header="Bağlantılar ve Notlar" leftIcon="pi pi-link">
                            <div class="grid">
                                <div class="col-12 md:col-6">
                                    <p-card header="Bağlantılar">
                                        <div class="field" *ngIf="platform.website">
                                            <label class="font-medium">Website:</label>
                                            <div class="mt-1">
                                                <a [href]="platform.website" target="_blank" class="text-primary">
                                                    {{ platform.website }}
                                                    <i class="pi pi-external-link ml-1"></i>
                                                </a>
                                            </div>
                                        </div>
                                        <div class="field" *ngIf="platform.documentation">
                                            <label class="font-medium">Dokümantasyon:</label>
                                            <div class="mt-1">
                                                <a [href]="platform.documentation" target="_blank" class="text-primary">
                                                    {{ platform.documentation }}
                                                    <i class="pi pi-external-link ml-1"></i>
                                                </a>
                                            </div>
                                        </div>
                                        <div class="field" *ngIf="platform.tags.length > 0">
                                            <label class="font-medium">Etiketler:</label>
                                            <div class="mt-2 flex flex-wrap gap-1">
                                                <p-tag *ngFor="let tag of platform.tags" [value]="tag" severity="info" class="text-xs"></p-tag>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                                <div class="col-12 md:col-6">
                                    <p-card header="Notlar">
                                        <div *ngIf="!platform.notes" class="text-500">
                                            Henüz not eklenmemiş
                                        </div>
                                        <div *ngIf="platform.notes" class="white-space-pre-line">
                                            {{ platform.notes }}
                                        </div>
                                    </p-card>
                                </div>
                            </div>
                        </p-tabPanel>
                    </p-tabView>
                </div>
            </div>
        </div>
    `
})
export class PlatformDetailComponent implements OnInit {
    platform: Platform | null = null;
    platformId: string = '';

    constructor(
        private route: ActivatedRoute,
        private router: Router,
        private platformService: PlatformService
    ) {}

    ngOnInit() {
        this.platformId = this.route.snapshot.params['id'];
        this.loadPlatform();
    }

    loadPlatform() {
        this.platformService.getPlatformById(this.platformId).subscribe(platform => {
            this.platform = platform || null;
        });
    }

    editPlatform() {
        this.router.navigate(['/pages/platforms', this.platformId, 'edit']);
    }

    goBack() {
        this.router.navigate(['/pages/platforms']);
    }

    getSupportSeverity(supportLevel: SupportLevel): string {
        switch (supportLevel) {
            case SupportLevel.FULL_SUPPORT:
                return 'success';
            case SupportLevel.LIMITED_SUPPORT:
                return 'warning';
            case SupportLevel.COMMUNITY_SUPPORT:
                return 'info';
            case SupportLevel.NO_SUPPORT:
                return 'secondary';
            case SupportLevel.DEPRECATED:
                return 'danger';
            default:
                return 'info';
        }
    }

    getLicenseSeverity(licenseType: LicenseType): string {
        switch (licenseType) {
            case LicenseType.OPEN_SOURCE:
                return 'success';
            case LicenseType.COMMERCIAL:
                return 'warning';
            case LicenseType.FREEMIUM:
                return 'info';
            case LicenseType.SUBSCRIPTION:
                return 'secondary';
            case LicenseType.ENTERPRISE:
                return 'danger';
            case LicenseType.CUSTOM:
                return 'info';
            default:
                return 'info';
        }
    }
}

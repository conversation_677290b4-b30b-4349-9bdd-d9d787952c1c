﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Technologies.Commands.Delete
{
    public class DeleteTechnologyCommandHandler : IRequestHandler<DeleteTechnologyCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public DeleteTechnologyCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(DeleteTechnologyCommand request, CancellationToken cancellationToken)
        {
            var tech = await _context.Technologies
                .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);

            if (tech is null)
                throw new Exception("Silinecek teknoloji bulunamadı.");

            _context.Technologies.Remove(tech);
            await _context.SaveChangesAsync(cancellationToken);

            return tech.Id;
        }
    }
}

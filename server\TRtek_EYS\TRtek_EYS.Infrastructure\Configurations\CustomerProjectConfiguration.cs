﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TRtek_EYS.Domain.Entities;

namespace TRtek_EYS.Infrastructure.Configurations
{
    public class CustomerProjectConfiguration : IEntityTypeConfiguration<CustomerProject>
    {
        public void Configure(EntityTypeBuilder<CustomerProject> builder)
        {
            builder.ToTable("CustomerProjects");

            builder.<PERSON><PERSON><PERSON>(cp => new { cp.CustomerId, cp.ProjectId });

            builder.HasOne(cp => cp.Customer)
                   .WithMany()
                   .HasForeignKey(cp => cp.CustomerId)
                   .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(cp => cp.Project)
                   .WithMany()
                   .HasForeignKey(cp => cp.ProjectId)
                   .OnDelete(DeleteBehavior.Cascade);
        }
    }
}

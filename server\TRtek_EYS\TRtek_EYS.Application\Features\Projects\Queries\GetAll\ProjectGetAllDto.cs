﻿using TRtek_EYS.Domain.Enums;

namespace TRtek_EYS.Application.Features.Projects.Queries.GetAll
{
    // V1 DTO
    public class ProjectGetAllDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = null!;
        public string Code { get; set; } = null!;
        public string? Description { get; set; }
        public Guid CreatedByUserId { get; set; }
        public string? CreatedByUserName { get; set; }
    }

    // V2 DTO - Genişletilmiş
    public class ProjectGetAllDtoV2
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = null!;
        public string Code { get; set; } = null!;
        public string? Description { get; set; }
        public ProjectStatus Status { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public Guid CreatedByUserId { get; set; }
        public string? CreatedByUserName { get; set; }

        // Yeni alanlar
        public ProjectType? ProjectType { get; set; }
        public ProjectPriority? Priority { get; set; }
        public decimal? Budget { get; set; }
        public string? Currency { get; set; }
        public decimal? ActualCost { get; set; }
        public int? EstimatedHours { get; set; }
        public int? ActualHours { get; set; }
        public int? ProgressPercentage { get; set; }
        public string? ProjectManager { get; set; }
        public string? TechnicalLead { get; set; }
        public string? ClientContact { get; set; }
        public string? Repository { get; set; }
        public string? DeploymentUrl { get; set; }
        public string? StagingUrl { get; set; }
        public string? DocumentationUrl { get; set; }
        public DateTime? ActualStartDate { get; set; }
        public DateTime? ActualEndDate { get; set; }
        public DateTime? DeploymentDate { get; set; }
        public string[]? Tags { get; set; }
        public string? Notes { get; set; }
        public bool IsActive { get; set; }
        public bool IsPublic { get; set; }
        public string? Methodology { get; set; }
        public int? TeamSize { get; set; }
        public string? RiskAssessment { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}

﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using TRtek_EYS.Application.Features.VersionInfos.Commands.Add;
using TRtek_EYS.Application.Features.VersionInfos.Commands.Delete;
using TRtek_EYS.Application.Features.VersionInfos.Commands.Update;
using TRtek_EYS.Application.Features.VersionInfos.Queries.GetAll;
using TRtek_EYS.Application.Features.VersionInfos.Queries.GetById;

namespace TRtek_EYS.WebAPI.Controllers;

[Route("api/[controller]")]
[ApiController]
public class VersionInfosController : ApiController
{
    public VersionInfosController(IMediator mediator) : base(mediator) { }

    [HttpGet]
    public async Task<IActionResult> GetAll()
    {
        var result = await Mediator.Send(new GetAllVersionInfosQuery());
        return Ok(result);
    }

    [HttpGet("{id:guid}")]
    public async Task<IActionResult> GetById(Guid id)
    {
        var result = await Mediator.Send(new GetVersionInfoByIdQuery(id));
        return result is null ? NotFound() : Ok(result); 
    }

    [HttpPost]
    public async Task<IActionResult> Add([FromBody] AddVersionInfoCommand command)
    {
        var id = await Mediator.Send(command);
        return CreatedAtAction(nameof(GetById), new { id }, null);
    }

    [HttpPut("{id:guid}")]
    public async Task<IActionResult> Update(Guid id, [FromBody] UpdateVersionInfoCommand command)
    {
        if (id != command.Id)
            return BadRequest("ID uyuşmazlığı.");

        await Mediator.Send(command);
        return NoContent();
    }

    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> Delete(Guid id)
    {
        await Mediator.Send(new DeleteVersionInfoCommand { Id = id });
        return NoContent();
    }
}

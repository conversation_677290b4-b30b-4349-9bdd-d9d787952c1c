﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;
using TRtek_EYS.Domain.Enums;

namespace TRtek_EYS.Application.Features.Contracts.Commands.Update
{
    public class UpdateContractCommandHandler : IRequestHandler<UpdateContractCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public UpdateContractCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(UpdateContractCommand request, CancellationToken cancellationToken)
        {
            var contract = await _context.Contracts
                .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken);

            if (contract is null)
                throw new Exception("Sözleşme bulunamadı.");

            contract.Name = request.Name;
            contract.Number = request.Number;
            contract.Status = (ContractStatus)request.Status;
            contract.StartDate = request.StartDate;
            contract.EndDate = request.EndDate;
            contract.FilePath = request.FilePath;
            contract.CustomerId = request.CustomerId;
            contract.ProjectId = request.ProjectId;

            await _context.SaveChangesAsync(cancellationToken);

            return contract.Id;
        }
    }
}

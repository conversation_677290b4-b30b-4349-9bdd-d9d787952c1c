﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography;
using System.Text;
using TRtek_EYS.Application.Common.Interfaces;
using TRtek_EYS.Application.Features.Auth.Login;
using TRtek_EYS.Domain.Entities;

namespace TRtek_EYS.Application.Features.Auth.Register
{
    public class RegisterCommandHandler : IRequestHandler<RegisterCommand, LoginCommandResponse>
    {
        private readonly IApplicationDbContext _context;
        private readonly IJwtProvider _jwtProvider;

        public RegisterCommandHandler(IApplicationDbContext context, IJwtProvider jwtProvider)
        {
            _context = context;
            _jwtProvider = jwtProvider;
        }

        public async Task<LoginCommandResponse> Handle(RegisterCommand request, CancellationToken cancellationToken)
        {
            var exists = await _context.Users.AnyAsync(u => u.Username == request.Username, cancellationToken);
            if (exists)
                throw new InvalidOperationException("<PERSON>u kullanıcı adı zaten alınmış.");

            var user = new User
            {
                Username = request.Username,
                PasswordHash = HashPassword(request.Password),
                FullName = request.FullName ?? string.Empty,
                Email = request.Email ?? string.Empty,
                IsActive = true
            };

            _context.Users.Add(user);
            await _context.SaveChangesAsync(cancellationToken);

            // JWT + Refresh Token üretimi
            var tokenResponse = await _jwtProvider.CreateToken(user);

            // Refresh token'ı kullanıcıya sakla
            user.RefreshToken = tokenResponse.RefreshToken;
            user.RefreshTokenExpires = tokenResponse.RefreshTokenExpires;

            await _context.SaveChangesAsync(cancellationToken);

            return tokenResponse;
        }

        private static string HashPassword(string password)
        {
            using var sha256 = SHA256.Create();
            var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
            return Convert.ToBase64String(hash);
        }
    }
}

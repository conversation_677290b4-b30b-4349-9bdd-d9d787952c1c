﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Contracts.Queries.GetById
{
    public class GetContractByIdQueryHandler : IRequestHandler<GetContractByIdQuery, ContractGetByIdDto>
    {
        private readonly IApplicationDbContext _context;

        public GetContractByIdQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<ContractGetByIdDto> Handle(GetContractByIdQuery request, CancellationToken cancellationToken)
        {
            var contract = await _context.Contracts
                .AsNoTracking()
                .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken);

            if (contract is null)
                throw new Exception("Sözleşme bulunamadı.");

            return new ContractGetByIdDto
            {
                Id = contract.Id,
                Name = contract.Name,
                Number = contract.Number,
                Status = contract.Status.ToString(),
                StartDate = contract.StartDate,
                EndDate = contract.EndDate,
                FilePath = contract.FilePath,
                CustomerId = contract.CustomerId,
                ProjectId = contract.ProjectId
            };
        }
    }
}

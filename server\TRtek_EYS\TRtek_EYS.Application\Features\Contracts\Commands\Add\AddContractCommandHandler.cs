﻿using MediatR;
using TRtek_EYS.Application.Common.Interfaces;
using TRtek_EYS.Domain.Entities;
using TRtek_EYS.Domain.Enums;

namespace TRtek_EYS.Application.Features.Contracts.Commands.Add
{
    public class AddContractCommandHandler : IRequestHandler<AddContractCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public AddContractCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(AddContractCommand request, CancellationToken cancellationToken)
        {
            var contract = new Contract
            {
                Name = request.Name,
                Number = request.Number,
                Status = (ContractStatus)request.Status,
                StartDate = request.StartDate,
                EndDate = request.EndDate,
                FilePath = request.FilePath,
                CustomerId = request.CustomerId,
                ProjectId = request.ProjectId,
                CreatedAt = DateTime.UtcNow
            };

            await _context.Contracts.AddAsync(contract, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);

            return contract.Id;
        }
    }
}

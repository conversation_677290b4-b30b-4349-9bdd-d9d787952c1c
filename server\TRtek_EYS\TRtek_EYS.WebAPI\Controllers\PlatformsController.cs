﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TRtek_EYS.Application.Features.Platforms.Commands.Add;
using TRtek_EYS.Application.Features.Platforms.Commands.Delete;
using TRtek_EYS.Application.Features.Platforms.Commands.Update;
using TRtek_EYS.Application.Features.Platforms.Queries.GetAll;
using TRtek_EYS.Application.Features.Platforms.Queries.GetById;

namespace TRtek_EYS.WebAPI.Controllers;

[Route("api/[controller]")]
[ApiController]
public class PlatformsController : ApiController
{
    public PlatformsController(IMediator mediator) : base(mediator) { }

    [HttpGet]
    public async Task<IActionResult> GetAll()
    {
        var result = await Mediator.Send(new GetAllPlatformsQuery());
        return Ok(result); // List<PlatformGetAllDto>
    }

    [HttpGet("v2")]
    [AllowAnonymous]
    public async Task<IActionResult> GetAllV2()
    {
        var result = await Mediator.Send(new GetAllPlatformsV2Query());
        return Ok(result); // List<PlatformGetAllDtoV2>
    }

    [HttpGet("{id:guid}")]
    public async Task<IActionResult> GetById(Guid id)
    {
        var result = await Mediator.Send(new GetPlatformByIdQuery(id));
        return result is null ? NotFound() : Ok(result); // PlatformGetByIdDto
    }

    [HttpPost]
    public async Task<IActionResult> Add([FromBody] AddPlatformCommand command)
    {
        var id = await Mediator.Send(command);
        return CreatedAtAction(nameof(GetById), new { id }, null);
    }

    [HttpPut("{id:guid}")]
    public async Task<IActionResult> Update(Guid id, [FromBody] UpdatePlatformCommand command)
    {
        if (id != command.Id)
            return BadRequest("URL'deki ID uyuşmuyor.");

        await Mediator.Send(command);
        return NoContent();
    }

    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> Delete(Guid id)
    {
        await Mediator.Send(new DeletePlatformCommand { Id = id });
        return NoContent();
    }
}

﻿using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Domain.Entities;

namespace TRtek_EYS.Application.Common.Interfaces;

public interface IApplicationDbContext
{
    DbSet<User> Users { get; }
    DbSet<Role> Roles { get; }
    DbSet<UserRole> UserRoles { get; }
    DbSet<Project> Projects { get; }
    DbSet<ProjectUserRole> ProjectUserRoles { get; }
    DbSet<ProjectTeam> ProjectTeams { get; }
    DbSet<Customer> Customers { get; }
    DbSet<CustomerProject> CustomerProjects { get; }
    DbSet<Team> Teams { get; }
    DbSet<Technology> Technologies { get; }
    DbSet<Platform> Platforms { get; }
    DbSet<Document> Documents { get; }
    DbSet<Contract> Contracts { get; }
    DbSet<Configuration> Configurations { get; }
    DbSet<RepositoryInfo> RepositoryInfos { get; }
    DbSet<VersionInfo> VersionInfos { get; }

    Task<int> SaveChangesAsync(CancellationToken cancellationToken);
}

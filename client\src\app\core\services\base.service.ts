import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { ApiResponse, BaseModel } from '../models/base.model';

@Injectable()
export abstract class BaseService<T extends BaseModel> {
    protected http = inject(HttpClient);
    protected apiUrl = environment.apiUrl;
    protected abstract endpoint: string;

    getAll(params?: any): Observable<ApiResponse<T[]>> {
        let httpParams = new HttpParams();
        if (params) {
            Object.keys(params).forEach(key => {
                if (params[key] !== null && params[key] !== undefined) {
                    httpParams = httpParams.set(key, params[key]);
                }
            });
        }
        return this.http.get<ApiResponse<T[]>>(`${this.apiUrl}/${this.endpoint}`, { params: httpParams });
    }

    getById(id: string): Observable<ApiResponse<T>> {
        return this.http.get<ApiResponse<T>>(`${this.apiUrl}/${this.endpoint}/${id}`);
    }

    create(item: Partial<T>): Observable<ApiResponse<T>> {
        return this.http.post<ApiResponse<T>>(`${this.apiUrl}/${this.endpoint}`, item);
    }

    update(id: string, item: Partial<T>): Observable<ApiResponse<T>> {
        return this.http.put<ApiResponse<T>>(`${this.apiUrl}/${this.endpoint}/${id}`, item);
    }

    delete(id: string): Observable<ApiResponse<void>> {
        return this.http.delete<ApiResponse<void>>(`${this.apiUrl}/${this.endpoint}/${id}`);
    }
}

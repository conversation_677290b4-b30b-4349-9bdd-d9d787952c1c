import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { TabViewModule } from 'primeng/tabview';
import { TagModule } from 'primeng/tag';
import { CardModule } from 'primeng/card';
import { ProgressBarModule } from 'primeng/progressbar';
import { ProjectAssignment, AssignmentStatus, ProjectRole } from '../../models/assignment.model';

@Component({
    selector: 'app-assignment-detail',
    standalone: true,
    imports: [
        CommonModule,
        ButtonModule,
        TabViewModule,
        TagModule,
        CardModule,
        ProgressBarModule
    ],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <div>
                            <h5 class="m-0">Pro<PERSON>ı Detayı</h5>
                            <p class="text-500 mt-1">{{ assignment?.assignmentCode }}</p>
                        </div>
                        <div class="flex gap-2">
                            <p-button 
                                label="Düzenle" 
                                icon="pi pi-pencil" 
                                class="p-button-warning"
                                (onClick)="editAssignment()">
                            </p-button>
                            <p-button 
                                label="Geri" 
                                icon="pi pi-arrow-left" 
                                class="p-button-secondary"
                                (onClick)="goBack()">
                            </p-button>
                        </div>
                    </div>

                    <p-tabView *ngIf="assignment">
                        <p-tabPanel header="Atama Bilgileri" leftIcon="pi pi-user">
                            <div class="grid">
                                <div class="col-12 md:col-6">
                                    <p-card header="Kullanıcı Bilgileri">
                                        <div class="field">
                                            <label class="font-medium">Kullanıcı Adı:</label>
                                            <div class="mt-1">{{ assignment.userName }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">E-posta:</label>
                                            <div class="mt-1">{{ assignment.userEmail }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Takım:</label>
                                            <div class="mt-1">{{ assignment.teamName }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Rol:</label>
                                            <div class="mt-1">
                                                <p-tag [value]="assignment.role" severity="info"></p-tag>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                                <div class="col-12 md:col-6">
                                    <p-card header="Proje Bilgileri">
                                        <div class="field">
                                            <label class="font-medium">Proje Adı:</label>
                                            <div class="mt-1">{{ assignment.projectName }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Proje Kodu:</label>
                                            <div class="mt-1">{{ assignment.projectCode }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Başlangıç Tarihi:</label>
                                            <div class="mt-1">{{ assignment.startDate | date:'dd/MM/yyyy' }}</div>
                                        </div>
                                        <div class="field" *ngIf="assignment.endDate">
                                            <label class="font-medium">Bitiş Tarihi:</label>
                                            <div class="mt-1">{{ assignment.endDate | date:'dd/MM/yyyy' }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Durum:</label>
                                            <div class="mt-1">
                                                <p-tag 
                                                    [value]="assignment.status" 
                                                    [severity]="getStatusSeverity(assignment.status)">
                                                </p-tag>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                            </div>
                        </p-tabPanel>

                        <p-tabPanel header="İş Yükü ve Performans" leftIcon="pi pi-chart-bar">
                            <div class="grid">
                                <div class="col-12 md:col-6">
                                    <p-card header="İş Yükü">
                                        <div class="field">
                                            <label class="font-medium">İş Yükü Yüzdesi:</label>
                                            <div class="mt-2">
                                                <div class="flex align-items-center">
                                                    <div class="w-full bg-gray-200 border-round-lg h-2rem mr-3">
                                                        <div 
                                                            class="bg-blue-500 h-full border-round-lg flex align-items-center justify-content-center" 
                                                            [style.width.%]="assignment.workloadPercentage">
                                                            <span class="text-white font-bold">{{ assignment.workloadPercentage }}%</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Atama Türü:</label>
                                            <div class="mt-1">{{ assignment.assignmentType }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Öncelik:</label>
                                            <div class="mt-1">
                                                <p-tag [value]="assignment.priority" severity="warning"></p-tag>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                                <div class="col-12 md:col-6">
                                    <p-card header="Sorumluluklar">
                                        <div *ngIf="assignment.responsibilities.length === 0" class="text-500">
                                            Henüz sorumluluk tanımlanmamış
                                        </div>
                                        <ul *ngIf="assignment.responsibilities.length > 0" class="list-none p-0">
                                            <li *ngFor="let responsibility of assignment.responsibilities" class="flex align-items-center mb-2">
                                                <i class="pi pi-check text-green-500 mr-2"></i>
                                                <span>{{ responsibility }}</span>
                                            </li>
                                        </ul>
                                    </p-card>
                                </div>
                            </div>
                        </p-tabPanel>

                        <p-tabPanel header="Atama Geçmişi" leftIcon="pi pi-history">
                            <div class="text-center py-4">
                                <i class="pi pi-info-circle text-4xl text-500 mb-3"></i>
                                <div class="text-500 font-medium">Atama geçmişi özelliği yakında eklenecek</div>
                            </div>
                        </p-tabPanel>
                    </p-tabView>
                </div>
            </div>
        </div>
    `
})
export class AssignmentDetailComponent implements OnInit {
    assignment: ProjectAssignment | null = null;
    assignmentId: string = '';

    constructor(
        private route: ActivatedRoute,
        private router: Router
    ) {}

    ngOnInit() {
        this.assignmentId = this.route.snapshot.params['id'];
        this.loadAssignment();
    }

    loadAssignment() {
        // Dummy data
        this.assignment = {
            id: '1',
            assignmentCode: 'ASG-2025-001',
            projectId: '1',
            projectName: 'E-Ticaret Platformu',
            projectCode: 'PRJ-2025-001',
            teamId: '1',
            teamName: 'Frontend Takımı',
            userId: '1',
            userName: 'Ahmet Yılmaz',
            userEmail: '<EMAIL>',
            role: 'Frontend Geliştirici' as any,
            assignmentType: 'Tam Zamanlı' as any,
            startDate: new Date('2025-01-15'),
            endDate: new Date('2025-12-31'),
            workloadPercentage: 80,
            status: 'Aktif' as any,
            priority: 'Yüksek' as any,
            responsibilities: [
                'React component geliştirme',
                'UI/UX tasarımı uygulama',
                'Frontend test yazma',
                'Code review yapma'
            ],
            skills: [],
            assignedBy: 'Proje Yöneticisi',
            assignedDate: new Date('2025-01-10'),
            isActive: true
        };
    }

    editAssignment() {
        this.router.navigate(['/pages/assignments', this.assignmentId, 'edit']);
    }

    goBack() {
        this.router.navigate(['/pages/assignments']);
    }

    getStatusSeverity(status: AssignmentStatus): string {
        switch (status) {
            case AssignmentStatus.ACTIVE:
                return 'success';
            case AssignmentStatus.PENDING:
                return 'warning';
            case AssignmentStatus.COMPLETED:
                return 'info';
            case AssignmentStatus.SUSPENDED:
                return 'secondary';
            case AssignmentStatus.CANCELLED:
                return 'danger';
            case AssignmentStatus.ON_HOLD:
                return 'warning';
            default:
                return 'info';
        }
    }
}

﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Users.Queries.GetAll
{
    public class GetAllUsersQueryHandler : IRequestHandler<GetAllUsersQuery, List<UserGetAllDto>>
    {
        private readonly IApplicationDbContext _context;

        public GetAllUsersQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<UserGetAllDto>> Handle(GetAllUsersQuery request, CancellationToken cancellationToken)
        {
            return await _context.Users
                .AsNoTracking()
                .Select(u => new UserGetAllDto
                {
                    Id = u.Id,
                    Username = u.Username,
                    FullName = u.FullName,
                    Email = u.Email,
                    IsActive = u.IsActive
                })
                .ToListAsync(cancellationToken);
        }
    }
}

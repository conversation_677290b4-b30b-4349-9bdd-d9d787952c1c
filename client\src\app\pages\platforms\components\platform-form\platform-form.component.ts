import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { Select } from 'primeng/select';
import { Textarea } from 'primeng/inputtextarea';
import { CheckboxModule } from 'primeng/checkbox';
import { InputNumber } from 'primeng/inputnumber';
import { ToastModule } from 'primeng/toast';
import { TooltipModule } from 'primeng/tooltip';
import { MessageService } from 'primeng/api';
import { PlatformType, PlatformCategory, SupportLevel, LicenseType } from '../../models/platform.model';
import { PlatformService } from '../../services/platform.service';

@Component({
    selector: 'app-platform-form',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        ButtonModule,
        InputTextModule,
        Select,
        Textarea,
        CheckboxModule,
        InputNumber,
        ToastModule,
        TooltipModule
    ],
    providers: [MessageService],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <h5>{{ isEditMode ? 'Platform Düzenle' : 'Yeni Platform' }}</h5>
                        <p-button 
                            label="Geri" 
                            icon="pi pi-arrow-left" 
                            class="p-button-secondary"
                            (onClick)="goBack()">
                        </p-button>
                    </div>

                    <form [formGroup]="platformForm" (ngSubmit)="onSubmit()">
                        <div class="grid">
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="platformCode" class="font-medium">Platform Kodu *</label>
                                    <input 
                                        id="platformCode"
                                        type="text" 
                                        pInputText 
                                        formControlName="platformCode"
                                        [readonly]="isEditMode"
                                        class="w-full"
                                        placeholder="Otomatik oluşturulacak" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="name" class="font-medium">Platform Adı *</label>
                                    <input 
                                        id="name"
                                        type="text" 
                                        pInputText 
                                        formControlName="name"
                                        class="w-full"
                                        placeholder="Platform adı" />
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field">
                                    <label for="description" class="font-medium">Açıklama *</label>
                                    <textarea 
                                        id="description"
                                        pTextarea 
                                        formControlName="description"
                                        rows="3"
                                        class="w-full"
                                        placeholder="Platform açıklaması...">
                                    </textarea>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="type" class="font-medium">Platform Tipi *</label>
                                    <p-select 
                                        id="type"
                                        formControlName="type"
                                        [options]="typeOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Platform tipi seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="category" class="font-medium">Kategori *</label>
                                    <p-select 
                                        id="category"
                                        formControlName="category"
                                        [options]="categoryOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Kategori seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="vendor" class="font-medium">Sağlayıcı *</label>
                                    <input 
                                        id="vendor"
                                        type="text" 
                                        pInputText 
                                        formControlName="vendor"
                                        class="w-full"
                                        placeholder="Sağlayıcı firma" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="version" class="font-medium">Versiyon *</label>
                                    <input 
                                        id="version"
                                        type="text" 
                                        pInputText 
                                        formControlName="version"
                                        class="w-full"
                                        placeholder="Versiyon numarası" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="supportLevel" class="font-medium">Destek Seviyesi *</label>
                                    <p-select 
                                        id="supportLevel"
                                        formControlName="supportLevel"
                                        [options]="supportOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Destek seviyesi seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="licenseType" class="font-medium">Lisans Tipi *</label>
                                    <p-select 
                                        id="licenseType"
                                        formControlName="licenseType"
                                        [options]="licenseOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Lisans tipi seçin"
                                        class="w-full">
                                    </p-select>
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="website" class="font-medium">Website</label>
                                    <input 
                                        id="website"
                                        type="url" 
                                        pInputText 
                                        formControlName="website"
                                        class="w-full"
                                        placeholder="https://example.com" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="documentation" class="font-medium">Dokümantasyon</label>
                                    <input 
                                        id="documentation"
                                        type="url" 
                                        pInputText 
                                        formControlName="documentation"
                                        class="w-full"
                                        placeholder="https://docs.example.com" />
                                </div>
                            </div>

                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="cost" class="font-medium">Maliyet</label>
                                    <p-inputNumber 
                                        id="cost"
                                        formControlName="cost"
                                        mode="currency"
                                        currency="USD"
                                        placeholder="Maliyet"
                                        class="w-full">
                                    </p-inputNumber>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="field">
                                    <label for="notes" class="font-medium">Notlar</label>
                                    <textarea 
                                        id="notes"
                                        pTextarea 
                                        formControlName="notes"
                                        rows="3"
                                        class="w-full"
                                        placeholder="Platform hakkında notlar...">
                                    </textarea>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="flex gap-4">
                                    <div class="field-checkbox">
                                        <p-checkbox 
                                            inputId="isActive"
                                            formControlName="isActive">
                                        </p-checkbox>
                                        <label for="isActive" class="ml-2 font-medium">Aktif</label>
                                    </div>
                                    <div class="field-checkbox">
                                        <p-checkbox 
                                            inputId="isSupported"
                                            formControlName="isSupported">
                                        </p-checkbox>
                                        <label for="isSupported" class="ml-2 font-medium">Destekleniyor</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-content-end gap-3 mt-4">
                            <p-button
                                icon="pi pi-times"
                                severity="secondary"
                                rounded
                                outlined
                                type="button"
                                pTooltip="İptal"
                                (onClick)="goBack()">
                            </p-button>
                            <p-button
                                icon="pi pi-check"
                                severity="success"
                                rounded
                                type="submit"
                                [pTooltip]="isEditMode ? 'Güncelle' : 'Kaydet'"
                                [disabled]="platformForm.invalid">
                            </p-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <p-toast></p-toast>
    `
})
export class PlatformFormComponent implements OnInit {
    platformForm!: FormGroup;
    isEditMode = false;
    platformId: string = '';

    typeOptions = [
        { label: 'İşletim Sistemi', value: PlatformType.OPERATING_SYSTEM },
        { label: 'Veritabanı', value: PlatformType.DATABASE },
        { label: 'Web Sunucusu', value: PlatformType.WEB_SERVER },
        { label: 'Uygulama Sunucusu', value: PlatformType.APPLICATION_SERVER },
        { label: 'Bulut Platformu', value: PlatformType.CLOUD_PLATFORM },
        { label: 'Container Platformu', value: PlatformType.CONTAINER_PLATFORM },
        { label: 'Mobil Platform', value: PlatformType.MOBILE_PLATFORM },
        { label: 'Geliştirme Platformu', value: PlatformType.DEVELOPMENT_PLATFORM },
        { label: 'Entegrasyon Platformu', value: PlatformType.INTEGRATION_PLATFORM },
        { label: 'Analitik Platformu', value: PlatformType.ANALYTICS_PLATFORM }
    ];

    categoryOptions = [
        { label: 'Altyapı', value: PlatformCategory.INFRASTRUCTURE },
        { label: 'Geliştirme', value: PlatformCategory.DEVELOPMENT },
        { label: 'Veritabanı', value: PlatformCategory.DATABASE },
        { label: 'Güvenlik', value: PlatformCategory.SECURITY },
        { label: 'İzleme', value: PlatformCategory.MONITORING },
        { label: 'Dağıtım', value: PlatformCategory.DEPLOYMENT },
        { label: 'Test', value: PlatformCategory.TESTING },
        { label: 'İşbirliği', value: PlatformCategory.COLLABORATION },
        { label: 'İletişim', value: PlatformCategory.COMMUNICATION },
        { label: 'Verimlilik', value: PlatformCategory.PRODUCTIVITY }
    ];

    supportOptions = [
        { label: 'Tam Destek', value: SupportLevel.FULL_SUPPORT },
        { label: 'Sınırlı Destek', value: SupportLevel.LIMITED_SUPPORT },
        { label: 'Topluluk Desteği', value: SupportLevel.COMMUNITY_SUPPORT },
        { label: 'Destek Yok', value: SupportLevel.NO_SUPPORT },
        { label: 'Kullanımdan Kaldırıldı', value: SupportLevel.DEPRECATED }
    ];

    licenseOptions = [
        { label: 'Açık Kaynak', value: LicenseType.OPEN_SOURCE },
        { label: 'Ticari', value: LicenseType.COMMERCIAL },
        { label: 'Freemium', value: LicenseType.FREEMIUM },
        { label: 'Abonelik', value: LicenseType.SUBSCRIPTION },
        { label: 'Kurumsal', value: LicenseType.ENTERPRISE },
        { label: 'Özel', value: LicenseType.CUSTOM }
    ];

    constructor(
        private fb: FormBuilder,
        private route: ActivatedRoute,
        private router: Router,
        private platformService: PlatformService,
        private messageService: MessageService
    ) {}

    ngOnInit() {
        this.initForm();
        this.checkEditMode();
    }

    initForm() {
        this.platformForm = this.fb.group({
            platformCode: ['', Validators.required],
            name: ['', Validators.required],
            description: ['', Validators.required],
            type: [PlatformType.CLOUD_PLATFORM, Validators.required],
            category: [PlatformCategory.INFRASTRUCTURE, Validators.required],
            vendor: ['', Validators.required],
            version: ['', Validators.required],
            supportLevel: [SupportLevel.COMMUNITY_SUPPORT, Validators.required],
            licenseType: [LicenseType.OPEN_SOURCE, Validators.required],
            website: [''],
            documentation: [''],
            cost: [null],
            isActive: [true],
            isSupported: [true],
            notes: [''],
            tags: [[]]
        });

        if (!this.isEditMode) {
            this.generatePlatformCode();
        }
    }

    checkEditMode() {
        this.platformId = this.route.snapshot.params['id'];
        this.isEditMode = !!this.platformId && this.router.url.includes('edit');
        
        if (this.isEditMode) {
            this.loadPlatform();
        }
    }

    generatePlatformCode() {
        const year = new Date().getFullYear();
        const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        const platformCode = `PLT-${year}-${randomNum}`;
        this.platformForm.patchValue({ platformCode });
    }

    loadPlatform() {
        this.platformService.getPlatformById(this.platformId).subscribe(platform => {
            if (platform) {
                this.platformForm.patchValue({
                    platformCode: platform.platformCode,
                    name: platform.name,
                    description: platform.description,
                    type: platform.type,
                    category: platform.category,
                    vendor: platform.vendor,
                    version: platform.version,
                    supportLevel: platform.supportLevel,
                    licenseType: platform.licenseType,
                    website: platform.website,
                    documentation: platform.documentation,
                    cost: platform.cost,
                    isActive: platform.isActive,
                    isSupported: platform.isSupported,
                    notes: platform.notes,
                    tags: platform.tags
                });
            }
        });
    }

    onSubmit() {
        if (this.platformForm.valid) {
            const formData = this.platformForm.value;
            
            if (this.isEditMode) {
                this.platformService.updatePlatform(this.platformId, formData).subscribe(() => {
                    this.messageService.add({
                        severity: 'success',
                        summary: 'Başarılı',
                        detail: 'Platform başarıyla güncellendi'
                    });
                    setTimeout(() => {
                        this.router.navigate(['/pages/platforms']);
                    }, 1500);
                });
            } else {
                this.platformService.createPlatform(formData).subscribe(() => {
                    this.messageService.add({
                        severity: 'success',
                        summary: 'Başarılı',
                        detail: 'Platform başarıyla oluşturuldu'
                    });
                    setTimeout(() => {
                        this.router.navigate(['/pages/platforms']);
                    }, 1500);
                });
            }
        }
    }

    goBack() {
        this.router.navigate(['/pages/platforms']);
    }
}

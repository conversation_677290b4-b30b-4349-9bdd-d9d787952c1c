{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-menubar.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { CommonModule, isPlatformBrowser, DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, inject, numberAttribute, booleanAttribute, ViewChild, Output, Input, ViewEncapsulation, Component, signal, effect, PLATFORM_ID, ContentChildren, ContentChild, Inject, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport { resolve, isNotEmpty, uuid, isEmpty, focus, isTouchDevice, findSingle, isPrintableCharacter, findLastIndex } from '@primeuix/utils';\nimport { SharedModule, PrimeTemplate } from 'primeng/api';\nimport * as i4 from 'primeng/badge';\nimport { BadgeModule } from 'primeng/badge';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { AngleDownIcon, AngleRightIcon, BarsIcon } from 'primeng/icons';\nimport { Ripple } from 'primeng/ripple';\nimport * as i3 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ZIndexUtils } from 'primeng/utils';\nimport { Subject, interval } from 'rxjs';\nimport { debounce, filter } from 'rxjs/operators';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"menubar\"];\nconst _c1 = (a0, a1) => ({\n  \"p-menubar-submenu\": a0,\n  \"p-menubar-root-list\": a1\n});\nconst _c2 = a0 => ({\n  \"p-menubar-item-link\": true,\n  \"p-disabled\": a0\n});\nconst _c3 = () => ({\n  exact: false\n});\nconst _c4 = (a0, a1) => ({\n  $implicit: a0,\n  root: a1\n});\nconst _c5 = a0 => ({\n  display: a0\n});\nfunction MenubarSub_ng_template_2_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 8);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(ctx_r2.getItemProp(processedItem_r2, \"style\"));\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getSeparatorItemClass(processedItem_r2));\n    i0.ɵɵattribute(\"id\", ctx_r2.getItemId(processedItem_r2))(\"data-pc-section\", \"separator\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 19);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getItemProp(processedItem_r2, \"icon\"))(\"ngStyle\", ctx_r2.getItemProp(processedItem_r2, \"iconStyle\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\")(\"tabindex\", -1);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"id\", ctx_r2.getItemLabelId(processedItem_r2));\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getItemLabel(processedItem_r2), \" \");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 21);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.getItemLabel(processedItem_r2), i0.ɵɵsanitizeHtml)(\"id\", ctx_r2.getItemLabelId(processedItem_r2));\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_p_badge_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-badge\", 22);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"styleClass\", ctx_r2.getItemProp(processedItem_r2, \"badgeStyleClass\"))(\"value\", ctx_r2.getItemProp(processedItem_r2, \"badge\"));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_AngleDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleDownIcon\", 25);\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_AngleRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleRightIcon\", 25);\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_AngleDownIcon_1_Template, 1, 1, \"AngleDownIcon\", 24)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_AngleRightIcon_2_Template, 1, 1, \"AngleRightIcon\", 24);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.root);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.root);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_ng_template_0_Template(rf, ctx) {}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_ng_template_0_Template, 0, 0, \"ng-template\", 26);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"data-pc-section\", \"submenuicon\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_Template, 3, 2, \"ng-container\", 11)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_Template, 1, 1, null, 23);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.submenuiconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.submenuiconTemplate);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 15);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_1_Template, 1, 4, \"span\", 16)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_2_Template, 2, 3, \"span\", 17)(3, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_template_3_Template, 1, 3, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(5, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_p_badge_5_Template, 1, 2, \"p-badge\", 18)(6, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_Template, 3, 2, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlLabel_r5 = i0.ɵɵreference(4);\n    const processedItem_r2 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"target\", ctx_r2.getItemProp(processedItem_r2, \"target\"))(\"ngClass\", i0.ɵɵpureFunction1(11, _c2, ctx_r2.getItemProp(processedItem_r2, \"disabled\")));\n    i0.ɵɵattribute(\"href\", ctx_r2.getItemProp(processedItem_r2, \"url\"), i0.ɵɵsanitizeUrl)(\"data-automationid\", ctx_r2.getItemProp(processedItem_r2, \"automationId\"))(\"data-pc-section\", \"action\")(\"tabindex\", -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"icon\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"escape\"))(\"ngIfElse\", htmlLabel_r5);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"badge\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemGroup(processedItem_r2));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 19);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getItemProp(processedItem_r2, \"icon\"))(\"ngStyle\", ctx_r2.getItemProp(processedItem_r2, \"iconStyle\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\")(\"tabindex\", -1);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getItemLabel(processedItem_r2));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 30);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.getItemLabel(processedItem_r2), i0.ɵɵsanitizeHtml);\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_p_badge_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-badge\", 22);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"styleClass\", ctx_r2.getItemProp(processedItem_r2, \"badgeStyleClass\"))(\"value\", ctx_r2.getItemProp(processedItem_r2, \"badge\"));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleDownIcon\", 25);\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleRightIcon\", 25);\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleDownIcon_1_Template, 1, 1, \"AngleDownIcon\", 24)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleRightIcon_2_Template, 1, 1, \"AngleRightIcon\", 24);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.root);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.root);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_ng_template_0_Template(rf, ctx) {}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_ng_template_0_Template, 0, 0, \"ng-template\", 26);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"data-pc-section\", \"submenuicon\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_Template, 3, 2, \"ng-container\", 11)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_Template, 1, 1, null, 23);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.submenuiconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.submenuiconTemplate);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 27);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_1_Template, 1, 4, \"span\", 16)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_2_Template, 2, 1, \"span\", 28)(3, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_template_3_Template, 1, 2, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(5, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_p_badge_5_Template, 1, 2, \"p-badge\", 18)(6, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_Template, 3, 2, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlRouteLabel_r6 = i0.ɵɵreference(4);\n    const processedItem_r2 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", ctx_r2.getItemProp(processedItem_r2, \"routerLink\"))(\"queryParams\", ctx_r2.getItemProp(processedItem_r2, \"queryParams\"))(\"routerLinkActive\", \"p-menubar-item-link-active\")(\"routerLinkActiveOptions\", ctx_r2.getItemProp(processedItem_r2, \"routerLinkActiveOptions\") || i0.ɵɵpureFunction0(20, _c3))(\"target\", ctx_r2.getItemProp(processedItem_r2, \"target\"))(\"ngClass\", i0.ɵɵpureFunction1(21, _c2, ctx_r2.getItemProp(processedItem_r2, \"disabled\")))(\"fragment\", ctx_r2.getItemProp(processedItem_r2, \"fragment\"))(\"queryParamsHandling\", ctx_r2.getItemProp(processedItem_r2, \"queryParamsHandling\"))(\"preserveFragment\", ctx_r2.getItemProp(processedItem_r2, \"preserveFragment\"))(\"skipLocationChange\", ctx_r2.getItemProp(processedItem_r2, \"skipLocationChange\"))(\"replaceUrl\", ctx_r2.getItemProp(processedItem_r2, \"replaceUrl\"))(\"state\", ctx_r2.getItemProp(processedItem_r2, \"state\"));\n    i0.ɵɵattribute(\"data-automationid\", ctx_r2.getItemProp(processedItem_r2, \"automationId\"))(\"tabindex\", -1)(\"data-pc-section\", \"action\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"icon\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"escape\"))(\"ngIfElse\", htmlRouteLabel_r6);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"badge\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemGroup(processedItem_r2));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_Template, 7, 13, \"a\", 13)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_Template, 7, 23, \"a\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.getItemProp(processedItem_r2, \"routerLink\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"routerLink\"));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_4_1_ng_template_0_Template(rf, ctx) {}\nfunction MenubarSub_ng_template_2_li_1_ng_container_4_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MenubarSub_ng_template_2_li_1_ng_container_4_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_4_1_Template, 1, 0, null, 31);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c4, processedItem_r2.item, ctx_r2.root));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_p_menubarSub_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-menubarSub\", 32);\n    i0.ɵɵlistener(\"itemClick\", function MenubarSub_ng_template_2_li_1_p_menubarSub_5_Template_p_menubarSub_itemClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.itemClick.emit($event));\n    })(\"itemMouseEnter\", function MenubarSub_ng_template_2_li_1_p_menubarSub_5_Template_p_menubarSub_itemMouseEnter_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onItemMouseEnter($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"itemTemplate\", ctx_r2.itemTemplate)(\"items\", processedItem_r2.items)(\"mobileActive\", ctx_r2.mobileActive)(\"autoDisplay\", ctx_r2.autoDisplay)(\"menuId\", ctx_r2.menuId)(\"activeItemPath\", ctx_r2.activeItemPath)(\"focusedItemId\", ctx_r2.focusedItemId)(\"level\", ctx_r2.level + 1)(\"ariaLabelledBy\", ctx_r2.getItemLabelId(processedItem_r2))(\"inlineStyles\", i0.ɵɵpureFunction1(10, _c5, ctx_r2.isItemActive(processedItem_r2) ? \"flex\" : \"none\"));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 9, 1)(2, \"div\", 10);\n    i0.ɵɵlistener(\"click\", function MenubarSub_ng_template_2_li_1_Template_div_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const processedItem_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemClick($event, processedItem_r2));\n    })(\"mouseenter\", function MenubarSub_ng_template_2_li_1_Template_div_mouseenter_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const processedItem_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemMouseEnter({\n        $event: $event,\n        processedItem: processedItem_r2\n      }));\n    });\n    i0.ɵɵtemplate(3, MenubarSub_ng_template_2_li_1_ng_container_3_Template, 3, 2, \"ng-container\", 11)(4, MenubarSub_ng_template_2_li_1_ng_container_4_Template, 2, 5, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, MenubarSub_ng_template_2_li_1_p_menubarSub_5_Template, 1, 12, \"p-menubarSub\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    const processedItem_r2 = ctx_r7.$implicit;\n    const index_r9 = ctx_r7.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r2.getItemProp(processedItem_r2, \"styleClass\"));\n    i0.ɵɵproperty(\"ngStyle\", ctx_r2.getItemProp(processedItem_r2, \"style\"))(\"ngClass\", ctx_r2.getItemClass(processedItem_r2))(\"tooltipOptions\", ctx_r2.getItemProp(processedItem_r2, \"tooltipOptions\"));\n    i0.ɵɵattribute(\"id\", ctx_r2.getItemId(processedItem_r2))(\"data-pc-section\", \"menuitem\")(\"data-p-highlight\", ctx_r2.isItemActive(processedItem_r2))(\"data-p-focused\", ctx_r2.isItemFocused(processedItem_r2))(\"data-p-disabled\", ctx_r2.isItemDisabled(processedItem_r2))(\"aria-label\", ctx_r2.getItemLabel(processedItem_r2))(\"aria-disabled\", ctx_r2.isItemDisabled(processedItem_r2) || undefined)(\"aria-haspopup\", ctx_r2.isItemGroup(processedItem_r2) && !ctx_r2.getItemProp(processedItem_r2, \"to\") ? \"menu\" : undefined)(\"aria-expanded\", ctx_r2.isItemGroup(processedItem_r2) ? ctx_r2.isItemActive(processedItem_r2) : undefined)(\"aria-setsize\", ctx_r2.getAriaSetSize())(\"aria-posinset\", ctx_r2.getAriaPosInset(index_r9));\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"content\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemVisible(processedItem_r2) && ctx_r2.isItemGroup(processedItem_r2));\n  }\n}\nfunction MenubarSub_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MenubarSub_ng_template_2_li_0_Template, 1, 5, \"li\", 6)(1, MenubarSub_ng_template_2_li_1_Template, 6, 20, \"li\", 7);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemVisible(processedItem_r2) && ctx_r2.getItemProp(processedItem_r2, \"separator\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemVisible(processedItem_r2) && !ctx_r2.getItemProp(processedItem_r2, \"separator\"));\n  }\n}\nconst _c6 = [\"start\"];\nconst _c7 = [\"end\"];\nconst _c8 = [\"item\"];\nconst _c9 = [\"menuicon\"];\nconst _c10 = [\"submenuicon\"];\nconst _c11 = [\"menubutton\"];\nconst _c12 = [\"rootmenu\"];\nconst _c13 = [\"*\"];\nconst _c14 = (a0, a1) => ({\n  \"p-menubar p-component\": true,\n  \"p-menubar-mobile\": a0,\n  \"p-menubar-mobile-active\": a1\n});\nfunction Menubar_div_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Menubar_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtemplate(1, Menubar_div_1_ng_container_1_Template, 1, 0, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.startTemplate || ctx_r1._startTemplate);\n  }\n}\nfunction Menubar_a_2_BarsIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"BarsIcon\");\n  }\n}\nfunction Menubar_a_2_3_ng_template_0_Template(rf, ctx) {}\nfunction Menubar_a_2_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Menubar_a_2_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Menubar_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 10, 2);\n    i0.ɵɵlistener(\"click\", function Menubar_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.menuButtonClick($event));\n    })(\"keydown\", function Menubar_a_2_Template_a_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.menuButtonKeydown($event));\n    });\n    i0.ɵɵtemplate(2, Menubar_a_2_BarsIcon_2_Template, 1, 0, \"BarsIcon\", 11)(3, Menubar_a_2_3_Template, 1, 0, null, 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"aria-haspopup\", ctx_r1.model.length && ctx_r1.model.length > 0 ? true : false)(\"aria-expanded\", ctx_r1.mobileActive)(\"aria-controls\", ctx_r1.id)(\"aria-label\", ctx_r1.config.translation.aria.navigation)(\"data-pc-section\", \"button\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.menuIconTemplate && !ctx_r1._menuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.menuIconTemplate || ctx_r1._menuIconTemplate);\n  }\n}\nfunction Menubar_div_5_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Menubar_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtemplate(1, Menubar_div_5_ng_container_1_Template, 1, 0, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.endTemplate || ctx_r1._endTemplate);\n  }\n}\nfunction Menubar_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵprojection(1);\n    i0.ɵɵelementEnd();\n  }\n}\nconst theme = ({\n  dt\n}) => `\n.p-menubar {\n    display: flex;\n    align-items: center;\n    background: ${dt('menubar.background')};\n    border: 1px solid ${dt('menubar.border.color')};\n    border-radius: ${dt('menubar.border.radius')};\n    color: ${dt('menubar.color')};\n    padding: ${dt('menubar.padding')};\n    gap: ${dt('menubar.gap')};\n}\n\n.p-menubar-start,\n.p-megamenu-end {\n    display: flex;\n    align-items: center;\n}\n\n.p-menubar-root-list,\n.p-menubar-submenu {\n    display: flex;\n    margin: 0;\n    padding: 0;\n    list-style: none;\n    outline: 0 none;\n}\n\n.p-menubar-root-list {\n    align-items: center;\n    flex-wrap: wrap;\n    gap: ${dt('menubar.gap')};\n}\n\n.p-menubar-root-list > .p-menubar-item > .p-menubar-item-content {\n    border-radius: ${dt('menubar.base.item.border.radius')};\n}\n\n.p-menubar-root-list > .p-menubar-item > .p-menubar-item-content > .p-menubar-item-link {\n    padding: ${dt('menubar.base.item.padding')};\n}\n\n.p-menubar-item-content {\n    transition: background ${dt('menubar.transition.duration')}, color ${dt('menubar.transition.duration')};\n    border-radius: ${dt('menubar.item.border.radius')};\n    color: ${dt('menubar.item.color')};\n}\n\n.p-menubar-item-link {\n    cursor: pointer;\n    display: flex;\n    align-items: center;\n    text-decoration: none;\n    overflow: hidden;\n    position: relative;\n    color: inherit;\n    padding: ${dt('menubar.item.padding')};\n    gap: ${dt('menubar.item.gap')};\n    user-select: none;\n    outline: 0 none;\n}\n\n.p-menubar-item-label {\n    line-height: 1;\n}\n\n.p-menubar-item-icon {\n    color: ${dt('menubar.item.icon.color')};\n}\n\n.p-menubar-submenu-icon {\n    color: ${dt('menubar.submenu.icon.color')};\n    margin-left: auto;\n    font-size: ${dt('menubar.submenu.icon.size')};\n    width: ${dt('menubar.submenu.icon.size')};\n    height: ${dt('menubar.submenu.icon.size')};\n}\n\n.p-menubar-submenu .p-menubar-submenu-icon:dir(rtl) {\n    margin-left: 0;\n    margin-right: auto;\n}\n\n.p-menubar-item.p-focus > .p-menubar-item-content {\n    color: ${dt('menubar.item.focus.color')};\n    background: ${dt('menubar.item.focus.background')};\n}\n\n.p-menubar-item.p-focus > .p-menubar-item-content .p-menubar-item-icon {\n    color: ${dt('menubar.item.icon.focus.color')};\n}\n\n.p-menubar-item.p-focus > .p-menubar-item-content .p-menubar-submenu-icon {\n    color: ${dt('menubar.submenu.icon.focus.color')};\n}\n\n.p-menubar-item:not(.p-disabled) > .p-menubar-item-content:hover {\n    color: ${dt('menubar.item.focus.color')};\n    background: ${dt('menubar.item.focus.background')};\n}\n\n.p-menubar-item:not(.p-disabled) > .p-menubar-item-content:hover .p-menubar-item-icon {\n    color: ${dt('menubar.item.icon.focus.color')};\n}\n\n.p-menubar-item:not(.p-disabled) > .p-menubar-item-content:hover .p-menubar-submenu-icon {\n    color: ${dt('menubar.submenu.icon.focus.color')};\n}\n\n.p-menubar-item-active > .p-menubar-item-content {\n    color: ${dt('menubar.item.active.color')};\n    background: ${dt('menubar.item.active.background')};\n}\n\n.p-menubar-item-active > .p-menubar-item-content .p-menubar-item-icon {\n    color: ${dt('menubar.item.icon.active.color')};\n}\n\n.p-menubar-item-active > .p-menubar-item-content .p-menubar-submenu-icon {\n    color: ${dt('menubar.submenu.icon.active.color')};\n}\n\n.p-menubar-submenu {\n    display: none;\n    position: absolute;\n    min-width: 12.5rem;\n    z-index: 1;\n    background: ${dt('menubar.submenu.background')};\n    border: 1px solid ${dt('menubar.submenu.border.color')};\n    border-radius: ${dt('menubar.border.radius')};\n    box-shadow: ${dt('menubar.submenu.shadow')};\n    color: ${dt('menubar.submenu.color')};\n    flex-direction: column;\n    padding: ${dt('menubar.submenu.padding')};\n    gap: ${dt('menubar.submenu.gap')};\n}\n\n.p-menubar-submenu .p-menubar-separator {\n    border-top: 1px solid ${dt('menubar.separator.border.color')};\n}\n\n.p-menubar-submenu .p-menubar-item {\n    position: relative;\n}\n\n.p-menubar-submenu > .p-menubar-item-active .p-menubar-submenu {\n    display: block;\n    left: 100%;\n    top: 0;\n}\n\n.p-menubar-end {\n    margin-left: auto;\n    align-self: center;\n}\n\n.p-menubar-end:dir(rtl) {\n    margin-left: 0;\n    margin-right: auto;\n}\n\n.p-menubar-button {\n    display: none;\n    justify-content: center;\n    align-items: center;\n    cursor: pointer;\n    width: ${dt('menubar.mobile.button.size')};\n    height: ${dt('menubar.mobile.button.size')};\n    position: relative;\n    color: ${dt('menubar.mobile.button.color')};\n    border: 0 none;\n    background: transparent;\n    border-radius: ${dt('menubar.mobile.button.border.radius')};\n    transition: background ${dt('menubar.transition.duration')}, color ${dt('menubar.transition.duration')}, outline-color ${dt('menubar.transition.duration')};\n    outline-color: transparent;\n}\n\n.p-menubar-button:hover {\n    color: ${dt('menubar.mobile.button.hover.color')};\n    background: ${dt('menubar.mobile.button.hover.background')};\n}\n\n.p-menubar-button:focus-visible {\n    box-shadow: ${dt('menubar.mobile.button.focus.ring.shadow')};\n    outline: ${dt('menubar.mobile.button.focus.ring.width')} ${dt('menubar.mobile.button.focus.ring.style')} ${dt('menubar.mobile.button.focus.ring.color')};\n    outline-offset: ${dt('menubar.mobile.button.focus.ring.offset')};\n}\n\n.p-menubar-mobile {\n    position: relative;\n}\n\n.p-menubar-mobile .p-menubar-button {\n    display: flex;\n}\n\n.p-menubar-mobile .p-menubar-root-list {\n    position: absolute;\n    display: none;\n    width: 100%;\n    padding: ${dt('menubar.submenu.padding')};\n    background: ${dt('menubar.submenu.background')};\n    border: 1px solid ${dt('menubar.submenu.border.color')};\n    box-shadow: ${dt('menubar.submenu.shadow')};\n}\n\n.p-menubar-mobile .p-menubar-root-list > .p-menubar-item > .p-menubar-item-content {\n    border-radius: ${dt('menubar.item.border.radius')};\n}\n\n.p-menubar-mobile .p-menubar-root-list > .p-menubar-item > .p-menubar-item-content > .p-menubar-item-link {\n    padding: ${dt('menubar.item.padding')};\n}\n\n.p-menubar-mobile-active .p-menubar-root-list {\n    display: flex;\n    flex-direction: column;\n    top: 100%;\n    left: 0;\n    z-index: 1;\n}\n\n.p-menubar-mobile .p-menubar-root-list:dir(rtl) {\n    left: auto;\n    right: 0;\n}\n\n.p-menubar-mobile .p-menubar-root-list .p-menubar-item {\n    width: 100%;\n    position: static;\n}\n\n.p-menubar-mobile .p-menubar-root-list .p-menubar-separator {\n    border-top: 1px solid ${dt('menubar.separator.border.color')};\n}\n\n.p-menubar-mobile .p-menubar-root-list > .p-menubar-item > .p-menubar-item-content .p-menubar-submenu-icon {\n    margin-left: auto;\n    transition: transform 0.2s;\n}\n\n.p-menubar-mobile .p-menubar-root-list > .p-menubar-item > .p-menubar-item-content .p-menubar-submenu-icon:dir(rtl) {\n    margin-left: 0;\n    margin-right: auto;\n}\n\n.p-menubar-mobile .p-menubar-root-list > .p-menubar-item-active > .p-menubar-item-content .p-menubar-submenu-icon {\n    transform: rotate(-180deg);\n}\n\n.p-menubar-mobile .p-menubar-submenu .p-menubar-submenu-icon {\n    transition: transform 0.2s;\n    transform: rotate(90deg);\n}\n\n.p-menubar-mobile  .p-menubar-item-active > .p-menubar-item-content .p-menubar-submenu-icon {\n    transform: rotate(-90deg);\n}\n\n.p-menubar-mobile .p-menubar-submenu {\n    width: 100%;\n    position: static;\n    box-shadow: none;\n    border: 0 none;\n    padding-left: ${dt('menubar.submenu.mobile.indent')};\n}\n`;\nconst inlineStyles = {\n  submenu: ({\n    instance,\n    processedItem\n  }) => ({\n    display: instance.isItemActive(processedItem) ? 'flex' : 'none'\n  })\n};\nconst classes = {\n  root: ({\n    instance\n  }) => ['p-menubar p-component', {\n    'p-menubar-mobile': instance.queryMatches,\n    'p-menubar-mobile-active': instance.mobileActive\n  }],\n  start: 'p-menubar-start',\n  button: 'p-menubar-button',\n  rootList: 'p-menubar-root-list',\n  item: ({\n    instance,\n    processedItem\n  }) => ['p-menubar-item', {\n    'p-menubar-item-active': instance.isItemActive(processedItem),\n    'p-focus': instance.isItemFocused(processedItem),\n    'p-disabled': instance.isItemDisabled(processedItem)\n  }],\n  itemContent: 'p-menubar-item-content',\n  itemLink: 'p-menubar-item-link',\n  itemIcon: 'p-menubar-item-icon',\n  itemLabel: 'p-menubar-item-label',\n  submenuIcon: 'p-menubar-submenu-icon',\n  submenu: 'p-menubar-submenu',\n  separator: 'p-menubar-separator',\n  end: 'p-menubar-end'\n};\nclass MenuBarStyle extends BaseStyle {\n  name = 'menubar';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMenuBarStyle_BaseFactory;\n    return function MenuBarStyle_Factory(__ngFactoryType__) {\n      return (ɵMenuBarStyle_BaseFactory || (ɵMenuBarStyle_BaseFactory = i0.ɵɵgetInheritedFactory(MenuBarStyle)))(__ngFactoryType__ || MenuBarStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MenuBarStyle,\n    factory: MenuBarStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MenuBarStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * Menubar is a horizontal menu component.\n *\n * [Live Demo](https://www.primeng.org/menubar/)\n *\n * @module menubarstyle\n *\n */\nvar MenubarClasses;\n(function (MenubarClasses) {\n  /**\n   * Class name of the root element\n   */\n  MenubarClasses[\"root\"] = \"p-menubar\";\n  /**\n   * Class name of the start element\n   */\n  MenubarClasses[\"start\"] = \"p-menubar-start\";\n  /**\n   * Class name of the button element\n   */\n  MenubarClasses[\"button\"] = \"p-menubar-button\";\n  /**\n   * Class name of the root list element\n   */\n  MenubarClasses[\"rootList\"] = \"p-menubar-root-list\";\n  /**\n   * Class name of the item element\n   */\n  MenubarClasses[\"item\"] = \"p-menubar-item\";\n  /**\n   * Class name of the item content element\n   */\n  MenubarClasses[\"itemContent\"] = \"p-menubar-item-content\";\n  /**\n   * Class name of the item link element\n   */\n  MenubarClasses[\"itemLink\"] = \"p-menubar-item-link\";\n  /**\n   * Class name of the item icon element\n   */\n  MenubarClasses[\"itemIcon\"] = \"p-menubar-item-icon\";\n  /**\n   * Class name of the item label element\n   */\n  MenubarClasses[\"itemLabel\"] = \"p-menubar-item-label\";\n  /**\n   * Class name of the submenu icon element\n   */\n  MenubarClasses[\"submenuIcon\"] = \"p-menubar-submenu-icon\";\n  /**\n   * Class name of the submenu element\n   */\n  MenubarClasses[\"submenu\"] = \"p-menubar-submenu\";\n  /**\n   * Class name of the separator element\n   */\n  MenubarClasses[\"separator\"] = \"p-menubar-separator\";\n  /**\n   * Class name of the end element\n   */\n  MenubarClasses[\"end\"] = \"p-menubar-end\";\n})(MenubarClasses || (MenubarClasses = {}));\nclass MenubarService {\n  autoHide;\n  autoHideDelay;\n  mouseLeaves = new Subject();\n  mouseLeft$ = this.mouseLeaves.pipe(debounce(() => interval(this.autoHideDelay)), filter(mouseLeft => this.autoHide && mouseLeft));\n  static ɵfac = function MenubarService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MenubarService)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MenubarService,\n    factory: MenubarService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MenubarService, [{\n    type: Injectable\n  }], null, null);\n})();\nclass MenubarSub extends BaseComponent {\n  items;\n  itemTemplate;\n  root = false;\n  autoZIndex = true;\n  baseZIndex = 0;\n  mobileActive;\n  autoDisplay;\n  menuId;\n  ariaLabel;\n  ariaLabelledBy;\n  level = 0;\n  focusedItemId;\n  activeItemPath;\n  inlineStyles;\n  submenuiconTemplate;\n  itemClick = new EventEmitter();\n  itemMouseEnter = new EventEmitter();\n  menuFocus = new EventEmitter();\n  menuBlur = new EventEmitter();\n  menuKeydown = new EventEmitter();\n  menubarViewChild;\n  mouseLeaveSubscriber;\n  menubarService = inject(MenubarService);\n  ngOnInit() {\n    super.ngOnInit();\n    this.mouseLeaveSubscriber = this.menubarService.mouseLeft$.subscribe(() => {\n      this.cd.markForCheck();\n    });\n  }\n  onItemClick(event, processedItem) {\n    this.getItemProp(processedItem, 'command', {\n      originalEvent: event,\n      item: processedItem.item\n    });\n    this.itemClick.emit({\n      originalEvent: event,\n      processedItem,\n      isFocus: true\n    });\n  }\n  getItemProp(processedItem, name, params = null) {\n    return processedItem && processedItem.item ? resolve(processedItem.item[name], params) : undefined;\n  }\n  getItemId(processedItem) {\n    return processedItem.item && processedItem.item?.id ? processedItem.item.id : `${this.menuId}_${processedItem.key}`;\n  }\n  getItemKey(processedItem) {\n    return this.getItemId(processedItem);\n  }\n  getItemLabelId(processedItem) {\n    return `${this.menuId}_${processedItem.key}_label`;\n  }\n  getItemClass(processedItem) {\n    return {\n      ...this.getItemProp(processedItem, 'class'),\n      'p-menubar-item': true,\n      'p-menubar-item-active': this.isItemActive(processedItem),\n      'p-focus': this.isItemFocused(processedItem),\n      'p-disabled': this.isItemDisabled(processedItem)\n    };\n  }\n  getItemLabel(processedItem) {\n    return this.getItemProp(processedItem, 'label');\n  }\n  getSeparatorItemClass(processedItem) {\n    return {\n      ...this.getItemProp(processedItem, 'class'),\n      'p-menubar-separator': true\n    };\n  }\n  isItemVisible(processedItem) {\n    return this.getItemProp(processedItem, 'visible') !== false;\n  }\n  isItemActive(processedItem) {\n    if (this.activeItemPath) {\n      return this.activeItemPath.some(path => path.key === processedItem.key);\n    }\n  }\n  isItemDisabled(processedItem) {\n    return this.getItemProp(processedItem, 'disabled');\n  }\n  isItemFocused(processedItem) {\n    return this.focusedItemId === this.getItemId(processedItem);\n  }\n  isItemGroup(processedItem) {\n    return isNotEmpty(processedItem.items);\n  }\n  getAriaSetSize() {\n    return this.items.filter(processedItem => this.isItemVisible(processedItem) && !this.getItemProp(processedItem, 'separator')).length;\n  }\n  getAriaPosInset(index) {\n    return index - this.items.slice(0, index).filter(processedItem => this.isItemVisible(processedItem) && this.getItemProp(processedItem, 'separator')).length + 1;\n  }\n  onItemMouseLeave() {\n    this.menubarService.mouseLeaves.next(true);\n  }\n  onItemMouseEnter(param) {\n    if (this.autoDisplay) {\n      this.menubarService.mouseLeaves.next(false);\n      const {\n        event,\n        processedItem\n      } = param;\n      this.itemMouseEnter.emit({\n        originalEvent: event,\n        processedItem\n      });\n    }\n  }\n  ngOnDestroy() {\n    this.mouseLeaveSubscriber?.unsubscribe();\n    super.ngOnDestroy();\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMenubarSub_BaseFactory;\n    return function MenubarSub_Factory(__ngFactoryType__) {\n      return (ɵMenubarSub_BaseFactory || (ɵMenubarSub_BaseFactory = i0.ɵɵgetInheritedFactory(MenubarSub)))(__ngFactoryType__ || MenubarSub);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MenubarSub,\n    selectors: [[\"p-menubarSub\"], [\"p-menubarsub\"]],\n    viewQuery: function MenubarSub_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menubarViewChild = _t.first);\n      }\n    },\n    inputs: {\n      items: \"items\",\n      itemTemplate: \"itemTemplate\",\n      root: [2, \"root\", \"root\", booleanAttribute],\n      autoZIndex: [2, \"autoZIndex\", \"autoZIndex\", booleanAttribute],\n      baseZIndex: [2, \"baseZIndex\", \"baseZIndex\", numberAttribute],\n      mobileActive: [2, \"mobileActive\", \"mobileActive\", booleanAttribute],\n      autoDisplay: [2, \"autoDisplay\", \"autoDisplay\", booleanAttribute],\n      menuId: \"menuId\",\n      ariaLabel: \"ariaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\",\n      level: [2, \"level\", \"level\", numberAttribute],\n      focusedItemId: \"focusedItemId\",\n      activeItemPath: \"activeItemPath\",\n      inlineStyles: \"inlineStyles\",\n      submenuiconTemplate: \"submenuiconTemplate\"\n    },\n    outputs: {\n      itemClick: \"itemClick\",\n      itemMouseEnter: \"itemMouseEnter\",\n      menuFocus: \"menuFocus\",\n      menuBlur: \"menuBlur\",\n      menuKeydown: \"menuKeydown\"\n    },\n    features: [i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature],\n    decls: 3,\n    vars: 12,\n    consts: [[\"menubar\", \"\"], [\"listItem\", \"\"], [\"htmlLabel\", \"\"], [\"htmlRouteLabel\", \"\"], [\"role\", \"menubar\", 3, \"focus\", \"blur\", \"keydown\", \"ngClass\", \"tabindex\", \"ngStyle\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"role\", \"separator\", 3, \"style\", \"ngClass\", 4, \"ngIf\"], [\"role\", \"menuitem\", \"pTooltip\", \"\", 3, \"ngStyle\", \"ngClass\", \"class\", \"tooltipOptions\", 4, \"ngIf\"], [\"role\", \"separator\", 3, \"ngClass\"], [\"role\", \"menuitem\", \"pTooltip\", \"\", 3, \"ngStyle\", \"ngClass\", \"tooltipOptions\"], [1, \"p-menubar-item-content\", 3, \"click\", \"mouseenter\"], [4, \"ngIf\"], [3, \"itemTemplate\", \"items\", \"mobileActive\", \"autoDisplay\", \"menuId\", \"activeItemPath\", \"focusedItemId\", \"level\", \"ariaLabelledBy\", \"inlineStyles\", \"itemClick\", \"itemMouseEnter\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"target\", \"ngClass\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"target\", \"ngClass\"], [\"class\", \"p-menubar-item-icon\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-menubar-item-label\", 3, \"id\", 4, \"ngIf\", \"ngIfElse\"], [3, \"styleClass\", \"value\", 4, \"ngIf\"], [1, \"p-menubar-item-icon\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-menubar-item-label\", 3, \"id\"], [1, \"p-menubar-item-label\", 3, \"innerHTML\", \"id\"], [3, \"styleClass\", \"value\"], [4, \"ngTemplateOutlet\"], [\"class\", \"p-menubar-submenu-icon\", 4, \"ngIf\"], [1, \"p-menubar-submenu-icon\"], [3, \"data-pc-section\"], [\"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\"], [\"class\", \"p-menubar-item-label\", 4, \"ngIf\", \"ngIfElse\"], [1, \"p-menubar-item-label\"], [1, \"p-menubar-item-label\", 3, \"innerHTML\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"itemClick\", \"itemMouseEnter\", \"itemTemplate\", \"items\", \"mobileActive\", \"autoDisplay\", \"menuId\", \"activeItemPath\", \"focusedItemId\", \"level\", \"ariaLabelledBy\", \"inlineStyles\"]],\n    template: function MenubarSub_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"ul\", 4, 0);\n        i0.ɵɵlistener(\"focus\", function MenubarSub_Template_ul_focus_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.menuFocus.emit($event));\n        })(\"blur\", function MenubarSub_Template_ul_blur_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.menuBlur.emit($event));\n        })(\"keydown\", function MenubarSub_Template_ul_keydown_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.menuKeydown.emit($event));\n        });\n        i0.ɵɵtemplate(2, MenubarSub_ng_template_2_Template, 2, 2, \"ng-template\", 5);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(9, _c1, !ctx.root, ctx.root))(\"tabindex\", 0)(\"ngStyle\", ctx.inlineStyles);\n        i0.ɵɵattribute(\"data-pc-section\", \"menu\")(\"aria-label\", ctx.ariaLabel)(\"aria-labelledBy\", ctx.ariaLabelledBy)(\"id\", ctx.root ? ctx.menuId : null)(\"aria-activedescendant\", ctx.focusedItemId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.items);\n      }\n    },\n    dependencies: [MenubarSub, CommonModule, i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, RouterModule, i2.RouterLink, i2.RouterLinkActive, Ripple, TooltipModule, i3.Tooltip, AngleDownIcon, AngleRightIcon, BadgeModule, i4.Badge, SharedModule],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MenubarSub, [{\n    type: Component,\n    args: [{\n      selector: 'p-menubarSub, p-menubarsub',\n      standalone: true,\n      imports: [CommonModule, RouterModule, Ripple, TooltipModule, AngleDownIcon, AngleRightIcon, BadgeModule, SharedModule],\n      template: `\n        <ul\n            #menubar\n            [ngClass]=\"{ 'p-menubar-submenu': !root, 'p-menubar-root-list': root }\"\n            [attr.data-pc-section]=\"'menu'\"\n            role=\"menubar\"\n            (focus)=\"menuFocus.emit($event)\"\n            (blur)=\"menuBlur.emit($event)\"\n            [tabindex]=\"0\"\n            [attr.aria-label]=\"ariaLabel\"\n            [attr.aria-labelledBy]=\"ariaLabelledBy\"\n            (keydown)=\"menuKeydown.emit($event)\"\n            [attr.id]=\"root ? menuId : null\"\n            [attr.aria-activedescendant]=\"focusedItemId\"\n            [ngStyle]=\"inlineStyles\"\n        >\n            <ng-template ngFor let-processedItem [ngForOf]=\"items\" let-index=\"index\">\n                <li\n                    *ngIf=\"isItemVisible(processedItem) && getItemProp(processedItem, 'separator')\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [style]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getSeparatorItemClass(processedItem)\"\n                    role=\"separator\"\n                    [attr.data-pc-section]=\"'separator'\"\n                ></li>\n                <li\n                    #listItem\n                    *ngIf=\"isItemVisible(processedItem) && !getItemProp(processedItem, 'separator')\"\n                    role=\"menuitem\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [attr.data-pc-section]=\"'menuitem'\"\n                    [attr.data-p-highlight]=\"isItemActive(processedItem)\"\n                    [attr.data-p-focused]=\"isItemFocused(processedItem)\"\n                    [attr.data-p-disabled]=\"isItemDisabled(processedItem)\"\n                    [attr.aria-label]=\"getItemLabel(processedItem)\"\n                    [attr.aria-disabled]=\"isItemDisabled(processedItem) || undefined\"\n                    [attr.aria-haspopup]=\"isItemGroup(processedItem) && !getItemProp(processedItem, 'to') ? 'menu' : undefined\"\n                    [attr.aria-expanded]=\"isItemGroup(processedItem) ? isItemActive(processedItem) : undefined\"\n                    [attr.aria-setsize]=\"getAriaSetSize()\"\n                    [attr.aria-posinset]=\"getAriaPosInset(index)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getItemClass(processedItem)\"\n                    [class]=\"getItemProp(processedItem, 'styleClass')\"\n                    pTooltip\n                    [tooltipOptions]=\"getItemProp(processedItem, 'tooltipOptions')\"\n                >\n                    <div class=\"p-menubar-item-content\" [attr.data-pc-section]=\"'content'\" (click)=\"onItemClick($event, processedItem)\" (mouseenter)=\"onItemMouseEnter({ $event, processedItem })\">\n                        <ng-container *ngIf=\"!itemTemplate\">\n                            <a\n                                *ngIf=\"!getItemProp(processedItem, 'routerLink')\"\n                                [attr.href]=\"getItemProp(processedItem, 'url')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menubar-item-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [attr.tabindex]=\"-1\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-menubar-item-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menubar-item-label\" [attr.data-pc-section]=\"'label'\" [id]=\"getItemLabelId(processedItem)\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-menubar-item-label\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\" [id]=\"getItemLabelId(processedItem)\"></span>\n                                </ng-template>\n                                <p-badge *ngIf=\"getItemProp(processedItem, 'badge')\" [styleClass]=\"getItemProp(processedItem, 'badgeStyleClass')\" [value]=\"getItemProp(processedItem, 'badge')\" />\n\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!submenuiconTemplate\">\n                                        <AngleDownIcon class=\"p-menubar-submenu-icon\" *ngIf=\"root\" [attr.data-pc-section]=\"'submenuicon'\" />\n                                        <AngleRightIcon class=\"p-menubar-submenu-icon\" *ngIf=\"!root\" [attr.data-pc-section]=\"'submenuicon'\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"submenuiconTemplate\" [attr.data-pc-section]=\"'submenuicon'\"></ng-template>\n                                </ng-container>\n                            </a>\n                            <a\n                                *ngIf=\"getItemProp(processedItem, 'routerLink')\"\n                                [routerLink]=\"getItemProp(processedItem, 'routerLink')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.tabindex]=\"-1\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [queryParams]=\"getItemProp(processedItem, 'queryParams')\"\n                                [routerLinkActive]=\"'p-menubar-item-link-active'\"\n                                [routerLinkActiveOptions]=\"getItemProp(processedItem, 'routerLinkActiveOptions') || { exact: false }\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menubar-item-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [fragment]=\"getItemProp(processedItem, 'fragment')\"\n                                [queryParamsHandling]=\"getItemProp(processedItem, 'queryParamsHandling')\"\n                                [preserveFragment]=\"getItemProp(processedItem, 'preserveFragment')\"\n                                [skipLocationChange]=\"getItemProp(processedItem, 'skipLocationChange')\"\n                                [replaceUrl]=\"getItemProp(processedItem, 'replaceUrl')\"\n                                [state]=\"getItemProp(processedItem, 'state')\"\n                                pRipple\n                            >\n                                <span\n                                    class=\"p-menubar-item-icon\"\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.tabindex]=\"-1\"\n                                ></span>\n                                <span class=\"p-menubar-item-label\" *ngIf=\"getItemProp(processedItem, 'escape'); else htmlRouteLabel\">{{ getItemLabel(processedItem) }}</span>\n                                <ng-template #htmlRouteLabel><span class=\"p-menubar-item-label\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span></ng-template>\n                                <p-badge *ngIf=\"getItemProp(processedItem, 'badge')\" [styleClass]=\"getItemProp(processedItem, 'badgeStyleClass')\" [value]=\"getItemProp(processedItem, 'badge')\" />\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!submenuiconTemplate\">\n                                        <AngleDownIcon class=\"p-menubar-submenu-icon\" [attr.data-pc-section]=\"'submenuicon'\" *ngIf=\"root\" />\n                                        <AngleRightIcon class=\"p-menubar-submenu-icon\" [attr.data-pc-section]=\"'submenuicon'\" *ngIf=\"!root\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"submenuiconTemplate\" [attr.data-pc-section]=\"'submenuicon'\"></ng-template>\n                                </ng-container>\n                            </a>\n                        </ng-container>\n                        <ng-container *ngIf=\"itemTemplate\">\n                            <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: processedItem.item, root: root }\"></ng-template>\n                        </ng-container>\n                    </div>\n                    <p-menubarSub\n                        *ngIf=\"isItemVisible(processedItem) && isItemGroup(processedItem)\"\n                        [itemTemplate]=\"itemTemplate\"\n                        [items]=\"processedItem.items\"\n                        [mobileActive]=\"mobileActive\"\n                        [autoDisplay]=\"autoDisplay\"\n                        [menuId]=\"menuId\"\n                        [activeItemPath]=\"activeItemPath\"\n                        [focusedItemId]=\"focusedItemId\"\n                        [level]=\"level + 1\"\n                        [ariaLabelledBy]=\"getItemLabelId(processedItem)\"\n                        (itemClick)=\"itemClick.emit($event)\"\n                        (itemMouseEnter)=\"onItemMouseEnter($event)\"\n                        [inlineStyles]=\"{ display: isItemActive(processedItem) ? 'flex' : 'none' }\"\n                    >\n                    </p-menubarSub>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n      encapsulation: ViewEncapsulation.None\n    }]\n  }], null, {\n    items: [{\n      type: Input\n    }],\n    itemTemplate: [{\n      type: Input\n    }],\n    root: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    autoZIndex: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    baseZIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    mobileActive: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    autoDisplay: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    menuId: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    level: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    focusedItemId: [{\n      type: Input\n    }],\n    activeItemPath: [{\n      type: Input\n    }],\n    inlineStyles: [{\n      type: Input\n    }],\n    submenuiconTemplate: [{\n      type: Input\n    }],\n    itemClick: [{\n      type: Output\n    }],\n    itemMouseEnter: [{\n      type: Output\n    }],\n    menuFocus: [{\n      type: Output\n    }],\n    menuBlur: [{\n      type: Output\n    }],\n    menuKeydown: [{\n      type: Output\n    }],\n    menubarViewChild: [{\n      type: ViewChild,\n      args: ['menubar', {\n        static: true\n      }]\n    }]\n  });\n})();\n/**\n * Menubar is a horizontal menu component.\n * @group Components\n */\nclass Menubar extends BaseComponent {\n  document;\n  platformId;\n  el;\n  renderer;\n  cd;\n  menubarService;\n  /**\n   * An array of menuitems.\n   * @group Props\n   */\n  set model(value) {\n    this._model = value;\n    this._processedItems = this.createProcessedItems(this._model || []);\n  }\n  get model() {\n    return this._model;\n  }\n  /**\n   * Inline style of the element.\n   * @group Props\n   */\n  style;\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Whether to automatically manage layering.\n   * @group Props\n   */\n  autoZIndex = true;\n  /**\n   * Base zIndex value to use in layering.\n   * @group Props\n   */\n  baseZIndex = 0;\n  /**\n   * Whether to show a root submenu on mouse over.\n   * @defaultValue true\n   * @group Props\n   */\n  autoDisplay = false;\n  /**\n   * Whether to hide a root submenu when mouse leaves.\n   * @group Props\n   */\n  autoHide;\n  /**\n   * The breakpoint to define the maximum width boundary.\n   * @group Props\n   */\n  breakpoint = '960px';\n  /**\n   * Delay to hide the root submenu in milliseconds when mouse leaves.\n   * @group Props\n   */\n  autoHideDelay = 100;\n  /**\n   * Current id state as a string.\n   * @group Props\n   */\n  id;\n  /**\n   * Defines a string value that labels an interactive element.\n   * @group Props\n   */\n  ariaLabel;\n  /**\n   * Identifier of the underlying input element.\n   * @group Props\n   */\n  ariaLabelledBy;\n  /**\n   * Callback to execute when button is focused.\n   * @param {FocusEvent} event - Focus event.\n   * @group Emits\n   */\n  onFocus = new EventEmitter();\n  /**\n   * Callback to execute when button loses focus.\n   * @param {FocusEvent} event - Focus event.\n   * @group Emits\n   */\n  onBlur = new EventEmitter();\n  menubutton;\n  rootmenu;\n  mobileActive;\n  matchMediaListener;\n  query;\n  queryMatches;\n  outsideClickListener;\n  resizeListener;\n  mouseLeaveSubscriber;\n  dirty = false;\n  focused = false;\n  activeItemPath = signal([]);\n  number = signal(0);\n  focusedItemInfo = signal({\n    index: -1,\n    level: 0,\n    parentKey: '',\n    item: null\n  });\n  searchValue = '';\n  searchTimeout;\n  _processedItems;\n  _componentStyle = inject(MenuBarStyle);\n  _model;\n  get visibleItems() {\n    const processedItem = this.activeItemPath().find(p => p.key === this.focusedItemInfo().parentKey);\n    return processedItem ? processedItem.items : this.processedItems;\n  }\n  get processedItems() {\n    if (!this._processedItems || !this._processedItems.length) {\n      this._processedItems = this.createProcessedItems(this.model || []);\n    }\n    return this._processedItems;\n  }\n  get focusedItemId() {\n    const focusedItem = this.focusedItemInfo();\n    return focusedItem.item && focusedItem.item?.id ? focusedItem.item.id : focusedItem.index !== -1 ? `${this.id}${isNotEmpty(focusedItem.parentKey) ? '_' + focusedItem.parentKey : ''}_${focusedItem.index}` : null;\n  }\n  constructor(document, platformId, el, renderer, cd, menubarService) {\n    super();\n    this.document = document;\n    this.platformId = platformId;\n    this.el = el;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.menubarService = menubarService;\n    effect(() => {\n      const path = this.activeItemPath();\n      if (isNotEmpty(path)) {\n        this.bindOutsideClickListener();\n        this.bindResizeListener();\n      } else {\n        this.unbindOutsideClickListener();\n        this.unbindResizeListener();\n      }\n    });\n  }\n  ngOnInit() {\n    super.ngOnInit();\n    this.bindMatchMediaListener();\n    this.menubarService.autoHide = this.autoHide;\n    this.menubarService.autoHideDelay = this.autoHideDelay;\n    this.mouseLeaveSubscriber = this.menubarService.mouseLeft$.subscribe(() => this.unbindOutsideClickListener());\n    this.id = this.id || uuid('pn_id_');\n  }\n  /**\n   * Defines template option for start.\n   * @group Templates\n   */\n  startTemplate;\n  /**\n   * Defines template option for end.\n   * @group Templates\n   */\n  endTemplate;\n  /**\n   * Defines template option for item.\n   * @group Templates\n   */\n  itemTemplate;\n  /**\n   * Defines template option for item.\n   * @group Templates\n   */\n  menuIconTemplate;\n  /**\n   * Defines template option for submenu icon.\n   * @group Templates\n   */\n  submenuIconTemplate;\n  templates;\n  _startTemplate;\n  _endTemplate;\n  _itemTemplate;\n  _menuIconTemplate;\n  _submenuIconTemplate;\n  ngAfterContentInit() {\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'start':\n          this._startTemplate = item.template;\n          break;\n        case 'end':\n          this._endTemplate = item.template;\n          break;\n        case 'menuicon':\n          this._menuIconTemplate = item.template;\n          break;\n        case 'submenuicon':\n          this._submenuIconTemplate = item.template;\n          break;\n        case 'item':\n          this._itemTemplate = item.template;\n          break;\n        default:\n          this._itemTemplate = item.template;\n          break;\n      }\n    });\n  }\n  createProcessedItems(items, level = 0, parent = {}, parentKey = '') {\n    const processedItems = [];\n    items && items.forEach((item, index) => {\n      const key = (parentKey !== '' ? parentKey + '_' : '') + index;\n      const newItem = {\n        item,\n        index,\n        level,\n        key,\n        parent,\n        parentKey\n      };\n      newItem['items'] = this.createProcessedItems(item.items, level + 1, newItem, key);\n      processedItems.push(newItem);\n    });\n    return processedItems;\n  }\n  bindMatchMediaListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.matchMediaListener) {\n        const query = window.matchMedia(`(max-width: ${this.breakpoint})`);\n        this.query = query;\n        this.queryMatches = query.matches;\n        this.matchMediaListener = () => {\n          this.queryMatches = query.matches;\n          this.mobileActive = false;\n          this.cd.markForCheck();\n        };\n        query.addEventListener('change', this.matchMediaListener);\n      }\n    }\n  }\n  unbindMatchMediaListener() {\n    if (this.matchMediaListener) {\n      this.query.removeEventListener('change', this.matchMediaListener);\n      this.matchMediaListener = null;\n    }\n  }\n  getItemProp(item, name) {\n    return item ? resolve(item[name]) : undefined;\n  }\n  menuButtonClick(event) {\n    this.toggle(event);\n  }\n  menuButtonKeydown(event) {\n    (event.code === 'Enter' || event.code === 'Space') && this.menuButtonClick(event);\n  }\n  onItemClick(event) {\n    const {\n      originalEvent,\n      processedItem\n    } = event;\n    const grouped = this.isProcessedItemGroup(processedItem);\n    const root = isEmpty(processedItem.parent);\n    const selected = this.isSelected(processedItem);\n    if (selected) {\n      const {\n        index,\n        key,\n        level,\n        parentKey,\n        item\n      } = processedItem;\n      this.activeItemPath.set(this.activeItemPath().filter(p => key !== p.key && key.startsWith(p.key)));\n      this.focusedItemInfo.set({\n        index,\n        level,\n        parentKey,\n        item\n      });\n      this.dirty = !root;\n      focus(this.rootmenu.menubarViewChild.nativeElement);\n    } else {\n      if (grouped) {\n        this.onItemChange(event);\n      } else {\n        const rootProcessedItem = root ? processedItem : this.activeItemPath().find(p => p.parentKey === '');\n        this.hide(originalEvent);\n        this.changeFocusedItemIndex(originalEvent, rootProcessedItem ? rootProcessedItem.index : -1);\n        this.mobileActive = false;\n        focus(this.rootmenu.menubarViewChild.nativeElement);\n      }\n    }\n  }\n  onItemMouseEnter(event) {\n    if (!isTouchDevice()) {\n      if (!this.mobileActive) {\n        this.onItemChange(event);\n      }\n    }\n  }\n  changeFocusedItemIndex(event, index) {\n    const processedItem = this.findVisibleItem(index);\n    if (this.focusedItemInfo().index !== index) {\n      const focusedItemInfo = this.focusedItemInfo();\n      this.focusedItemInfo.set({\n        ...focusedItemInfo,\n        item: processedItem.item,\n        index\n      });\n      this.scrollInView();\n    }\n  }\n  scrollInView(index = -1) {\n    const id = index !== -1 ? `${this.id}_${index}` : this.focusedItemId;\n    const element = findSingle(this.rootmenu.el.nativeElement, `li[id=\"${id}\"]`);\n    if (element) {\n      element.scrollIntoView && element.scrollIntoView({\n        block: 'nearest',\n        inline: 'nearest'\n      });\n    }\n  }\n  onItemChange(event) {\n    const {\n      processedItem,\n      isFocus\n    } = event;\n    if (isEmpty(processedItem)) return;\n    const {\n      index,\n      key,\n      level,\n      parentKey,\n      items,\n      item\n    } = processedItem;\n    const grouped = isNotEmpty(items);\n    const activeItemPath = this.activeItemPath().filter(p => p.parentKey !== parentKey && p.parentKey !== key);\n    grouped && activeItemPath.push(processedItem);\n    this.focusedItemInfo.set({\n      index,\n      level,\n      parentKey,\n      item\n    });\n    this.activeItemPath.set(activeItemPath);\n    grouped && (this.dirty = true);\n    isFocus && focus(this.rootmenu.menubarViewChild.nativeElement);\n  }\n  toggle(event) {\n    if (this.mobileActive) {\n      this.mobileActive = false;\n      ZIndexUtils.clear(this.rootmenu.el.nativeElement);\n      this.hide();\n    } else {\n      this.mobileActive = true;\n      ZIndexUtils.set('menu', this.rootmenu.el.nativeElement, this.config.zIndex.menu);\n      setTimeout(() => {\n        this.show();\n      }, 0);\n    }\n    this.bindOutsideClickListener();\n    event.preventDefault();\n  }\n  hide(event, isFocus) {\n    if (this.mobileActive) {\n      setTimeout(() => {\n        focus(this.menubutton.nativeElement);\n      }, 0);\n    }\n    this.activeItemPath.set([]);\n    this.focusedItemInfo.set({\n      index: -1,\n      level: 0,\n      parentKey: '',\n      item: null\n    });\n    isFocus && focus(this.rootmenu?.menubarViewChild.nativeElement);\n    this.dirty = false;\n  }\n  show() {\n    const processedItem = this.findVisibleItem(this.findFirstFocusedItemIndex());\n    this.focusedItemInfo.set({\n      index: this.findFirstFocusedItemIndex(),\n      level: 0,\n      parentKey: '',\n      item: processedItem?.item\n    });\n    focus(this.rootmenu?.menubarViewChild.nativeElement);\n  }\n  onMenuFocus(event) {\n    this.focused = true;\n    const processedItem = this.findVisibleItem(this.findFirstFocusedItemIndex());\n    const focusedItemInfo = this.focusedItemInfo().index !== -1 ? this.focusedItemInfo() : {\n      index: this.findFirstFocusedItemIndex(),\n      level: 0,\n      parentKey: '',\n      item: processedItem?.item\n    };\n    this.focusedItemInfo.set(focusedItemInfo);\n    this.onFocus.emit(event);\n  }\n  onMenuBlur(event) {\n    this.focused = false;\n    this.focusedItemInfo.set({\n      index: -1,\n      level: 0,\n      parentKey: '',\n      item: null\n    });\n    this.searchValue = '';\n    this.dirty = false;\n    this.onBlur.emit(event);\n  }\n  onKeyDown(event) {\n    const metaKey = event.metaKey || event.ctrlKey;\n    switch (event.code) {\n      case 'ArrowDown':\n        this.onArrowDownKey(event);\n        break;\n      case 'ArrowUp':\n        this.onArrowUpKey(event);\n        break;\n      case 'ArrowLeft':\n        this.onArrowLeftKey(event);\n        break;\n      case 'ArrowRight':\n        this.onArrowRightKey(event);\n        break;\n      case 'Home':\n        this.onHomeKey(event);\n        break;\n      case 'End':\n        this.onEndKey(event);\n        break;\n      case 'Space':\n        this.onSpaceKey(event);\n        break;\n      case 'Enter':\n        this.onEnterKey(event);\n        break;\n      case 'Escape':\n        this.onEscapeKey(event);\n        break;\n      case 'Tab':\n        this.onTabKey(event);\n        break;\n      case 'PageDown':\n      case 'PageUp':\n      case 'Backspace':\n      case 'ShiftLeft':\n      case 'ShiftRight':\n        //NOOP\n        break;\n      default:\n        if (!metaKey && isPrintableCharacter(event.key)) {\n          this.searchItems(event, event.key);\n        }\n        break;\n    }\n  }\n  findVisibleItem(index) {\n    return isNotEmpty(this.visibleItems) ? this.visibleItems[index] : null;\n  }\n  findFirstFocusedItemIndex() {\n    const selectedIndex = this.findSelectedItemIndex();\n    return selectedIndex < 0 ? this.findFirstItemIndex() : selectedIndex;\n  }\n  findFirstItemIndex() {\n    return this.visibleItems.findIndex(processedItem => this.isValidItem(processedItem));\n  }\n  findSelectedItemIndex() {\n    return this.visibleItems.findIndex(processedItem => this.isValidSelectedItem(processedItem));\n  }\n  isProcessedItemGroup(processedItem) {\n    return processedItem && isNotEmpty(processedItem.items);\n  }\n  isSelected(processedItem) {\n    return this.activeItemPath().some(p => p.key === processedItem.key);\n  }\n  isValidSelectedItem(processedItem) {\n    return this.isValidItem(processedItem) && this.isSelected(processedItem);\n  }\n  isValidItem(processedItem) {\n    return !!processedItem && !this.isItemDisabled(processedItem.item) && !this.isItemSeparator(processedItem.item);\n  }\n  isItemDisabled(item) {\n    return this.getItemProp(item, 'disabled');\n  }\n  isItemSeparator(item) {\n    return this.getItemProp(item, 'separator');\n  }\n  isItemMatched(processedItem) {\n    return this.isValidItem(processedItem) && this.getProccessedItemLabel(processedItem).toLocaleLowerCase().startsWith(this.searchValue.toLocaleLowerCase());\n  }\n  isProccessedItemGroup(processedItem) {\n    return processedItem && isNotEmpty(processedItem.items);\n  }\n  searchItems(event, char) {\n    this.searchValue = (this.searchValue || '') + char;\n    let itemIndex = -1;\n    let matched = false;\n    if (this.focusedItemInfo().index !== -1) {\n      itemIndex = this.visibleItems.slice(this.focusedItemInfo().index).findIndex(processedItem => this.isItemMatched(processedItem));\n      itemIndex = itemIndex === -1 ? this.visibleItems.slice(0, this.focusedItemInfo().index).findIndex(processedItem => this.isItemMatched(processedItem)) : itemIndex + this.focusedItemInfo().index;\n    } else {\n      itemIndex = this.visibleItems.findIndex(processedItem => this.isItemMatched(processedItem));\n    }\n    if (itemIndex !== -1) {\n      matched = true;\n    }\n    if (itemIndex === -1 && this.focusedItemInfo().index === -1) {\n      itemIndex = this.findFirstFocusedItemIndex();\n    }\n    if (itemIndex !== -1) {\n      this.changeFocusedItemIndex(event, itemIndex);\n    }\n    if (this.searchTimeout) {\n      clearTimeout(this.searchTimeout);\n    }\n    this.searchTimeout = setTimeout(() => {\n      this.searchValue = '';\n      this.searchTimeout = null;\n    }, 500);\n    return matched;\n  }\n  getProccessedItemLabel(processedItem) {\n    return processedItem ? this.getItemLabel(processedItem.item) : undefined;\n  }\n  getItemLabel(item) {\n    return this.getItemProp(item, 'label');\n  }\n  onArrowDownKey(event) {\n    const processedItem = this.visibleItems[this.focusedItemInfo().index];\n    const root = processedItem ? isEmpty(processedItem.parent) : null;\n    if (root) {\n      const grouped = this.isProccessedItemGroup(processedItem);\n      if (grouped) {\n        this.onItemChange({\n          originalEvent: event,\n          processedItem\n        });\n        this.focusedItemInfo.set({\n          index: -1,\n          parentKey: processedItem.key,\n          item: processedItem.item\n        });\n        this.onArrowRightKey(event);\n      }\n    } else {\n      const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n      this.changeFocusedItemIndex(event, itemIndex);\n      event.preventDefault();\n    }\n  }\n  onArrowRightKey(event) {\n    const processedItem = this.visibleItems[this.focusedItemInfo().index];\n    const parentItem = processedItem ? this.activeItemPath().find(p => p.key === processedItem.parentKey) : null;\n    if (parentItem) {\n      const grouped = this.isProccessedItemGroup(processedItem);\n      if (grouped) {\n        this.onItemChange({\n          originalEvent: event,\n          processedItem\n        });\n        this.focusedItemInfo.set({\n          index: -1,\n          parentKey: processedItem.key,\n          item: processedItem.item\n        });\n        this.onArrowDownKey(event);\n      }\n    } else {\n      const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n      this.changeFocusedItemIndex(event, itemIndex);\n      event.preventDefault();\n    }\n  }\n  onArrowUpKey(event) {\n    const processedItem = this.visibleItems[this.focusedItemInfo().index];\n    const root = isEmpty(processedItem.parent);\n    if (root) {\n      const grouped = this.isProccessedItemGroup(processedItem);\n      if (grouped) {\n        this.onItemChange({\n          originalEvent: event,\n          processedItem\n        });\n        this.focusedItemInfo.set({\n          index: -1,\n          parentKey: processedItem.key,\n          item: processedItem.item\n        });\n        const itemIndex = this.findLastItemIndex();\n        this.changeFocusedItemIndex(event, itemIndex);\n      }\n    } else {\n      const parentItem = this.activeItemPath().find(p => p.key === processedItem.parentKey);\n      if (this.focusedItemInfo().index === 0) {\n        this.focusedItemInfo.set({\n          index: -1,\n          parentKey: parentItem ? parentItem.parentKey : '',\n          item: processedItem.item\n        });\n        this.searchValue = '';\n        this.onArrowLeftKey(event);\n        const activeItemPath = this.activeItemPath().filter(p => p.parentKey !== this.focusedItemInfo().parentKey);\n        this.activeItemPath.set(activeItemPath);\n      } else {\n        const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n        this.changeFocusedItemIndex(event, itemIndex);\n      }\n    }\n    event.preventDefault();\n  }\n  onArrowLeftKey(event) {\n    const processedItem = this.visibleItems[this.focusedItemInfo().index];\n    const parentItem = processedItem ? this.activeItemPath().find(p => p.key === processedItem.parentKey) : null;\n    if (parentItem) {\n      this.onItemChange({\n        originalEvent: event,\n        processedItem: parentItem\n      });\n      const activeItemPath = this.activeItemPath().filter(p => p.parentKey !== this.focusedItemInfo().parentKey);\n      this.activeItemPath.set(activeItemPath);\n      event.preventDefault();\n    } else {\n      const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n      this.changeFocusedItemIndex(event, itemIndex);\n      event.preventDefault();\n    }\n  }\n  onHomeKey(event) {\n    this.changeFocusedItemIndex(event, this.findFirstItemIndex());\n    event.preventDefault();\n  }\n  onEndKey(event) {\n    this.changeFocusedItemIndex(event, this.findLastItemIndex());\n    event.preventDefault();\n  }\n  onSpaceKey(event) {\n    this.onEnterKey(event);\n  }\n  onEscapeKey(event) {\n    this.hide(event, true);\n    this.focusedItemInfo().index = this.findFirstFocusedItemIndex();\n    event.preventDefault();\n  }\n  onTabKey(event) {\n    if (this.focusedItemInfo().index !== -1) {\n      const processedItem = this.visibleItems[this.focusedItemInfo().index];\n      const grouped = this.isProccessedItemGroup(processedItem);\n      !grouped && this.onItemChange({\n        originalEvent: event,\n        processedItem\n      });\n    }\n    this.hide();\n  }\n  onEnterKey(event) {\n    if (this.focusedItemInfo().index !== -1) {\n      const element = findSingle(this.rootmenu.el.nativeElement, `li[id=\"${`${this.focusedItemId}`}\"]`);\n      const anchorElement = element && findSingle(element, 'a[data-pc-section=\"action\"]');\n      anchorElement ? anchorElement.click() : element && element.click();\n    }\n    event.preventDefault();\n  }\n  findLastFocusedItemIndex() {\n    const selectedIndex = this.findSelectedItemIndex();\n    return selectedIndex < 0 ? this.findLastItemIndex() : selectedIndex;\n  }\n  findLastItemIndex() {\n    return findLastIndex(this.visibleItems, processedItem => this.isValidItem(processedItem));\n  }\n  findPrevItemIndex(index) {\n    const matchedItemIndex = index > 0 ? findLastIndex(this.visibleItems.slice(0, index), processedItem => this.isValidItem(processedItem)) : -1;\n    return matchedItemIndex > -1 ? matchedItemIndex : index;\n  }\n  findNextItemIndex(index) {\n    const matchedItemIndex = index < this.visibleItems.length - 1 ? this.visibleItems.slice(index + 1).findIndex(processedItem => this.isValidItem(processedItem)) : -1;\n    return matchedItemIndex > -1 ? matchedItemIndex + index + 1 : index;\n  }\n  bindResizeListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.resizeListener) {\n        this.resizeListener = this.renderer.listen(this.document.defaultView, 'resize', event => {\n          if (!isTouchDevice()) {\n            this.hide(event, true);\n          }\n          this.mobileActive = false;\n        });\n      }\n    }\n  }\n  bindOutsideClickListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.outsideClickListener) {\n        this.outsideClickListener = this.renderer.listen(this.document, 'click', event => {\n          const isOutsideContainer = this.rootmenu.el.nativeElement !== event.target && !this.rootmenu.el.nativeElement.contains(event.target);\n          const isOutsideMenuButton = this.mobileActive && this.menubutton.nativeElement !== event.target && !this.menubutton.nativeElement.contains(event.target);\n          if (isOutsideContainer) {\n            isOutsideMenuButton ? this.mobileActive = false : this.hide();\n          }\n        });\n      }\n    }\n  }\n  unbindOutsideClickListener() {\n    if (this.outsideClickListener) {\n      this.outsideClickListener();\n      this.outsideClickListener = null;\n    }\n  }\n  unbindResizeListener() {\n    if (this.resizeListener) {\n      this.resizeListener();\n      this.resizeListener = null;\n    }\n  }\n  ngOnDestroy() {\n    this.mouseLeaveSubscriber?.unsubscribe();\n    this.unbindOutsideClickListener();\n    this.unbindResizeListener();\n    this.unbindMatchMediaListener();\n    super.ngOnDestroy();\n  }\n  static ɵfac = function Menubar_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Menubar)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MenubarService));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Menubar,\n    selectors: [[\"p-menubar\"]],\n    contentQueries: function Menubar_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, _c6, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c7, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c8, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c9, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c10, 4);\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.startTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.endTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.itemTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menuIconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.submenuIconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function Menubar_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c11, 5);\n        i0.ɵɵviewQuery(_c12, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menubutton = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.rootmenu = _t.first);\n      }\n    },\n    inputs: {\n      model: \"model\",\n      style: \"style\",\n      styleClass: \"styleClass\",\n      autoZIndex: [2, \"autoZIndex\", \"autoZIndex\", booleanAttribute],\n      baseZIndex: [2, \"baseZIndex\", \"baseZIndex\", numberAttribute],\n      autoDisplay: [2, \"autoDisplay\", \"autoDisplay\", booleanAttribute],\n      autoHide: [2, \"autoHide\", \"autoHide\", booleanAttribute],\n      breakpoint: \"breakpoint\",\n      autoHideDelay: [2, \"autoHideDelay\", \"autoHideDelay\", numberAttribute],\n      id: \"id\",\n      ariaLabel: \"ariaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\"\n    },\n    outputs: {\n      onFocus: \"onFocus\",\n      onBlur: \"onBlur\"\n    },\n    features: [i0.ɵɵProvidersFeature([MenubarService, MenuBarStyle]), i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c13,\n    decls: 8,\n    vars: 26,\n    consts: [[\"rootmenu\", \"\"], [\"legacy\", \"\"], [\"menubutton\", \"\"], [3, \"ngClass\", \"ngStyle\"], [\"class\", \"p-menubar-start\", 4, \"ngIf\"], [\"tabindex\", \"0\", \"role\", \"button\", \"class\", \"p-menubar-button\", 3, \"click\", \"keydown\", 4, \"ngIf\"], [3, \"itemClick\", \"menuFocus\", \"menuBlur\", \"menuKeydown\", \"itemMouseEnter\", \"items\", \"itemTemplate\", \"menuId\", \"root\", \"baseZIndex\", \"autoZIndex\", \"mobileActive\", \"autoDisplay\", \"ariaLabel\", \"ariaLabelledBy\", \"focusedItemId\", \"submenuiconTemplate\", \"activeItemPath\"], [\"class\", \"p-menubar-end\", 4, \"ngIf\", \"ngIfElse\"], [1, \"p-menubar-start\"], [4, \"ngTemplateOutlet\"], [\"tabindex\", \"0\", \"role\", \"button\", 1, \"p-menubar-button\", 3, \"click\", \"keydown\"], [4, \"ngIf\"], [1, \"p-menubar-end\"]],\n    template: function Menubar_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 3);\n        i0.ɵɵtemplate(1, Menubar_div_1_Template, 2, 1, \"div\", 4)(2, Menubar_a_2_Template, 4, 7, \"a\", 5);\n        i0.ɵɵelementStart(3, \"p-menubarSub\", 6, 0);\n        i0.ɵɵlistener(\"itemClick\", function Menubar_Template_p_menubarSub_itemClick_3_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onItemClick($event));\n        })(\"menuFocus\", function Menubar_Template_p_menubarSub_menuFocus_3_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onMenuFocus($event));\n        })(\"menuBlur\", function Menubar_Template_p_menubarSub_menuBlur_3_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onMenuBlur($event));\n        })(\"menuKeydown\", function Menubar_Template_p_menubarSub_menuKeydown_3_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onKeyDown($event));\n        })(\"itemMouseEnter\", function Menubar_Template_p_menubarSub_itemMouseEnter_3_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onItemMouseEnter($event));\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(5, Menubar_div_5_Template, 2, 1, \"div\", 7)(6, Menubar_ng_template_6_Template, 2, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        const legacy_r4 = i0.ɵɵreference(7);\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(23, _c14, ctx.queryMatches, ctx.mobileActive))(\"ngStyle\", ctx.style);\n        i0.ɵɵattribute(\"data-pc-section\", \"root\")(\"data-pc-name\", \"menubar\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.startTemplate || ctx._startTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.model && ctx.model.length > 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"items\", ctx.processedItems)(\"itemTemplate\", ctx.itemTemplate)(\"menuId\", ctx.id)(\"root\", true)(\"baseZIndex\", ctx.baseZIndex)(\"autoZIndex\", ctx.autoZIndex)(\"mobileActive\", ctx.mobileActive)(\"autoDisplay\", ctx.autoDisplay)(\"ariaLabel\", ctx.ariaLabel)(\"ariaLabelledBy\", ctx.ariaLabelledBy)(\"focusedItemId\", ctx.focused ? ctx.focusedItemId : undefined)(\"submenuiconTemplate\", ctx.submenuIconTemplate || ctx._submenuIconTemplate)(\"activeItemPath\", ctx.activeItemPath());\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.endTemplate || ctx._endTemplate)(\"ngIfElse\", legacy_r4);\n      }\n    },\n    dependencies: [CommonModule, i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, RouterModule, MenubarSub, TooltipModule, BarsIcon, BadgeModule, SharedModule],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Menubar, [{\n    type: Component,\n    args: [{\n      selector: 'p-menubar',\n      standalone: true,\n      imports: [CommonModule, RouterModule, MenubarSub, TooltipModule, BarsIcon, BadgeModule, SharedModule],\n      template: `\n        <div [ngClass]=\"{ 'p-menubar p-component': true, 'p-menubar-mobile': queryMatches, 'p-menubar-mobile-active': mobileActive }\" [class]=\"styleClass\" [ngStyle]=\"style\" [attr.data-pc-section]=\"'root'\" [attr.data-pc-name]=\"'menubar'\">\n            <div class=\"p-menubar-start\" *ngIf=\"startTemplate || _startTemplate\">\n                <ng-container *ngTemplateOutlet=\"startTemplate || _startTemplate\"></ng-container>\n            </div>\n            <a\n                #menubutton\n                tabindex=\"0\"\n                role=\"button\"\n                [attr.aria-haspopup]=\"model.length && model.length > 0 ? true : false\"\n                [attr.aria-expanded]=\"mobileActive\"\n                [attr.aria-controls]=\"id\"\n                [attr.aria-label]=\"config.translation.aria.navigation\"\n                [attr.data-pc-section]=\"'button'\"\n                *ngIf=\"model && model.length > 0\"\n                class=\"p-menubar-button\"\n                (click)=\"menuButtonClick($event)\"\n                (keydown)=\"menuButtonKeydown($event)\"\n            >\n                <BarsIcon *ngIf=\"!menuIconTemplate && !_menuIconTemplate\" />\n                <ng-template *ngTemplateOutlet=\"menuIconTemplate || _menuIconTemplate\"></ng-template>\n            </a>\n            <p-menubarSub\n                #rootmenu\n                [items]=\"processedItems\"\n                [itemTemplate]=\"itemTemplate\"\n                [menuId]=\"id\"\n                [root]=\"true\"\n                [baseZIndex]=\"baseZIndex\"\n                [autoZIndex]=\"autoZIndex\"\n                [mobileActive]=\"mobileActive\"\n                [autoDisplay]=\"autoDisplay\"\n                [ariaLabel]=\"ariaLabel\"\n                [ariaLabelledBy]=\"ariaLabelledBy\"\n                [focusedItemId]=\"focused ? focusedItemId : undefined\"\n                [submenuiconTemplate]=\"submenuIconTemplate || _submenuIconTemplate\"\n                [activeItemPath]=\"activeItemPath()\"\n                (itemClick)=\"onItemClick($event)\"\n                (menuFocus)=\"onMenuFocus($event)\"\n                (menuBlur)=\"onMenuBlur($event)\"\n                (menuKeydown)=\"onKeyDown($event)\"\n                (itemMouseEnter)=\"onItemMouseEnter($event)\"\n            ></p-menubarSub>\n            <div class=\"p-menubar-end\" *ngIf=\"endTemplate || _endTemplate; else legacy\">\n                <ng-container *ngTemplateOutlet=\"endTemplate || _endTemplate\"></ng-container>\n            </div>\n            <ng-template #legacy>\n                <div class=\"p-menubar-end\">\n                    <ng-content></ng-content>\n                </div>\n            </ng-template>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [MenubarService, MenuBarStyle]\n    }]\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: MenubarService\n  }], {\n    model: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    baseZIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    autoDisplay: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    autoHide: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    breakpoint: [{\n      type: Input\n    }],\n    autoHideDelay: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    id: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    onFocus: [{\n      type: Output\n    }],\n    onBlur: [{\n      type: Output\n    }],\n    menubutton: [{\n      type: ViewChild,\n      args: ['menubutton']\n    }],\n    rootmenu: [{\n      type: ViewChild,\n      args: ['rootmenu']\n    }],\n    startTemplate: [{\n      type: ContentChild,\n      args: ['start', {\n        descendants: false\n      }]\n    }],\n    endTemplate: [{\n      type: ContentChild,\n      args: ['end', {\n        descendants: false\n      }]\n    }],\n    itemTemplate: [{\n      type: ContentChild,\n      args: ['item', {\n        descendants: false\n      }]\n    }],\n    menuIconTemplate: [{\n      type: ContentChild,\n      args: ['menuicon', {\n        descendants: false\n      }]\n    }],\n    submenuIconTemplate: [{\n      type: ContentChild,\n      args: ['submenuicon', {\n        descendants: false\n      }]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass MenubarModule {\n  static ɵfac = function MenubarModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MenubarModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MenubarModule,\n    imports: [Menubar, SharedModule],\n    exports: [Menubar, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [Menubar, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MenubarModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Menubar, SharedModule],\n      exports: [Menubar, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MenuBarStyle, Menubar, MenubarClasses, MenubarModule, MenubarService, MenubarSub };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBA,IAAM,MAAM,CAAC,SAAS;AACtB,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,qBAAqB;AAAA,EACrB,uBAAuB;AACzB;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,uBAAuB;AAAA,EACvB,cAAc;AAChB;AACA,IAAM,MAAM,OAAO;AAAA,EACjB,OAAO;AACT;AACA,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,WAAW;AAAA,EACX,MAAM;AACR;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,SAAS;AACX;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,MAAM,CAAC;AAAA,EACzB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,EAAE;AAC5C,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,YAAY,kBAAkB,OAAO,CAAC;AAC3D,IAAG,WAAW,WAAW,OAAO,sBAAsB,gBAAgB,CAAC;AACvE,IAAG,YAAY,MAAM,OAAO,UAAU,gBAAgB,CAAC,EAAE,mBAAmB,WAAW;AAAA,EACzF;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAW,OAAO,YAAY,kBAAkB,MAAM,CAAC,EAAE,WAAW,OAAO,YAAY,kBAAkB,WAAW,CAAC;AACnI,IAAG,YAAY,mBAAmB,MAAM,EAAE,YAAY,EAAE;AAAA,EAC1D;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,MAAM,OAAO,eAAe,gBAAgB,CAAC;AAC3D,IAAG,YAAY,mBAAmB,OAAO;AACzC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,aAAa,gBAAgB,GAAG,GAAG;AAAA,EACvE;AACF;AACA,SAAS,wEAAwE,IAAI,KAAK;AACxF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,aAAa,OAAO,aAAa,gBAAgB,GAAM,cAAc,EAAE,MAAM,OAAO,eAAe,gBAAgB,CAAC;AAClI,IAAG,YAAY,mBAAmB,OAAO;AAAA,EAC3C;AACF;AACA,SAAS,oEAAoE,IAAI,KAAK;AACpF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,EAAE;AAAA,EAC/B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,cAAc,OAAO,YAAY,kBAAkB,iBAAiB,CAAC,EAAE,SAAS,OAAO,YAAY,kBAAkB,OAAO,CAAC;AAAA,EAC7I;AACF;AACA,SAAS,wGAAwG,IAAI,KAAK;AACxH,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,iBAAiB,EAAE;AAAA,EACrC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,YAAY,mBAAmB,aAAa;AAAA,EACjD;AACF;AACA,SAAS,yGAAyG,IAAI,KAAK;AACzH,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,kBAAkB,EAAE;AAAA,EACtC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,YAAY,mBAAmB,aAAa;AAAA,EACjD;AACF;AACA,SAAS,wFAAwF,IAAI,KAAK;AACxG,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,yGAAyG,GAAG,GAAG,iBAAiB,EAAE,EAAE,GAAG,0GAA0G,GAAG,GAAG,kBAAkB,EAAE;AAC5R,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,IAAI;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,IAAI;AAAA,EACpC;AACF;AACA,SAAS,yFAAyF,IAAI,KAAK;AAAC;AAC5G,SAAS,2EAA2E,IAAI,KAAK;AAC3F,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,0FAA0F,GAAG,GAAG,eAAe,EAAE;AAAA,EACpI;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,mBAAmB,aAAa;AAAA,EAChD;AACF;AACA,SAAS,yEAAyE,IAAI,KAAK;AACzF,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,yFAAyF,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,4EAA4E,GAAG,GAAG,MAAM,EAAE;AACjO,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,mBAAmB;AACjD,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,mBAAmB;AAAA,EAC9D;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,GAAG,kEAAkE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,kEAAkE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,yEAAyE,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,qEAAqE,GAAG,GAAG,WAAW,EAAE,EAAE,GAAG,0EAA0E,GAAG,GAAG,gBAAgB,EAAE;AACngB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAkB,YAAY,CAAC;AACrC,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,UAAU,OAAO,YAAY,kBAAkB,QAAQ,CAAC,EAAE,WAAc,gBAAgB,IAAI,KAAK,OAAO,YAAY,kBAAkB,UAAU,CAAC,CAAC;AAChK,IAAG,YAAY,QAAQ,OAAO,YAAY,kBAAkB,KAAK,GAAM,aAAa,EAAE,qBAAqB,OAAO,YAAY,kBAAkB,cAAc,CAAC,EAAE,mBAAmB,QAAQ,EAAE,YAAY,EAAE;AAC5M,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,MAAM,CAAC;AAClE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,QAAQ,CAAC,EAAE,YAAY,YAAY;AAC9F,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,OAAO,CAAC;AACnE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,gBAAgB,CAAC;AAAA,EAC5D;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAW,OAAO,YAAY,kBAAkB,MAAM,CAAC,EAAE,WAAW,OAAO,YAAY,kBAAkB,WAAW,CAAC;AACnI,IAAG,YAAY,mBAAmB,MAAM,EAAE,YAAY,EAAE;AAAA,EAC1D;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,aAAa,gBAAgB,CAAC;AAAA,EAC5D;AACF;AACA,SAAS,wEAAwE,IAAI,KAAK;AACxF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,aAAa,OAAO,aAAa,gBAAgB,GAAM,cAAc;AACnF,IAAG,YAAY,mBAAmB,OAAO;AAAA,EAC3C;AACF;AACA,SAAS,oEAAoE,IAAI,KAAK;AACpF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,EAAE;AAAA,EAC/B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,cAAc,OAAO,YAAY,kBAAkB,iBAAiB,CAAC,EAAE,SAAS,OAAO,YAAY,kBAAkB,OAAO,CAAC;AAAA,EAC7I;AACF;AACA,SAAS,wGAAwG,IAAI,KAAK;AACxH,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,iBAAiB,EAAE;AAAA,EACrC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,YAAY,mBAAmB,aAAa;AAAA,EACjD;AACF;AACA,SAAS,yGAAyG,IAAI,KAAK;AACzH,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,kBAAkB,EAAE;AAAA,EACtC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,YAAY,mBAAmB,aAAa;AAAA,EACjD;AACF;AACA,SAAS,wFAAwF,IAAI,KAAK;AACxG,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,yGAAyG,GAAG,GAAG,iBAAiB,EAAE,EAAE,GAAG,0GAA0G,GAAG,GAAG,kBAAkB,EAAE;AAC5R,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,IAAI;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,IAAI;AAAA,EACpC;AACF;AACA,SAAS,yFAAyF,IAAI,KAAK;AAAC;AAC5G,SAAS,2EAA2E,IAAI,KAAK;AAC3F,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,0FAA0F,GAAG,GAAG,eAAe,EAAE;AAAA,EACpI;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,mBAAmB,aAAa;AAAA,EAChD;AACF;AACA,SAAS,yEAAyE,IAAI,KAAK;AACzF,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,yFAAyF,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,4EAA4E,GAAG,GAAG,MAAM,EAAE;AACjO,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,mBAAmB;AACjD,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,mBAAmB;AAAA,EAC9D;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,GAAG,kEAAkE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,kEAAkE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,yEAAyE,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,qEAAqE,GAAG,GAAG,WAAW,EAAE,EAAE,GAAG,0EAA0E,GAAG,GAAG,gBAAgB,EAAE;AACngB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,oBAAuB,YAAY,CAAC;AAC1C,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,cAAc,OAAO,YAAY,kBAAkB,YAAY,CAAC,EAAE,eAAe,OAAO,YAAY,kBAAkB,aAAa,CAAC,EAAE,oBAAoB,4BAA4B,EAAE,2BAA2B,OAAO,YAAY,kBAAkB,yBAAyB,KAAQ,gBAAgB,IAAI,GAAG,CAAC,EAAE,UAAU,OAAO,YAAY,kBAAkB,QAAQ,CAAC,EAAE,WAAc,gBAAgB,IAAI,KAAK,OAAO,YAAY,kBAAkB,UAAU,CAAC,CAAC,EAAE,YAAY,OAAO,YAAY,kBAAkB,UAAU,CAAC,EAAE,uBAAuB,OAAO,YAAY,kBAAkB,qBAAqB,CAAC,EAAE,oBAAoB,OAAO,YAAY,kBAAkB,kBAAkB,CAAC,EAAE,sBAAsB,OAAO,YAAY,kBAAkB,oBAAoB,CAAC,EAAE,cAAc,OAAO,YAAY,kBAAkB,YAAY,CAAC,EAAE,SAAS,OAAO,YAAY,kBAAkB,OAAO,CAAC;AAC/3B,IAAG,YAAY,qBAAqB,OAAO,YAAY,kBAAkB,cAAc,CAAC,EAAE,YAAY,EAAE,EAAE,mBAAmB,QAAQ;AACrI,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,MAAM,CAAC;AAClE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,QAAQ,CAAC,EAAE,YAAY,iBAAiB;AACnG,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,OAAO,CAAC;AACnE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,gBAAgB,CAAC;AAAA,EAC5D;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,2DAA2D,GAAG,IAAI,KAAK,EAAE,EAAE,GAAG,2DAA2D,GAAG,IAAI,KAAK,EAAE;AACxK,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,YAAY,kBAAkB,YAAY,CAAC;AACzE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,YAAY,CAAC;AAAA,EAC1E;AACF;AACA,SAAS,sEAAsE,IAAI,KAAK;AAAC;AACzF,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,uEAAuE,GAAG,GAAG,aAAa;AAAA,EAC7G;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,MAAM,EAAE;AACxF,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,YAAY,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,iBAAiB,MAAM,OAAO,IAAI,CAAC;AAAA,EAClJ;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,gBAAgB,EAAE;AACvC,IAAG,WAAW,aAAa,SAAS,wFAAwF,QAAQ;AAClI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,UAAU,KAAK,MAAM,CAAC;AAAA,IACrD,CAAC,EAAE,kBAAkB,SAAS,6FAA6F,QAAQ;AACjI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,gBAAgB,OAAO,YAAY,EAAE,SAAS,iBAAiB,KAAK,EAAE,gBAAgB,OAAO,YAAY,EAAE,eAAe,OAAO,WAAW,EAAE,UAAU,OAAO,MAAM,EAAE,kBAAkB,OAAO,cAAc,EAAE,iBAAiB,OAAO,aAAa,EAAE,SAAS,OAAO,QAAQ,CAAC,EAAE,kBAAkB,OAAO,eAAe,gBAAgB,CAAC,EAAE,gBAAmB,gBAAgB,IAAI,KAAK,OAAO,aAAa,gBAAgB,IAAI,SAAS,MAAM,CAAC;AAAA,EACjc;AACF;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,GAAG,CAAC,EAAE,GAAG,OAAO,EAAE;AAC7C,IAAG,WAAW,SAAS,SAAS,4DAA4D,QAAQ;AAClG,MAAG,cAAc,GAAG;AACpB,YAAM,mBAAsB,cAAc,EAAE;AAC5C,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,QAAQ,gBAAgB,CAAC;AAAA,IACpE,CAAC,EAAE,cAAc,SAAS,iEAAiE,QAAQ;AACjG,MAAG,cAAc,GAAG;AACpB,YAAM,mBAAsB,cAAc,EAAE;AAC5C,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,iBAAiB;AAAA,QAC5C;AAAA,QACA,eAAe;AAAA,MACjB,CAAC,CAAC;AAAA,IACJ,CAAC;AACD,IAAG,WAAW,GAAG,uDAAuD,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,uDAAuD,GAAG,GAAG,gBAAgB,EAAE;AACpL,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,uDAAuD,GAAG,IAAI,gBAAgB,EAAE;AACjG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,mBAAmB,OAAO;AAChC,UAAM,WAAW,OAAO;AACxB,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,YAAY,kBAAkB,YAAY,CAAC;AAChE,IAAG,WAAW,WAAW,OAAO,YAAY,kBAAkB,OAAO,CAAC,EAAE,WAAW,OAAO,aAAa,gBAAgB,CAAC,EAAE,kBAAkB,OAAO,YAAY,kBAAkB,gBAAgB,CAAC;AAClM,IAAG,YAAY,MAAM,OAAO,UAAU,gBAAgB,CAAC,EAAE,mBAAmB,UAAU,EAAE,oBAAoB,OAAO,aAAa,gBAAgB,CAAC,EAAE,kBAAkB,OAAO,cAAc,gBAAgB,CAAC,EAAE,mBAAmB,OAAO,eAAe,gBAAgB,CAAC,EAAE,cAAc,OAAO,aAAa,gBAAgB,CAAC,EAAE,iBAAiB,OAAO,eAAe,gBAAgB,KAAK,MAAS,EAAE,iBAAiB,OAAO,YAAY,gBAAgB,KAAK,CAAC,OAAO,YAAY,kBAAkB,IAAI,IAAI,SAAS,MAAS,EAAE,iBAAiB,OAAO,YAAY,gBAAgB,IAAI,OAAO,aAAa,gBAAgB,IAAI,MAAS,EAAE,gBAAgB,OAAO,eAAe,CAAC,EAAE,iBAAiB,OAAO,gBAAgB,QAAQ,CAAC;AACrsB,IAAG,UAAU,CAAC;AACd,IAAG,YAAY,mBAAmB,SAAS;AAC3C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,YAAY;AAC1C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY;AACzC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,cAAc,gBAAgB,KAAK,OAAO,YAAY,gBAAgB,CAAC;AAAA,EACtG;AACF;AACA,SAAS,kCAAkC,IAAI,KAAK;AAClD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,wCAAwC,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,wCAAwC,GAAG,IAAI,MAAM,CAAC;AAAA,EACnI;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAmB,IAAI;AAC7B,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,QAAQ,OAAO,cAAc,gBAAgB,KAAK,OAAO,YAAY,kBAAkB,WAAW,CAAC;AACjH,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,cAAc,gBAAgB,KAAK,CAAC,OAAO,YAAY,kBAAkB,WAAW,CAAC;AAAA,EACpH;AACF;AACA,IAAM,MAAM,CAAC,OAAO;AACpB,IAAM,MAAM,CAAC,KAAK;AAClB,IAAM,MAAM,CAAC,MAAM;AACnB,IAAM,MAAM,CAAC,UAAU;AACvB,IAAM,OAAO,CAAC,aAAa;AAC3B,IAAM,OAAO,CAAC,YAAY;AAC1B,IAAM,OAAO,CAAC,UAAU;AACxB,IAAM,OAAO,CAAC,GAAG;AACjB,IAAM,OAAO,CAAC,IAAI,QAAQ;AAAA,EACxB,yBAAyB;AAAA,EACzB,oBAAoB;AAAA,EACpB,2BAA2B;AAC7B;AACA,SAAS,sCAAsC,IAAI,KAAK;AACtD,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,uBAAuB,IAAI,KAAK;AACvC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,uCAAuC,GAAG,GAAG,gBAAgB,CAAC;AAC/E,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,iBAAiB,OAAO,cAAc;AAAA,EACjF;AACF;AACA,SAAS,gCAAgC,IAAI,KAAK;AAChD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,UAAU;AAAA,EAC5B;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AAAC;AACxD,SAAS,uBAAuB,IAAI,KAAK;AACvC,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,sCAAsC,GAAG,GAAG,aAAa;AAAA,EAC5E;AACF;AACA,SAAS,qBAAqB,IAAI,KAAK;AACrC,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,KAAK,IAAI,CAAC;AAC/B,IAAG,WAAW,SAAS,SAAS,wCAAwC,QAAQ;AAC9E,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,gBAAgB,MAAM,CAAC;AAAA,IACtD,CAAC,EAAE,WAAW,SAAS,0CAA0C,QAAQ;AACvE,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,kBAAkB,MAAM,CAAC;AAAA,IACxD,CAAC;AACD,IAAG,WAAW,GAAG,iCAAiC,GAAG,GAAG,YAAY,EAAE,EAAE,GAAG,wBAAwB,GAAG,GAAG,MAAM,CAAC;AAChH,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,iBAAiB,OAAO,MAAM,UAAU,OAAO,MAAM,SAAS,IAAI,OAAO,KAAK,EAAE,iBAAiB,OAAO,YAAY,EAAE,iBAAiB,OAAO,EAAE,EAAE,cAAc,OAAO,OAAO,YAAY,KAAK,UAAU,EAAE,mBAAmB,QAAQ;AACrP,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,CAAC,OAAO,oBAAoB,CAAC,OAAO,iBAAiB;AAC3E,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,oBAAoB,OAAO,iBAAiB;AAAA,EACvF;AACF;AACA,SAAS,sCAAsC,IAAI,KAAK;AACtD,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,uBAAuB,IAAI,KAAK;AACvC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,uCAAuC,GAAG,GAAG,gBAAgB,CAAC;AAC/E,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,eAAe,OAAO,YAAY;AAAA,EAC7E;AACF;AACA,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,aAAa,CAAC;AACjB,IAAG,aAAa;AAAA,EAClB;AACF;AACA,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA;AAAA;AAAA,kBAIY,GAAG,oBAAoB,CAAC;AAAA,wBAClB,GAAG,sBAAsB,CAAC;AAAA,qBAC7B,GAAG,uBAAuB,CAAC;AAAA,aACnC,GAAG,eAAe,CAAC;AAAA,eACjB,GAAG,iBAAiB,CAAC;AAAA,WACzB,GAAG,aAAa,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WAqBjB,GAAG,aAAa,CAAC;AAAA;AAAA;AAAA;AAAA,qBAIP,GAAG,iCAAiC,CAAC;AAAA;AAAA;AAAA;AAAA,eAI3C,GAAG,2BAA2B,CAAC;AAAA;AAAA;AAAA;AAAA,6BAIjB,GAAG,6BAA6B,CAAC,WAAW,GAAG,6BAA6B,CAAC;AAAA,qBACrF,GAAG,4BAA4B,CAAC;AAAA,aACxC,GAAG,oBAAoB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAWtB,GAAG,sBAAsB,CAAC;AAAA,WAC9B,GAAG,kBAAkB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAUpB,GAAG,yBAAyB,CAAC;AAAA;AAAA;AAAA;AAAA,aAI7B,GAAG,4BAA4B,CAAC;AAAA;AAAA,iBAE5B,GAAG,2BAA2B,CAAC;AAAA,aACnC,GAAG,2BAA2B,CAAC;AAAA,cAC9B,GAAG,2BAA2B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAShC,GAAG,0BAA0B,CAAC;AAAA,kBACzB,GAAG,+BAA+B,CAAC;AAAA;AAAA;AAAA;AAAA,aAIxC,GAAG,+BAA+B,CAAC;AAAA;AAAA;AAAA;AAAA,aAInC,GAAG,kCAAkC,CAAC;AAAA;AAAA;AAAA;AAAA,aAItC,GAAG,0BAA0B,CAAC;AAAA,kBACzB,GAAG,+BAA+B,CAAC;AAAA;AAAA;AAAA;AAAA,aAIxC,GAAG,+BAA+B,CAAC;AAAA;AAAA;AAAA;AAAA,aAInC,GAAG,kCAAkC,CAAC;AAAA;AAAA;AAAA;AAAA,aAItC,GAAG,2BAA2B,CAAC;AAAA,kBAC1B,GAAG,gCAAgC,CAAC;AAAA;AAAA;AAAA;AAAA,aAIzC,GAAG,gCAAgC,CAAC;AAAA;AAAA;AAAA;AAAA,aAIpC,GAAG,mCAAmC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAQlC,GAAG,4BAA4B,CAAC;AAAA,wBAC1B,GAAG,8BAA8B,CAAC;AAAA,qBACrC,GAAG,uBAAuB,CAAC;AAAA,kBAC9B,GAAG,wBAAwB,CAAC;AAAA,aACjC,GAAG,uBAAuB,CAAC;AAAA;AAAA,eAEzB,GAAG,yBAAyB,CAAC;AAAA,WACjC,GAAG,qBAAqB,CAAC;AAAA;AAAA;AAAA;AAAA,4BAIR,GAAG,gCAAgC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aA4BnD,GAAG,4BAA4B,CAAC;AAAA,cAC/B,GAAG,4BAA4B,CAAC;AAAA;AAAA,aAEjC,GAAG,6BAA6B,CAAC;AAAA;AAAA;AAAA,qBAGzB,GAAG,qCAAqC,CAAC;AAAA,6BACjC,GAAG,6BAA6B,CAAC,WAAW,GAAG,6BAA6B,CAAC,mBAAmB,GAAG,6BAA6B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,aAKjJ,GAAG,mCAAmC,CAAC;AAAA,kBAClC,GAAG,wCAAwC,CAAC;AAAA;AAAA;AAAA;AAAA,kBAI5C,GAAG,yCAAyC,CAAC;AAAA,eAChD,GAAG,wCAAwC,CAAC,IAAI,GAAG,wCAAwC,CAAC,IAAI,GAAG,wCAAwC,CAAC;AAAA,sBACrI,GAAG,yCAAyC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAepD,GAAG,yBAAyB,CAAC;AAAA,kBAC1B,GAAG,4BAA4B,CAAC;AAAA,wBAC1B,GAAG,8BAA8B,CAAC;AAAA,kBACxC,GAAG,wBAAwB,CAAC;AAAA;AAAA;AAAA;AAAA,qBAIzB,GAAG,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA,eAItC,GAAG,sBAAsB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,4BAsBb,GAAG,gCAAgC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBA+B5C,GAAG,+BAA+B,CAAC;AAAA;AAAA;AAWvD,IAAM,UAAU;AAAA,EACd,MAAM,CAAC;AAAA,IACL;AAAA,EACF,MAAM,CAAC,yBAAyB;AAAA,IAC9B,oBAAoB,SAAS;AAAA,IAC7B,2BAA2B,SAAS;AAAA,EACtC,CAAC;AAAA,EACD,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,MAAM,CAAC;AAAA,IACL;AAAA,IACA;AAAA,EACF,MAAM,CAAC,kBAAkB;AAAA,IACvB,yBAAyB,SAAS,aAAa,aAAa;AAAA,IAC5D,WAAW,SAAS,cAAc,aAAa;AAAA,IAC/C,cAAc,SAAS,eAAe,aAAa;AAAA,EACrD,CAAC;AAAA,EACD,aAAa;AAAA,EACb,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS;AAAA,EACT,WAAW;AAAA,EACX,KAAK;AACP;AACA,IAAM,eAAN,MAAM,sBAAqB,UAAU;AAAA,EACnC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,qBAAqB,mBAAmB;AACtD,cAAQ,8BAA8B,4BAA+B,sBAAsB,aAAY,IAAI,qBAAqB,aAAY;AAAA,IAC9I;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,cAAa;AAAA,EACxB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,iBAAgB;AAIzB,EAAAA,gBAAe,MAAM,IAAI;AAIzB,EAAAA,gBAAe,OAAO,IAAI;AAI1B,EAAAA,gBAAe,QAAQ,IAAI;AAI3B,EAAAA,gBAAe,UAAU,IAAI;AAI7B,EAAAA,gBAAe,MAAM,IAAI;AAIzB,EAAAA,gBAAe,aAAa,IAAI;AAIhC,EAAAA,gBAAe,UAAU,IAAI;AAI7B,EAAAA,gBAAe,UAAU,IAAI;AAI7B,EAAAA,gBAAe,WAAW,IAAI;AAI9B,EAAAA,gBAAe,aAAa,IAAI;AAIhC,EAAAA,gBAAe,SAAS,IAAI;AAI5B,EAAAA,gBAAe,WAAW,IAAI;AAI9B,EAAAA,gBAAe,KAAK,IAAI;AAC1B,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAC1C,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB;AAAA,EACA;AAAA,EACA,cAAc,IAAI,QAAQ;AAAA,EAC1B,aAAa,KAAK,YAAY,KAAK,SAAS,MAAM,SAAS,KAAK,aAAa,CAAC,GAAG,OAAO,eAAa,KAAK,YAAY,SAAS,CAAC;AAAA,EAChI,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,gBAAe;AAAA,EAC1B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,aAAN,MAAM,oBAAmB,cAAc;AAAA,EACrC;AAAA,EACA;AAAA,EACA,OAAO;AAAA,EACP,aAAa;AAAA,EACb,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,IAAI,aAAa;AAAA,EAC7B,iBAAiB,IAAI,aAAa;AAAA,EAClC,YAAY,IAAI,aAAa;AAAA,EAC7B,WAAW,IAAI,aAAa;AAAA,EAC5B,cAAc,IAAI,aAAa;AAAA,EAC/B;AAAA,EACA;AAAA,EACA,iBAAiB,OAAO,cAAc;AAAA,EACtC,WAAW;AACT,UAAM,SAAS;AACf,SAAK,uBAAuB,KAAK,eAAe,WAAW,UAAU,MAAM;AACzE,WAAK,GAAG,aAAa;AAAA,IACvB,CAAC;AAAA,EACH;AAAA,EACA,YAAY,OAAO,eAAe;AAChC,SAAK,YAAY,eAAe,WAAW;AAAA,MACzC,eAAe;AAAA,MACf,MAAM,cAAc;AAAA,IACtB,CAAC;AACD,SAAK,UAAU,KAAK;AAAA,MAClB,eAAe;AAAA,MACf;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAAA,EACA,YAAY,eAAe,MAAM,SAAS,MAAM;AAC9C,WAAO,iBAAiB,cAAc,OAAO,QAAQ,cAAc,KAAK,IAAI,GAAG,MAAM,IAAI;AAAA,EAC3F;AAAA,EACA,UAAU,eAAe;AACvB,WAAO,cAAc,QAAQ,cAAc,MAAM,KAAK,cAAc,KAAK,KAAK,GAAG,KAAK,MAAM,IAAI,cAAc,GAAG;AAAA,EACnH;AAAA,EACA,WAAW,eAAe;AACxB,WAAO,KAAK,UAAU,aAAa;AAAA,EACrC;AAAA,EACA,eAAe,eAAe;AAC5B,WAAO,GAAG,KAAK,MAAM,IAAI,cAAc,GAAG;AAAA,EAC5C;AAAA,EACA,aAAa,eAAe;AAC1B,WAAO,iCACF,KAAK,YAAY,eAAe,OAAO,IADrC;AAAA,MAEL,kBAAkB;AAAA,MAClB,yBAAyB,KAAK,aAAa,aAAa;AAAA,MACxD,WAAW,KAAK,cAAc,aAAa;AAAA,MAC3C,cAAc,KAAK,eAAe,aAAa;AAAA,IACjD;AAAA,EACF;AAAA,EACA,aAAa,eAAe;AAC1B,WAAO,KAAK,YAAY,eAAe,OAAO;AAAA,EAChD;AAAA,EACA,sBAAsB,eAAe;AACnC,WAAO,iCACF,KAAK,YAAY,eAAe,OAAO,IADrC;AAAA,MAEL,uBAAuB;AAAA,IACzB;AAAA,EACF;AAAA,EACA,cAAc,eAAe;AAC3B,WAAO,KAAK,YAAY,eAAe,SAAS,MAAM;AAAA,EACxD;AAAA,EACA,aAAa,eAAe;AAC1B,QAAI,KAAK,gBAAgB;AACvB,aAAO,KAAK,eAAe,KAAK,UAAQ,KAAK,QAAQ,cAAc,GAAG;AAAA,IACxE;AAAA,EACF;AAAA,EACA,eAAe,eAAe;AAC5B,WAAO,KAAK,YAAY,eAAe,UAAU;AAAA,EACnD;AAAA,EACA,cAAc,eAAe;AAC3B,WAAO,KAAK,kBAAkB,KAAK,UAAU,aAAa;AAAA,EAC5D;AAAA,EACA,YAAY,eAAe;AACzB,WAAO,WAAW,cAAc,KAAK;AAAA,EACvC;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,MAAM,OAAO,mBAAiB,KAAK,cAAc,aAAa,KAAK,CAAC,KAAK,YAAY,eAAe,WAAW,CAAC,EAAE;AAAA,EAChI;AAAA,EACA,gBAAgB,OAAO;AACrB,WAAO,QAAQ,KAAK,MAAM,MAAM,GAAG,KAAK,EAAE,OAAO,mBAAiB,KAAK,cAAc,aAAa,KAAK,KAAK,YAAY,eAAe,WAAW,CAAC,EAAE,SAAS;AAAA,EAChK;AAAA,EACA,mBAAmB;AACjB,SAAK,eAAe,YAAY,KAAK,IAAI;AAAA,EAC3C;AAAA,EACA,iBAAiB,OAAO;AACtB,QAAI,KAAK,aAAa;AACpB,WAAK,eAAe,YAAY,KAAK,KAAK;AAC1C,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,WAAK,eAAe,KAAK;AAAA,QACvB,eAAe;AAAA,QACf;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,sBAAsB,YAAY;AACvC,UAAM,YAAY;AAAA,EACpB;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,mBAAmB,mBAAmB;AACpD,cAAQ,4BAA4B,0BAA6B,sBAAsB,WAAU,IAAI,qBAAqB,WAAU;AAAA,IACtI;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,GAAG,CAAC,cAAc,CAAC;AAAA,IAC9C,WAAW,SAAS,iBAAiB,IAAI,KAAK;AAC5C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AAAA,MACzE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,MAAM,CAAC,GAAG,QAAQ,QAAQ,gBAAgB;AAAA,MAC1C,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,YAAY,CAAC,GAAG,cAAc,cAAc,eAAe;AAAA,MAC3D,cAAc,CAAC,GAAG,gBAAgB,gBAAgB,gBAAgB;AAAA,MAClE,aAAa,CAAC,GAAG,eAAe,eAAe,gBAAgB;AAAA,MAC/D,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,OAAO,CAAC,GAAG,SAAS,SAAS,eAAe;AAAA,MAC5C,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,qBAAqB;AAAA,IACvB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,UAAU,CAAI,0BAA6B,0BAA0B;AAAA,IACrE,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC,kBAAkB,EAAE,GAAG,CAAC,QAAQ,WAAW,GAAG,SAAS,QAAQ,WAAW,WAAW,YAAY,SAAS,GAAG,CAAC,SAAS,IAAI,GAAG,SAAS,GAAG,CAAC,QAAQ,aAAa,GAAG,SAAS,WAAW,GAAG,MAAM,GAAG,CAAC,QAAQ,YAAY,YAAY,IAAI,GAAG,WAAW,WAAW,SAAS,kBAAkB,GAAG,MAAM,GAAG,CAAC,QAAQ,aAAa,GAAG,SAAS,GAAG,CAAC,QAAQ,YAAY,YAAY,IAAI,GAAG,WAAW,WAAW,gBAAgB,GAAG,CAAC,GAAG,0BAA0B,GAAG,SAAS,YAAY,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,gBAAgB,SAAS,gBAAgB,eAAe,UAAU,kBAAkB,iBAAiB,SAAS,kBAAkB,gBAAgB,aAAa,kBAAkB,GAAG,MAAM,GAAG,CAAC,WAAW,IAAI,GAAG,UAAU,WAAW,GAAG,MAAM,GAAG,CAAC,WAAW,IAAI,GAAG,cAAc,eAAe,oBAAoB,2BAA2B,UAAU,WAAW,YAAY,uBAAuB,oBAAoB,sBAAsB,cAAc,SAAS,GAAG,MAAM,GAAG,CAAC,WAAW,IAAI,GAAG,UAAU,SAAS,GAAG,CAAC,SAAS,uBAAuB,GAAG,WAAW,WAAW,GAAG,MAAM,GAAG,CAAC,SAAS,wBAAwB,GAAG,MAAM,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,cAAc,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,uBAAuB,GAAG,WAAW,SAAS,GAAG,CAAC,GAAG,wBAAwB,GAAG,IAAI,GAAG,CAAC,GAAG,wBAAwB,GAAG,aAAa,IAAI,GAAG,CAAC,GAAG,cAAc,OAAO,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,SAAS,0BAA0B,GAAG,MAAM,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC,WAAW,IAAI,GAAG,cAAc,eAAe,oBAAoB,2BAA2B,UAAU,WAAW,YAAY,uBAAuB,oBAAoB,sBAAsB,cAAc,OAAO,GAAG,CAAC,SAAS,wBAAwB,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC,GAAG,wBAAwB,GAAG,WAAW,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,aAAa,kBAAkB,gBAAgB,SAAS,gBAAgB,eAAe,UAAU,kBAAkB,iBAAiB,SAAS,kBAAkB,cAAc,CAAC;AAAA,IACjkE,UAAU,SAAS,oBAAoB,IAAI,KAAK;AAC9C,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,eAAe,GAAG,MAAM,GAAG,CAAC;AAC/B,QAAG,WAAW,SAAS,SAAS,wCAAwC,QAAQ;AAC9E,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,UAAU,KAAK,MAAM,CAAC;AAAA,QAClD,CAAC,EAAE,QAAQ,SAAS,uCAAuC,QAAQ;AACjE,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,SAAS,KAAK,MAAM,CAAC;AAAA,QACjD,CAAC,EAAE,WAAW,SAAS,0CAA0C,QAAQ;AACvE,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,YAAY,KAAK,MAAM,CAAC;AAAA,QACpD,CAAC;AACD,QAAG,WAAW,GAAG,mCAAmC,GAAG,GAAG,eAAe,CAAC;AAC1E,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,WAAc,gBAAgB,GAAG,KAAK,CAAC,IAAI,MAAM,IAAI,IAAI,CAAC,EAAE,YAAY,CAAC,EAAE,WAAW,IAAI,YAAY;AACpH,QAAG,YAAY,mBAAmB,MAAM,EAAE,cAAc,IAAI,SAAS,EAAE,mBAAmB,IAAI,cAAc,EAAE,MAAM,IAAI,OAAO,IAAI,SAAS,IAAI,EAAE,yBAAyB,IAAI,aAAa;AAC5L,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,WAAW,IAAI,KAAK;AAAA,MACpC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,aAAY,cAAiB,SAAY,SAAY,MAAS,kBAAqB,SAAS,cAAiB,YAAe,kBAAkB,QAAQ,eAAkB,SAAS,eAAe,gBAAgB,aAAgB,OAAO,YAAY;AAAA,IAClQ,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,cAAc,QAAQ,eAAe,eAAe,gBAAgB,aAAa,YAAY;AAAA,MACrH,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkJV,eAAe,kBAAkB;AAAA,IACnC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,UAAN,MAAM,iBAAgB,cAAc;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,MAAM,OAAO;AACf,SAAK,SAAS;AACd,SAAK,kBAAkB,KAAK,qBAAqB,KAAK,UAAU,CAAC,CAAC;AAAA,EACpE;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMb,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM3B,SAAS,IAAI,aAAa;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,iBAAiB,OAAO,CAAC,CAAC;AAAA,EAC1B,SAAS,OAAO,CAAC;AAAA,EACjB,kBAAkB,OAAO;AAAA,IACvB,OAAO;AAAA,IACP,OAAO;AAAA,IACP,WAAW;AAAA,IACX,MAAM;AAAA,EACR,CAAC;AAAA,EACD,cAAc;AAAA,EACd;AAAA,EACA;AAAA,EACA,kBAAkB,OAAO,YAAY;AAAA,EACrC;AAAA,EACA,IAAI,eAAe;AACjB,UAAM,gBAAgB,KAAK,eAAe,EAAE,KAAK,OAAK,EAAE,QAAQ,KAAK,gBAAgB,EAAE,SAAS;AAChG,WAAO,gBAAgB,cAAc,QAAQ,KAAK;AAAA,EACpD;AAAA,EACA,IAAI,iBAAiB;AACnB,QAAI,CAAC,KAAK,mBAAmB,CAAC,KAAK,gBAAgB,QAAQ;AACzD,WAAK,kBAAkB,KAAK,qBAAqB,KAAK,SAAS,CAAC,CAAC;AAAA,IACnE;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB;AAClB,UAAM,cAAc,KAAK,gBAAgB;AACzC,WAAO,YAAY,QAAQ,YAAY,MAAM,KAAK,YAAY,KAAK,KAAK,YAAY,UAAU,KAAK,GAAG,KAAK,EAAE,GAAG,WAAW,YAAY,SAAS,IAAI,MAAM,YAAY,YAAY,EAAE,IAAI,YAAY,KAAK,KAAK;AAAA,EAChN;AAAA,EACA,YAAY,UAAU,YAAY,IAAI,UAAU,IAAI,gBAAgB;AAClE,UAAM;AACN,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,KAAK;AACV,SAAK,WAAW;AAChB,SAAK,KAAK;AACV,SAAK,iBAAiB;AACtB,WAAO,MAAM;AACX,YAAM,OAAO,KAAK,eAAe;AACjC,UAAI,WAAW,IAAI,GAAG;AACpB,aAAK,yBAAyB;AAC9B,aAAK,mBAAmB;AAAA,MAC1B,OAAO;AACL,aAAK,2BAA2B;AAChC,aAAK,qBAAqB;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AACT,UAAM,SAAS;AACf,SAAK,uBAAuB;AAC5B,SAAK,eAAe,WAAW,KAAK;AACpC,SAAK,eAAe,gBAAgB,KAAK;AACzC,SAAK,uBAAuB,KAAK,eAAe,WAAW,UAAU,MAAM,KAAK,2BAA2B,CAAC;AAC5G,SAAK,KAAK,KAAK,MAAM,KAAK,QAAQ;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,qBAAqB;AACnB,SAAK,WAAW,QAAQ,UAAQ;AAC9B,cAAQ,KAAK,QAAQ,GAAG;AAAA,QACtB,KAAK;AACH,eAAK,iBAAiB,KAAK;AAC3B;AAAA,QACF,KAAK;AACH,eAAK,eAAe,KAAK;AACzB;AAAA,QACF,KAAK;AACH,eAAK,oBAAoB,KAAK;AAC9B;AAAA,QACF,KAAK;AACH,eAAK,uBAAuB,KAAK;AACjC;AAAA,QACF,KAAK;AACH,eAAK,gBAAgB,KAAK;AAC1B;AAAA,QACF;AACE,eAAK,gBAAgB,KAAK;AAC1B;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB,OAAO,QAAQ,GAAG,SAAS,CAAC,GAAG,YAAY,IAAI;AAClE,UAAM,iBAAiB,CAAC;AACxB,aAAS,MAAM,QAAQ,CAAC,MAAM,UAAU;AACtC,YAAM,OAAO,cAAc,KAAK,YAAY,MAAM,MAAM;AACxD,YAAM,UAAU;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,cAAQ,OAAO,IAAI,KAAK,qBAAqB,KAAK,OAAO,QAAQ,GAAG,SAAS,GAAG;AAChF,qBAAe,KAAK,OAAO;AAAA,IAC7B,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,yBAAyB;AACvB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,UAAI,CAAC,KAAK,oBAAoB;AAC5B,cAAM,QAAQ,OAAO,WAAW,eAAe,KAAK,UAAU,GAAG;AACjE,aAAK,QAAQ;AACb,aAAK,eAAe,MAAM;AAC1B,aAAK,qBAAqB,MAAM;AAC9B,eAAK,eAAe,MAAM;AAC1B,eAAK,eAAe;AACpB,eAAK,GAAG,aAAa;AAAA,QACvB;AACA,cAAM,iBAAiB,UAAU,KAAK,kBAAkB;AAAA,MAC1D;AAAA,IACF;AAAA,EACF;AAAA,EACA,2BAA2B;AACzB,QAAI,KAAK,oBAAoB;AAC3B,WAAK,MAAM,oBAAoB,UAAU,KAAK,kBAAkB;AAChE,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,YAAY,MAAM,MAAM;AACtB,WAAO,OAAO,QAAQ,KAAK,IAAI,CAAC,IAAI;AAAA,EACtC;AAAA,EACA,gBAAgB,OAAO;AACrB,SAAK,OAAO,KAAK;AAAA,EACnB;AAAA,EACA,kBAAkB,OAAO;AACvB,KAAC,MAAM,SAAS,WAAW,MAAM,SAAS,YAAY,KAAK,gBAAgB,KAAK;AAAA,EAClF;AAAA,EACA,YAAY,OAAO;AACjB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,UAAU,KAAK,qBAAqB,aAAa;AACvD,UAAM,OAAO,QAAQ,cAAc,MAAM;AACzC,UAAM,WAAW,KAAK,WAAW,aAAa;AAC9C,QAAI,UAAU;AACZ,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,WAAK,eAAe,IAAI,KAAK,eAAe,EAAE,OAAO,OAAK,QAAQ,EAAE,OAAO,IAAI,WAAW,EAAE,GAAG,CAAC,CAAC;AACjG,WAAK,gBAAgB,IAAI;AAAA,QACvB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,WAAK,QAAQ,CAAC;AACd,YAAM,KAAK,SAAS,iBAAiB,aAAa;AAAA,IACpD,OAAO;AACL,UAAI,SAAS;AACX,aAAK,aAAa,KAAK;AAAA,MACzB,OAAO;AACL,cAAM,oBAAoB,OAAO,gBAAgB,KAAK,eAAe,EAAE,KAAK,OAAK,EAAE,cAAc,EAAE;AACnG,aAAK,KAAK,aAAa;AACvB,aAAK,uBAAuB,eAAe,oBAAoB,kBAAkB,QAAQ,EAAE;AAC3F,aAAK,eAAe;AACpB,cAAM,KAAK,SAAS,iBAAiB,aAAa;AAAA,MACpD;AAAA,IACF;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO;AACtB,QAAI,CAAC,cAAc,GAAG;AACpB,UAAI,CAAC,KAAK,cAAc;AACtB,aAAK,aAAa,KAAK;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA,EACA,uBAAuB,OAAO,OAAO;AACnC,UAAM,gBAAgB,KAAK,gBAAgB,KAAK;AAChD,QAAI,KAAK,gBAAgB,EAAE,UAAU,OAAO;AAC1C,YAAM,kBAAkB,KAAK,gBAAgB;AAC7C,WAAK,gBAAgB,IAAI,iCACpB,kBADoB;AAAA,QAEvB,MAAM,cAAc;AAAA,QACpB;AAAA,MACF,EAAC;AACD,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,aAAa,QAAQ,IAAI;AACvB,UAAM,KAAK,UAAU,KAAK,GAAG,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK;AACvD,UAAM,UAAU,WAAW,KAAK,SAAS,GAAG,eAAe,UAAU,EAAE,IAAI;AAC3E,QAAI,SAAS;AACX,cAAQ,kBAAkB,QAAQ,eAAe;AAAA,QAC/C,OAAO;AAAA,QACP,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,aAAa,OAAO;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,QAAQ,aAAa,EAAG;AAC5B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,UAAU,WAAW,KAAK;AAChC,UAAM,iBAAiB,KAAK,eAAe,EAAE,OAAO,OAAK,EAAE,cAAc,aAAa,EAAE,cAAc,GAAG;AACzG,eAAW,eAAe,KAAK,aAAa;AAC5C,SAAK,gBAAgB,IAAI;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,SAAK,eAAe,IAAI,cAAc;AACtC,gBAAY,KAAK,QAAQ;AACzB,eAAW,MAAM,KAAK,SAAS,iBAAiB,aAAa;AAAA,EAC/D;AAAA,EACA,OAAO,OAAO;AACZ,QAAI,KAAK,cAAc;AACrB,WAAK,eAAe;AACpB,kBAAY,MAAM,KAAK,SAAS,GAAG,aAAa;AAChD,WAAK,KAAK;AAAA,IACZ,OAAO;AACL,WAAK,eAAe;AACpB,kBAAY,IAAI,QAAQ,KAAK,SAAS,GAAG,eAAe,KAAK,OAAO,OAAO,IAAI;AAC/E,iBAAW,MAAM;AACf,aAAK,KAAK;AAAA,MACZ,GAAG,CAAC;AAAA,IACN;AACA,SAAK,yBAAyB;AAC9B,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,KAAK,OAAO,SAAS;AACnB,QAAI,KAAK,cAAc;AACrB,iBAAW,MAAM;AACf,cAAM,KAAK,WAAW,aAAa;AAAA,MACrC,GAAG,CAAC;AAAA,IACN;AACA,SAAK,eAAe,IAAI,CAAC,CAAC;AAC1B,SAAK,gBAAgB,IAAI;AAAA,MACvB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,WAAW;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AACD,eAAW,MAAM,KAAK,UAAU,iBAAiB,aAAa;AAC9D,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,OAAO;AACL,UAAM,gBAAgB,KAAK,gBAAgB,KAAK,0BAA0B,CAAC;AAC3E,SAAK,gBAAgB,IAAI;AAAA,MACvB,OAAO,KAAK,0BAA0B;AAAA,MACtC,OAAO;AAAA,MACP,WAAW;AAAA,MACX,MAAM,eAAe;AAAA,IACvB,CAAC;AACD,UAAM,KAAK,UAAU,iBAAiB,aAAa;AAAA,EACrD;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,UAAU;AACf,UAAM,gBAAgB,KAAK,gBAAgB,KAAK,0BAA0B,CAAC;AAC3E,UAAM,kBAAkB,KAAK,gBAAgB,EAAE,UAAU,KAAK,KAAK,gBAAgB,IAAI;AAAA,MACrF,OAAO,KAAK,0BAA0B;AAAA,MACtC,OAAO;AAAA,MACP,WAAW;AAAA,MACX,MAAM,eAAe;AAAA,IACvB;AACA,SAAK,gBAAgB,IAAI,eAAe;AACxC,SAAK,QAAQ,KAAK,KAAK;AAAA,EACzB;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,UAAU;AACf,SAAK,gBAAgB,IAAI;AAAA,MACvB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,WAAW;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AACD,SAAK,cAAc;AACnB,SAAK,QAAQ;AACb,SAAK,OAAO,KAAK,KAAK;AAAA,EACxB;AAAA,EACA,UAAU,OAAO;AACf,UAAM,UAAU,MAAM,WAAW,MAAM;AACvC,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK;AACH,aAAK,eAAe,KAAK;AACzB;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK;AACvB;AAAA,MACF,KAAK;AACH,aAAK,eAAe,KAAK;AACzB;AAAA,MACF,KAAK;AACH,aAAK,gBAAgB,KAAK;AAC1B;AAAA,MACF,KAAK;AACH,aAAK,UAAU,KAAK;AACpB;AAAA,MACF,KAAK;AACH,aAAK,SAAS,KAAK;AACnB;AAAA,MACF,KAAK;AACH,aAAK,WAAW,KAAK;AACrB;AAAA,MACF,KAAK;AACH,aAAK,WAAW,KAAK;AACrB;AAAA,MACF,KAAK;AACH,aAAK,YAAY,KAAK;AACtB;AAAA,MACF,KAAK;AACH,aAAK,SAAS,KAAK;AACnB;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAEH;AAAA,MACF;AACE,YAAI,CAAC,WAAW,qBAAqB,MAAM,GAAG,GAAG;AAC/C,eAAK,YAAY,OAAO,MAAM,GAAG;AAAA,QACnC;AACA;AAAA,IACJ;AAAA,EACF;AAAA,EACA,gBAAgB,OAAO;AACrB,WAAO,WAAW,KAAK,YAAY,IAAI,KAAK,aAAa,KAAK,IAAI;AAAA,EACpE;AAAA,EACA,4BAA4B;AAC1B,UAAM,gBAAgB,KAAK,sBAAsB;AACjD,WAAO,gBAAgB,IAAI,KAAK,mBAAmB,IAAI;AAAA,EACzD;AAAA,EACA,qBAAqB;AACnB,WAAO,KAAK,aAAa,UAAU,mBAAiB,KAAK,YAAY,aAAa,CAAC;AAAA,EACrF;AAAA,EACA,wBAAwB;AACtB,WAAO,KAAK,aAAa,UAAU,mBAAiB,KAAK,oBAAoB,aAAa,CAAC;AAAA,EAC7F;AAAA,EACA,qBAAqB,eAAe;AAClC,WAAO,iBAAiB,WAAW,cAAc,KAAK;AAAA,EACxD;AAAA,EACA,WAAW,eAAe;AACxB,WAAO,KAAK,eAAe,EAAE,KAAK,OAAK,EAAE,QAAQ,cAAc,GAAG;AAAA,EACpE;AAAA,EACA,oBAAoB,eAAe;AACjC,WAAO,KAAK,YAAY,aAAa,KAAK,KAAK,WAAW,aAAa;AAAA,EACzE;AAAA,EACA,YAAY,eAAe;AACzB,WAAO,CAAC,CAAC,iBAAiB,CAAC,KAAK,eAAe,cAAc,IAAI,KAAK,CAAC,KAAK,gBAAgB,cAAc,IAAI;AAAA,EAChH;AAAA,EACA,eAAe,MAAM;AACnB,WAAO,KAAK,YAAY,MAAM,UAAU;AAAA,EAC1C;AAAA,EACA,gBAAgB,MAAM;AACpB,WAAO,KAAK,YAAY,MAAM,WAAW;AAAA,EAC3C;AAAA,EACA,cAAc,eAAe;AAC3B,WAAO,KAAK,YAAY,aAAa,KAAK,KAAK,uBAAuB,aAAa,EAAE,kBAAkB,EAAE,WAAW,KAAK,YAAY,kBAAkB,CAAC;AAAA,EAC1J;AAAA,EACA,sBAAsB,eAAe;AACnC,WAAO,iBAAiB,WAAW,cAAc,KAAK;AAAA,EACxD;AAAA,EACA,YAAY,OAAO,MAAM;AACvB,SAAK,eAAe,KAAK,eAAe,MAAM;AAC9C,QAAI,YAAY;AAChB,QAAI,UAAU;AACd,QAAI,KAAK,gBAAgB,EAAE,UAAU,IAAI;AACvC,kBAAY,KAAK,aAAa,MAAM,KAAK,gBAAgB,EAAE,KAAK,EAAE,UAAU,mBAAiB,KAAK,cAAc,aAAa,CAAC;AAC9H,kBAAY,cAAc,KAAK,KAAK,aAAa,MAAM,GAAG,KAAK,gBAAgB,EAAE,KAAK,EAAE,UAAU,mBAAiB,KAAK,cAAc,aAAa,CAAC,IAAI,YAAY,KAAK,gBAAgB,EAAE;AAAA,IAC7L,OAAO;AACL,kBAAY,KAAK,aAAa,UAAU,mBAAiB,KAAK,cAAc,aAAa,CAAC;AAAA,IAC5F;AACA,QAAI,cAAc,IAAI;AACpB,gBAAU;AAAA,IACZ;AACA,QAAI,cAAc,MAAM,KAAK,gBAAgB,EAAE,UAAU,IAAI;AAC3D,kBAAY,KAAK,0BAA0B;AAAA,IAC7C;AACA,QAAI,cAAc,IAAI;AACpB,WAAK,uBAAuB,OAAO,SAAS;AAAA,IAC9C;AACA,QAAI,KAAK,eAAe;AACtB,mBAAa,KAAK,aAAa;AAAA,IACjC;AACA,SAAK,gBAAgB,WAAW,MAAM;AACpC,WAAK,cAAc;AACnB,WAAK,gBAAgB;AAAA,IACvB,GAAG,GAAG;AACN,WAAO;AAAA,EACT;AAAA,EACA,uBAAuB,eAAe;AACpC,WAAO,gBAAgB,KAAK,aAAa,cAAc,IAAI,IAAI;AAAA,EACjE;AAAA,EACA,aAAa,MAAM;AACjB,WAAO,KAAK,YAAY,MAAM,OAAO;AAAA,EACvC;AAAA,EACA,eAAe,OAAO;AACpB,UAAM,gBAAgB,KAAK,aAAa,KAAK,gBAAgB,EAAE,KAAK;AACpE,UAAM,OAAO,gBAAgB,QAAQ,cAAc,MAAM,IAAI;AAC7D,QAAI,MAAM;AACR,YAAM,UAAU,KAAK,sBAAsB,aAAa;AACxD,UAAI,SAAS;AACX,aAAK,aAAa;AAAA,UAChB,eAAe;AAAA,UACf;AAAA,QACF,CAAC;AACD,aAAK,gBAAgB,IAAI;AAAA,UACvB,OAAO;AAAA,UACP,WAAW,cAAc;AAAA,UACzB,MAAM,cAAc;AAAA,QACtB,CAAC;AACD,aAAK,gBAAgB,KAAK;AAAA,MAC5B;AAAA,IACF,OAAO;AACL,YAAM,YAAY,KAAK,gBAAgB,EAAE,UAAU,KAAK,KAAK,kBAAkB,KAAK,gBAAgB,EAAE,KAAK,IAAI,KAAK,0BAA0B;AAC9I,WAAK,uBAAuB,OAAO,SAAS;AAC5C,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA,EACA,gBAAgB,OAAO;AACrB,UAAM,gBAAgB,KAAK,aAAa,KAAK,gBAAgB,EAAE,KAAK;AACpE,UAAM,aAAa,gBAAgB,KAAK,eAAe,EAAE,KAAK,OAAK,EAAE,QAAQ,cAAc,SAAS,IAAI;AACxG,QAAI,YAAY;AACd,YAAM,UAAU,KAAK,sBAAsB,aAAa;AACxD,UAAI,SAAS;AACX,aAAK,aAAa;AAAA,UAChB,eAAe;AAAA,UACf;AAAA,QACF,CAAC;AACD,aAAK,gBAAgB,IAAI;AAAA,UACvB,OAAO;AAAA,UACP,WAAW,cAAc;AAAA,UACzB,MAAM,cAAc;AAAA,QACtB,CAAC;AACD,aAAK,eAAe,KAAK;AAAA,MAC3B;AAAA,IACF,OAAO;AACL,YAAM,YAAY,KAAK,gBAAgB,EAAE,UAAU,KAAK,KAAK,kBAAkB,KAAK,gBAAgB,EAAE,KAAK,IAAI,KAAK,0BAA0B;AAC9I,WAAK,uBAAuB,OAAO,SAAS;AAC5C,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA,EACA,aAAa,OAAO;AAClB,UAAM,gBAAgB,KAAK,aAAa,KAAK,gBAAgB,EAAE,KAAK;AACpE,UAAM,OAAO,QAAQ,cAAc,MAAM;AACzC,QAAI,MAAM;AACR,YAAM,UAAU,KAAK,sBAAsB,aAAa;AACxD,UAAI,SAAS;AACX,aAAK,aAAa;AAAA,UAChB,eAAe;AAAA,UACf;AAAA,QACF,CAAC;AACD,aAAK,gBAAgB,IAAI;AAAA,UACvB,OAAO;AAAA,UACP,WAAW,cAAc;AAAA,UACzB,MAAM,cAAc;AAAA,QACtB,CAAC;AACD,cAAM,YAAY,KAAK,kBAAkB;AACzC,aAAK,uBAAuB,OAAO,SAAS;AAAA,MAC9C;AAAA,IACF,OAAO;AACL,YAAM,aAAa,KAAK,eAAe,EAAE,KAAK,OAAK,EAAE,QAAQ,cAAc,SAAS;AACpF,UAAI,KAAK,gBAAgB,EAAE,UAAU,GAAG;AACtC,aAAK,gBAAgB,IAAI;AAAA,UACvB,OAAO;AAAA,UACP,WAAW,aAAa,WAAW,YAAY;AAAA,UAC/C,MAAM,cAAc;AAAA,QACtB,CAAC;AACD,aAAK,cAAc;AACnB,aAAK,eAAe,KAAK;AACzB,cAAM,iBAAiB,KAAK,eAAe,EAAE,OAAO,OAAK,EAAE,cAAc,KAAK,gBAAgB,EAAE,SAAS;AACzG,aAAK,eAAe,IAAI,cAAc;AAAA,MACxC,OAAO;AACL,cAAM,YAAY,KAAK,gBAAgB,EAAE,UAAU,KAAK,KAAK,kBAAkB,KAAK,gBAAgB,EAAE,KAAK,IAAI,KAAK,yBAAyB;AAC7I,aAAK,uBAAuB,OAAO,SAAS;AAAA,MAC9C;AAAA,IACF;AACA,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,eAAe,OAAO;AACpB,UAAM,gBAAgB,KAAK,aAAa,KAAK,gBAAgB,EAAE,KAAK;AACpE,UAAM,aAAa,gBAAgB,KAAK,eAAe,EAAE,KAAK,OAAK,EAAE,QAAQ,cAAc,SAAS,IAAI;AACxG,QAAI,YAAY;AACd,WAAK,aAAa;AAAA,QAChB,eAAe;AAAA,QACf,eAAe;AAAA,MACjB,CAAC;AACD,YAAM,iBAAiB,KAAK,eAAe,EAAE,OAAO,OAAK,EAAE,cAAc,KAAK,gBAAgB,EAAE,SAAS;AACzG,WAAK,eAAe,IAAI,cAAc;AACtC,YAAM,eAAe;AAAA,IACvB,OAAO;AACL,YAAM,YAAY,KAAK,gBAAgB,EAAE,UAAU,KAAK,KAAK,kBAAkB,KAAK,gBAAgB,EAAE,KAAK,IAAI,KAAK,yBAAyB;AAC7I,WAAK,uBAAuB,OAAO,SAAS;AAC5C,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA,EACA,UAAU,OAAO;AACf,SAAK,uBAAuB,OAAO,KAAK,mBAAmB,CAAC;AAC5D,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,SAAS,OAAO;AACd,SAAK,uBAAuB,OAAO,KAAK,kBAAkB,CAAC;AAC3D,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,WAAW,KAAK;AAAA,EACvB;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,KAAK,OAAO,IAAI;AACrB,SAAK,gBAAgB,EAAE,QAAQ,KAAK,0BAA0B;AAC9D,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,SAAS,OAAO;AACd,QAAI,KAAK,gBAAgB,EAAE,UAAU,IAAI;AACvC,YAAM,gBAAgB,KAAK,aAAa,KAAK,gBAAgB,EAAE,KAAK;AACpE,YAAM,UAAU,KAAK,sBAAsB,aAAa;AACxD,OAAC,WAAW,KAAK,aAAa;AAAA,QAC5B,eAAe;AAAA,QACf;AAAA,MACF,CAAC;AAAA,IACH;AACA,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,WAAW,OAAO;AAChB,QAAI,KAAK,gBAAgB,EAAE,UAAU,IAAI;AACvC,YAAM,UAAU,WAAW,KAAK,SAAS,GAAG,eAAe,UAAU,GAAG,KAAK,aAAa,EAAE,IAAI;AAChG,YAAM,gBAAgB,WAAW,WAAW,SAAS,6BAA6B;AAClF,sBAAgB,cAAc,MAAM,IAAI,WAAW,QAAQ,MAAM;AAAA,IACnE;AACA,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,2BAA2B;AACzB,UAAM,gBAAgB,KAAK,sBAAsB;AACjD,WAAO,gBAAgB,IAAI,KAAK,kBAAkB,IAAI;AAAA,EACxD;AAAA,EACA,oBAAoB;AAClB,WAAO,cAAc,KAAK,cAAc,mBAAiB,KAAK,YAAY,aAAa,CAAC;AAAA,EAC1F;AAAA,EACA,kBAAkB,OAAO;AACvB,UAAM,mBAAmB,QAAQ,IAAI,cAAc,KAAK,aAAa,MAAM,GAAG,KAAK,GAAG,mBAAiB,KAAK,YAAY,aAAa,CAAC,IAAI;AAC1I,WAAO,mBAAmB,KAAK,mBAAmB;AAAA,EACpD;AAAA,EACA,kBAAkB,OAAO;AACvB,UAAM,mBAAmB,QAAQ,KAAK,aAAa,SAAS,IAAI,KAAK,aAAa,MAAM,QAAQ,CAAC,EAAE,UAAU,mBAAiB,KAAK,YAAY,aAAa,CAAC,IAAI;AACjK,WAAO,mBAAmB,KAAK,mBAAmB,QAAQ,IAAI;AAAA,EAChE;AAAA,EACA,qBAAqB;AACnB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,UAAI,CAAC,KAAK,gBAAgB;AACxB,aAAK,iBAAiB,KAAK,SAAS,OAAO,KAAK,SAAS,aAAa,UAAU,WAAS;AACvF,cAAI,CAAC,cAAc,GAAG;AACpB,iBAAK,KAAK,OAAO,IAAI;AAAA,UACvB;AACA,eAAK,eAAe;AAAA,QACtB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,2BAA2B;AACzB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,UAAI,CAAC,KAAK,sBAAsB;AAC9B,aAAK,uBAAuB,KAAK,SAAS,OAAO,KAAK,UAAU,SAAS,WAAS;AAChF,gBAAM,qBAAqB,KAAK,SAAS,GAAG,kBAAkB,MAAM,UAAU,CAAC,KAAK,SAAS,GAAG,cAAc,SAAS,MAAM,MAAM;AACnI,gBAAM,sBAAsB,KAAK,gBAAgB,KAAK,WAAW,kBAAkB,MAAM,UAAU,CAAC,KAAK,WAAW,cAAc,SAAS,MAAM,MAAM;AACvJ,cAAI,oBAAoB;AACtB,kCAAsB,KAAK,eAAe,QAAQ,KAAK,KAAK;AAAA,UAC9D;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,6BAA6B;AAC3B,QAAI,KAAK,sBAAsB;AAC7B,WAAK,qBAAqB;AAC1B,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,gBAAgB;AACvB,WAAK,eAAe;AACpB,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,sBAAsB,YAAY;AACvC,SAAK,2BAA2B;AAChC,SAAK,qBAAqB;AAC1B,SAAK,yBAAyB;AAC9B,UAAM,YAAY;AAAA,EACpB;AAAA,EACA,OAAO,OAAO,SAAS,gBAAgB,mBAAmB;AACxD,WAAO,KAAK,qBAAqB,UAAY,kBAAkB,QAAQ,GAAM,kBAAkB,WAAW,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,iBAAiB,GAAM,kBAAkB,cAAc,CAAC;AAAA,EACxQ;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,IACzB,gBAAgB,SAAS,uBAAuB,IAAI,KAAK,UAAU;AACjE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,MAAM,CAAC;AACnC,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AACpE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAClE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AACnE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AACvE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,sBAAsB,GAAG;AAC1E,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW,SAAS,cAAc,IAAI,KAAK;AACzC,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,MAAM,CAAC;AACtB,QAAG,YAAY,MAAM,CAAC;AAAA,MACxB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,aAAa,GAAG;AACjE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAAA,MACjE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,YAAY,CAAC,GAAG,cAAc,cAAc,eAAe;AAAA,MAC3D,aAAa,CAAC,GAAG,eAAe,eAAe,gBAAgB;AAAA,MAC/D,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,YAAY;AAAA,MACZ,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,eAAe;AAAA,MACpE,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,gBAAgB,YAAY,CAAC,GAAM,0BAA6B,0BAA0B;AAAA,IAC5H,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,YAAY,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,cAAc,EAAE,GAAG,CAAC,GAAG,WAAW,SAAS,GAAG,CAAC,SAAS,mBAAmB,GAAG,MAAM,GAAG,CAAC,YAAY,KAAK,QAAQ,UAAU,SAAS,oBAAoB,GAAG,SAAS,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,aAAa,aAAa,YAAY,eAAe,kBAAkB,SAAS,gBAAgB,UAAU,QAAQ,cAAc,cAAc,gBAAgB,eAAe,aAAa,kBAAkB,iBAAiB,uBAAuB,gBAAgB,GAAG,CAAC,SAAS,iBAAiB,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,YAAY,KAAK,QAAQ,UAAU,GAAG,oBAAoB,GAAG,SAAS,SAAS,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,eAAe,CAAC;AAAA,IAC1sB,UAAU,SAAS,iBAAiB,IAAI,KAAK;AAC3C,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,WAAW,GAAG,wBAAwB,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,sBAAsB,GAAG,GAAG,KAAK,CAAC;AAC9F,QAAG,eAAe,GAAG,gBAAgB,GAAG,CAAC;AACzC,QAAG,WAAW,aAAa,SAAS,mDAAmD,QAAQ;AAC7F,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,YAAY,MAAM,CAAC;AAAA,QAC/C,CAAC,EAAE,aAAa,SAAS,mDAAmD,QAAQ;AAClF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,YAAY,MAAM,CAAC;AAAA,QAC/C,CAAC,EAAE,YAAY,SAAS,kDAAkD,QAAQ;AAChF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,WAAW,MAAM,CAAC;AAAA,QAC9C,CAAC,EAAE,eAAe,SAAS,qDAAqD,QAAQ;AACtF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,UAAU,MAAM,CAAC;AAAA,QAC7C,CAAC,EAAE,kBAAkB,SAAS,wDAAwD,QAAQ;AAC5F,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,iBAAiB,MAAM,CAAC;AAAA,QACpD,CAAC;AACD,QAAG,aAAa;AAChB,QAAG,WAAW,GAAG,wBAAwB,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,gCAAgC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AACnJ,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,cAAM,YAAe,YAAY,CAAC;AAClC,QAAG,WAAW,IAAI,UAAU;AAC5B,QAAG,WAAW,WAAc,gBAAgB,IAAI,MAAM,IAAI,cAAc,IAAI,YAAY,CAAC,EAAE,WAAW,IAAI,KAAK;AAC/G,QAAG,YAAY,mBAAmB,MAAM,EAAE,gBAAgB,SAAS;AACnE,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,iBAAiB,IAAI,cAAc;AAC7D,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,SAAS,IAAI,MAAM,SAAS,CAAC;AACvD,QAAG,UAAU;AACb,QAAG,WAAW,SAAS,IAAI,cAAc,EAAE,gBAAgB,IAAI,YAAY,EAAE,UAAU,IAAI,EAAE,EAAE,QAAQ,IAAI,EAAE,cAAc,IAAI,UAAU,EAAE,cAAc,IAAI,UAAU,EAAE,gBAAgB,IAAI,YAAY,EAAE,eAAe,IAAI,WAAW,EAAE,aAAa,IAAI,SAAS,EAAE,kBAAkB,IAAI,cAAc,EAAE,iBAAiB,IAAI,UAAU,IAAI,gBAAgB,MAAS,EAAE,uBAAuB,IAAI,uBAAuB,IAAI,oBAAoB,EAAE,kBAAkB,IAAI,eAAe,CAAC;AAC7d,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,QAAQ,IAAI,eAAe,IAAI,YAAY,EAAE,YAAY,SAAS;AAAA,MAClF;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAiB,SAAY,MAAS,kBAAqB,SAAS,cAAc,YAAY,eAAe,UAAU,aAAa,YAAY;AAAA,IAC/J,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,cAAc,YAAY,eAAe,UAAU,aAAa,YAAY;AAAA,MACpG,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqDV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,gBAAgB,YAAY;AAAA,IAC1C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,QACd,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,QACZ,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,QACb,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,QACjB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,QACpB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,SAAS,YAAY;AAAA,IAC/B,SAAS,CAAC,SAAS,YAAY;AAAA,EACjC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,SAAS,cAAc,YAAY;AAAA,EAC/C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,SAAS,YAAY;AAAA,MAC/B,SAAS,CAAC,SAAS,YAAY;AAAA,IACjC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["MenubarClasses"]}
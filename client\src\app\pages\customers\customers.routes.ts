import { Routes } from '@angular/router';

export default [
    {
        path: '',
        loadComponent: () => import('./components/customers-list/customers-list.component').then(m => m.CustomersListComponent)
    },
    {
        path: 'new',
        loadComponent: () => import('./components/customer-form/customer-form.component').then(m => m.CustomerFormComponent)
    },
    {
        path: ':id',
        loadComponent: () => import('./components/customer-detail/customer-detail.component').then(m => m.CustomerDetailComponent)
    },
    {
        path: ':id/edit',
        loadComponent: () => import('./components/customer-form/customer-form.component').then(m => m.CustomerFormComponent)
    }
] as Routes;

﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;
using TRtek_EYS.Application.Features.Auth.Login;
using TRtek_EYS.Domain.Entities;
using System.Security.Cryptography;
using System.Text;

namespace TRtek_EYS.Application.Features.Auth.Login
{
    public class LoginCommandHandler : IRequestHandler<LoginCommand, LoginCommandResponse>
    {
        private readonly IApplicationDbContext _context;
        private readonly IJwtProvider _jwtProvider;

        public LoginCommandHandler(IApplicationDbContext context, IJwtProvider jwtProvider)
        {
            _context = context;
            _jwtProvider = jwtProvider;
        }

        public async Task<LoginCommandResponse> Handle(LoginCommand request, CancellationToken cancellationToken)
        {
            var user = await _context.Users
                .Include(u => u.UserRoles)
                    .ThenInclude(ur => ur.Role)
                .FirstOrDefaultAsync(u => u.Username == request.Username && u.IsActive, cancellationToken);

            if (user is null || !VerifyPassword(request.Password, user.PasswordHash))
                throw new UnauthorizedAccessException("Geçersiz kullanıcı adı veya şifre.");

            // Token + kullanıcı bilgilerini oluştur
            var response = await _jwtProvider.CreateToken(user);

            return response;
        }

        private static bool VerifyPassword(string password, string storedHash)
        {
            using var sha256 = SHA256.Create();
            var computedHash = Convert.ToBase64String(sha256.ComputeHash(Encoding.UTF8.GetBytes(password)));
            return computedHash == storedHash;
        }
    }
}

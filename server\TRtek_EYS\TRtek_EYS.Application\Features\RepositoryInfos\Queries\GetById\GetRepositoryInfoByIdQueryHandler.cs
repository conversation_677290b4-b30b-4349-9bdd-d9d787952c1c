﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.RepositoryInfos.Queries.GetById
{
    public class GetRepositoryInfoByIdQueryHandler : IRequestHandler<GetRepositoryInfoByIdQuery, RepositoryInfoGetByIdDto>
    {
        private readonly IApplicationDbContext _context;

        public GetRepositoryInfoByIdQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<RepositoryInfoGetByIdDto> Handle(GetRepositoryInfoByIdQuery request, CancellationToken cancellationToken)
        {
            var repo = await _context.RepositoryInfos
                .AsNoTracking()
                .FirstOrDefaultAsync(r => r.Id == request.Id, cancellationToken);

            if (repo is null)
                throw new Exception("Repository bilgisi bulunamadı.");

            return new RepositoryInfoGetByIdDto
            {
                Id = repo.Id,
                Url = repo.Url,
                Provider = repo.Provider,
                IsPrivate = repo.IsPrivate,
                ProjectId = repo.ProjectId
            };
        }
    }
}

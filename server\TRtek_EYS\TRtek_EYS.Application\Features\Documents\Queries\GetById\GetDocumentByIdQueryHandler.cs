﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Documents.Queries.GetById
{
    public class GetDocumentByIdQueryHandler : IRequestHandler<GetDocumentByIdQuery, DocumentGetByIdDto>
    {
        private readonly IApplicationDbContext _context;

        public GetDocumentByIdQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<DocumentGetByIdDto> Handle(GetDocumentByIdQuery request, CancellationToken cancellationToken)
        {
            var document = await _context.Documents
                .AsNoTracking()
                .FirstOrDefaultAsync(d => d.Id == request.Id, cancellationToken);

            if (document is null)
                throw new Exception("Doküman bulunamadı.");

            return new DocumentGetByIdDto
            {
                Id = document.Id,
                FileName = document.FileName,
                FilePath = document.FilePath,
                FileSize = document.FileSize,
                VersionNumber = document.VersionNumber,
                Type = document.Type.ToString(),
                ProjectId = document.ProjectId
            };
        }
    }
}

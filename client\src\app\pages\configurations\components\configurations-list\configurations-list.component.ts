import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Table, TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { InputIconModule } from 'primeng/inputicon';
import { IconFieldModule } from 'primeng/iconfield';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToastModule } from 'primeng/toast';
import { ConfirmationService, MessageService } from 'primeng/api';
import { Configuration, ConfigurationStatus, Environment, DeploymentStatus } from '../../models/configuration.model';

@Component({
    selector: 'app-configurations-list',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        TableModule,
        ButtonModule,
        InputTextModule,
        InputIconModule,
        IconFieldModule,
        TagModule,
        TooltipModule,
        ConfirmDialogModule,
        ToastModule
    ],
    providers: [ConfirmationService, MessageService],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <h5>Konfigürasyon Yönetimi</h5>
                        <p-button
                            label="Yeni Konfigürasyon"
                            icon="pi pi-plus"
                            (onClick)="createConfiguration()"
                            severity="primary"
                            size="small">
                        </p-button>
                    </div>

                    <p-table 
                        #dt 
                        [value]="configurations" 
                        [rows]="10" 
                        [paginator]="true"
                        [globalFilterFields]="['configurationCode', 'configurationName', 'customerName', 'projectName', 'environment']"
                        [tableStyle]="{ 'min-width': '75rem' }"
                        [rowHover]="true"
                        dataKey="id"
                        currentPageReportTemplate="{first} - {last} / {totalRecords} konfigürasyon"
                        [showCurrentPageReport]="true">
                        
                        <ng-template pTemplate="caption">
                            <div class="flex align-items-center justify-content-between">
                                <h5 class="m-0">Konfigürasyonlar</h5>
                                <p-iconfield iconPosition="left">
                                    <p-inputicon>
                                        <i class="pi pi-search"></i>
                                    </p-inputicon>
                                    <input 
                                        pInputText 
                                        type="text" 
                                        (input)="dt.filterGlobal($any($event.target).value, 'contains')" 
                                        placeholder="Konfigürasyon ara..." />
                                </p-iconfield>
                            </div>
                        </ng-template>

                        <ng-template pTemplate="header">
                            <tr>
                                <th pSortableColumn="configurationCode">
                                    Kod <p-sortIcon field="configurationCode"></p-sortIcon>
                                </th>
                                <th pSortableColumn="configurationName">
                                    Konfigürasyon Adı <p-sortIcon field="configurationName"></p-sortIcon>
                                </th>
                                <th pSortableColumn="customerName">
                                    Müşteri <p-sortIcon field="customerName"></p-sortIcon>
                                </th>
                                <th pSortableColumn="projectName">
                                    Proje <p-sortIcon field="projectName"></p-sortIcon>
                                </th>
                                <th pSortableColumn="environment">
                                    Ortam <p-sortIcon field="environment"></p-sortIcon>
                                </th>
                                <th pSortableColumn="version">
                                    Versiyon <p-sortIcon field="version"></p-sortIcon>
                                </th>
                                <th pSortableColumn="status">
                                    Durum <p-sortIcon field="status"></p-sortIcon>
                                </th>
                                <th>Dağıtım</th>
                                <th pSortableColumn="lastModifiedDate">
                                    Son Güncelleme <p-sortIcon field="lastModifiedDate"></p-sortIcon>
                                </th>
                                <th>İşlemler</th>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="body" let-config>
                            <tr>
                                <td>
                                    <span class="font-medium">{{ config.configurationCode }}</span>
                                </td>
                                <td>
                                    <div>
                                        <span class="font-medium">{{ config.configurationName }}</span>
                                        <div class="text-sm text-500 mt-1">{{ config.description | slice:0:40 }}...</div>
                                    </div>
                                </td>
                                <td>
                                    <span>{{ config.customerName }}</span>
                                </td>
                                <td>
                                    <div>
                                        <span class="font-medium">{{ config.projectName }}</span>
                                        <div class="text-sm text-500 mt-1">{{ config.projectCode }}</div>
                                    </div>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="config.environment" 
                                        [severity]="getEnvironmentSeverity(config.environment)">
                                    </p-tag>
                                </td>
                                <td>
                                    <span class="font-medium">{{ config.version }}</span>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="config.status" 
                                        [severity]="getStatusSeverity(config.status)">
                                    </p-tag>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="config.deploymentInfo.deploymentStatus" 
                                        [severity]="getDeploymentSeverity(config.deploymentInfo.deploymentStatus)">
                                    </p-tag>
                                </td>
                                <td>
                                    <span *ngIf="config.lastModifiedDate">{{ config.lastModifiedDate | date:'dd/MM/yyyy' }}</span>
                                    <span *ngIf="!config.lastModifiedDate" class="text-500">-</span>
                                </td>
                                <td>
                                    <div class="flex gap-2">
                                        <p-button 
                                            icon="pi pi-eye" 
                                            class="p-button-rounded p-button-text p-button-info"
                                            pTooltip="Detay"
                                            (onClick)="viewConfiguration(config.id)">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-pencil" 
                                            class="p-button-rounded p-button-text p-button-warning"
                                            pTooltip="Düzenle"
                                            (onClick)="editConfiguration(config.id)">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-cloud-upload" 
                                            class="p-button-rounded p-button-text p-button-success"
                                            pTooltip="Dağıt"
                                            (onClick)="deployConfiguration(config)">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-trash" 
                                            class="p-button-rounded p-button-text p-button-danger"
                                            pTooltip="Sil"
                                            (onClick)="deleteConfiguration(config)">
                                        </p-button>
                                    </div>
                                </td>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="emptymessage">
                            <tr>
                                <td colspan="10" class="text-center py-4">
                                    <i class="pi pi-info-circle text-4xl text-500 mb-3"></i>
                                    <div class="text-500 font-medium">Henüz konfigürasyon bulunmuyor</div>
                                    <div class="text-500">Yeni konfigürasyon eklemek için "Yeni Konfigürasyon" butonunu kullanın</div>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                </div>
            </div>
        </div>

        <p-confirmDialog></p-confirmDialog>
        <p-toast></p-toast>
    `
})
export class ConfigurationsListComponent implements OnInit {
    @ViewChild('dt') table!: Table;
    
    configurations: Configuration[] = [];

    constructor(
        private router: Router,
        private confirmationService: ConfirmationService,
        private messageService: MessageService
    ) {}

    ngOnInit() {
        this.loadConfigurations();
    }

    loadConfigurations() {
        this.configurations = [
            {
                id: '1',
                configurationCode: 'CFG-2025-001',
                customerId: '1',
                customerName: 'ABC Teknoloji A.Ş.',
                projectId: '1',
                projectName: 'E-Ticaret Platformu',
                projectCode: 'PRJ-2025-001',
                configurationName: 'Üretim Ortamı Konfigürasyonu',
                description: 'E-ticaret platformu için üretim ortamı ayarları ve veritabanı bağlantıları',
                environment: Environment.PRODUCTION,
                status: ConfigurationStatus.ACTIVE,
                version: 'v2.1.0',
                settings: [],
                deploymentInfo: {
                    serverUrl: 'https://api.abc.com',
                    databaseConnection: 'prod-db.abc.com',
                    apiEndpoints: [],
                    environmentVariables: [],
                    lastDeploymentDate: new Date('2025-01-10'),
                    deploymentStatus: DeploymentStatus.DEPLOYED,
                    deployedBy: 'DevOps Team'
                },
                createdBy: 'Admin',
                createdDate: new Date('2025-01-01'),
                lastModifiedBy: 'DevOps',
                lastModifiedDate: new Date('2025-01-10'),
                isActive: true,
                notes: 'Üretim ortamı için kritik konfigürasyon'
            },
            {
                id: '2',
                configurationCode: 'CFG-2025-002',
                customerId: '2',
                customerName: 'XYZ Finans Ltd.',
                projectId: '2',
                projectName: 'CRM Sistemi',
                projectCode: 'PRJ-2025-002',
                configurationName: 'Test Ortamı Konfigürasyonu',
                description: 'CRM sistemi için test ortamı ayarları',
                environment: Environment.TESTING,
                status: ConfigurationStatus.ACTIVE,
                version: 'v1.5.2',
                settings: [],
                deploymentInfo: {
                    serverUrl: 'https://test-api.xyz.com',
                    databaseConnection: 'test-db.xyz.com',
                    apiEndpoints: [],
                    environmentVariables: [],
                    lastDeploymentDate: new Date('2025-01-08'),
                    deploymentStatus: DeploymentStatus.DEPLOYED,
                    deployedBy: 'Test Team'
                },
                createdBy: 'Admin',
                createdDate: new Date('2023-12-15'),
                lastModifiedBy: 'Tester',
                lastModifiedDate: new Date('2025-01-08'),
                isActive: true
            },
            {
                id: '3',
                configurationCode: 'CFG-2025-003',
                customerId: '3',
                customerName: 'DEF Sağlık Hizmetleri',
                projectId: '3',
                projectName: 'Mobil Bankacılık',
                projectCode: 'PRJ-2025-003',
                configurationName: 'Demo Ortamı Konfigürasyonu',
                description: 'Mobil bankacılık uygulaması demo ortamı',
                environment: Environment.DEMO,
                status: ConfigurationStatus.DRAFT,
                version: 'v3.2.1',
                settings: [],
                deploymentInfo: {
                    serverUrl: 'https://demo-api.def.com',
                    apiEndpoints: [],
                    environmentVariables: [],
                    deploymentStatus: DeploymentStatus.NOT_DEPLOYED
                },
                createdBy: 'Admin',
                createdDate: new Date('2025-01-05'),
                isActive: false,
                notes: 'Demo amaçlı konfigürasyon'
            }
        ];
    }

    createConfiguration() {
        this.router.navigate(['/pages/configurations/new']);
    }

    viewConfiguration(id: string) {
        this.router.navigate(['/pages/configurations', id]);
    }

    editConfiguration(id: string) {
        this.router.navigate(['/pages/configurations', id, 'edit']);
    }

    deployConfiguration(config: Configuration) {
        this.confirmationService.confirm({
            message: `"${config.configurationName}" konfigürasyonunu dağıtmak istediğinizden emin misiniz?`,
            header: 'Konfigürasyon Dağıtım Onayı',
            icon: 'pi pi-cloud-upload',
            acceptLabel: 'Evet',
            rejectLabel: 'Hayır',
            accept: () => {
                this.messageService.add({
                    severity: 'success',
                    summary: 'Başarılı',
                    detail: 'Konfigürasyon dağıtım işlemi başlatıldı'
                });
            }
        });
    }

    deleteConfiguration(config: Configuration) {
        this.confirmationService.confirm({
            message: `"${config.configurationName}" konfigürasyonunu silmek istediğinizden emin misiniz?`,
            header: 'Konfigürasyon Silme Onayı',
            icon: 'pi pi-exclamation-triangle',
            acceptLabel: 'Evet',
            rejectLabel: 'Hayır',
            accept: () => {
                this.configurations = this.configurations.filter(c => c.id !== config.id);
                this.messageService.add({
                    severity: 'success',
                    summary: 'Başarılı',
                    detail: 'Konfigürasyon başarıyla silindi'
                });
            }
        });
    }

    getStatusSeverity(status: ConfigurationStatus): string {
        switch (status) {
            case ConfigurationStatus.ACTIVE:
                return 'success';
            case ConfigurationStatus.DRAFT:
                return 'info';
            case ConfigurationStatus.PENDING:
                return 'warning';
            case ConfigurationStatus.INACTIVE:
                return 'secondary';
            case ConfigurationStatus.DEPRECATED:
                return 'danger';
            case ConfigurationStatus.ARCHIVED:
                return 'secondary';
            default:
                return 'info';
        }
    }

    getEnvironmentSeverity(environment: Environment): string {
        switch (environment) {
            case Environment.PRODUCTION:
                return 'danger';
            case Environment.STAGING:
                return 'warning';
            case Environment.TESTING:
                return 'info';
            case Environment.DEVELOPMENT:
                return 'secondary';
            case Environment.DEMO:
                return 'help';
            default:
                return 'info';
        }
    }

    getDeploymentSeverity(status: DeploymentStatus): string {
        switch (status) {
            case DeploymentStatus.DEPLOYED:
                return 'success';
            case DeploymentStatus.DEPLOYING:
                return 'warning';
            case DeploymentStatus.FAILED:
                return 'danger';
            case DeploymentStatus.NOT_DEPLOYED:
                return 'secondary';
            case DeploymentStatus.ROLLBACK:
                return 'info';
            default:
                return 'secondary';
        }
    }
}

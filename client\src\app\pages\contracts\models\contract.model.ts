export interface Contract {
    id: string;
    contractNumber: string;
    customerId: string;
    customerName: string;
    projectId: string;
    projectName: string;
    projectCode: string;
    contractDate: Date;
    startDate: Date;
    endDate?: Date;
    licenseType: LicenseType;
    contractValue: number;
    currency: string;
    status: ContractStatus;
    renewalDate?: Date;
    isActive: boolean;
    paymentTerms: PaymentTerms;
    supportLevel: SupportLevel;
    maxUsers?: number;
    features: ContractFeature[];
    documents: ContractDocument[];
    notes?: string;
    createdBy: string;
    createdDate: Date;
    lastModifiedBy?: string;
    lastModifiedDate?: Date;
}

export interface ContractFeature {
    id: string;
    featureName: string;
    isIncluded: boolean;
    limitValue?: number;
    description?: string;
}

export interface ContractDocument {
    id: string;
    documentName: string;
    documentType: DocumentType;
    filePath: string;
    uploadDate: Date;
    uploadedBy: string;
    fileSize: number;
    version: string;
}

export enum LicenseType {
    PERPETUAL = 'Kalıcı Lisa<PERSON>',
    SUBSCRIPTION = 'Abonelik',
    TRIAL = 'Deneme',
    CUSTOM = 'Özel',
    SAAS = 'SaaS',
    ENTERPRISE = 'Kurumsal'
}

export enum ContractStatus {
    DRAFT = 'Taslak',
    ACTIVE = 'Aktif',
    EXPIRED = 'Süresi Dolmuş',
    PENDING = 'Beklemede',
    CANCELLED = 'İptal Edilmiş',
    RENEWED = 'Yenilenmiş',
    SUSPENDED = 'Askıya Alınmış'
}

export enum PaymentTerms {
    MONTHLY = 'Aylık',
    QUARTERLY = 'Üç Aylık',
    SEMI_ANNUAL = 'Altı Aylık',
    ANNUAL = 'Yıllık',
    ONE_TIME = 'Tek Seferlik',
    CUSTOM = 'Özel'
}

export enum SupportLevel {
    BASIC = 'Temel',
    STANDARD = 'Standart',
    PREMIUM = 'Premium',
    ENTERPRISE = 'Kurumsal',
    NONE = 'Destek Yok'
}

export enum DocumentType {
    CONTRACT = 'Sözleşme',
    AMENDMENT = 'Ek Sözleşme',
    INVOICE = 'Fatura',
    PAYMENT_RECEIPT = 'Ödeme Makbuzu',
    TECHNICAL_SPEC = 'Teknik Şartname',
    USER_MANUAL = 'Kullanım Kılavuzu',
    OTHER = 'Diğer'
}

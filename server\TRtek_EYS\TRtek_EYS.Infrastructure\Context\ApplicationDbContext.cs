﻿using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;
using TRtek_EYS.Domain.Entities;

namespace TRtek_EYS.Infrastructure.Persistence
{
    public class ApplicationDbContext : DbContext, IApplicationDbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        // DbSet Tanımları
        public DbSet<User> Users => Set<User>();
        public DbSet<Role> Roles => Set<Role>();
        public DbSet<UserRole> UserRoles => Set<UserRole>();
        public DbSet<Project> Projects => Set<Project>();
        public DbSet<ProjectUserRole> ProjectUserRoles => Set<ProjectUserRole>();
        public DbSet<ProjectTeam> ProjectTeams => Set<ProjectTeam>();
        public DbSet<Customer> Customers => Set<Customer>();
        public DbSet<CustomerProject> CustomerProjects => Set<CustomerProject>();
        public DbSet<Team> Teams => Set<Team>();
        public DbSet<Technology> Technologies => Set<Technology>();
        public DbSet<Platform> Platforms => Set<Platform>();
        public DbSet<Document> Documents => Set<Document>();
        public DbSet<Contract> Contracts => Set<Contract>();
        public DbSet<Configuration> Configurations => Set<Configuration>();
        public DbSet<RepositoryInfo> RepositoryInfos => Set<RepositoryInfo>();
        public DbSet<VersionInfo> VersionInfos => Set<VersionInfo>();

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            return base.SaveChangesAsync(cancellationToken);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Tüm IEntityTypeConfiguration<> konfigürasyonlarını uygula
            modelBuilder.ApplyConfigurationsFromAssembly(typeof(ApplicationDbContext).Assembly);

            base.OnModelCreating(modelBuilder);
        }
    }
}

{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-popover.mjs"], "sourcesContent": ["import { trigger, state, transition, style, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, inject, NgZone, booleanAttribute, numberAttribute, HostListener, ContentChildren, ContentChild, Output, Input, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { isIOS, appendChild, absolutePosition, getOffset, addClass, findSingle, isTouchDevice } from '@primeuix/utils';\nimport { OverlayService, SharedModule, PrimeTemplate } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { ZIndexUtils } from 'primeng/utils';\nimport { BaseStyle } from 'primeng/base';\nimport { $dt } from '@primeuix/styled';\nconst _c0 = [\"content\"];\nconst _c1 = [\"*\"];\nconst _c2 = (a0, a1) => ({\n  showTransitionParams: a0,\n  hideTransitionParams: a1\n});\nconst _c3 = (a0, a1) => ({\n  value: a0,\n  params: a1\n});\nconst _c4 = a0 => ({\n  closeCallback: a0\n});\nfunction Popover_div_0_3_ng_template_0_Template(rf, ctx) {}\nfunction Popover_div_0_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Popover_div_0_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Popover_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵlistener(\"click\", function Popover_div_0_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onOverlayClick($event));\n    })(\"@animation.start\", function Popover_div_0_Template_div_animation_animation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAnimationStart($event));\n    })(\"@animation.done\", function Popover_div_0_Template_div_animation_animation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAnimationEnd($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 2);\n    i0.ɵɵlistener(\"click\", function Popover_div_0_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onContentClick($event));\n    })(\"mousedown\", function Popover_div_0_Template_div_mousedown_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onContentClick($event));\n    });\n    i0.ɵɵprojection(2);\n    i0.ɵɵtemplate(3, Popover_div_0_3_Template, 1, 0, null, 3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.styleClass);\n    i0.ɵɵproperty(\"ngClass\", \"p-popover p-component\")(\"ngStyle\", ctx_r1.style)(\"@animation\", i0.ɵɵpureFunction2(13, _c3, ctx_r1.overlayVisible ? \"open\" : \"close\", i0.ɵɵpureFunction2(10, _c2, ctx_r1.showTransitionOptions, ctx_r1.hideTransitionOptions)));\n    i0.ɵɵattribute(\"aria-modal\", ctx_r1.overlayVisible)(\"aria-label\", ctx_r1.ariaLabel)(\"aria-labelledBy\", ctx_r1.ariaLabelledBy);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.contentTemplate || ctx_r1._contentTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(16, _c4, ctx_r1.onCloseClick.bind(ctx_r1)));\n  }\n}\nconst theme = ({\n  dt\n}) => `\n.p-popover {\n    margin-top: ${dt('popover.gutter')};\n    background: ${dt('popover.background')};\n    color: ${dt('popover.color')};\n    border: 1px solid ${dt('popover.border.color')};\n    border-radius: ${dt('popover.border.radius')};\n    box-shadow: ${dt('popover.shadow')};\n    position: absolute\n}\n\n.p-popover-content {\n    padding: ${dt('popover.content.padding')};\n}\n\n.p-popover-flipped {\n    margin-top: calc(${dt('popover.gutter')} * -1);\n    margin-bottom: ${dt('popover.gutter')};\n}\n\n.p-popover-enter-from {\n    opacity: 0;\n    transform: scaleY(0.8);\n}\n\n.p-popover-leave-to {\n    opacity: 0;\n}\n\n.p-popover-enter-active {\n    transition: transform 0.12s cubic-bezier(0, 0, 0.2, 1), opacity 0.12s cubic-bezier(0, 0, 0.2, 1);\n}\n\n.p-popover-leave-active {\n    transition: opacity 0.1s linear;\n}\n\n.p-popover:after,\n.p-popover:before {\n    bottom: 100%;\n    left: calc(${dt('popover.arrow.offset')} + ${dt('popover.arrow.left')});\n    content: \" \";\n    height: 0;\n    width: 0;\n    position: absolute;\n    pointer-events: none;\n}\n\n.p-popover:after {\n    border-width: calc(${dt('popover.gutter')} - 2px);\n    margin-left: calc(-1 * (${dt('popover.gutter')} - 2px));\n    border-style: solid;\n    border-color: transparent;\n    border-bottom-color: ${dt('popover.background')};\n}\n\n.p-popover:before {\n    border-width: ${dt('popover.gutter')};\n    margin-left: calc(-1 * ${dt('popover.gutter')});\n    border-style: solid;\n    border-color: transparent;\n    border-bottom-color: ${dt('popover.border.color')};\n}\n\n.p-popover-flipped:after,\n.p-popover-flipped:before {\n    bottom: auto;\n    top: 100%;\n}\n\n.p-popover.p-popover-flipped:after {\n    border-bottom-color: transparent;\n    border-top-color: ${dt('popover.background')};\n}\n\n.p-popover.p-popover-flipped:before {\n    border-bottom-color: transparent;\n    border-top-color: ${dt('popover.border.color')};\n}\n\n`;\nconst classes = {\n  root: 'p-popover p-component',\n  content: 'p-popover-content'\n};\nclass PopoverStyle extends BaseStyle {\n  name = 'popover';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵPopoverStyle_BaseFactory;\n    return function PopoverStyle_Factory(__ngFactoryType__) {\n      return (ɵPopoverStyle_BaseFactory || (ɵPopoverStyle_BaseFactory = i0.ɵɵgetInheritedFactory(PopoverStyle)))(__ngFactoryType__ || PopoverStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: PopoverStyle,\n    factory: PopoverStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PopoverStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * Popover is a container component that can overlay other components on page.\n * @group Components\n */\nclass Popover extends BaseComponent {\n  /**\n   * Defines a string that labels the input for accessibility.\n   * @group Props\n   */\n  ariaLabel;\n  /**\n   * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n   * @group Props\n   */\n  ariaLabelledBy;\n  /**\n   * Enables to hide the overlay when outside is clicked.\n   * @group Props\n   */\n  dismissable = true;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Target element to attach the panel, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n   * @group Props\n   */\n  appendTo = 'body';\n  /**\n   * Whether to automatically manage layering.\n   * @group Props\n   */\n  autoZIndex = true;\n  /**\n   * Aria label of the close icon.\n   * @group Props\n   */\n  ariaCloseLabel;\n  /**\n   * Base zIndex value to use in layering.\n   * @group Props\n   */\n  baseZIndex = 0;\n  /**\n   * When enabled, first button receives focus on show.\n   * @group Props\n   */\n  focusOnShow = true;\n  /**\n   * Transition options of the show animation.\n   * @group Props\n   */\n  showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n  /**\n   * Transition options of the hide animation.\n   * @group Props\n   */\n  hideTransitionOptions = '.1s linear';\n  /**\n   * Callback to invoke when an overlay becomes visible.\n   * @group Emits\n   */\n  onShow = new EventEmitter();\n  /**\n   * Callback to invoke when an overlay gets hidden.\n   * @group Emits\n   */\n  onHide = new EventEmitter();\n  container;\n  overlayVisible = false;\n  render = false;\n  isOverlayAnimationInProgress = false;\n  selfClick = false;\n  documentClickListener;\n  target;\n  willHide;\n  scrollHandler;\n  documentResizeListener;\n  /**\n   * Custom content template.\n   * @group Templates\n   */\n  contentTemplate;\n  templates;\n  _contentTemplate;\n  destroyCallback;\n  overlayEventListener;\n  overlaySubscription;\n  _componentStyle = inject(PopoverStyle);\n  zone = inject(NgZone);\n  overlayService = inject(OverlayService);\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'content':\n          this._contentTemplate = item.template;\n          break;\n      }\n    });\n  }\n  bindDocumentClickListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.documentClickListener) {\n        let documentEvent = isIOS() ? 'touchstart' : 'click';\n        const documentTarget = this.el ? this.el.nativeElement.ownerDocument : this.document;\n        this.documentClickListener = this.renderer.listen(documentTarget, documentEvent, event => {\n          if (!this.dismissable) {\n            return;\n          }\n          if (!this.container?.contains(event.target) && this.target !== event.target && !this.target.contains(event.target) && !this.selfClick) {\n            this.hide();\n          }\n          this.selfClick = false;\n          this.cd.markForCheck();\n        });\n      }\n    }\n  }\n  unbindDocumentClickListener() {\n    if (this.documentClickListener) {\n      this.documentClickListener();\n      this.documentClickListener = null;\n      this.selfClick = false;\n    }\n  }\n  /**\n   * Toggles the visibility of the panel.\n   * @param {Event} event - Browser event\n   * @param {Target} target - Target element.\n   * @group Method\n   */\n  toggle(event, target) {\n    if (this.isOverlayAnimationInProgress) {\n      return;\n    }\n    if (this.overlayVisible) {\n      if (this.hasTargetChanged(event, target)) {\n        this.destroyCallback = () => {\n          this.show(null, target || event.currentTarget || event.target);\n        };\n      }\n      this.hide();\n    } else {\n      this.show(event, target);\n    }\n  }\n  /**\n   * Displays the panel.\n   * @param {Event} event - Browser event\n   * @param {Target} target - Target element.\n   * @group Method\n   */\n  show(event, target) {\n    target && event && event.stopPropagation();\n    if (this.isOverlayAnimationInProgress) {\n      return;\n    }\n    this.target = target || event.currentTarget || event.target;\n    this.overlayVisible = true;\n    this.render = true;\n    this.cd.markForCheck();\n  }\n  onOverlayClick(event) {\n    this.overlayService.add({\n      originalEvent: event,\n      target: this.el.nativeElement\n    });\n    this.selfClick = true;\n  }\n  onContentClick(event) {\n    const targetElement = event.target;\n    this.selfClick = event.offsetX < targetElement.clientWidth && event.offsetY < targetElement.clientHeight;\n  }\n  hasTargetChanged(event, target) {\n    return this.target != null && this.target !== (target || event.currentTarget || event.target);\n  }\n  appendContainer() {\n    if (this.appendTo) {\n      if (this.appendTo === 'body') this.renderer.appendChild(this.document.body, this.container);else appendChild(this.appendTo, this.container);\n    }\n  }\n  restoreAppend() {\n    if (this.container && this.appendTo) {\n      this.renderer.appendChild(this.el.nativeElement, this.container);\n    }\n  }\n  align() {\n    if (this.autoZIndex) {\n      ZIndexUtils.set('overlay', this.container, this.baseZIndex + this.config.zIndex.overlay);\n    }\n    absolutePosition(this.container, this.target, false);\n    const containerOffset = getOffset(this.container);\n    const targetOffset = getOffset(this.target);\n    const borderRadius = this.document.defaultView?.getComputedStyle(this.container).getPropertyValue('border-radius');\n    let arrowLeft = 0;\n    if (containerOffset.left < targetOffset.left) {\n      arrowLeft = targetOffset.left - containerOffset.left - parseFloat(borderRadius) * 2;\n    }\n    this.container?.style.setProperty($dt('popover.arrow.left').name, `${arrowLeft}px`);\n    if (containerOffset.top < targetOffset.top) {\n      this.container.setAttribute('data-p-popover-flipped', 'true');\n      addClass(this.container, 'p-popover-flipped');\n    }\n  }\n  onAnimationStart(event) {\n    if (event.toState === 'open') {\n      this.container = event.element;\n      this.container?.setAttribute(this.attrSelector, '');\n      this.appendContainer();\n      this.align();\n      this.bindDocumentClickListener();\n      this.bindDocumentResizeListener();\n      this.bindScrollListener();\n      if (this.focusOnShow) {\n        this.focus();\n      }\n      this.overlayEventListener = e => {\n        if (this.container && this.container.contains(e.target)) {\n          this.selfClick = true;\n        }\n      };\n      this.overlaySubscription = this.overlayService.clickObservable.subscribe(this.overlayEventListener);\n      this.onShow.emit(null);\n    }\n    this.isOverlayAnimationInProgress = true;\n  }\n  onAnimationEnd(event) {\n    switch (event.toState) {\n      case 'void':\n        if (this.destroyCallback) {\n          this.destroyCallback();\n          this.destroyCallback = null;\n        }\n        if (this.overlaySubscription) {\n          this.overlaySubscription.unsubscribe();\n        }\n        break;\n      case 'close':\n        if (this.autoZIndex) {\n          ZIndexUtils.clear(this.container);\n        }\n        if (this.overlaySubscription) {\n          this.overlaySubscription.unsubscribe();\n        }\n        this.onContainerDestroy();\n        this.onHide.emit({});\n        this.render = false;\n        break;\n    }\n    this.isOverlayAnimationInProgress = false;\n  }\n  focus() {\n    let focusable = findSingle(this.container, '[autofocus]');\n    if (focusable) {\n      this.zone.runOutsideAngular(() => {\n        setTimeout(() => focusable.focus(), 5);\n      });\n    }\n  }\n  /**\n   * Hides the panel.\n   * @group Method\n   */\n  hide() {\n    this.overlayVisible = false;\n    this.cd.markForCheck();\n  }\n  onCloseClick(event) {\n    this.hide();\n    event.preventDefault();\n  }\n  onEscapeKeydown(event) {\n    this.hide();\n  }\n  onWindowResize() {\n    if (this.overlayVisible && !isTouchDevice()) {\n      this.hide();\n    }\n  }\n  bindDocumentResizeListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.documentResizeListener) {\n        const window = this.document.defaultView;\n        this.documentResizeListener = this.renderer.listen(window, 'resize', this.onWindowResize.bind(this));\n      }\n    }\n  }\n  unbindDocumentResizeListener() {\n    if (this.documentResizeListener) {\n      this.documentResizeListener();\n      this.documentResizeListener = null;\n    }\n  }\n  bindScrollListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.scrollHandler) {\n        this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, () => {\n          if (this.overlayVisible) {\n            this.hide();\n          }\n        });\n      }\n      this.scrollHandler.bindScrollListener();\n    }\n  }\n  unbindScrollListener() {\n    if (this.scrollHandler) {\n      this.scrollHandler.unbindScrollListener();\n    }\n  }\n  onContainerDestroy() {\n    if (!this.cd.destroyed) {\n      this.target = null;\n    }\n    this.unbindDocumentClickListener();\n    this.unbindDocumentResizeListener();\n    this.unbindScrollListener();\n  }\n  ngOnDestroy() {\n    if (this.scrollHandler) {\n      this.scrollHandler.destroy();\n      this.scrollHandler = null;\n    }\n    if (this.container && this.autoZIndex) {\n      ZIndexUtils.clear(this.container);\n    }\n    if (!this.cd.destroyed) {\n      this.target = null;\n    }\n    this.destroyCallback = null;\n    if (this.container) {\n      this.restoreAppend();\n      this.onContainerDestroy();\n    }\n    if (this.overlaySubscription) {\n      this.overlaySubscription.unsubscribe();\n    }\n    super.ngOnDestroy();\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵPopover_BaseFactory;\n    return function Popover_Factory(__ngFactoryType__) {\n      return (ɵPopover_BaseFactory || (ɵPopover_BaseFactory = i0.ɵɵgetInheritedFactory(Popover)))(__ngFactoryType__ || Popover);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Popover,\n    selectors: [[\"p-popover\"]],\n    contentQueries: function Popover_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, _c0, 4);\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    hostBindings: function Popover_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"keydown.escape\", function Popover_keydown_escape_HostBindingHandler($event) {\n          return ctx.onEscapeKeydown($event);\n        }, false, i0.ɵɵresolveDocument);\n      }\n    },\n    inputs: {\n      ariaLabel: \"ariaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\",\n      dismissable: [2, \"dismissable\", \"dismissable\", booleanAttribute],\n      style: \"style\",\n      styleClass: \"styleClass\",\n      appendTo: \"appendTo\",\n      autoZIndex: [2, \"autoZIndex\", \"autoZIndex\", booleanAttribute],\n      ariaCloseLabel: \"ariaCloseLabel\",\n      baseZIndex: [2, \"baseZIndex\", \"baseZIndex\", numberAttribute],\n      focusOnShow: [2, \"focusOnShow\", \"focusOnShow\", booleanAttribute],\n      showTransitionOptions: \"showTransitionOptions\",\n      hideTransitionOptions: \"hideTransitionOptions\"\n    },\n    outputs: {\n      onShow: \"onShow\",\n      onHide: \"onHide\"\n    },\n    features: [i0.ɵɵProvidersFeature([PopoverStyle]), i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c1,\n    decls: 1,\n    vars: 1,\n    consts: [[\"role\", \"dialog\", 3, \"ngClass\", \"ngStyle\", \"class\", \"click\", 4, \"ngIf\"], [\"role\", \"dialog\", 3, \"click\", \"ngClass\", \"ngStyle\"], [1, \"p-popover-content\", 3, \"click\", \"mousedown\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n    template: function Popover_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, Popover_div_0_Template, 4, 18, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.render);\n      }\n    },\n    dependencies: [CommonModule, i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, SharedModule],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('animation', [state('void', style({\n        transform: 'scaleY(0.8)',\n        opacity: 0\n      })), state('close', style({\n        opacity: 0\n      })), state('open', style({\n        transform: 'translateY(0)',\n        opacity: 1\n      })), transition('void => open', animate('{{showTransitionParams}}')), transition('open => close', animate('{{hideTransitionParams}}'))])]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Popover, [{\n    type: Component,\n    args: [{\n      selector: 'p-popover',\n      standalone: true,\n      imports: [CommonModule, SharedModule],\n      template: `\n        <div\n            *ngIf=\"render\"\n            [ngClass]=\"'p-popover p-component'\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            (click)=\"onOverlayClick($event)\"\n            [@animation]=\"{\n                value: overlayVisible ? 'open' : 'close',\n                params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions }\n            }\"\n            (@animation.start)=\"onAnimationStart($event)\"\n            (@animation.done)=\"onAnimationEnd($event)\"\n            role=\"dialog\"\n            [attr.aria-modal]=\"overlayVisible\"\n            [attr.aria-label]=\"ariaLabel\"\n            [attr.aria-labelledBy]=\"ariaLabelledBy\"\n        >\n            <div class=\"p-popover-content\" (click)=\"onContentClick($event)\" (mousedown)=\"onContentClick($event)\">\n                <ng-content></ng-content>\n                <ng-template *ngTemplateOutlet=\"contentTemplate || _contentTemplate; context: { closeCallback: onCloseClick.bind(this) }\"></ng-template>\n            </div>\n        </div>\n    `,\n      animations: [trigger('animation', [state('void', style({\n        transform: 'scaleY(0.8)',\n        opacity: 0\n      })), state('close', style({\n        opacity: 0\n      })), state('open', style({\n        transform: 'translateY(0)',\n        opacity: 1\n      })), transition('void => open', animate('{{showTransitionParams}}')), transition('open => close', animate('{{hideTransitionParams}}'))])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [PopoverStyle]\n    }]\n  }], null, {\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    dismissable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    ariaCloseLabel: [{\n      type: Input\n    }],\n    baseZIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    focusOnShow: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    showTransitionOptions: [{\n      type: Input\n    }],\n    hideTransitionOptions: [{\n      type: Input\n    }],\n    onShow: [{\n      type: Output\n    }],\n    onHide: [{\n      type: Output\n    }],\n    contentTemplate: [{\n      type: ContentChild,\n      args: ['content', {\n        descendants: false\n      }]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    onEscapeKeydown: [{\n      type: HostListener,\n      args: ['document:keydown.escape', ['$event']]\n    }]\n  });\n})();\nclass PopoverModule {\n  static ɵfac = function PopoverModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PopoverModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: PopoverModule,\n    imports: [Popover, SharedModule],\n    exports: [Popover, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [Popover, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PopoverModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Popover, SharedModule],\n      exports: [Popover, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Popover, PopoverModule, PopoverStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,IAAM,MAAM,CAAC,SAAS;AACtB,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,sBAAsB;AAAA,EACtB,sBAAsB;AACxB;AACA,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,OAAO;AAAA,EACP,QAAQ;AACV;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,eAAe;AACjB;AACA,SAAS,uCAAuC,IAAI,KAAK;AAAC;AAC1D,SAAS,yBAAyB,IAAI,KAAK;AACzC,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,wCAAwC,GAAG,GAAG,aAAa;AAAA,EAC9E;AACF;AACA,SAAS,uBAAuB,IAAI,KAAK;AACvC,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,SAAS,SAAS,4CAA4C,QAAQ;AAClF,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,eAAe,MAAM,CAAC;AAAA,IACrD,CAAC,EAAE,oBAAoB,SAAS,gEAAgE,QAAQ;AACtG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC,EAAE,mBAAmB,SAAS,+DAA+D,QAAQ;AACpG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,eAAe,MAAM,CAAC;AAAA,IACrD,CAAC;AACD,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,SAAS,SAAS,4CAA4C,QAAQ;AAClF,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,eAAe,MAAM,CAAC;AAAA,IACrD,CAAC,EAAE,aAAa,SAAS,gDAAgD,QAAQ;AAC/E,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,eAAe,MAAM,CAAC;AAAA,IACrD,CAAC;AACD,IAAG,aAAa,CAAC;AACjB,IAAG,WAAW,GAAG,0BAA0B,GAAG,GAAG,MAAM,CAAC;AACxD,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,UAAU;AAC/B,IAAG,WAAW,WAAW,uBAAuB,EAAE,WAAW,OAAO,KAAK,EAAE,cAAiB,gBAAgB,IAAI,KAAK,OAAO,iBAAiB,SAAS,SAAY,gBAAgB,IAAI,KAAK,OAAO,uBAAuB,OAAO,qBAAqB,CAAC,CAAC;AACvP,IAAG,YAAY,cAAc,OAAO,cAAc,EAAE,cAAc,OAAO,SAAS,EAAE,mBAAmB,OAAO,cAAc;AAC5H,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,OAAO,mBAAmB,OAAO,gBAAgB,EAAE,2BAA8B,gBAAgB,IAAI,KAAK,OAAO,aAAa,KAAK,MAAM,CAAC,CAAC;AAAA,EAC/K;AACF;AACA,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA,kBAEY,GAAG,gBAAgB,CAAC;AAAA,kBACpB,GAAG,oBAAoB,CAAC;AAAA,aAC7B,GAAG,eAAe,CAAC;AAAA,wBACR,GAAG,sBAAsB,CAAC;AAAA,qBAC7B,GAAG,uBAAuB,CAAC;AAAA,kBAC9B,GAAG,gBAAgB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,eAKvB,GAAG,yBAAyB,CAAC;AAAA;AAAA;AAAA;AAAA,uBAIrB,GAAG,gBAAgB,CAAC;AAAA,qBACtB,GAAG,gBAAgB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAuBxB,GAAG,sBAAsB,CAAC,MAAM,GAAG,oBAAoB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yBAShD,GAAG,gBAAgB,CAAC;AAAA,8BACf,GAAG,gBAAgB,CAAC;AAAA;AAAA;AAAA,2BAGvB,GAAG,oBAAoB,CAAC;AAAA;AAAA;AAAA;AAAA,oBAI/B,GAAG,gBAAgB,CAAC;AAAA,6BACX,GAAG,gBAAgB,CAAC;AAAA;AAAA;AAAA,2BAGtB,GAAG,sBAAsB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBAW7B,GAAG,oBAAoB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,wBAKxB,GAAG,sBAAsB,CAAC;AAAA;AAAA;AAAA;AAIlD,IAAM,UAAU;AAAA,EACd,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,eAAN,MAAM,sBAAqB,UAAU;AAAA,EACnC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,qBAAqB,mBAAmB;AACtD,cAAQ,8BAA8B,4BAA+B,sBAAsB,aAAY,IAAI,qBAAqB,aAAY;AAAA,IAC9I;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,cAAa;AAAA,EACxB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,UAAN,MAAM,iBAAgB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,SAAS,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1B,SAAS,IAAI,aAAa;AAAA,EAC1B;AAAA,EACA,iBAAiB;AAAA,EACjB,SAAS;AAAA,EACT,+BAA+B;AAAA,EAC/B,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAkB,OAAO,YAAY;AAAA,EACrC,OAAO,OAAO,MAAM;AAAA,EACpB,iBAAiB,OAAO,cAAc;AAAA,EACtC,qBAAqB;AACnB,SAAK,UAAU,QAAQ,UAAQ;AAC7B,cAAQ,KAAK,QAAQ,GAAG;AAAA,QACtB,KAAK;AACH,eAAK,mBAAmB,KAAK;AAC7B;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,4BAA4B;AAC1B,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,UAAI,CAAC,KAAK,uBAAuB;AAC/B,YAAI,gBAAgB,MAAM,IAAI,eAAe;AAC7C,cAAM,iBAAiB,KAAK,KAAK,KAAK,GAAG,cAAc,gBAAgB,KAAK;AAC5E,aAAK,wBAAwB,KAAK,SAAS,OAAO,gBAAgB,eAAe,WAAS;AACxF,cAAI,CAAC,KAAK,aAAa;AACrB;AAAA,UACF;AACA,cAAI,CAAC,KAAK,WAAW,SAAS,MAAM,MAAM,KAAK,KAAK,WAAW,MAAM,UAAU,CAAC,KAAK,OAAO,SAAS,MAAM,MAAM,KAAK,CAAC,KAAK,WAAW;AACrI,iBAAK,KAAK;AAAA,UACZ;AACA,eAAK,YAAY;AACjB,eAAK,GAAG,aAAa;AAAA,QACvB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,8BAA8B;AAC5B,QAAI,KAAK,uBAAuB;AAC9B,WAAK,sBAAsB;AAC3B,WAAK,wBAAwB;AAC7B,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,OAAO,QAAQ;AACpB,QAAI,KAAK,8BAA8B;AACrC;AAAA,IACF;AACA,QAAI,KAAK,gBAAgB;AACvB,UAAI,KAAK,iBAAiB,OAAO,MAAM,GAAG;AACxC,aAAK,kBAAkB,MAAM;AAC3B,eAAK,KAAK,MAAM,UAAU,MAAM,iBAAiB,MAAM,MAAM;AAAA,QAC/D;AAAA,MACF;AACA,WAAK,KAAK;AAAA,IACZ,OAAO;AACL,WAAK,KAAK,OAAO,MAAM;AAAA,IACzB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KAAK,OAAO,QAAQ;AAClB,cAAU,SAAS,MAAM,gBAAgB;AACzC,QAAI,KAAK,8BAA8B;AACrC;AAAA,IACF;AACA,SAAK,SAAS,UAAU,MAAM,iBAAiB,MAAM;AACrD,SAAK,iBAAiB;AACtB,SAAK,SAAS;AACd,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA,EACA,eAAe,OAAO;AACpB,SAAK,eAAe,IAAI;AAAA,MACtB,eAAe;AAAA,MACf,QAAQ,KAAK,GAAG;AAAA,IAClB,CAAC;AACD,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,eAAe,OAAO;AACpB,UAAM,gBAAgB,MAAM;AAC5B,SAAK,YAAY,MAAM,UAAU,cAAc,eAAe,MAAM,UAAU,cAAc;AAAA,EAC9F;AAAA,EACA,iBAAiB,OAAO,QAAQ;AAC9B,WAAO,KAAK,UAAU,QAAQ,KAAK,YAAY,UAAU,MAAM,iBAAiB,MAAM;AAAA,EACxF;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,UAAU;AACjB,UAAI,KAAK,aAAa,OAAQ,MAAK,SAAS,YAAY,KAAK,SAAS,MAAM,KAAK,SAAS;AAAA,UAAO,aAAY,KAAK,UAAU,KAAK,SAAS;AAAA,IAC5I;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,QAAI,KAAK,aAAa,KAAK,UAAU;AACnC,WAAK,SAAS,YAAY,KAAK,GAAG,eAAe,KAAK,SAAS;AAAA,IACjE;AAAA,EACF;AAAA,EACA,QAAQ;AACN,QAAI,KAAK,YAAY;AACnB,kBAAY,IAAI,WAAW,KAAK,WAAW,KAAK,aAAa,KAAK,OAAO,OAAO,OAAO;AAAA,IACzF;AACA,qBAAiB,KAAK,WAAW,KAAK,QAAQ,KAAK;AACnD,UAAM,kBAAkB,UAAU,KAAK,SAAS;AAChD,UAAM,eAAe,UAAU,KAAK,MAAM;AAC1C,UAAM,eAAe,KAAK,SAAS,aAAa,iBAAiB,KAAK,SAAS,EAAE,iBAAiB,eAAe;AACjH,QAAI,YAAY;AAChB,QAAI,gBAAgB,OAAO,aAAa,MAAM;AAC5C,kBAAY,aAAa,OAAO,gBAAgB,OAAO,WAAW,YAAY,IAAI;AAAA,IACpF;AACA,SAAK,WAAW,MAAM,YAAY,IAAI,oBAAoB,EAAE,MAAM,GAAG,SAAS,IAAI;AAClF,QAAI,gBAAgB,MAAM,aAAa,KAAK;AAC1C,WAAK,UAAU,aAAa,0BAA0B,MAAM;AAC5D,eAAS,KAAK,WAAW,mBAAmB;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO;AACtB,QAAI,MAAM,YAAY,QAAQ;AAC5B,WAAK,YAAY,MAAM;AACvB,WAAK,WAAW,aAAa,KAAK,cAAc,EAAE;AAClD,WAAK,gBAAgB;AACrB,WAAK,MAAM;AACX,WAAK,0BAA0B;AAC/B,WAAK,2BAA2B;AAChC,WAAK,mBAAmB;AACxB,UAAI,KAAK,aAAa;AACpB,aAAK,MAAM;AAAA,MACb;AACA,WAAK,uBAAuB,OAAK;AAC/B,YAAI,KAAK,aAAa,KAAK,UAAU,SAAS,EAAE,MAAM,GAAG;AACvD,eAAK,YAAY;AAAA,QACnB;AAAA,MACF;AACA,WAAK,sBAAsB,KAAK,eAAe,gBAAgB,UAAU,KAAK,oBAAoB;AAClG,WAAK,OAAO,KAAK,IAAI;AAAA,IACvB;AACA,SAAK,+BAA+B;AAAA,EACtC;AAAA,EACA,eAAe,OAAO;AACpB,YAAQ,MAAM,SAAS;AAAA,MACrB,KAAK;AACH,YAAI,KAAK,iBAAiB;AACxB,eAAK,gBAAgB;AACrB,eAAK,kBAAkB;AAAA,QACzB;AACA,YAAI,KAAK,qBAAqB;AAC5B,eAAK,oBAAoB,YAAY;AAAA,QACvC;AACA;AAAA,MACF,KAAK;AACH,YAAI,KAAK,YAAY;AACnB,sBAAY,MAAM,KAAK,SAAS;AAAA,QAClC;AACA,YAAI,KAAK,qBAAqB;AAC5B,eAAK,oBAAoB,YAAY;AAAA,QACvC;AACA,aAAK,mBAAmB;AACxB,aAAK,OAAO,KAAK,CAAC,CAAC;AACnB,aAAK,SAAS;AACd;AAAA,IACJ;AACA,SAAK,+BAA+B;AAAA,EACtC;AAAA,EACA,QAAQ;AACN,QAAI,YAAY,WAAW,KAAK,WAAW,aAAa;AACxD,QAAI,WAAW;AACb,WAAK,KAAK,kBAAkB,MAAM;AAChC,mBAAW,MAAM,UAAU,MAAM,GAAG,CAAC;AAAA,MACvC,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AACL,SAAK,iBAAiB;AACtB,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA,EACA,aAAa,OAAO;AAClB,SAAK,KAAK;AACV,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,gBAAgB,OAAO;AACrB,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,iBAAiB;AACf,QAAI,KAAK,kBAAkB,CAAC,cAAc,GAAG;AAC3C,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EACA,6BAA6B;AAC3B,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,UAAI,CAAC,KAAK,wBAAwB;AAChC,cAAM,SAAS,KAAK,SAAS;AAC7B,aAAK,yBAAyB,KAAK,SAAS,OAAO,QAAQ,UAAU,KAAK,eAAe,KAAK,IAAI,CAAC;AAAA,MACrG;AAAA,IACF;AAAA,EACF;AAAA,EACA,+BAA+B;AAC7B,QAAI,KAAK,wBAAwB;AAC/B,WAAK,uBAAuB;AAC5B,WAAK,yBAAyB;AAAA,IAChC;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,UAAI,CAAC,KAAK,eAAe;AACvB,aAAK,gBAAgB,IAAI,8BAA8B,KAAK,QAAQ,MAAM;AACxE,cAAI,KAAK,gBAAgB;AACvB,iBAAK,KAAK;AAAA,UACZ;AAAA,QACF,CAAC;AAAA,MACH;AACA,WAAK,cAAc,mBAAmB;AAAA,IACxC;AAAA,EACF;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,qBAAqB;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,GAAG,WAAW;AACtB,WAAK,SAAS;AAAA,IAChB;AACA,SAAK,4BAA4B;AACjC,SAAK,6BAA6B;AAClC,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,QAAQ;AAC3B,WAAK,gBAAgB;AAAA,IACvB;AACA,QAAI,KAAK,aAAa,KAAK,YAAY;AACrC,kBAAY,MAAM,KAAK,SAAS;AAAA,IAClC;AACA,QAAI,CAAC,KAAK,GAAG,WAAW;AACtB,WAAK,SAAS;AAAA,IAChB;AACA,SAAK,kBAAkB;AACvB,QAAI,KAAK,WAAW;AAClB,WAAK,cAAc;AACnB,WAAK,mBAAmB;AAAA,IAC1B;AACA,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,YAAY;AAAA,IACvC;AACA,UAAM,YAAY;AAAA,EACpB;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,gBAAgB,mBAAmB;AACjD,cAAQ,yBAAyB,uBAA0B,sBAAsB,QAAO,IAAI,qBAAqB,QAAO;AAAA,IAC1H;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,IACzB,gBAAgB,SAAS,uBAAuB,IAAI,KAAK,UAAU;AACjE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,kBAAkB,GAAG;AACtE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,cAAc,SAAS,qBAAqB,IAAI,KAAK;AACnD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,kBAAkB,SAAS,0CAA0C,QAAQ;AACzF,iBAAO,IAAI,gBAAgB,MAAM;AAAA,QACnC,GAAG,OAAU,iBAAiB;AAAA,MAChC;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,aAAa,CAAC,GAAG,eAAe,eAAe,gBAAgB;AAAA,MAC/D,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,gBAAgB;AAAA,MAChB,YAAY,CAAC,GAAG,cAAc,cAAc,eAAe;AAAA,MAC3D,aAAa,CAAC,GAAG,eAAe,eAAe,gBAAgB;AAAA,MAC/D,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,IACzB;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,YAAY,CAAC,GAAM,0BAA6B,0BAA0B;AAAA,IAC5G,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,UAAU,GAAG,WAAW,WAAW,SAAS,SAAS,GAAG,MAAM,GAAG,CAAC,QAAQ,UAAU,GAAG,SAAS,WAAW,SAAS,GAAG,CAAC,GAAG,qBAAqB,GAAG,SAAS,WAAW,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,CAAC;AAAA,IAC9O,UAAU,SAAS,iBAAiB,IAAI,KAAK;AAC3C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,WAAW,GAAG,wBAAwB,GAAG,IAAI,OAAO,CAAC;AAAA,MAC1D;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,QAAQ,IAAI,MAAM;AAAA,MAClC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAiB,SAAY,MAAS,kBAAqB,SAAS,YAAY;AAAA,IAC/F,eAAe;AAAA,IACf,MAAM;AAAA,MACJ,WAAW,CAAC,QAAQ,aAAa,CAAC,MAAM,QAAQ,MAAM;AAAA,QACpD,WAAW;AAAA,QACX,SAAS;AAAA,MACX,CAAC,CAAC,GAAG,MAAM,SAAS,MAAM;AAAA,QACxB,SAAS;AAAA,MACX,CAAC,CAAC,GAAG,MAAM,QAAQ,MAAM;AAAA,QACvB,WAAW;AAAA,QACX,SAAS;AAAA,MACX,CAAC,CAAC,GAAG,WAAW,gBAAgB,QAAQ,0BAA0B,CAAC,GAAG,WAAW,iBAAiB,QAAQ,0BAA0B,CAAC,CAAC,CAAC,CAAC;AAAA,IAC1I;AAAA,IACA,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,YAAY;AAAA,MACpC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAwBV,YAAY,CAAC,QAAQ,aAAa,CAAC,MAAM,QAAQ,MAAM;AAAA,QACrD,WAAW;AAAA,QACX,SAAS;AAAA,MACX,CAAC,CAAC,GAAG,MAAM,SAAS,MAAM;AAAA,QACxB,SAAS;AAAA,MACX,CAAC,CAAC,GAAG,MAAM,QAAQ,MAAM;AAAA,QACvB,WAAW;AAAA,QACX,SAAS;AAAA,MACX,CAAC,CAAC,GAAG,WAAW,gBAAgB,QAAQ,0BAA0B,CAAC,GAAG,WAAW,iBAAiB,QAAQ,0BAA0B,CAAC,CAAC,CAAC,CAAC;AAAA,MACxI,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,YAAY;AAAA,IAC1B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B,CAAC,QAAQ,CAAC;AAAA,IAC9C,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,SAAS,YAAY;AAAA,IAC/B,SAAS,CAAC,SAAS,YAAY;AAAA,EACjC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,SAAS,cAAc,YAAY;AAAA,EAC/C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,SAAS,YAAY;AAAA,MAC/B,SAAS,CAAC,SAAS,YAAY;AAAA,IACjC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}
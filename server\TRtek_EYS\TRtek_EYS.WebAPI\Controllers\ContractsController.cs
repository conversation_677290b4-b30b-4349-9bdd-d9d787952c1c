﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using TRtek_EYS.Application.Features.Contracts.Commands.Add;
using TRtek_EYS.Application.Features.Contracts.Commands.Delete;
using TRtek_EYS.Application.Features.Contracts.Commands.Update;
using TRtek_EYS.Application.Features.Contracts.Queries.GetAll;
using TRtek_EYS.Application.Features.Contracts.Queries.GetById;

namespace TRtek_EYS.WebAPI.Controllers;

[Route("api/[controller]")]
[ApiController]
public class ContractsController : ApiController
{
    public ContractsController(IMediator mediator) : base(mediator) { }

    [HttpGet]
    public async Task<IActionResult> GetAll()
    {
        var result = await Mediator.Send(new GetAllContractsQuery());
        return Ok(result); // List<ContractGetAllDto>
    }

    [HttpGet("{id:guid}")]
    public async Task<IActionResult> GetById(Guid id)
    {
        var result = await Mediator.Send(new GetContractByIdQuery(id));
        return result is null ? NotFound() : Ok(result); // ContractGetByIdDto
    }

    [HttpPost]
    public async Task<IActionResult> Add([FromBody] AddContractCommand command)
    {
        var id = await Mediator.Send(command);
        return CreatedAtAction(nameof(GetById), new { id }, null);
    }

    [HttpPut("{id:guid}")]
    public async Task<IActionResult> Update(Guid id, [FromBody] UpdateContractCommand command)
    {
        if (id != command.Id)
            return BadRequest("URL'deki ID uyuşmuyor.");

        await Mediator.Send(command);
        return NoContent();
    }

    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> Delete(Guid id)
    {
        await Mediator.Send(new DeleteContractCommand { Id = id });
        return NoContent();
    }
}

﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Contracts.Queries.GetAll
{
    public class GetAllContractsQueryHandler : IRequestHandler<GetAllContractsQuery, List<ContractGetAllDto>>
    {
        private readonly IApplicationDbContext _context;

        public GetAllContractsQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<ContractGetAllDto>> Handle(GetAllContractsQuery request, CancellationToken cancellationToken)
        {
            return await _context.Contracts
                .AsNoTracking()
                .Select(c => new ContractGetAllDto
                {
                    Id = c.Id,
                    Name = c.Name,
                    Number = c.Number,
                    Status = c.Status.ToString(),
                    StartDate = c.StartDate,
                    EndDate = c.EndDate
                })
                .ToListAsync(cancellationToken);
        }
    }
}

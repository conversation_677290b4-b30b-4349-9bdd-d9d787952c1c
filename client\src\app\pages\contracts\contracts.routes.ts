import { Routes } from '@angular/router';

export default [
    {
        path: '',
        loadComponent: () => import('./components/contracts-list/contracts-list.component').then(m => m.ContractsListComponent)
    },
    {
        path: 'new',
        loadComponent: () => import('./components/contract-form/contract-form.component').then(m => m.ContractFormComponent)
    },
    {
        path: ':id',
        loadComponent: () => import('./components/contract-detail/contract-detail.component').then(m => m.ContractDetailComponent)
    },
    {
        path: ':id/edit',
        loadComponent: () => import('./components/contract-form/contract-form.component').then(m => m.ContractFormComponent)
    }
] as Routes;

import { Injectable } from '@angular/core';
import { BaseModel } from '../models/base.model';
import { BaseService } from './base.service';

export interface User extends BaseModel {
    userName: string;
    email: string;
    fullName?: string;
    isActive: boolean;
}

@Injectable({
    providedIn: 'root'
})
export class UsersService extends BaseService<User> {
    protected override endpoint = 'users';
}

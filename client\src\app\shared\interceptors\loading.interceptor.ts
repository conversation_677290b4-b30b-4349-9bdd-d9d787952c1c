import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent } from '@angular/common/http';
import { Observable } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { LoadingService } from '../services/loading.service';

@Injectable()
export class LoadingInterceptor implements HttpInterceptor {
    constructor(private loadingService: LoadingService) {}

    intercept(req: HttpRequest<any>, next: <PERSON>ttpHandler): Observable<HttpEvent<any>> {
        // Skip loading for certain requests
        if (req.url.includes('/health-check') || req.headers.has('X-Skip-Loading')) {
            return next.handle(req);
        }

        this.loadingService.show();

        return next.handle(req).pipe(
            finalize(() => {
                this.loadingService.hide();
            })
        );
    }
}

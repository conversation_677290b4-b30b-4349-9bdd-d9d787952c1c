﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;
using TRtek_EYS.Domain.Enums;

namespace TRtek_EYS.Application.Features.Platforms.Commands.Update
{
    public class UpdatePlatformCommandHandler : IRequestHandler<UpdatePlatformCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public UpdatePlatformCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(UpdatePlatformCommand request, CancellationToken cancellationToken)
        {
            var platform = await _context.Platforms
                .FirstOrDefaultAsync(p => p.Id == request.Id, cancellationToken);

            if (platform is null)
                throw new Exception("Platform bulunamadı.");

            platform.Type = (PlatformType)request.Type;
            platform.ProjectId = request.ProjectId;

            await _context.SaveChangesAsync(cancellationToken);

            return platform.Id;
        }
    }
}

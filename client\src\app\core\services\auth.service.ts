import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { Permission } from '../models/permission.model';
import { Role } from '../models/role.model';
import { AuthUser, User } from '../models/user.model';
import { SYSTEM_ROLES } from '../models/role.model';

@Injectable({
    providedIn: 'root'
})
export class AuthService {
    private currentUserSubject: BehaviorSubject<AuthUser | null>;
    public currentUser$: Observable<AuthUser | null>;

    constructor() {
        this.currentUserSubject = new BehaviorSubject<AuthUser | null>(this.getUserFromStorage());
        this.currentUser$ = this.currentUserSubject.asObservable();

        // Development için otomatik login
        if (!this.getCurrentUser()) {
            this.autoLogin();
        }
    }

    private getUserFromStorage(): AuthUser | null {
        const stored = localStorage.getItem('currentUser');
        return stored ? JSON.parse(stored) : null;
    }

    getCurrentUser(): AuthUser | null {
        return this.currentUserSubject.value;
    }

    hasPermission(permission: Permission): boolean {
        const currentUser = this.getCurrentUser();
        if (!currentUser) {
            console.log('No current user');
            return false;
        }

        console.log('Checking permission:', permission);
        console.log('User roles:', currentUser.user.roles);

        const hasPermission = currentUser.user.roles.some(role => {
            console.log('Role:', role.name, 'Permissions:', role.permissions);
            console.log('Permission type:', typeof permission, 'Permission value:', permission);
            console.log('Permissions array:', role.permissions);
            console.log('Includes check:', role.permissions.includes(permission));
            return role.permissions.includes(permission);
        });

        console.log('Has permission:', hasPermission);
        return hasPermission;
    }

    hasAnyPermission(permissions: Permission[]): boolean {
        return permissions.some(permission => this.hasPermission(permission));
    }

    hasAllPermissions(permissions: Permission[]): boolean {
        return permissions.every(permission => this.hasPermission(permission));
    }

    getUserRoles(): Role[] {
        const currentUser = this.getCurrentUser();
        return currentUser?.user.roles || [];
    }

    // Bu metodlar backend entegrasyonu olduğunda güncellenecek
    login(email: string, password: string): Observable<AuthUser> {
        // Test için mock veri
        let mockUser: AuthUser;

        if (email === '<EMAIL>' && password === 'su') {
            mockUser = {
                user: new User({
                    id: '1',
                    email: email,
                    firstName: 'Super',
                    lastName: 'Admin',
                    roles: [SYSTEM_ROLES['SUPER_ADMIN']],
                    isActive: true,
                    createdAt: new Date(),
                    updatedAt: new Date()
                }),
                accessToken: 'mock-access-token',
                refreshToken: 'mock-refresh-token',
                tokenExpires: new Date(Date.now() + 3600000) // 1 saat
            };
        } else {
            mockUser = {
                user: new User({
                    id: '2',
                    email: email,
                    firstName: 'System',
                    lastName: 'Admin',
                    roles: [SYSTEM_ROLES['SYSTEM_ADMIN']],
                    isActive: true,
                    createdAt: new Date(),
                    updatedAt: new Date()
                }),
                accessToken: 'mock-access-token',
                refreshToken: 'mock-refresh-token',
                tokenExpires: new Date(Date.now() + 3600000) // 1 saat
            };
        }
        
        return of(mockUser)
            .pipe(
                map(response => {
                    localStorage.setItem('currentUser', JSON.stringify(response));
                    this.currentUserSubject.next(response);
                    return response;
                })
            );
    }

    logout(): void {
        localStorage.removeItem('currentUser');
        this.currentUserSubject.next(null);
    }

    refreshToken(): Observable<AuthUser> {
        // Test için mock veri
        const mockUser = this.getCurrentUser();
        if (!mockUser) {
            return of(null as any);
        }
        
        mockUser.tokenExpires = new Date(Date.now() + 3600000); // 1 saat
        return of(mockUser)
            .pipe(
                map(response => {
                    localStorage.setItem('currentUser', JSON.stringify(response));
                    this.currentUserSubject.next(response);
                    return response;
                })
            );
    }

    logout(): Observable<boolean> {
        localStorage.removeItem('currentUser');
        this.currentUserSubject.next(null);
        return of(true);
    }

    private autoLogin() {
        // Development için otomatik super admin login
        const mockUser: AuthUser = {
            user: new User({
                id: '1',
                email: '<EMAIL>',
                firstName: 'Super',
                lastName: 'Admin',
                roles: [SYSTEM_ROLES['SUPER_ADMIN']],
                isActive: true,
                createdAt: new Date(),
                updatedAt: new Date()
            }),
            accessToken: 'mock-access-token',
            refreshToken: 'mock-refresh-token',
            tokenExpires: new Date(Date.now() + 3600000) // 1 saat
        };

        localStorage.setItem('currentUser', JSON.stringify(mockUser));
        this.currentUserSubject.next(mockUser);
        console.log('Auto-logged in as Super Admin for development');
    }
}

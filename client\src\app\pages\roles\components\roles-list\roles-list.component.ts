import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Table, TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { InputIconModule } from 'primeng/inputicon';
import { IconFieldModule } from 'primeng/iconfield';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToastModule } from 'primeng/toast';
import { ConfirmationService, MessageService } from 'primeng/api';
import { Role } from '../../models/role.model';
import { RoleService } from '../../services/role.service';

@Component({
    selector: 'app-roles-list',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        TableModule,
        ButtonModule,
        InputTextModule,
        InputIconModule,
        IconFieldModule,
        TagModule,
        TooltipModule,
        ConfirmDialogModule,
        ToastModule
    ],
    providers: [ConfirmationService, MessageService],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <h5>Rol Yönetimi</h5>
                        <p-button 
                            label="Yeni Rol" 
                            icon="pi pi-plus" 
                            (onClick)="createRole()"
                            severity="primary"
                            size="small">
                        </p-button>
                    </div>

                    <p-table 
                        #dt 
                        [value]="roles" 
                        [rows]="10" 
                        [paginator]="true"
                        [globalFilterFields]="['roleCode', 'name', 'description']"
                        [tableStyle]="{ 'min-width': '75rem' }"
                        [rowHover]="true"
                        dataKey="id"
                        currentPageReportTemplate="{first} - {last} / {totalRecords} rol"
                        [showCurrentPageReport]="true">
                        
                        <ng-template pTemplate="caption">
                            <div class="flex align-items-center justify-content-between">
                                <h5 class="m-0">Roller</h5>
                                <p-iconfield iconPosition="left">
                                    <p-inputicon>
                                        <i class="pi pi-search"></i>
                                    </p-inputicon>
                                    <input 
                                        pInputText 
                                        type="text" 
                                        (input)="dt.filterGlobal($any($event.target).value, 'contains')" 
                                        placeholder="Rol ara..." />
                                </p-iconfield>
                            </div>
                        </ng-template>

                        <ng-template pTemplate="header">
                            <tr>
                                <th pSortableColumn="roleCode">
                                    Rol Kodu <p-sortIcon field="roleCode"></p-sortIcon>
                                </th>
                                <th pSortableColumn="name">
                                    Rol Adı <p-sortIcon field="name"></p-sortIcon>
                                </th>
                                <th pSortableColumn="description">
                                    Açıklama <p-sortIcon field="description"></p-sortIcon>
                                </th>
                                <th>Yetki Sayısı</th>
                                <th pSortableColumn="userCount">
                                    Kullanıcı Sayısı <p-sortIcon field="userCount"></p-sortIcon>
                                </th>
                                <th>Rol Tipi</th>
                                <th pSortableColumn="isActive">
                                    Durum <p-sortIcon field="isActive"></p-sortIcon>
                                </th>
                                <th pSortableColumn="updatedAt">
                                    Son Güncelleme <p-sortIcon field="updatedAt"></p-sortIcon>
                                </th>
                                <th>İşlemler</th>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="body" let-role>
                            <tr>
                                <td>
                                    <span class="font-medium">{{ role.roleCode }}</span>
                                </td>
                                <td>
                                    <div>
                                        <span class="font-medium">{{ role.name }}</span>
                                        <i class="pi pi-shield text-orange-500 ml-2" 
                                           *ngIf="role.isSystemRole"
                                           pTooltip="Sistem Rolü"></i>
                                    </div>
                                </td>
                                <td>
                                    <span>{{ role.description | slice:0:60 }}{{ role.description.length > 60 ? '...' : '' }}</span>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <span class="font-medium text-primary">{{ role.permissions.length }}</span>
                                        <span class="text-500"> yetki</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <span class="font-medium" 
                                              [class]="role.userCount > 0 ? 'text-green-600' : 'text-500'">
                                            {{ role.userCount }}
                                        </span>
                                        <span class="text-500"> kullanıcı</span>
                                    </div>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="role.isSystemRole ? 'Sistem' : 'Özel'" 
                                        [severity]="role.isSystemRole ? 'warning' : 'info'">
                                    </p-tag>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="role.isActive ? 'Aktif' : 'Pasif'" 
                                        [severity]="role.isActive ? 'success' : 'secondary'">
                                    </p-tag>
                                </td>
                                <td>
                                    <span>{{ role.updatedAt | date:'dd/MM/yyyy' }}</span>
                                </td>
                                <td>
                                    <div class="flex gap-2">
                                        <p-button 
                                            icon="pi pi-eye" 
                                            class="p-button-rounded p-button-text p-button-info"
                                            pTooltip="Detay"
                                            (onClick)="viewRole(role.id)">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-pencil" 
                                            class="p-button-rounded p-button-text p-button-warning"
                                            pTooltip="Düzenle"
                                            (onClick)="editRole(role.id)"
                                            [disabled]="role.isSystemRole">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-copy" 
                                            class="p-button-rounded p-button-text p-button-secondary"
                                            pTooltip="Kopyala"
                                            (onClick)="cloneRole(role)">
                                        </p-button>
                                        <p-button 
                                            [icon]="role.isActive ? 'pi pi-pause' : 'pi pi-play'" 
                                            class="p-button-rounded p-button-text"
                                            [class.p-button-secondary]="role.isActive"
                                            [class.p-button-success]="!role.isActive"
                                            [pTooltip]="role.isActive ? 'Pasifleştir' : 'Aktifleştir'"
                                            (onClick)="toggleRoleStatus(role)"
                                            [disabled]="role.isSystemRole">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-trash" 
                                            class="p-button-rounded p-button-text p-button-danger"
                                            pTooltip="Sil"
                                            (onClick)="deleteRole(role)"
                                            [disabled]="role.isSystemRole || role.userCount > 0">
                                        </p-button>
                                    </div>
                                </td>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="emptymessage">
                            <tr>
                                <td colspan="9" class="text-center py-4">
                                    <i class="pi pi-info-circle text-4xl text-500 mb-3"></i>
                                    <div class="text-500 font-medium">Henüz rol bulunmuyor</div>
                                    <div class="text-500">Yeni rol eklemek için "Yeni Rol" butonunu kullanın</div>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                </div>
            </div>
        </div>

        <p-confirmDialog></p-confirmDialog>
        <p-toast></p-toast>
    `
})
export class RolesListComponent implements OnInit {
    @ViewChild('dt') table!: Table;
    
    roles: Role[] = [];

    constructor(
        private roleService: RoleService,
        private router: Router,
        private confirmationService: ConfirmationService,
        private messageService: MessageService
    ) {}

    ngOnInit() {
        this.loadRoles();
    }

    loadRoles() {
        this.roleService.getRoles().subscribe(roles => {
            this.roles = roles;
        });
    }

    createRole() {
        this.router.navigate(['/pages/roles/new']);
    }

    viewRole(id: string) {
        this.router.navigate(['/pages/roles', id]);
    }

    editRole(id: string) {
        this.router.navigate(['/pages/roles', id, 'edit']);
    }

    cloneRole(role: Role) {
        this.confirmationService.confirm({
            message: `"${role.name}" rolünü kopyalamak istediğinizden emin misiniz?`,
            header: 'Rol Kopyalama Onayı',
            icon: 'pi pi-copy',
            acceptLabel: 'Evet',
            rejectLabel: 'Hayır',
            accept: () => {
                const newName = `${role.name} (Kopya)`;
                this.roleService.cloneRole(role.id, newName).subscribe(() => {
                    this.loadRoles();
                    this.messageService.add({
                        severity: 'success',
                        summary: 'Başarılı',
                        detail: 'Rol başarıyla kopyalandı'
                    });
                });
            }
        });
    }

    toggleRoleStatus(role: Role) {
        const action = role.isActive ? 'pasifleştirmek' : 'aktifleştirmek';
        this.confirmationService.confirm({
            message: `"${role.name}" rolünü ${action} istediğinizden emin misiniz?`,
            header: 'Rol Durumu Değiştirme Onayı',
            icon: role.isActive ? 'pi pi-pause' : 'pi pi-play',
            acceptLabel: 'Evet',
            rejectLabel: 'Hayır',
            accept: () => {
                this.roleService.toggleRoleStatus(role.id).subscribe(() => {
                    this.loadRoles();
                    this.messageService.add({
                        severity: 'info',
                        summary: 'Başarılı',
                        detail: `Rol ${role.isActive ? 'pasifleştirildi' : 'aktifleştirildi'}`
                    });
                });
            }
        });
    }

    deleteRole(role: Role) {
        this.confirmationService.confirm({
            message: `"${role.name}" rolünü silmek istediğinizden emin misiniz?`,
            header: 'Rol Silme Onayı',
            icon: 'pi pi-exclamation-triangle',
            acceptLabel: 'Evet',
            rejectLabel: 'Hayır',
            accept: () => {
                this.roleService.deleteRole(role.id).subscribe(success => {
                    if (success) {
                        this.roles = this.roles.filter(r => r.id !== role.id);
                        this.messageService.add({
                            severity: 'success',
                            summary: 'Başarılı',
                            detail: 'Rol başarıyla silindi'
                        });
                    } else {
                        this.messageService.add({
                            severity: 'error',
                            summary: 'Hata',
                            detail: 'Rol silinemedi. Sistem rolü veya kullanıcısı olan roller silinemez.'
                        });
                    }
                });
            }
        });
    }
}

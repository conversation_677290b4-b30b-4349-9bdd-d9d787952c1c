import { Component } from '@angular/core';
import { ProjectStatsWidget } from './components/project-stats-widget';
import { RecentProjectsWidget } from './components/recent-projects-widget';
import { TechnologyDistributionWidget } from './components/technology-distribution-widget';
import { TeamOverviewWidget } from './components/team-overview-widget';
import { DocumentActivityWidget } from './components/document-activity-widget';
import { PlatformUsageWidget } from './components/platform-usage-widget';
import { CustomerOverviewWidget } from './components/customer-overview-widget';
import { MonthlyPerformanceWidget } from './components/monthly-performance-widget';
import { TeamSkillsWidget } from './components/team-skills-widget';
import { ProjectStatusWidget } from './components/project-status-widget';

@Component({
    selector: 'app-dashboard',
    imports: [
        ProjectStatsWidget,
        RecentProjectsWidget,
        TechnologyDistributionWidget,
        TeamOverviewWidget,
        DocumentActivityWidget,
        PlatformUsageWidget,
        CustomerOverviewWidget,
        MonthlyPerformanceWidget,
        TeamSkillsWidget,
        ProjectStatusWidget
    ],
    template: `
        <div class="grid grid-cols-12 gap-8">
            <!-- İstatistik Kartları -->
            <app-project-stats-widget class="contents" />

            <!-- Ana İçerik - Sol Panel -->
            <div class="col-span-12 xl:col-span-8 space-y-8">
                <!-- Performans Grafikleri -->
                <app-monthly-performance-widget />

                <!-- Proje ve Müşteri Tabloları -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <app-recent-projects-widget />
                    <app-customer-overview-widget />
                </div>

                <!-- Alt Kısım: Teknoloji + Takım Genel Bakış -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <app-technology-distribution-widget />
                    <app-team-overview-widget />
                </div>
            </div>

            <!-- Yan Panel - Sağ Panel -->
            <div class="col-span-12 xl:col-span-4 space-y-8">
                <!-- Platform Kullanımı -->
                <app-platform-usage-widget />

                <!-- Proje Durumları -->
                <app-project-status-widget />

                <!-- Takım Yetenekleri -->
                <app-team-skills-widget />

                <!-- Doküman Aktiviteleri -->
                <app-document-activity-widget />
            </div>
        </div>
    `
})
export class Dashboard {}

/*
// Eğer Tailwind CSS sorun çıkarırsa bu CSS grid yöntemi kullanılacak
@Component({
    selector: 'app-dashboard',
    imports: [...],
    template: `
        <div class="dashboard-grid">
            <div class="stats-container">
                <app-project-stats-widget />
            </div>
            <div class="main-content">
                <app-recent-projects-widget />
                <app-customer-overview-widget />
                <app-technology-distribution-widget />
            </div>
            <div class="side-panel">
                <app-platform-usage-widget />
                <app-document-activity-widget />
                <app-team-overview-widget />
            </div>
        </div>
    `,
    styles: [`
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            gap: 2rem;
        }

        .stats-container {
            grid-column: 1 / -1;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .main-content {
            grid-column: span 8;
        }

        .side-panel {
            grid-column: span 4;
        }

        @media (max-width: 1200px) {
            .main-content,
            .side-panel {
                grid-column: 1 / -1;
            }
        }
    `]
})
*/

﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TRtek_EYS.Domain.Entities;

namespace TRtek_EYS.Infrastructure.Configurations
{
    public class DocumentConfiguration : IEntityTypeConfiguration<Document>
    {
        public void Configure(EntityTypeBuilder<Document> builder)
        {
            builder.ToTable("Documents");

            builder.HasKey(d => d.Id);

            builder.Property(d => d.Type)
                   .IsRequired(); // Enum: DocumentType

            builder.Property(d => d.FileName)
                   .IsRequired()
                   .HasMaxLength(200);

            builder.Property(d => d.FilePath)
                   .IsRequired()
                   .HasMaxLength(500);

            builder.Property(d => d.FileSize)
                   .IsRequired();

            builder.Property(d => d.VersionNumber)
                   .IsRequired();

            builder.Property(d => d.CreatedAt)
                   .IsRequired();

            // <PERSON><PERSON><PERSON><PERSON>: her belge bir projeye aittir
            builder.HasOne(d => d.Project)
                   .WithMany()
                   .HasForeignKey(d => d.ProjectId)
                   .OnDelete(DeleteBehavior.Cascade);
        }
    }
}

{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-galleria.mjs"], "sourcesContent": ["import { trigger, transition, style, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, inject, PLATFORM_ID, numberAttribute, booleanAttribute, ContentChildren, ContentChild, ViewChild, Output, Input, Inject, ViewEncapsulation, ChangeDetectionStrategy, Component, HostListener, NgModule } from '@angular/core';\nimport { addClass, focus, findSingle, blockBodyScroll, unblockBodyScroll, removeClass, uuid, setAttribute, find, getAttribute } from '@primeuix/utils';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { FocusTrap } from 'primeng/focustrap';\nimport { TimesIcon, ChevronRightIcon, ChevronLeftIcon, ChevronUpIcon, ChevronDownIcon, WindowMaximizeIcon, WindowMinimizeIcon } from 'primeng/icons';\nimport { Ripple } from 'primeng/ripple';\nimport { ZIndexUtils } from 'primeng/utils';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"header\"];\nconst _c1 = [\"footer\"];\nconst _c2 = [\"indicator\"];\nconst _c3 = [\"caption\"];\nconst _c4 = [\"closeicon\"];\nconst _c5 = [\"previousthumbnailicon\"];\nconst _c6 = [\"nextthumbnailicon\"];\nconst _c7 = [\"itempreviousicon\"];\nconst _c8 = [\"itemnexticon\"];\nconst _c9 = [\"item\"];\nconst _c10 = [\"thumbnail\"];\nconst _c11 = [\"mask\"];\nconst _c12 = [\"container\"];\nconst _c13 = () => ({\n  \"p-galleria-mask p-overlay-mask p-overlay-mask-enter\": true\n});\nconst _c14 = (a0, a1) => ({\n  showTransitionParams: a0,\n  hideTransitionParams: a1\n});\nconst _c15 = a0 => ({\n  value: \"visible\",\n  params: a0\n});\nfunction Galleria_div_0_div_2_p_galleriaContent_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-galleriaContent\", 7);\n    i0.ɵɵlistener(\"@animation.start\", function Galleria_div_0_div_2_p_galleriaContent_2_Template_p_galleriaContent_animation_animation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onAnimationStart($event));\n    })(\"@animation.done\", function Galleria_div_0_div_2_p_galleriaContent_2_Template_p_galleriaContent_animation_animation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onAnimationEnd($event));\n    })(\"maskHide\", function Galleria_div_0_div_2_p_galleriaContent_2_Template_p_galleriaContent_maskHide_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onMaskHide());\n    })(\"activeItemChange\", function Galleria_div_0_div_2_p_galleriaContent_2_Template_p_galleriaContent_activeItemChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onActiveItemChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"@animation\", i0.ɵɵpureFunction1(9, _c15, i0.ɵɵpureFunction2(6, _c14, ctx_r1.showTransitionOptions, ctx_r1.hideTransitionOptions)))(\"value\", ctx_r1.value)(\"activeIndex\", ctx_r1.activeIndex)(\"numVisible\", ctx_r1.numVisibleLimit || ctx_r1.numVisible)(\"ngStyle\", ctx_r1.containerStyle)(\"fullScreen\", ctx_r1.fullScreen);\n  }\n}\nfunction Galleria_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5, 2);\n    i0.ɵɵtemplate(2, Galleria_div_0_div_2_p_galleriaContent_2_Template, 1, 11, \"p-galleriaContent\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r1.maskClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(6, _c13));\n    i0.ɵɵattribute(\"role\", ctx_r1.fullScreen ? \"dialog\" : \"region\")(\"aria-modal\", ctx_r1.fullScreen ? \"true\" : undefined);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.visible);\n  }\n}\nfunction Galleria_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", null, 1);\n    i0.ɵɵtemplate(2, Galleria_div_0_div_2_Template, 3, 7, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.maskVisible);\n  }\n}\nfunction Galleria_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-galleriaContent\", 8);\n    i0.ɵɵlistener(\"activeItemChange\", function Galleria_ng_template_1_Template_p_galleriaContent_activeItemChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onActiveItemChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r1.value)(\"activeIndex\", ctx_r1.activeIndex)(\"numVisible\", ctx_r1.numVisibleLimit || ctx_r1.numVisible);\n  }\n}\nconst _c16 = [\"closeButton\"];\nconst _c17 = (a0, a1, a2) => ({\n  \"p-galleria p-component\": true,\n  \"p-galleria-fullscreen\": a0,\n  \"p-galleria-inset-indicators\": a1,\n  \"p-galleria-hover-navigators\": a2\n});\nconst _c18 = () => ({});\nfunction GalleriaContent_div_0_button_1_TimesIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"TimesIcon\", 11);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-galleria-close-icon\");\n  }\n}\nfunction GalleriaContent_div_0_button_1_2_ng_template_0_Template(rf, ctx) {}\nfunction GalleriaContent_div_0_button_1_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, GalleriaContent_div_0_button_1_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction GalleriaContent_div_0_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function GalleriaContent_div_0_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.maskHide.emit());\n    });\n    i0.ɵɵtemplate(1, GalleriaContent_div_0_button_1_TimesIcon_1_Template, 1, 1, \"TimesIcon\", 9)(2, GalleriaContent_div_0_button_1_2_Template, 1, 0, null, 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"aria-label\", ctx_r2.closeAriaLabel())(\"data-pc-section\", \"closebutton\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.galleria.closeIconTemplate && !ctx_r2.galleria._closeIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.galleria.closeIconTemplate || ctx_r2.galleria._closeIconTemplate);\n  }\n}\nfunction GalleriaContent_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"p-galleriaItemSlot\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"templates\", ctx_r2.galleria.templates);\n  }\n}\nfunction GalleriaContent_div_0_p_galleriaThumbnails_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-galleriaThumbnails\", 14);\n    i0.ɵɵlistener(\"onActiveIndexChange\", function GalleriaContent_div_0_p_galleriaThumbnails_5_Template_p_galleriaThumbnails_onActiveIndexChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onActiveIndexChange($event));\n    })(\"stopSlideShow\", function GalleriaContent_div_0_p_galleriaThumbnails_5_Template_p_galleriaThumbnails_stopSlideShow_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.stopSlideShow());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"containerId\", ctx_r2.id)(\"value\", ctx_r2.value)(\"activeIndex\", ctx_r2.activeIndex)(\"templates\", ctx_r2.galleria.templates)(\"numVisible\", ctx_r2.numVisible)(\"responsiveOptions\", ctx_r2.galleria.responsiveOptions)(\"circular\", ctx_r2.galleria.circular)(\"isVertical\", ctx_r2.isVertical())(\"contentHeight\", ctx_r2.galleria.verticalThumbnailViewPortHeight)(\"showThumbnailNavigators\", ctx_r2.galleria.showThumbnailNavigators)(\"slideShowActive\", ctx_r2.slideShowActive);\n  }\n}\nfunction GalleriaContent_div_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵelement(1, \"p-galleriaItemSlot\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"templates\", ctx_r2.galleria.templates);\n  }\n}\nfunction GalleriaContent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, GalleriaContent_div_0_button_1_Template, 3, 4, \"button\", 2)(2, GalleriaContent_div_0_div_2_Template, 2, 1, \"div\", 3);\n    i0.ɵɵelementStart(3, \"div\", 4)(4, \"p-galleriaItem\", 5);\n    i0.ɵɵlistener(\"onActiveIndexChange\", function GalleriaContent_div_0_Template_p_galleriaItem_onActiveIndexChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onActiveIndexChange($event));\n    })(\"startSlideShow\", function GalleriaContent_div_0_Template_p_galleriaItem_startSlideShow_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.startSlideShow());\n    })(\"stopSlideShow\", function GalleriaContent_div_0_Template_p_galleriaItem_stopSlideShow_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.stopSlideShow());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, GalleriaContent_div_0_p_galleriaThumbnails_5_Template, 1, 11, \"p-galleriaThumbnails\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, GalleriaContent_div_0_div_6_Template, 2, 1, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r2.galleriaClass());\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(24, _c17, ctx_r2.galleria.fullScreen, ctx_r2.galleria.showIndicatorsOnItem, ctx_r2.galleria.showItemNavigatorsOnHover && !ctx_r2.galleria.fullScreen))(\"ngStyle\", !ctx_r2.galleria.fullScreen ? ctx_r2.galleria.containerStyle : i0.ɵɵpureFunction0(28, _c18))(\"pFocusTrapDisabled\", !ctx_r2.fullScreen);\n    i0.ɵɵattribute(\"id\", ctx_r2.id)(\"role\", \"region\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.galleria.fullScreen);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.galleria.templates && (ctx_r2.galleria.headerFacet || ctx_r2.galleria.headerTemplate));\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-live\", ctx_r2.galleria.autoPlay ? \"polite\" : \"off\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", ctx_r2.id)(\"value\", ctx_r2.value)(\"activeIndex\", ctx_r2.activeIndex)(\"circular\", ctx_r2.galleria.circular)(\"templates\", ctx_r2.galleria.templates)(\"showIndicators\", ctx_r2.galleria.showIndicators)(\"changeItemOnIndicatorHover\", ctx_r2.galleria.changeItemOnIndicatorHover)(\"indicatorFacet\", ctx_r2.galleria.indicatorFacet)(\"captionFacet\", ctx_r2.galleria.captionFacet)(\"showItemNavigators\", ctx_r2.galleria.showItemNavigators)(\"autoPlay\", ctx_r2.galleria.autoPlay)(\"slideShowActive\", ctx_r2.slideShowActive);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.galleria.showThumbnails);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.shouldRenderFooter());\n  }\n}\nfunction GalleriaItemSlot_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction GalleriaItemSlot_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, GalleriaItemSlot_ng_container_0_ng_container_1_Template, 1, 0, \"ng-container\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.contentTemplate)(\"ngTemplateOutletContext\", ctx_r0.context);\n  }\n}\nconst _c19 = a0 => ({\n  \"p-galleria-prev-button p-galleria-nav-button\": true,\n  \"p-disabled\": a0\n});\nconst _c20 = a0 => ({\n  \"p-galleria-next-button p-galleria-nav-button\": true,\n  \"p-disabled\": a0\n});\nconst _c21 = a0 => ({\n  \"p-galleria-indicator\": true,\n  \"p-galleria-indicator-active\": a0\n});\nfunction GalleriaItem_button_1_ChevronLeftIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronLeftIcon\", 9);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-galleria-prev-icon\");\n  }\n}\nfunction GalleriaItem_button_1_2_ng_template_0_Template(rf, ctx) {}\nfunction GalleriaItem_button_1_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, GalleriaItem_button_1_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction GalleriaItem_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function GalleriaItem_button_1_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navBackward($event));\n    })(\"focus\", function GalleriaItem_button_1_Template_button_focus_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onButtonFocus(\"left\"));\n    })(\"blur\", function GalleriaItem_button_1_Template_button_blur_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onButtonBlur(\"left\"));\n    });\n    i0.ɵɵtemplate(1, GalleriaItem_button_1_ChevronLeftIcon_1_Template, 1, 1, \"ChevronLeftIcon\", 7)(2, GalleriaItem_button_1_2_Template, 1, 0, null, 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c19, ctx_r1.isNavBackwardDisabled()))(\"disabled\", ctx_r1.isNavBackwardDisabled());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.galleria.itemPreviousIconTemplate && !ctx_r1.galleria._itemPreviousIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.galleria.itemPreviousIconTemplate || ctx_r1.galleria._itemPreviousIconTemplate);\n  }\n}\nfunction GalleriaItem_button_4_ChevronRightIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\", 9);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-galleria-next-icon\");\n  }\n}\nfunction GalleriaItem_button_4_2_ng_template_0_Template(rf, ctx) {}\nfunction GalleriaItem_button_4_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, GalleriaItem_button_4_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction GalleriaItem_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function GalleriaItem_button_4_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navForward($event));\n    })(\"focus\", function GalleriaItem_button_4_Template_button_focus_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onButtonFocus(\"right\"));\n    })(\"blur\", function GalleriaItem_button_4_Template_button_blur_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onButtonBlur(\"right\"));\n    });\n    i0.ɵɵtemplate(1, GalleriaItem_button_4_ChevronRightIcon_1_Template, 1, 1, \"ChevronRightIcon\", 7)(2, GalleriaItem_button_4_2_Template, 1, 0, null, 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c20, ctx_r1.isNavForwardDisabled()))(\"disabled\", ctx_r1.isNavForwardDisabled());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.galleria.itemNextIconTemplate && !ctx_r1.galleria._itemNextIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.galleria.itemNextIconTemplate || ctx_r1.galleria._itemNextIconTemplate);\n  }\n}\nfunction GalleriaItem_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵelement(1, \"p-galleriaItemSlot\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"item\", ctx_r1.activeItem)(\"templates\", ctx_r1.templates);\n  }\n}\nfunction GalleriaItem_ul_6_li_1_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"button\", 17);\n  }\n}\nfunction GalleriaItem_ul_6_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 14);\n    i0.ɵɵlistener(\"click\", function GalleriaItem_ul_6_li_1_Template_li_click_0_listener() {\n      const index_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onIndicatorClick(index_r5));\n    })(\"mouseenter\", function GalleriaItem_ul_6_li_1_Template_li_mouseenter_0_listener() {\n      const index_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onIndicatorMouseEnter(index_r5));\n    })(\"keydown\", function GalleriaItem_ul_6_li_1_Template_li_keydown_0_listener($event) {\n      const index_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onIndicatorKeyDown($event, index_r5));\n    });\n    i0.ɵɵtemplate(1, GalleriaItem_ul_6_li_1_button_1_Template, 1, 0, \"button\", 15);\n    i0.ɵɵelement(2, \"p-galleriaItemSlot\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const index_r5 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c21, ctx_r1.isIndicatorItemActive(index_r5)));\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.ariaPageLabel(index_r5 + 1))(\"aria-selected\", ctx_r1.activeIndex === index_r5)(\"aria-controls\", ctx_r1.id + \"_item_\" + index_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.indicatorFacet && !ctx_r1.galleria.indicatorTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"index\", index_r5)(\"templates\", ctx_r1.templates);\n  }\n}\nfunction GalleriaItem_ul_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 12);\n    i0.ɵɵtemplate(1, GalleriaItem_ul_6_li_1_Template, 3, 9, \"li\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.value);\n  }\n}\nconst _c22 = [\"itemsContainer\"];\nconst _c23 = a0 => ({\n  height: a0\n});\nconst _c24 = a0 => ({\n  \"p-galleria-thumbnail-prev-button p-galleria-thumbnail-nav-button\": true,\n  \"p-disabled\": a0\n});\nconst _c25 = (a0, a1, a2, a3) => ({\n  \"p-galleria-thumbnail-item\": true,\n  \"p-galleria-thumbnail-item-current\": a0,\n  \"p-galleria-thumbnail-item-active\": a1,\n  \"p-galleria-thumbnail-item-start\": a2,\n  \"p-galleria-thumbnail-item-end\": a3\n});\nconst _c26 = a0 => ({\n  \"p-galleria-thumbnail-next-button p-galleria-thumbnail-nav-button\": true,\n  \"p-disabled\": a0\n});\nfunction GalleriaThumbnails_button_2_ng_container_1_ChevronLeftIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronLeftIcon\", 11);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-galleria-thumbnail-prev-icon\");\n  }\n}\nfunction GalleriaThumbnails_button_2_ng_container_1_ChevronUpIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronUpIcon\", 11);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-galleria-thumbnail-prev-icon\");\n  }\n}\nfunction GalleriaThumbnails_button_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, GalleriaThumbnails_button_2_ng_container_1_ChevronLeftIcon_1_Template, 1, 1, \"ChevronLeftIcon\", 10)(2, GalleriaThumbnails_button_2_ng_container_1_ChevronUpIcon_2_Template, 1, 1, \"ChevronUpIcon\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isVertical);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isVertical);\n  }\n}\nfunction GalleriaThumbnails_button_2_2_ng_template_0_Template(rf, ctx) {}\nfunction GalleriaThumbnails_button_2_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, GalleriaThumbnails_button_2_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction GalleriaThumbnails_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function GalleriaThumbnails_button_2_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navBackward($event));\n    });\n    i0.ɵɵtemplate(1, GalleriaThumbnails_button_2_ng_container_1_Template, 3, 2, \"ng-container\", 8)(2, GalleriaThumbnails_button_2_2_Template, 1, 0, null, 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c24, ctx_r2.isNavBackwardDisabled()))(\"disabled\", ctx_r2.isNavBackwardDisabled());\n    i0.ɵɵattribute(\"aria-label\", ctx_r2.ariaPrevButtonLabel());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.galleria.previousThumbnailIconTemplate && !ctx_r2.galleria._previousThumbnailIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.galleria.previousThumbnailIconTemplate || ctx_r2.galleria._previousThumbnailIconTemplate);\n  }\n}\nfunction GalleriaThumbnails_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵlistener(\"keydown\", function GalleriaThumbnails_div_6_Template_div_keydown_0_listener($event) {\n      const index_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onThumbnailKeydown($event, index_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 13);\n    i0.ɵɵlistener(\"click\", function GalleriaThumbnails_div_6_Template_div_click_1_listener() {\n      const index_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemClick(index_r5));\n    })(\"touchend\", function GalleriaThumbnails_div_6_Template_div_touchend_1_listener() {\n      const index_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemClick(index_r5));\n    })(\"keydown.enter\", function GalleriaThumbnails_div_6_Template_div_keydown_enter_1_listener() {\n      const index_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemClick(index_r5));\n    });\n    i0.ɵɵelement(2, \"p-galleriaItemSlot\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const index_r5 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(10, _c25, ctx_r2.activeIndex === index_r5, ctx_r2.isItemActive(index_r5), ctx_r2.firstItemAciveIndex() === index_r5, ctx_r2.lastItemActiveIndex() === index_r5));\n    i0.ɵɵattribute(\"aria-selected\", ctx_r2.activeIndex === index_r5)(\"aria-controls\", ctx_r2.containerId + \"_item_\" + index_r5)(\"data-pc-section\", \"thumbnailitem\")(\"data-p-active\", ctx_r2.activeIndex === index_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"tabindex\", ctx_r2.activeIndex === index_r5 ? 0 : -1)(\"aria-current\", ctx_r2.activeIndex === index_r5 ? \"page\" : undefined)(\"aria-label\", ctx_r2.ariaPageLabel(index_r5 + 1));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"item\", item_r6)(\"templates\", ctx_r2.templates);\n  }\n}\nfunction GalleriaThumbnails_button_7_ng_container_1_ChevronRightIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\", 16);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngClass\", \"p-galleria-thumbnail-next-icon\");\n  }\n}\nfunction GalleriaThumbnails_button_7_ng_container_1_ChevronDownIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronDownIcon\", 16);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngClass\", \"p-galleria-thumbnail-next-icon\");\n  }\n}\nfunction GalleriaThumbnails_button_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, GalleriaThumbnails_button_7_ng_container_1_ChevronRightIcon_1_Template, 1, 1, \"ChevronRightIcon\", 15)(2, GalleriaThumbnails_button_7_ng_container_1_ChevronDownIcon_2_Template, 1, 1, \"ChevronDownIcon\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isVertical);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isVertical);\n  }\n}\nfunction GalleriaThumbnails_button_7_2_ng_template_0_Template(rf, ctx) {}\nfunction GalleriaThumbnails_button_7_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, GalleriaThumbnails_button_7_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction GalleriaThumbnails_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function GalleriaThumbnails_button_7_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navForward($event));\n    });\n    i0.ɵɵtemplate(1, GalleriaThumbnails_button_7_ng_container_1_Template, 3, 2, \"ng-container\", 8)(2, GalleriaThumbnails_button_7_2_Template, 1, 0, null, 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c26, ctx_r2.isNavForwardDisabled()))(\"disabled\", ctx_r2.isNavForwardDisabled());\n    i0.ɵɵattribute(\"aria-label\", ctx_r2.ariaNextButtonLabel());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.galleria.nextThumbnailIconTemplate && !ctx_r2.galleria._nextThumbnailIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.galleria.nextThumbnailIconTemplate || ctx_r2.galleria._nextThumbnailIconTemplate);\n  }\n}\nconst theme = ({\n  dt\n}) => `\n.p-galleria {\n    overflow: hidden;\n    border-style: solid;\n    border-width: ${dt('galleria.border.width')};\n    border-color: ${dt('galleria.border.color')};\n    border-radius: ${dt('galleria.border.radius')};\n}\n\n.p-galleria-content {\n    display: flex;\n    flex-direction: column;\n}\n\n.p-galleria-items-container {\n    display: flex;\n    flex-direction: column;\n    position: relative;\n}\n\n.p-galleria-items {\n    position: relative;\n    display: flex;\n    height: 100%;\n}\n\n.p-galleria-nav-button {\n    position: absolute;\n    top: 50%;\n    display: inline-flex;\n    justify-content: center;\n    align-items: center;\n    overflow: hidden;\n    background: ${dt('galleria.nav.button.background')};\n    color: ${dt('galleria.nav.button.color')};\n    width: ${dt('galleria.nav.button.size')};\n    height: ${dt('galleria.nav.button.size')};\n    transition: background ${dt('galleria.transition.duration')}, color ${dt('galleria.transition.duration')}, outline-color ${dt('galleria.transition.duration')}, box-shadow ${dt('galleria.transition.duration')};\n    margin: calc(-1 * calc(${dt('galleria.nav.button.size')}) / 2) ${dt('galleria.nav.button.gutter')} 0 ${dt('galleria.nav.button.gutter')};\n    padding: 0;\n    user-select: none;\n    border: 0 none;\n    cursor: pointer;\n    outline-color: transparent;\n}\n\n.p-galleria-nav-button:not(.p-disabled):hover {\n    background: ${dt('galleria.nav.button.hover.background')};\n    color: ${dt('galleria.nav.button.hover.color')};\n}\n\n.p-galleria-nav-button:not(.p-disabled):focus-visible {\n    box-shadow: ${dt('galleria.nav.button.focus.ring.shadow')};\n    outline: ${dt('galleria.nav.button.focus.ring.width')} ${dt('galleria.nav.button.focus.ring.style')} ${dt('galleria.nav.button.focus.ring.color')};\n    outline-offset: ${dt('galleria.nav.button.focus.ring.offset')};\n}\n\n.p-galleria-next-icon,\n.p-galleria-prev-icon {\n    font-size: ${dt('galleria.nav.icon.size')};\n    width: ${dt('galleria.nav.icon.size')};\n    height: ${dt('galleria.nav.icon.size')};\n}\n\n.p-galleria-prev-button {\n    border-radius: ${dt('galleria.nav.button.prev.border.radius')};\n    left: 0;\n}\n\n.p-galleria-next-button {\n    border-radius: ${dt('galleria.nav.button.next.border.radius')};\n    right: 0;\n}\n\n.p-galleria-prev-button:dir(rtl) {\n    left: auto;\n    right: 0;\n    transform: rotate(180deg);\n}\n\n.p-galleria-next-button:dir(rtl) {\n    right: auto;\n    left: 0;\n    transform: rotate(180deg);\n}\n\n.p-galleria-item {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    height: 100%;\n    width: 100%;\n}\n\n.p-galleria-hover-navigators .p-galleria-nav-button {\n    pointer-events: none;\n    opacity: 0;\n    transition: opacity ${dt('galleria.transition.duration')} ease-in-out;\n}\n\n.p-galleria-hover-navigators .p-galleria-items-container:hover .p-galleria-nav-button {\n    pointer-events: all;\n    opacity: 1;\n}\n\n.p-galleria-hover-navigators .p-galleria-items-container:hover .p-galleria-nav-button.p-disabled {\n    pointer-events: none;\n}\n\n.p-galleria-caption {\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    width: 100%;\n    background: ${dt('galleria.caption.background')};\n    color: ${dt('galleria.caption.color')};\n    padding: ${dt('galleria.caption.padding')};\n}\n\n.p-galleria-thumbnails {\n    display: flex;\n    flex-direction: column;\n    overflow: auto;\n    flex-shrink: 0;\n}\n\n.p-galleria-thumbnail-nav-button {\n    align-self: center;\n    flex: 0 0 auto;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    overflow: hidden;\n    position: relative;\n    margin: 0 ${dt('galleria.thumbnail.nav.button.gutter')};\n    padding: 0;\n    border: none;\n    user-select: none;\n    cursor: pointer;\n    background: transparent;\n    color: ${dt('galleria.thumbnail.nav.button.color')};\n    width: ${dt('galleria.thumbnail.nav.button.size')};\n    height: ${dt('galleria.thumbnail.nav.button.size')};\n    transition: background ${dt('galleria.transition.duration')}, color ${dt('galleria.transition.duration')}, outline-color ${dt('galleria.transition.duration')};\n    outline-color: transparent;\n    border-radius: ${dt('galleria.thumbnail.nav.button.border.radius')};\n}\n\n.p-galleria-thumbnail-nav-button:hover {\n    background: ${dt('galleria.thumbnail.nav.button.hover.background')};\n    color: ${dt('galleria.thumbnail.nav.button.hover.color')};\n}\n\n.p-galleria-thumbnail-nav-button:focus-visible {\n    box-shadow: ${dt('galleria.thumbnail.nav.button.focus.ring.shadow')};\n    outline: ${dt('galleria.thumbnail.nav.button.focus.ring.width')} ${dt('galleria.thumbnail.nav.button.focus.ring.style')} ${dt('galleria.thumbnail.nav.button.focus.ring.color')};\n    outline-offset: ${dt('galleria.thumbnail.nav.button.focus.ring.offset')};\n}\n\n.p-galleria-thumbnail-nav-button .p-galleria-thumbnail-next-icon,\n.p-galleria-thumbnail-nav-button .p-galleria-thumbnail-prev-icon {\n    font-size: ${dt('galleria.thumbnail.nav.button.icon.size')};\n    width: ${dt('galleria.thumbnail.nav.button.icon.size')};\n    height: ${dt('galleria.thumbnail.nav.button.icon.size')};\n}\n\n.p-galleria-thumbnails-content {\n    display: flex;\n    flex-direction: row;\n    background: ${dt('galleria.thumbnails.content.background')};\n    padding: ${dt('galleria.thumbnails.content.padding')};\n}\n\n.p-galleria-thumbnails-viewport {\n    overflow: hidden;\n    width: 100%;\n}\n\n.p-galleria:not(.p-galleria-thumbnails-right):not(.p-galleria-thumbnails-left) .p-galleria-thumbnail-prev-button:dir(rtl),\n.p-galleria:not(.p-galleria-thumbnails-right):not(.p-galleria-thumbnails-left) .p-galleria-thumbnail-next-button:dir(rtl) {\n    transform: rotate(180deg);\n}\n\n.p-galleria-thumbnail-items {\n    display: flex;\n}\n\n.p-galleria-thumbnail-item {\n    overflow: auto;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    cursor: pointer;\n    opacity: 0.5;\n}\n\n.p-galleria-thumbnail {\n    outline-color: transparent;\n}\n\n.p-galleria-thumbnail-item:hover {\n    opacity: 1;\n    transition: opacity 0.3s;\n}\n\n.p-galleria-thumbnail-item-current {\n    opacity: 1;\n}\n\n.p-galleria-thumbnails-left .p-galleria-content,\n.p-galleria-thumbnails-right .p-galleria-content {\n    flex-direction: row;\n}\n\n.p-galleria-thumbnails-left .p-galleria-items-container,\n.p-galleria-thumbnails-right .p-galleria-items-container {\n    flex-direction: row;\n}\n\n.p-galleria-thumbnails-left .p-galleria-items-container,\n.p-galleria-thumbnails-top .p-galleria-items-container {\n    order: 2;\n}\n\n.p-galleria-thumbnails-left .p-galleria-thumbnails,\n.p-galleria-thumbnails-top .p-galleria-thumbnails {\n    order: 1;\n}\n\n.p-galleria-thumbnails-left .p-galleria-thumbnails-content,\n.p-galleria-thumbnails-right .p-galleria-thumbnails-content {\n    flex-direction: column;\n    flex-grow: 1;\n}\n\n.p-galleria-thumbnails-left .p-galleria-thumbnail-items,\n.p-galleria-thumbnails-right .p-galleria-thumbnail-items {\n    flex-direction: column;\n    height: 100%;\n}\n\n.p-galleria-indicator-list {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    padding: ${dt('galleria.indicator.list.padding')};\n    gap: ${dt('galleria.indicator.list.gap')};\n    margin: 0;\n    list-style: none;\n}\n\n.p-galleria-indicator-button {\n    display: inline-flex;\n    align-items: center;\n    background: ${dt('galleria.indicator.button.background')};\n    width: ${dt('galleria.indicator.button.width')};\n    height: ${dt('galleria.indicator.button.height')};\n    transition: background ${dt('galleria.transition.duration')}, color ${dt('galleria.transition.duration')}, outline-color ${dt('galleria.transition.duration')}, box-shadow ${dt('galleria.transition.duration')};\n    outline-color: transparent;\n    border-radius: ${dt('galleria.indicator.button.border.radius')};\n    margin: 0;\n    padding: 0;\n    border: none;\n    user-select: none;\n    cursor: pointer;\n}\n\n.p-galleria-indicator-button:hover {\n    background: ${dt('galleria.indicator.button.hover.background')};\n}\n\n.p-galleria-indicator-button:focus-visible {\n    box-shadow: ${dt('galleria.indicator.button.focus.ring.shadow')};\n    outline: ${dt('galleria.indicator.button.focus.ring.width')} ${dt('galleria.indicator.button.focus.ring.style')} ${dt('galleria.indicator.button.focus.ring.color')};\n    outline-offset: ${dt('galleria.indicator.button.focus.ring.offset')};\n}\n\n.p-galleria-indicator-active .p-galleria-indicator-button {\n    background: ${dt('galleria.indicator.button.active.background')};\n}\n\n.p-galleria-indicators-left .p-galleria-items-container,\n.p-galleria-indicators-right .p-galleria-items-container {\n    flex-direction: row;\n    align-items: center;\n}\n\n.p-galleria-indicators-left .p-galleria-items,\n.p-galleria-indicators-top .p-galleria-items {\n    order: 2;\n}\n\n.p-galleria-indicators-left .p-galleria-indicator-list,\n.p-galleria-indicators-top .p-galleria-indicator-list {\n    order: 1;\n}\n\n.p-galleria-indicators-left .p-galleria-indicator-list,\n.p-galleria-indicators-right .p-galleria-indicator-list {\n    flex-direction: column;\n}\n\n.p-galleria-inset-indicators .p-galleria-indicator-list {\n    position: absolute;\n    display: flex;\n    z-index: 1;\n    background: ${dt('galleria.inset.indicator.list.background')};\n}\n\n.p-galleria-inset-indicators .p-galleria-indicator-button {\n    background: ${dt('galleria.inset.indicator.button.background')};\n}\n\n.p-galleria-inset-indicators .p-galleria-indicator-button:hover {\n    background: ${dt('galleria.inset.indicator.button.hover.background')};\n}\n\n.p-galleria-inset-indicators .p-galleria-indicator-active .p-galleria-indicator-button {\n    background: ${dt('galleria.inset.indicator.button.active.background')};\n}\n\n.p-galleria-inset-indicators.p-galleria-indicators-top .p-galleria-indicator-list {\n    top: 0;\n    left: 0;\n    width: 100%;\n    align-items: flex-start;\n}\n\n.p-galleria-inset-indicators.p-galleria-indicators-right .p-galleria-indicator-list {\n    right: 0;\n    top: 0;\n    height: 100%;\n    align-items: flex-end;\n}\n\n.p-galleria-inset-indicators.p-galleria-indicators-bottom .p-galleria-indicator-list {\n    bottom: 0;\n    left: 0;\n    width: 100%;\n    align-items: flex-end;\n}\n\n.p-galleria-inset-indicators.p-galleria-indicators-left .p-galleria-indicator-list {\n    left: 0;\n    top: 0;\n    height: 100%;\n    align-items: flex-start;\n}\n\n.p-galleria-mask {\n    position: fixed;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n}\n\n.p-galleria-close-button {\n    position: absolute;\n    top: 0;\n    right: 0;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    overflow: hidden;\n    margin: ${dt('galleria.close.button.gutter')};\n    background: ${dt('galleria.close.button.background')};\n    color: ${dt('galleria.close.button.color')};\n    width: ${dt('galleria.close.button.size')};\n    height: ${dt('galleria.close.button.size')};\n    padding: 0;\n    border: none;\n    user-select: none;\n    cursor: pointer;\n    border-radius: ${dt('galleria.close.button.border.radius')};\n    outline-color: transparent;\n    transition: background ${dt('galleria.transition.duration')}, color ${dt('galleria.transition.duration')}, outline-color ${dt('galleria.transition.duration')};\n}\n\n.p-galleria-close-icon {\n    font-size: ${dt('galleria.close.button.icon.size')};\n    width: ${dt('galleria.close.button.icon.size')};\n    height: ${dt('galleria.close.button.icon.size')};\n}\n\n.p-galleria-close-button:hover {\n    background: ${dt('galleria.close.button.hover.background')};\n    color: ${dt('galleria.close.button.hover.color')};\n}\n\n.p-galleria-close-button:focus-visible {\n    box-shadow: ${dt('galleria.close.button.focus.ring.shadow')};\n    outline: ${dt('galleria.close.button.focus.ring.width')} ${dt('galleria.close.button.focus.ring.style')} ${dt('galleria.close.button.focus.ring.color')};\n    outline-offset: ${dt('galleria.close.button.focus.ring.offset')};\n}\n\n.p-galleria-mask .p-galleria-nav-button {\n    position: fixed;\n    top: 50%;\n}\n\n.p-galleria-enter-active {\n    transition: all 150ms cubic-bezier(0, 0, 0.2, 1);\n}\n\n.p-galleria-leave-active {\n    transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.p-galleria-enter-from,\n.p-galleria-leave-to {\n    opacity: 0;\n    transform: scale(0.7);\n}\n\n.p-galleria-enter-active .p-galleria-nav-button {\n    opacity: 0;\n}\n\n.p-items-hidden .p-galleria-thumbnail-item {\n    visibility: hidden;\n}\n\n.p-items-hidden .p-galleria-thumbnail-item.p-galleria-thumbnail-item-active {\n    visibility: visible;\n}\n`;\nconst classes = {\n  mask: 'p-galleria-mask p-overlay-mask p-overlay-mask-enter',\n  root: ({\n    instance\n  }) => {\n    const thumbnailsPosClass = instance.$attrs.showThumbnails && instance.getPositionClass('p-galleria-thumbnails', instance.$attrs.thumbnailsPosition);\n    const indicatorPosClass = instance.$attrs.showIndicators && instance.getPositionClass('p-galleria-indicators', instance.$attrs.indicatorsPosition);\n    return ['p-galleria p-component', {\n      'p-galleria-fullscreen': instance.$attrs.fullScreen,\n      'p-galleria-inset-indicators': instance.$attrs.showIndicatorsOnItem,\n      'p-galleria-hover-navigators': instance.$attrs.showItemNavigatorsOnHover && !instance.$attrs.fullScreen\n    }, thumbnailsPosClass, indicatorPosClass];\n  },\n  closeButton: 'p-galleria-close-button',\n  closeIcon: 'p-galleria-close-icon',\n  header: 'p-galleria-header',\n  content: 'p-galleria-content',\n  footer: 'p-galleria-footer',\n  itemsContainer: 'p-galleria-items-container',\n  items: 'p-galleria-items',\n  prevButton: ({\n    instance\n  }) => ['p-galleria-prev-button p-galleria-nav-button', {\n    'p-disabled': instance.isNavBackwardDisabled()\n  }],\n  prevIcon: 'p-galleria-prev-icon',\n  item: 'p-galleria-item',\n  nextButton: ({\n    instance\n  }) => ['p-galleria-next-button p-galleria-nav-button', {\n    'p-disabled': instance.isNavForwardDisabled()\n  }],\n  nextIcon: 'p-galleria-next-icon',\n  caption: 'p-galleria-caption',\n  indicatorList: 'p-galleria-indicator-list',\n  indicator: ({\n    instance,\n    index\n  }) => ['p-galleria-indicator', {\n    'p-galleria-indicator-active': instance.isIndicatorItemActive(index)\n  }],\n  indicatorButton: 'p-galleria-indicator-button',\n  thumbnails: 'p-galleria-thumbnails',\n  thumbnailContent: 'p-galleria-thumbnails-content',\n  thumbnailPrevButton: ({\n    instance\n  }) => ['p-galleria-thumbnail-prev-button p-galleria-thumbnail-nav-button', {\n    'p-disabled': instance.isNavBackwardDisabled()\n  }],\n  thumbnailPrevIcon: 'p-galleria-thumbnail-prev-icon',\n  thumbnailsViewport: 'p-galleria-thumbnails-viewport',\n  thumbnailItems: 'p-galleria-thumbnail-items',\n  thumbnailItem: ({\n    instance,\n    index,\n    activeIndex\n  }) => ['p-galleria-thumbnail-item', {\n    'p-galleria-thumbnail-item-current': activeIndex === index,\n    'p-galleria-thumbnail-item-active': instance.isItemActive(index),\n    'p-galleria-thumbnail-item-start': instance.firstItemAciveIndex() === index,\n    'p-galleria-thumbnail-item-end': instance.lastItemActiveIndex() === index\n  }],\n  thumbnail: 'p-galleria-thumbnail',\n  thumbnailNextButton: ({\n    instance\n  }) => ['p-galleria-thumbnail-next-button  p-galleria-thumbnail-nav-button', {\n    'p-disabled': instance.isNavForwardDisabled()\n  }],\n  thumbnailNextIcon: 'p-galleria-thumbnail-next-icon'\n};\nclass GalleriaStyle extends BaseStyle {\n  name = 'galleria';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵGalleriaStyle_BaseFactory;\n    return function GalleriaStyle_Factory(__ngFactoryType__) {\n      return (ɵGalleriaStyle_BaseFactory || (ɵGalleriaStyle_BaseFactory = i0.ɵɵgetInheritedFactory(GalleriaStyle)))(__ngFactoryType__ || GalleriaStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: GalleriaStyle,\n    factory: GalleriaStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(GalleriaStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * Galleria is an advanced content gallery component.\n *\n * [Live Demo](https://www.primeng.org/galleria/)\n *\n * @module galleriastyle\n *\n */\nvar GalleriaClasses;\n(function (GalleriaClasses) {\n  /**\n   * Class name of the mask element\n   */\n  GalleriaClasses[\"mask\"] = \"p-galleria-mask\";\n  /**\n   * Class name of the root element\n   */\n  GalleriaClasses[\"root\"] = \"p-galleria\";\n  /**\n   * Class name of the close button element\n   */\n  GalleriaClasses[\"closeButton\"] = \"p-galleria-close-button\";\n  /**\n   * Class name of the close icon element\n   */\n  GalleriaClasses[\"closeIcon\"] = \"p-galleria-close-icon\";\n  /**\n   * Class name of the header element\n   */\n  GalleriaClasses[\"header\"] = \"p-galleria-header\";\n  /**\n   * Class name of the content element\n   */\n  GalleriaClasses[\"content\"] = \"p-galleria-content\";\n  /**\n   * Class name of the footer element\n   */\n  GalleriaClasses[\"footer\"] = \"p-galleria-footer\";\n  /**\n   * Class name of the items container element\n   */\n  GalleriaClasses[\"itemsContainer\"] = \"p-galleria-items-container\";\n  /**\n   * Class name of the items element\n   */\n  GalleriaClasses[\"items\"] = \"p-galleria-items\";\n  /**\n   * Class name of the previous item button element\n   */\n  GalleriaClasses[\"prevButton\"] = \"p-galleria-prev-button\";\n  /**\n   * Class name of the previous item icon element\n   */\n  GalleriaClasses[\"prevIcon\"] = \"p-galleria-prev-icon\";\n  /**\n   * Class name of the item element\n   */\n  GalleriaClasses[\"item\"] = \"p-galleria-item\";\n  /**\n   * Class name of the next item button element\n   */\n  GalleriaClasses[\"nextButton\"] = \"p-galleria-next-button\";\n  /**\n   * Class name of the next item icon element\n   */\n  GalleriaClasses[\"nextIcon\"] = \"p-galleria-next-icon\";\n  /**\n   * Class name of the caption element\n   */\n  GalleriaClasses[\"caption\"] = \"p-galleria-caption\";\n  /**\n   * Class name of the indicator list element\n   */\n  GalleriaClasses[\"indicatorList\"] = \"p-galleria-indicator-list\";\n  /**\n   * Class name of the indicator element\n   */\n  GalleriaClasses[\"indicator\"] = \"p-galleria-indicator\";\n  /**\n   * Class name of the indicator button element\n   */\n  GalleriaClasses[\"indicatorButton\"] = \"p-galleria-indicator-button\";\n  /**\n   * Class name of the thumbnails element\n   */\n  GalleriaClasses[\"thumbnails\"] = \"p-galleria-thumbnails\";\n  /**\n   * Class name of the thumbnail content element\n   */\n  GalleriaClasses[\"thumbnailContent\"] = \"p-galleria-thumbnails-content\";\n  /**\n   * Class name of the previous thumbnail button element\n   */\n  GalleriaClasses[\"previousThumbnailButton\"] = \"p-galleria-thumbnail-prev-button\";\n  /**\n   * Class name of the previous thumbnail icon element\n   */\n  GalleriaClasses[\"previousThumbnailIcon\"] = \"p-galleria-thumbnail-prev-icon\";\n  /**\n   * Class name of the thumbnails viewport element\n   */\n  GalleriaClasses[\"thumbnailsViewport\"] = \"p-galleria-thumbnails-viewport\";\n  /**\n   * Class name of the thumbnail items element\n   */\n  GalleriaClasses[\"thumbnailItems\"] = \"p-galleria-thumbnail-items\";\n  /**\n   * Class name of the thumbnail item element\n   */\n  GalleriaClasses[\"thumbnailItem\"] = \"p-galleria-thumbnail-item\";\n  /**\n   * Class name of the thumbnail element\n   */\n  GalleriaClasses[\"thumbnail\"] = \"p-galleria-thumbnail\";\n  /**\n   * Class name of the next thumbnail button element\n   */\n  GalleriaClasses[\"nextThumbnailButton\"] = \"p-galleria-thumbnail-next-button\";\n  /**\n   * Class name of the next thumbnail icon element\n   */\n  GalleriaClasses[\"nextThumbnailIcon\"] = \"p-galleria-thumbnail-next-icon\";\n})(GalleriaClasses || (GalleriaClasses = {}));\n\n/**\n * Galleria is an advanced content gallery component.\n * @group Components\n */\nclass Galleria extends BaseComponent {\n  platformId;\n  element;\n  cd;\n  /**\n   * Index of the first item.\n   * @group Props\n   */\n  get activeIndex() {\n    return this._activeIndex;\n  }\n  set activeIndex(activeIndex) {\n    this._activeIndex = activeIndex;\n  }\n  /**\n   * Whether to display the component on fullscreen.\n   * @group Props\n   */\n  fullScreen = false;\n  /**\n   * Unique identifier of the element.\n   * @group Props\n   */\n  id;\n  /**\n   * An array of objects to display.\n   * @group Props\n   */\n  value;\n  /**\n   * Number of items per page.\n   * @group Props\n   */\n  numVisible = 3;\n  /**\n   * An array of options for responsive design.\n   * @see {GalleriaResponsiveOptions}\n   * @group Props\n   */\n  responsiveOptions;\n  /**\n   * Whether to display navigation buttons in item section.\n   * @group Props\n   */\n  showItemNavigators = false;\n  /**\n   * Whether to display navigation buttons in thumbnail container.\n   * @group Props\n   */\n  showThumbnailNavigators = true;\n  /**\n   * Whether to display navigation buttons on item hover.\n   * @group Props\n   */\n  showItemNavigatorsOnHover = false;\n  /**\n   * When enabled, item is changed on indicator hover.\n   * @group Props\n   */\n  changeItemOnIndicatorHover = false;\n  /**\n   * Defines if scrolling would be infinite.\n   * @group Props\n   */\n  circular = false;\n  /**\n   * Items are displayed with a slideshow in autoPlay mode.\n   * @group Props\n   */\n  autoPlay = false;\n  /**\n   * When enabled, autorun should stop by click.\n   * @group Props\n   */\n  shouldStopAutoplayByClick = true;\n  /**\n   * Time in milliseconds to scroll items.\n   * @group Props\n   */\n  transitionInterval = 4000;\n  /**\n   * Whether to display thumbnail container.\n   * @group Props\n   */\n  showThumbnails = true;\n  /**\n   * Position of thumbnails.\n   * @group Props\n   */\n  thumbnailsPosition = 'bottom';\n  /**\n   * Height of the viewport in vertical thumbnail.\n   * @group Props\n   */\n  verticalThumbnailViewPortHeight = '300px';\n  /**\n   * Whether to display indicator container.\n   * @group Props\n   */\n  showIndicators = false;\n  /**\n   * When enabled, indicator container is displayed on item container.\n   * @group Props\n   */\n  showIndicatorsOnItem = false;\n  /**\n   * Position of indicators.\n   * @group Props\n   */\n  indicatorsPosition = 'bottom';\n  /**\n   * Base zIndex value to use in layering.\n   * @group Props\n   */\n  baseZIndex = 0;\n  /**\n   * Style class of the mask on fullscreen mode.\n   * @group Props\n   */\n  maskClass;\n  /**\n   * Style class of the component on fullscreen mode. Otherwise, the 'class' property can be used.\n   * @group Props\n   */\n  containerClass;\n  /**\n   * Inline style of the component on fullscreen mode. Otherwise, the 'style' property can be used.\n   * @group Props\n   */\n  containerStyle;\n  /**\n   * Transition options of the show animation.\n   * @group Props\n   */\n  showTransitionOptions = '150ms cubic-bezier(0, 0, 0.2, 1)';\n  /**\n   * Transition options of the hide animation.\n   * @group Props\n   */\n  hideTransitionOptions = '150ms cubic-bezier(0, 0, 0.2, 1)';\n  /**\n   * Specifies the visibility of the mask on fullscreen mode.\n   * @group Props\n   */\n  get visible() {\n    return this._visible;\n  }\n  set visible(visible) {\n    this._visible = visible;\n    if (this._visible && !this.maskVisible) {\n      this.maskVisible = true;\n    }\n  }\n  /**\n   * Callback to invoke on active index change.\n   * @param {number} number - Active index.\n   * @group Emits\n   */\n  activeIndexChange = new EventEmitter();\n  /**\n   * Callback to invoke on visiblity change.\n   * @param {boolean} boolean - Visible value.\n   * @group Emits\n   */\n  visibleChange = new EventEmitter();\n  mask;\n  container;\n  _visible = false;\n  _activeIndex = 0;\n  headerTemplate;\n  headerFacet;\n  footerTemplate;\n  footerFacet;\n  indicatorTemplate;\n  indicatorFacet;\n  captionTemplate;\n  captionFacet;\n  _closeIconTemplate;\n  closeIconTemplate;\n  _previousThumbnailIconTemplate;\n  previousThumbnailIconTemplate;\n  _nextThumbnailIconTemplate;\n  nextThumbnailIconTemplate;\n  _itemPreviousIconTemplate;\n  itemPreviousIconTemplate;\n  _itemNextIconTemplate;\n  itemNextIconTemplate;\n  _itemTemplate;\n  itemTemplate;\n  _thumbnailTemplate;\n  thumbnailTemplate;\n  maskVisible = false;\n  numVisibleLimit = 0;\n  _componentStyle = inject(GalleriaStyle);\n  constructor(platformId, element, cd) {\n    super();\n    this.platformId = platformId;\n    this.element = element;\n    this.cd = cd;\n  }\n  templates;\n  ngAfterContentInit() {\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'header':\n          this.headerFacet = item.template;\n          break;\n        case 'footer':\n          this.footerFacet = item.template;\n          break;\n        case 'indicator':\n          this.indicatorFacet = item.template;\n          break;\n        case 'closeicon':\n          this.closeIconTemplate = item.template;\n          break;\n        case 'itemnexticon':\n          this.itemNextIconTemplate = item.template;\n          break;\n        case 'itempreviousicon':\n          this.itemPreviousIconTemplate = item.template;\n          break;\n        case 'previousthumbnailicon':\n          this.previousThumbnailIconTemplate = item.template;\n          break;\n        case 'nextthumbnailicon':\n          this.nextThumbnailIconTemplate = item.template;\n          break;\n        case 'caption':\n          this.captionFacet = item.template;\n          break;\n        case 'item':\n          this.itemTemplate = item.template;\n          break;\n        case 'thumbnail':\n          this.thumbnailTemplate = item.template;\n          break;\n      }\n    });\n  }\n  ngOnChanges(simpleChanges) {\n    super.ngOnChanges(simpleChanges);\n    if (simpleChanges.value && simpleChanges.value.currentValue?.length < this.numVisible) {\n      this.numVisibleLimit = simpleChanges.value.currentValue.length;\n    } else {\n      this.numVisibleLimit = 0;\n    }\n  }\n  onMaskHide() {\n    this.visible = false;\n    this.visibleChange.emit(false);\n  }\n  onActiveItemChange(index) {\n    if (this.activeIndex !== index) {\n      this.activeIndex = index;\n      this.activeIndexChange.emit(index);\n    }\n  }\n  onAnimationStart(event) {\n    switch (event.toState) {\n      case 'visible':\n        this.enableModality();\n        setTimeout(() => {\n          focus(findSingle(this.container.nativeElement, '[data-pc-section=\"closebutton\"]'));\n        }, 25);\n        break;\n      case 'void':\n        addClass(this.mask?.nativeElement, 'p-overlay-mask-leave');\n        break;\n    }\n  }\n  onAnimationEnd(event) {\n    switch (event.toState) {\n      case 'void':\n        this.disableModality();\n        break;\n    }\n  }\n  enableModality() {\n    blockBodyScroll();\n    this.cd.markForCheck();\n    if (this.mask) {\n      ZIndexUtils.set('modal', this.mask.nativeElement, this.baseZIndex || this.config.zIndex.modal);\n    }\n  }\n  disableModality() {\n    unblockBodyScroll();\n    this.maskVisible = false;\n    this.cd.markForCheck();\n    if (this.mask) {\n      ZIndexUtils.clear(this.mask.nativeElement);\n    }\n  }\n  ngOnDestroy() {\n    if (this.fullScreen) {\n      removeClass(this.document.body, 'p-overflow-hidden');\n    }\n    if (this.mask) {\n      this.disableModality();\n    }\n  }\n  static ɵfac = function Galleria_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Galleria)(i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Galleria,\n    selectors: [[\"p-galleria\"]],\n    contentQueries: function Galleria_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, _c0, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c1, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c2, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c3, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c4, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c5, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c6, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c7, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c8, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c9, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c10, 4);\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.indicatorTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.captionTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._closeIconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._previousThumbnailIconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nextThumbnailIconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._itemPreviousIconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._itemNextIconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._itemTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._thumbnailTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function Galleria_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c11, 5);\n        i0.ɵɵviewQuery(_c12, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.mask = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.container = _t.first);\n      }\n    },\n    inputs: {\n      activeIndex: \"activeIndex\",\n      fullScreen: [2, \"fullScreen\", \"fullScreen\", booleanAttribute],\n      id: \"id\",\n      value: \"value\",\n      numVisible: [2, \"numVisible\", \"numVisible\", numberAttribute],\n      responsiveOptions: \"responsiveOptions\",\n      showItemNavigators: [2, \"showItemNavigators\", \"showItemNavigators\", booleanAttribute],\n      showThumbnailNavigators: [2, \"showThumbnailNavigators\", \"showThumbnailNavigators\", booleanAttribute],\n      showItemNavigatorsOnHover: [2, \"showItemNavigatorsOnHover\", \"showItemNavigatorsOnHover\", booleanAttribute],\n      changeItemOnIndicatorHover: [2, \"changeItemOnIndicatorHover\", \"changeItemOnIndicatorHover\", booleanAttribute],\n      circular: [2, \"circular\", \"circular\", booleanAttribute],\n      autoPlay: [2, \"autoPlay\", \"autoPlay\", booleanAttribute],\n      shouldStopAutoplayByClick: [2, \"shouldStopAutoplayByClick\", \"shouldStopAutoplayByClick\", booleanAttribute],\n      transitionInterval: [2, \"transitionInterval\", \"transitionInterval\", numberAttribute],\n      showThumbnails: [2, \"showThumbnails\", \"showThumbnails\", booleanAttribute],\n      thumbnailsPosition: \"thumbnailsPosition\",\n      verticalThumbnailViewPortHeight: \"verticalThumbnailViewPortHeight\",\n      showIndicators: [2, \"showIndicators\", \"showIndicators\", booleanAttribute],\n      showIndicatorsOnItem: [2, \"showIndicatorsOnItem\", \"showIndicatorsOnItem\", booleanAttribute],\n      indicatorsPosition: \"indicatorsPosition\",\n      baseZIndex: [2, \"baseZIndex\", \"baseZIndex\", numberAttribute],\n      maskClass: \"maskClass\",\n      containerClass: \"containerClass\",\n      containerStyle: \"containerStyle\",\n      showTransitionOptions: \"showTransitionOptions\",\n      hideTransitionOptions: \"hideTransitionOptions\",\n      visible: \"visible\"\n    },\n    outputs: {\n      activeIndexChange: \"activeIndexChange\",\n      visibleChange: \"visibleChange\"\n    },\n    standalone: false,\n    features: [i0.ɵɵProvidersFeature([GalleriaStyle]), i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n    decls: 3,\n    vars: 2,\n    consts: [[\"windowed\", \"\"], [\"container\", \"\"], [\"mask\", \"\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"ngClass\", \"class\", 4, \"ngIf\"], [3, \"ngClass\"], [3, \"value\", \"activeIndex\", \"numVisible\", \"ngStyle\", \"fullScreen\", \"maskHide\", \"activeItemChange\", 4, \"ngIf\"], [3, \"maskHide\", \"activeItemChange\", \"value\", \"activeIndex\", \"numVisible\", \"ngStyle\", \"fullScreen\"], [3, \"activeItemChange\", \"value\", \"activeIndex\", \"numVisible\"]],\n    template: function Galleria_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, Galleria_div_0_Template, 3, 1, \"div\", 3)(1, Galleria_ng_template_1_Template, 1, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        const windowed_r4 = i0.ɵɵreference(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.fullScreen)(\"ngIfElse\", windowed_r4);\n      }\n    },\n    dependencies: () => [i1.NgClass, i1.NgIf, i1.NgStyle, GalleriaContent],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('animation', [transition('void => visible', [style({\n        transform: 'scale(0.7)',\n        opacity: 0\n      }), animate('{{showTransitionParams}}')]), transition('visible => void', [animate('{{hideTransitionParams}}', style({\n        transform: 'scale(0.7)',\n        opacity: 0\n      }))])])]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Galleria, [{\n    type: Component,\n    args: [{\n      selector: 'p-galleria',\n      standalone: false,\n      template: `\n        <div *ngIf=\"fullScreen; else windowed\" #container>\n            <div *ngIf=\"maskVisible\" #mask [ngClass]=\"{ 'p-galleria-mask p-overlay-mask p-overlay-mask-enter': true }\" [class]=\"maskClass\" [attr.role]=\"fullScreen ? 'dialog' : 'region'\" [attr.aria-modal]=\"fullScreen ? 'true' : undefined\">\n                <p-galleriaContent\n                    *ngIf=\"visible\"\n                    [@animation]=\"{\n                        value: 'visible',\n                        params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions }\n                    }\"\n                    (@animation.start)=\"onAnimationStart($event)\"\n                    (@animation.done)=\"onAnimationEnd($event)\"\n                    [value]=\"value\"\n                    [activeIndex]=\"activeIndex\"\n                    [numVisible]=\"numVisibleLimit || numVisible\"\n                    (maskHide)=\"onMaskHide()\"\n                    (activeItemChange)=\"onActiveItemChange($event)\"\n                    [ngStyle]=\"containerStyle\"\n                    [fullScreen]=\"fullScreen\"\n                ></p-galleriaContent>\n            </div>\n        </div>\n\n        <ng-template #windowed>\n            <p-galleriaContent [value]=\"value\" [activeIndex]=\"activeIndex\" [numVisible]=\"numVisibleLimit || numVisible\" (activeItemChange)=\"onActiveItemChange($event)\"></p-galleriaContent>\n        </ng-template>\n    `,\n      animations: [trigger('animation', [transition('void => visible', [style({\n        transform: 'scale(0.7)',\n        opacity: 0\n      }), animate('{{showTransitionParams}}')]), transition('visible => void', [animate('{{hideTransitionParams}}', style({\n        transform: 'scale(0.7)',\n        opacity: 0\n      }))])])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [GalleriaStyle]\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    activeIndex: [{\n      type: Input\n    }],\n    fullScreen: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    id: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    numVisible: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    responsiveOptions: [{\n      type: Input\n    }],\n    showItemNavigators: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    showThumbnailNavigators: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    showItemNavigatorsOnHover: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    changeItemOnIndicatorHover: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    circular: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    autoPlay: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    shouldStopAutoplayByClick: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    transitionInterval: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    showThumbnails: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    thumbnailsPosition: [{\n      type: Input\n    }],\n    verticalThumbnailViewPortHeight: [{\n      type: Input\n    }],\n    showIndicators: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    showIndicatorsOnItem: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    indicatorsPosition: [{\n      type: Input\n    }],\n    baseZIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    maskClass: [{\n      type: Input\n    }],\n    containerClass: [{\n      type: Input\n    }],\n    containerStyle: [{\n      type: Input\n    }],\n    showTransitionOptions: [{\n      type: Input\n    }],\n    hideTransitionOptions: [{\n      type: Input\n    }],\n    visible: [{\n      type: Input\n    }],\n    activeIndexChange: [{\n      type: Output\n    }],\n    visibleChange: [{\n      type: Output\n    }],\n    mask: [{\n      type: ViewChild,\n      args: ['mask']\n    }],\n    container: [{\n      type: ViewChild,\n      args: ['container']\n    }],\n    headerTemplate: [{\n      type: ContentChild,\n      args: ['header', {\n        descendants: false\n      }]\n    }],\n    footerTemplate: [{\n      type: ContentChild,\n      args: ['footer', {\n        descendants: false,\n        static: false\n      }]\n    }],\n    indicatorTemplate: [{\n      type: ContentChild,\n      args: ['indicator', {\n        descendants: false\n      }]\n    }],\n    captionTemplate: [{\n      type: ContentChild,\n      args: ['caption', {\n        descendants: false\n      }]\n    }],\n    _closeIconTemplate: [{\n      type: ContentChild,\n      args: ['closeicon', {\n        descendants: false\n      }]\n    }],\n    _previousThumbnailIconTemplate: [{\n      type: ContentChild,\n      args: ['previousthumbnailicon', {\n        descendants: false\n      }]\n    }],\n    _nextThumbnailIconTemplate: [{\n      type: ContentChild,\n      args: ['nextthumbnailicon', {\n        descendants: false\n      }]\n    }],\n    _itemPreviousIconTemplate: [{\n      type: ContentChild,\n      args: ['itempreviousicon', {\n        descendants: false\n      }]\n    }],\n    _itemNextIconTemplate: [{\n      type: ContentChild,\n      args: ['itemnexticon', {\n        descendants: false\n      }]\n    }],\n    _itemTemplate: [{\n      type: ContentChild,\n      args: ['item', {\n        descendants: false\n      }]\n    }],\n    _thumbnailTemplate: [{\n      type: ContentChild,\n      args: ['thumbnail', {\n        descendants: false,\n        static: false\n      }]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass GalleriaContent extends BaseComponent {\n  galleria;\n  cd;\n  differs;\n  elementRef;\n  get activeIndex() {\n    return this._activeIndex;\n  }\n  set activeIndex(activeIndex) {\n    this._activeIndex = activeIndex;\n  }\n  value = [];\n  numVisible;\n  fullScreen;\n  maskHide = new EventEmitter();\n  activeItemChange = new EventEmitter();\n  closeButton;\n  id;\n  _activeIndex = 0;\n  slideShowActive = true;\n  interval;\n  styleClass;\n  differ;\n  constructor(galleria, cd, differs, elementRef) {\n    super();\n    this.galleria = galleria;\n    this.cd = cd;\n    this.differs = differs;\n    this.elementRef = elementRef;\n    this.id = this.galleria.id || uuid('pn_id_');\n    this.differ = this.differs.find(this.galleria).create();\n  }\n  // For custom fullscreen\n  handleFullscreenChange(event) {\n    if (document?.fullscreenElement === this.elementRef.nativeElement?.children[0]) {\n      this.fullScreen = true;\n    } else {\n      this.fullScreen = false;\n    }\n  }\n  ngDoCheck() {\n    if (isPlatformBrowser(this.galleria.platformId)) {\n      const changes = this.differ.diff(this.galleria);\n      if (changes && changes.forEachItem.length > 0) {\n        // Because we change the properties of the parent component,\n        // and the children take our entity from the injector.\n        // We can tell the children to redraw themselves when we change the properties of the parent component.\n        // Since we have an onPush strategy\n        this.cd.markForCheck();\n      }\n    }\n  }\n  shouldRenderFooter() {\n    return this.galleria.footerFacet && this.galleria.templates.toArray().length > 0 || this.galleria.footerTemplate;\n  }\n  galleriaClass() {\n    const thumbnailsPosClass = this.galleria.showThumbnails && this.getPositionClass('p-galleria-thumbnails', this.galleria.thumbnailsPosition);\n    const indicatorPosClass = this.galleria.showIndicators && this.getPositionClass('p-galleria-indicators', this.galleria.indicatorsPosition);\n    return (this.galleria.containerClass ? this.galleria.containerClass + ' ' : '') + (thumbnailsPosClass ? thumbnailsPosClass + ' ' : '') + (indicatorPosClass ? indicatorPosClass + ' ' : '');\n  }\n  startSlideShow() {\n    if (isPlatformBrowser(this.galleria.platformId)) {\n      this.interval = setInterval(() => {\n        let activeIndex = this.galleria.circular && this.value.length - 1 === this.activeIndex ? 0 : this.activeIndex + 1;\n        this.onActiveIndexChange(activeIndex);\n        this.activeIndex = activeIndex;\n      }, this.galleria.transitionInterval);\n      this.slideShowActive = true;\n    }\n  }\n  stopSlideShow() {\n    if (this.galleria.autoPlay && !this.galleria.shouldStopAutoplayByClick) {\n      return;\n    }\n    if (this.interval) {\n      clearInterval(this.interval);\n    }\n    this.slideShowActive = false;\n  }\n  getPositionClass(preClassName, position) {\n    const positions = ['top', 'left', 'bottom', 'right'];\n    const pos = positions.find(item => item === position);\n    return pos ? `${preClassName}-${pos}` : '';\n  }\n  isVertical() {\n    return this.galleria.thumbnailsPosition === 'left' || this.galleria.thumbnailsPosition === 'right';\n  }\n  onActiveIndexChange(index) {\n    if (this.activeIndex !== index) {\n      this.activeIndex = index;\n      this.activeItemChange.emit(this.activeIndex);\n    }\n  }\n  closeAriaLabel() {\n    return this.config.translation.aria ? this.config.translation.aria.close : undefined;\n  }\n  static ɵfac = function GalleriaContent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || GalleriaContent)(i0.ɵɵdirectiveInject(Galleria), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.KeyValueDiffers), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: GalleriaContent,\n    selectors: [[\"p-galleriaContent\"]],\n    viewQuery: function GalleriaContent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c16, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.closeButton = _t.first);\n      }\n    },\n    hostBindings: function GalleriaContent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"fullscreenchange\", function GalleriaContent_fullscreenchange_HostBindingHandler($event) {\n          return ctx.handleFullscreenChange($event);\n        }, false, i0.ɵɵresolveDocument);\n      }\n    },\n    inputs: {\n      activeIndex: \"activeIndex\",\n      value: \"value\",\n      numVisible: [2, \"numVisible\", \"numVisible\", numberAttribute],\n      fullScreen: [2, \"fullScreen\", \"fullScreen\", booleanAttribute]\n    },\n    outputs: {\n      maskHide: \"maskHide\",\n      activeItemChange: \"activeItemChange\"\n    },\n    standalone: false,\n    features: [i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature],\n    decls: 1,\n    vars: 1,\n    consts: [[\"pFocusTrap\", \"\", 3, \"ngClass\", \"ngStyle\", \"class\", \"pFocusTrapDisabled\", 4, \"ngIf\"], [\"pFocusTrap\", \"\", 3, \"ngClass\", \"ngStyle\", \"pFocusTrapDisabled\"], [\"type\", \"button\", \"class\", \"p-galleria-close-button\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"p-galleria-header\", 4, \"ngIf\"], [1, \"p-galleria-content\"], [3, \"onActiveIndexChange\", \"startSlideShow\", \"stopSlideShow\", \"id\", \"value\", \"activeIndex\", \"circular\", \"templates\", \"showIndicators\", \"changeItemOnIndicatorHover\", \"indicatorFacet\", \"captionFacet\", \"showItemNavigators\", \"autoPlay\", \"slideShowActive\"], [3, \"containerId\", \"value\", \"activeIndex\", \"templates\", \"numVisible\", \"responsiveOptions\", \"circular\", \"isVertical\", \"contentHeight\", \"showThumbnailNavigators\", \"slideShowActive\", \"onActiveIndexChange\", \"stopSlideShow\", 4, \"ngIf\"], [\"class\", \"p-galleria-footer\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"p-galleria-close-button\", 3, \"click\"], [3, \"styleClass\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [3, \"styleClass\"], [1, \"p-galleria-header\"], [\"type\", \"header\", 3, \"templates\"], [3, \"onActiveIndexChange\", \"stopSlideShow\", \"containerId\", \"value\", \"activeIndex\", \"templates\", \"numVisible\", \"responsiveOptions\", \"circular\", \"isVertical\", \"contentHeight\", \"showThumbnailNavigators\", \"slideShowActive\"], [1, \"p-galleria-footer\"], [\"type\", \"footer\", 3, \"templates\"]],\n    template: function GalleriaContent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, GalleriaContent_div_0_Template, 7, 29, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.value && ctx.value.length > 0);\n      }\n    },\n    dependencies: () => [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, TimesIcon, FocusTrap, GalleriaItemSlot, GalleriaItem, GalleriaThumbnails],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(GalleriaContent, [{\n    type: Component,\n    args: [{\n      selector: 'p-galleriaContent',\n      standalone: false,\n      template: `\n        <div\n            [attr.id]=\"id\"\n            [attr.role]=\"'region'\"\n            *ngIf=\"value && value.length > 0\"\n            [ngClass]=\"{\n                'p-galleria p-component': true,\n                'p-galleria-fullscreen': this.galleria.fullScreen,\n                'p-galleria-inset-indicators': this.galleria.showIndicatorsOnItem,\n                'p-galleria-hover-navigators': this.galleria.showItemNavigatorsOnHover && !this.galleria.fullScreen\n            }\"\n            [ngStyle]=\"!galleria.fullScreen ? galleria.containerStyle : {}\"\n            [class]=\"galleriaClass()\"\n            pFocusTrap\n            [pFocusTrapDisabled]=\"!fullScreen\"\n        >\n            <button *ngIf=\"galleria.fullScreen\" type=\"button\" class=\"p-galleria-close-button\" (click)=\"maskHide.emit()\" [attr.aria-label]=\"closeAriaLabel()\" [attr.data-pc-section]=\"'closebutton'\">\n                <TimesIcon *ngIf=\"!galleria.closeIconTemplate && !galleria._closeIconTemplate\" [styleClass]=\"'p-galleria-close-icon'\" />\n                <ng-template *ngTemplateOutlet=\"galleria.closeIconTemplate || galleria._closeIconTemplate\"></ng-template>\n            </button>\n            <div *ngIf=\"galleria.templates && (galleria.headerFacet || galleria.headerTemplate)\" class=\"p-galleria-header\">\n                <p-galleriaItemSlot type=\"header\" [templates]=\"galleria.templates\"></p-galleriaItemSlot>\n            </div>\n            <div class=\"p-galleria-content\" [attr.aria-live]=\"galleria.autoPlay ? 'polite' : 'off'\">\n                <p-galleriaItem\n                    [id]=\"id\"\n                    [value]=\"value\"\n                    [activeIndex]=\"activeIndex\"\n                    [circular]=\"galleria.circular\"\n                    [templates]=\"galleria.templates\"\n                    (onActiveIndexChange)=\"onActiveIndexChange($event)\"\n                    [showIndicators]=\"galleria.showIndicators\"\n                    [changeItemOnIndicatorHover]=\"galleria.changeItemOnIndicatorHover\"\n                    [indicatorFacet]=\"galleria.indicatorFacet\"\n                    [captionFacet]=\"galleria.captionFacet\"\n                    [showItemNavigators]=\"galleria.showItemNavigators\"\n                    [autoPlay]=\"galleria.autoPlay\"\n                    [slideShowActive]=\"slideShowActive\"\n                    (startSlideShow)=\"startSlideShow()\"\n                    (stopSlideShow)=\"stopSlideShow()\"\n                ></p-galleriaItem>\n\n                <p-galleriaThumbnails\n                    *ngIf=\"galleria.showThumbnails\"\n                    [containerId]=\"id\"\n                    [value]=\"value\"\n                    (onActiveIndexChange)=\"onActiveIndexChange($event)\"\n                    [activeIndex]=\"activeIndex\"\n                    [templates]=\"galleria.templates\"\n                    [numVisible]=\"numVisible\"\n                    [responsiveOptions]=\"galleria.responsiveOptions\"\n                    [circular]=\"galleria.circular\"\n                    [isVertical]=\"isVertical()\"\n                    [contentHeight]=\"galleria.verticalThumbnailViewPortHeight\"\n                    [showThumbnailNavigators]=\"galleria.showThumbnailNavigators\"\n                    [slideShowActive]=\"slideShowActive\"\n                    (stopSlideShow)=\"stopSlideShow()\"\n                ></p-galleriaThumbnails>\n            </div>\n            <div *ngIf=\"shouldRenderFooter()\" class=\"p-galleria-footer\">\n                <p-galleriaItemSlot type=\"footer\" [templates]=\"galleria.templates\"></p-galleriaItemSlot>\n            </div>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], () => [{\n    type: Galleria\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.KeyValueDiffers\n  }, {\n    type: i0.ElementRef\n  }], {\n    activeIndex: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    numVisible: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    fullScreen: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    maskHide: [{\n      type: Output\n    }],\n    activeItemChange: [{\n      type: Output\n    }],\n    closeButton: [{\n      type: ViewChild,\n      args: ['closeButton']\n    }],\n    handleFullscreenChange: [{\n      type: HostListener,\n      args: ['document:fullscreenchange', ['$event']]\n    }]\n  });\n})();\nclass GalleriaItemSlot {\n  templates;\n  index;\n  get item() {\n    return this._item;\n  }\n  shouldRender() {\n    return this.contentTemplate || this.galleria._itemTemplate || this.galleria.itemTemplate || this.galleria.captionTemplate || this.galleria.captionTemplate || this.galleria.captionFacet || this.galleria.thumbnailTemplate || this.galleria._thumbnailTemplate || this.galleria.footerTemplate;\n  }\n  galleria = inject(Galleria);\n  set item(item) {\n    this._item = item;\n    if (this.templates && this.templates?.toArray().length > 0) {\n      this.templates.forEach(item => {\n        if (item.getType() === this.type) {\n          switch (this.type) {\n            case 'item':\n            case 'caption':\n            case 'thumbnail':\n              this.context = {\n                $implicit: this.item\n              };\n              this.contentTemplate = item.template;\n              break;\n            case 'footer':\n              this.context = {\n                $implicit: this.item\n              };\n              this.contentTemplate = item.template;\n              break;\n          }\n        }\n      });\n    } else {\n      this.getContentTemplate();\n    }\n  }\n  getContentTemplate() {\n    switch (this.type) {\n      case 'item':\n        this.context = {\n          $implicit: this.item\n        };\n        this.contentTemplate = this.galleria._itemTemplate || this.galleria.itemTemplate;\n        break;\n      case 'caption':\n        this.context = {\n          $implicit: this.item\n        };\n        this.contentTemplate = this.galleria.captionTemplate || this.galleria.captionFacet;\n        break;\n      case 'thumbnail':\n        this.context = {\n          $implicit: this.item\n        };\n        this.contentTemplate = this.galleria.thumbnailTemplate || this.galleria._thumbnailTemplate;\n        break;\n      case 'indicator':\n        this.context = {\n          $implicit: this.index\n        };\n        this.contentTemplate = this.galleria.indicatorTemplate || this.galleria.indicatorFacet;\n        break;\n      case 'footer':\n        this.context = {\n          $implicit: this.item\n        };\n        this.contentTemplate = this.galleria.footerTemplate || this.galleria.footerFacet;\n        break;\n      default:\n        this.context = {\n          $implicit: this.item\n        };\n        this.contentTemplate = this.galleria._itemTemplate || this.galleria.itemTemplate;\n    }\n  }\n  type;\n  contentTemplate;\n  context;\n  _item;\n  ngAfterContentInit() {\n    if (this.templates && this.templates.toArray().length > 0) {\n      this.templates?.forEach(item => {\n        if (item.getType() === this.type) {\n          switch (this.type) {\n            case 'item':\n            case 'caption':\n            case 'thumbnail':\n              this.context = {\n                $implicit: this.item\n              };\n              this.contentTemplate = item.template;\n              break;\n            case 'indicator':\n              this.context = {\n                $implicit: this.index\n              };\n              this.contentTemplate = item.template;\n              break;\n            case 'footer':\n              this.context = {\n                $implicit: this.item\n              };\n              this.contentTemplate = item.template;\n              break;\n            default:\n              this.context = {\n                $implicit: this.item\n              };\n              this.contentTemplate = item.template;\n              break;\n          }\n        }\n      });\n    } else {\n      this.getContentTemplate();\n    }\n  }\n  static ɵfac = function GalleriaItemSlot_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || GalleriaItemSlot)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: GalleriaItemSlot,\n    selectors: [[\"p-galleriaItemSlot\"]],\n    inputs: {\n      templates: \"templates\",\n      index: [2, \"index\", \"index\", numberAttribute],\n      item: \"item\",\n      type: \"type\"\n    },\n    standalone: false,\n    features: [i0.ɵɵInputTransformsFeature],\n    decls: 1,\n    vars: 1,\n    consts: [[4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n    template: function GalleriaItemSlot_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, GalleriaItemSlot_ng_container_0_Template, 2, 2, \"ng-container\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldRender());\n      }\n    },\n    dependencies: [i1.NgIf, i1.NgTemplateOutlet],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(GalleriaItemSlot, [{\n    type: Component,\n    args: [{\n      selector: 'p-galleriaItemSlot',\n      standalone: false,\n      template: `\n        <ng-container *ngIf=\"shouldRender()\">\n            <ng-container *ngTemplateOutlet=\"contentTemplate; context: context\"></ng-container>\n        </ng-container>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, {\n    templates: [{\n      type: Input\n    }],\n    index: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    item: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }]\n  });\n})();\nclass GalleriaItem {\n  galleria;\n  id;\n  circular = false;\n  value;\n  showItemNavigators = false;\n  showIndicators = true;\n  slideShowActive = true;\n  changeItemOnIndicatorHover = true;\n  autoPlay = false;\n  templates;\n  indicatorFacet;\n  captionFacet;\n  startSlideShow = new EventEmitter();\n  stopSlideShow = new EventEmitter();\n  onActiveIndexChange = new EventEmitter();\n  get activeIndex() {\n    return this._activeIndex;\n  }\n  set activeIndex(activeIndex) {\n    this._activeIndex = activeIndex;\n  }\n  get activeItem() {\n    return this.value && this.value[this._activeIndex];\n  }\n  _activeIndex = 0;\n  leftButtonFocused = false;\n  rightButtonFocused = false;\n  constructor(galleria) {\n    this.galleria = galleria;\n  }\n  ngOnChanges({\n    autoPlay\n  }) {\n    if (autoPlay?.currentValue) {\n      this.startSlideShow.emit();\n    }\n    if (autoPlay && autoPlay.currentValue === false) {\n      this.stopTheSlideShow();\n    }\n  }\n  next() {\n    let nextItemIndex = this.activeIndex + 1;\n    let activeIndex = this.circular && this.value.length - 1 === this.activeIndex ? 0 : nextItemIndex;\n    this.onActiveIndexChange.emit(activeIndex);\n  }\n  prev() {\n    let prevItemIndex = this.activeIndex !== 0 ? this.activeIndex - 1 : 0;\n    let activeIndex = this.circular && this.activeIndex === 0 ? this.value.length - 1 : prevItemIndex;\n    this.onActiveIndexChange.emit(activeIndex);\n  }\n  onButtonFocus(pos) {\n    if (pos === 'left') {\n      this.leftButtonFocused = true;\n    } else this.rightButtonFocused = true;\n  }\n  onButtonBlur(pos) {\n    if (pos === 'left') {\n      this.leftButtonFocused = false;\n    } else this.rightButtonFocused = false;\n  }\n  stopTheSlideShow() {\n    if (this.slideShowActive && this.stopSlideShow) {\n      this.stopSlideShow.emit();\n    }\n  }\n  navForward(e) {\n    this.stopTheSlideShow();\n    this.next();\n    if (e && e.cancelable) {\n      e.preventDefault();\n    }\n  }\n  navBackward(e) {\n    this.stopTheSlideShow();\n    this.prev();\n    if (e && e.cancelable) {\n      e.preventDefault();\n    }\n  }\n  onIndicatorClick(index) {\n    this.stopTheSlideShow();\n    this.onActiveIndexChange.emit(index);\n  }\n  onIndicatorMouseEnter(index) {\n    if (this.changeItemOnIndicatorHover) {\n      this.stopTheSlideShow();\n      this.onActiveIndexChange.emit(index);\n    }\n  }\n  onIndicatorKeyDown(event, index) {\n    switch (event.code) {\n      case 'Enter':\n      case 'Space':\n        this.stopTheSlideShow();\n        this.onActiveIndexChange.emit(index);\n        event.preventDefault();\n        break;\n      case 'ArrowDown':\n      case 'ArrowUp':\n        event.preventDefault();\n        break;\n      default:\n        break;\n    }\n  }\n  isNavForwardDisabled() {\n    return !this.circular && this.activeIndex === this.value.length - 1;\n  }\n  isNavBackwardDisabled() {\n    return !this.circular && this.activeIndex === 0;\n  }\n  isIndicatorItemActive(index) {\n    return this.activeIndex === index;\n  }\n  ariaSlideLabel() {\n    return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.slide : undefined;\n  }\n  ariaSlideNumber(value) {\n    return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.slideNumber.replace(/{slideNumber}/g, value) : undefined;\n  }\n  ariaPageLabel(value) {\n    return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.pageLabel.replace(/{page}/g, value) : undefined;\n  }\n  static ɵfac = function GalleriaItem_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || GalleriaItem)(i0.ɵɵdirectiveInject(Galleria));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: GalleriaItem,\n    selectors: [[\"p-galleriaItem\"]],\n    hostAttrs: [1, \"p-galleria-items-container\"],\n    inputs: {\n      id: \"id\",\n      circular: [2, \"circular\", \"circular\", booleanAttribute],\n      value: \"value\",\n      showItemNavigators: [2, \"showItemNavigators\", \"showItemNavigators\", booleanAttribute],\n      showIndicators: [2, \"showIndicators\", \"showIndicators\", booleanAttribute],\n      slideShowActive: [2, \"slideShowActive\", \"slideShowActive\", booleanAttribute],\n      changeItemOnIndicatorHover: [2, \"changeItemOnIndicatorHover\", \"changeItemOnIndicatorHover\", booleanAttribute],\n      autoPlay: [2, \"autoPlay\", \"autoPlay\", booleanAttribute],\n      templates: \"templates\",\n      indicatorFacet: \"indicatorFacet\",\n      captionFacet: \"captionFacet\",\n      activeIndex: \"activeIndex\"\n    },\n    outputs: {\n      startSlideShow: \"startSlideShow\",\n      stopSlideShow: \"stopSlideShow\",\n      onActiveIndexChange: \"onActiveIndexChange\"\n    },\n    standalone: false,\n    features: [i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature],\n    decls: 7,\n    vars: 9,\n    consts: [[1, \"p-galleria-items\"], [\"type\", \"button\", \"role\", \"navigation\", 3, \"ngClass\", \"disabled\", \"click\", \"focus\", \"blur\", 4, \"ngIf\"], [\"role\", \"group\", 1, \"p-galleria-item\", 3, \"id\"], [\"type\", \"item\", 1, \"p-galleria-item\", 3, \"item\", \"templates\"], [\"class\", \"p-galleria-caption\", 4, \"ngIf\"], [\"class\", \"p-galleria-indicator-list\", 4, \"ngIf\"], [\"type\", \"button\", \"role\", \"navigation\", 3, \"click\", \"focus\", \"blur\", \"ngClass\", \"disabled\"], [3, \"styleClass\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [3, \"styleClass\"], [1, \"p-galleria-caption\"], [\"type\", \"caption\", 3, \"item\", \"templates\"], [1, \"p-galleria-indicator-list\"], [\"tabindex\", \"0\", 3, \"ngClass\", \"click\", \"mouseenter\", \"keydown\", 4, \"ngFor\", \"ngForOf\"], [\"tabindex\", \"0\", 3, \"click\", \"mouseenter\", \"keydown\", \"ngClass\"], [\"type\", \"button\", \"tabIndex\", \"-1\", \"class\", \"p-galleria-indicator-button\", 4, \"ngIf\"], [\"type\", \"indicator\", 3, \"index\", \"templates\"], [\"type\", \"button\", \"tabIndex\", \"-1\", 1, \"p-galleria-indicator-button\"]],\n    template: function GalleriaItem_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, GalleriaItem_button_1_Template, 3, 6, \"button\", 1);\n        i0.ɵɵelementStart(2, \"div\", 2);\n        i0.ɵɵelement(3, \"p-galleriaItemSlot\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(4, GalleriaItem_button_4_Template, 3, 6, \"button\", 1)(5, GalleriaItem_div_5_Template, 2, 2, \"div\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(6, GalleriaItem_ul_6_Template, 2, 1, \"ul\", 5);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showItemNavigators);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"id\", ctx.id + \"_item_\" + ctx.activeIndex);\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaSlideNumber(ctx.activeIndex + 1))(\"aria-roledescription\", ctx.ariaSlideLabel());\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"item\", ctx.activeItem)(\"templates\", ctx.templates);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showItemNavigators);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.captionFacet || ctx.galleria.captionTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showIndicators);\n      }\n    },\n    dependencies: () => [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, ChevronRightIcon, ChevronLeftIcon, GalleriaItemSlot],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(GalleriaItem, [{\n    type: Component,\n    args: [{\n      selector: 'p-galleriaItem',\n      standalone: false,\n      template: `\n        <div class=\"p-galleria-items\">\n            <button\n                *ngIf=\"showItemNavigators\"\n                type=\"button\"\n                role=\"navigation\"\n                [ngClass]=\"{ 'p-galleria-prev-button p-galleria-nav-button': true, 'p-disabled': this.isNavBackwardDisabled() }\"\n                (click)=\"navBackward($event)\"\n                [disabled]=\"isNavBackwardDisabled()\"\n                (focus)=\"onButtonFocus('left')\"\n                (blur)=\"onButtonBlur('left')\"\n            >\n                <ChevronLeftIcon *ngIf=\"!galleria.itemPreviousIconTemplate && !galleria._itemPreviousIconTemplate\" [styleClass]=\"'p-galleria-prev-icon'\" />\n                <ng-template *ngTemplateOutlet=\"galleria.itemPreviousIconTemplate || galleria._itemPreviousIconTemplate\"></ng-template>\n            </button>\n            <div [id]=\"id + '_item_' + activeIndex\" role=\"group\" class=\"p-galleria-item\" [attr.aria-label]=\"ariaSlideNumber(activeIndex + 1)\" [attr.aria-roledescription]=\"ariaSlideLabel()\">\n                <p-galleriaItemSlot type=\"item\" [item]=\"activeItem\" [templates]=\"templates\" class=\"p-galleria-item\"></p-galleriaItemSlot>\n            </div>\n            <button\n                *ngIf=\"showItemNavigators\"\n                type=\"button\"\n                [ngClass]=\"{ 'p-galleria-next-button p-galleria-nav-button': true, 'p-disabled': this.isNavForwardDisabled() }\"\n                (click)=\"navForward($event)\"\n                [disabled]=\"isNavForwardDisabled()\"\n                role=\"navigation\"\n                (focus)=\"onButtonFocus('right')\"\n                (blur)=\"onButtonBlur('right')\"\n            >\n                <ChevronRightIcon *ngIf=\"!galleria.itemNextIconTemplate && !galleria._itemNextIconTemplate\" [styleClass]=\"'p-galleria-next-icon'\" />\n                <ng-template *ngTemplateOutlet=\"galleria.itemNextIconTemplate || galleria._itemNextIconTemplate\"></ng-template>\n            </button>\n            <div class=\"p-galleria-caption\" *ngIf=\"captionFacet || galleria.captionTemplate\">\n                <p-galleriaItemSlot type=\"caption\" [item]=\"activeItem\" [templates]=\"templates\"></p-galleriaItemSlot>\n            </div>\n        </div>\n        <ul *ngIf=\"showIndicators\" class=\"p-galleria-indicator-list\">\n            <li\n                *ngFor=\"let item of value; let index = index\"\n                tabindex=\"0\"\n                (click)=\"onIndicatorClick(index)\"\n                (mouseenter)=\"onIndicatorMouseEnter(index)\"\n                (keydown)=\"onIndicatorKeyDown($event, index)\"\n                [ngClass]=\"{ 'p-galleria-indicator': true, 'p-galleria-indicator-active': isIndicatorItemActive(index) }\"\n                [attr.aria-label]=\"ariaPageLabel(index + 1)\"\n                [attr.aria-selected]=\"activeIndex === index\"\n                [attr.aria-controls]=\"id + '_item_' + index\"\n            >\n                <button type=\"button\" tabIndex=\"-1\" class=\"p-galleria-indicator-button\" *ngIf=\"!indicatorFacet && !galleria.indicatorTemplate\"></button>\n                <p-galleriaItemSlot type=\"indicator\" [index]=\"index\" [templates]=\"templates\"></p-galleriaItemSlot>\n            </li>\n        </ul>\n    `,\n      host: {\n        class: 'p-galleria-items-container'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], () => [{\n    type: Galleria\n  }], {\n    id: [{\n      type: Input\n    }],\n    circular: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    value: [{\n      type: Input\n    }],\n    showItemNavigators: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    showIndicators: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    slideShowActive: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    changeItemOnIndicatorHover: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    autoPlay: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    templates: [{\n      type: Input\n    }],\n    indicatorFacet: [{\n      type: Input\n    }],\n    captionFacet: [{\n      type: Input\n    }],\n    startSlideShow: [{\n      type: Output\n    }],\n    stopSlideShow: [{\n      type: Output\n    }],\n    onActiveIndexChange: [{\n      type: Output\n    }],\n    activeIndex: [{\n      type: Input\n    }]\n  });\n})();\nclass GalleriaThumbnails {\n  galleria;\n  document;\n  platformId;\n  renderer;\n  cd;\n  containerId;\n  value;\n  isVertical = false;\n  slideShowActive = false;\n  circular = false;\n  responsiveOptions;\n  contentHeight = '300px';\n  showThumbnailNavigators = true;\n  templates;\n  onActiveIndexChange = new EventEmitter();\n  stopSlideShow = new EventEmitter();\n  itemsContainer;\n  get numVisible() {\n    return this._numVisible;\n  }\n  set numVisible(numVisible) {\n    this._numVisible = numVisible;\n    this._oldNumVisible = this.d_numVisible;\n    this.d_numVisible = numVisible;\n  }\n  get activeIndex() {\n    return this._activeIndex;\n  }\n  set activeIndex(activeIndex) {\n    this._oldactiveIndex = this._activeIndex;\n    this._activeIndex = activeIndex;\n  }\n  index;\n  startPos = null;\n  thumbnailsStyle = null;\n  sortedResponsiveOptions = null;\n  totalShiftedItems = 0;\n  page = 0;\n  documentResizeListener;\n  _numVisible = 0;\n  d_numVisible = 0;\n  _oldNumVisible = 0;\n  _activeIndex = 0;\n  _oldactiveIndex = 0;\n  constructor(galleria, document, platformId, renderer, cd) {\n    this.galleria = galleria;\n    this.document = document;\n    this.platformId = platformId;\n    this.renderer = renderer;\n    this.cd = cd;\n  }\n  ngOnInit() {\n    if (isPlatformBrowser(this.platformId)) {\n      this.createStyle();\n      if (this.responsiveOptions) {\n        this.bindDocumentListeners();\n      }\n    }\n  }\n  ngAfterContentChecked() {\n    let totalShiftedItems = this.totalShiftedItems;\n    if ((this._oldNumVisible !== this.d_numVisible || this._oldactiveIndex !== this._activeIndex) && this.itemsContainer) {\n      if (this._activeIndex <= this.getMedianItemIndex()) {\n        totalShiftedItems = 0;\n      } else if (this.value.length - this.d_numVisible + this.getMedianItemIndex() < this._activeIndex) {\n        totalShiftedItems = this.d_numVisible - this.value.length;\n      } else if (this.value.length - this.d_numVisible < this._activeIndex && this.d_numVisible % 2 === 0) {\n        totalShiftedItems = this._activeIndex * -1 + this.getMedianItemIndex() + 1;\n      } else {\n        totalShiftedItems = this._activeIndex * -1 + this.getMedianItemIndex();\n      }\n      if (totalShiftedItems !== this.totalShiftedItems) {\n        this.totalShiftedItems = totalShiftedItems;\n      }\n      if (this.itemsContainer && this.itemsContainer.nativeElement) {\n        this.itemsContainer.nativeElement.style.transform = this.isVertical ? `translate3d(0, ${totalShiftedItems * (100 / this.d_numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this.d_numVisible)}%, 0, 0)`;\n      }\n      if (this._oldactiveIndex !== this._activeIndex) {\n        removeClass(this.itemsContainer.nativeElement, 'p-items-hidden');\n        this.itemsContainer.nativeElement.style.transition = 'transform 500ms ease 0s';\n      }\n      this._oldactiveIndex = this._activeIndex;\n      this._oldNumVisible = this.d_numVisible;\n    }\n  }\n  ngAfterViewInit() {\n    if (isPlatformBrowser(this.platformId)) {\n      this.calculatePosition();\n    }\n  }\n  createStyle() {\n    if (!this.thumbnailsStyle) {\n      this.thumbnailsStyle = this.document.createElement('style');\n      this.document.body.appendChild(this.thumbnailsStyle);\n    }\n    let innerHTML = `\n            #${this.containerId} .p-galleria-thumbnail-item {\n                flex: 1 0 ${100 / this.d_numVisible}%\n            }\n        `;\n    if (this.responsiveOptions) {\n      this.sortedResponsiveOptions = [...this.responsiveOptions];\n      this.sortedResponsiveOptions.sort((data1, data2) => {\n        const value1 = data1.breakpoint;\n        const value2 = data2.breakpoint;\n        let result = null;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2, undefined, {\n          numeric: true\n        });else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return -1 * result;\n      });\n      for (let i = 0; i < this.sortedResponsiveOptions.length; i++) {\n        let res = this.sortedResponsiveOptions[i];\n        innerHTML += `\n                    @media screen and (max-width: ${res.breakpoint}) {\n                        #${this.containerId} .p-galleria-thumbnail-item {\n                            flex: 1 0 ${100 / res.numVisible}%\n                        }\n                    }\n                `;\n      }\n    }\n    this.thumbnailsStyle.innerHTML = innerHTML;\n    setAttribute(this.thumbnailsStyle, 'nonce', this.galleria.config?.csp()?.nonce);\n  }\n  calculatePosition() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (this.itemsContainer && this.sortedResponsiveOptions) {\n        let windowWidth = window.innerWidth;\n        let matchedResponsiveData = {\n          numVisible: this._numVisible\n        };\n        for (let i = 0; i < this.sortedResponsiveOptions.length; i++) {\n          let res = this.sortedResponsiveOptions[i];\n          if (parseInt(res.breakpoint, 10) >= windowWidth) {\n            matchedResponsiveData = res;\n          }\n        }\n        if (this.d_numVisible !== matchedResponsiveData.numVisible) {\n          this.d_numVisible = matchedResponsiveData.numVisible;\n          this.cd.markForCheck();\n        }\n      }\n    }\n  }\n  getTabIndex(index) {\n    return this.isItemActive(index) ? 0 : null;\n  }\n  navForward(e) {\n    this.stopTheSlideShow();\n    let nextItemIndex = this._activeIndex + 1;\n    if (nextItemIndex + this.totalShiftedItems > this.getMedianItemIndex() && (-1 * this.totalShiftedItems < this.getTotalPageNumber() - 1 || this.circular)) {\n      this.step(-1);\n    }\n    let activeIndex = this.circular && this.value.length - 1 === this._activeIndex ? 0 : nextItemIndex;\n    this.onActiveIndexChange.emit(activeIndex);\n    if (e.cancelable) {\n      e.preventDefault();\n    }\n  }\n  navBackward(e) {\n    this.stopTheSlideShow();\n    let prevItemIndex = this._activeIndex !== 0 ? this._activeIndex - 1 : 0;\n    let diff = prevItemIndex + this.totalShiftedItems;\n    if (this.d_numVisible - diff - 1 > this.getMedianItemIndex() && (-1 * this.totalShiftedItems !== 0 || this.circular)) {\n      this.step(1);\n    }\n    let activeIndex = this.circular && this._activeIndex === 0 ? this.value.length - 1 : prevItemIndex;\n    this.onActiveIndexChange.emit(activeIndex);\n    if (e.cancelable) {\n      e.preventDefault();\n    }\n  }\n  onItemClick(index) {\n    this.stopTheSlideShow();\n    let selectedItemIndex = index;\n    if (selectedItemIndex !== this._activeIndex) {\n      const diff = selectedItemIndex + this.totalShiftedItems;\n      let dir = 0;\n      if (selectedItemIndex < this._activeIndex) {\n        dir = this.d_numVisible - diff - 1 - this.getMedianItemIndex();\n        if (dir > 0 && -1 * this.totalShiftedItems !== 0) {\n          this.step(dir);\n        }\n      } else {\n        dir = this.getMedianItemIndex() - diff;\n        if (dir < 0 && -1 * this.totalShiftedItems < this.getTotalPageNumber() - 1) {\n          this.step(dir);\n        }\n      }\n      this.activeIndex = selectedItemIndex;\n      this.onActiveIndexChange.emit(this.activeIndex);\n    }\n  }\n  onThumbnailKeydown(event, index) {\n    if (event.code === 'Enter' || event.code === 'Space') {\n      this.onItemClick(index);\n      event.preventDefault();\n    }\n    switch (event.code) {\n      case 'ArrowRight':\n        this.onRightKey();\n        break;\n      case 'ArrowLeft':\n        this.onLeftKey();\n        break;\n      case 'Home':\n        this.onHomeKey();\n        event.preventDefault();\n        break;\n      case 'End':\n        this.onEndKey();\n        event.preventDefault();\n        break;\n      case 'ArrowUp':\n      case 'ArrowDown':\n        event.preventDefault();\n        break;\n      case 'Tab':\n        this.onTabKey();\n        break;\n      default:\n        break;\n    }\n  }\n  onRightKey() {\n    const indicators = find(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"]');\n    const activeIndex = this.findFocusedIndicatorIndex();\n    this.changedFocusedIndicator(activeIndex, activeIndex + 1 === indicators.length ? indicators.length - 1 : activeIndex + 1);\n  }\n  onLeftKey() {\n    const activeIndex = this.findFocusedIndicatorIndex();\n    this.changedFocusedIndicator(activeIndex, activeIndex - 1 <= 0 ? 0 : activeIndex - 1);\n  }\n  onHomeKey() {\n    const activeIndex = this.findFocusedIndicatorIndex();\n    this.changedFocusedIndicator(activeIndex, 0);\n  }\n  onEndKey() {\n    const indicators = find(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"]');\n    const activeIndex = this.findFocusedIndicatorIndex();\n    this.changedFocusedIndicator(activeIndex, indicators.length - 1);\n  }\n  onTabKey() {\n    const indicators = [...find(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"]')];\n    const highlightedIndex = indicators.findIndex(ind => getAttribute(ind, 'data-p-active') === true);\n    const activeIndicator = findSingle(this.itemsContainer.nativeElement, '[tabindex=\"0\"]');\n    const activeIndex = indicators.findIndex(ind => ind === activeIndicator.parentElement);\n    indicators[activeIndex].children[0].tabIndex = '-1';\n    indicators[highlightedIndex].children[0].tabIndex = '0';\n  }\n  findFocusedIndicatorIndex() {\n    const indicators = [...find(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"]')];\n    const activeIndicator = findSingle(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"] > [tabindex=\"0\"]');\n    return indicators.findIndex(ind => ind === activeIndicator.parentElement);\n  }\n  changedFocusedIndicator(prevInd, nextInd) {\n    const indicators = find(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"]');\n    indicators[prevInd].children[0].tabIndex = '-1';\n    indicators[nextInd].children[0].tabIndex = '0';\n    indicators[nextInd].children[0].focus();\n  }\n  step(dir) {\n    let totalShiftedItems = this.totalShiftedItems + dir;\n    if (dir < 0 && -1 * totalShiftedItems + this.d_numVisible > this.value.length - 1) {\n      totalShiftedItems = this.d_numVisible - this.value.length;\n    } else if (dir > 0 && totalShiftedItems > 0) {\n      totalShiftedItems = 0;\n    }\n    if (this.circular) {\n      if (dir < 0 && this.value.length - 1 === this._activeIndex) {\n        totalShiftedItems = 0;\n      } else if (dir > 0 && this._activeIndex === 0) {\n        totalShiftedItems = this.d_numVisible - this.value.length;\n      }\n    }\n    if (this.itemsContainer) {\n      removeClass(this.itemsContainer.nativeElement, 'p-items-hidden');\n      this.itemsContainer.nativeElement.style.transform = this.isVertical ? `translate3d(0, ${totalShiftedItems * (100 / this.d_numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this.d_numVisible)}%, 0, 0)`;\n      this.itemsContainer.nativeElement.style.transition = 'transform 500ms ease 0s';\n    }\n    this.totalShiftedItems = totalShiftedItems;\n  }\n  stopTheSlideShow() {\n    if (this.slideShowActive && this.stopSlideShow) {\n      this.stopSlideShow.emit();\n    }\n  }\n  changePageOnTouch(e, diff) {\n    if (diff < 0) {\n      // left\n      this.navForward(e);\n    } else {\n      // right\n      this.navBackward(e);\n    }\n  }\n  getTotalPageNumber() {\n    return this.value.length > this.d_numVisible ? this.value.length - this.d_numVisible + 1 : 0;\n  }\n  getMedianItemIndex() {\n    let index = Math.floor(this.d_numVisible / 2);\n    return this.d_numVisible % 2 ? index : index - 1;\n  }\n  onTransitionEnd() {\n    if (this.itemsContainer && this.itemsContainer.nativeElement) {\n      addClass(this.itemsContainer.nativeElement, 'p-items-hidden');\n      this.itemsContainer.nativeElement.style.transition = '';\n    }\n  }\n  onTouchEnd(e) {\n    let touchobj = e.changedTouches[0];\n    if (this.isVertical) {\n      this.changePageOnTouch(e, touchobj.pageY - this.startPos.y);\n    } else {\n      this.changePageOnTouch(e, touchobj.pageX - this.startPos.x);\n    }\n  }\n  onTouchMove(e) {\n    if (e.cancelable) {\n      e.preventDefault();\n    }\n  }\n  onTouchStart(e) {\n    let touchobj = e.changedTouches[0];\n    this.startPos = {\n      x: touchobj.pageX,\n      y: touchobj.pageY\n    };\n  }\n  isNavBackwardDisabled() {\n    return !this.circular && this._activeIndex === 0 || this.value.length <= this.d_numVisible;\n  }\n  isNavForwardDisabled() {\n    return !this.circular && this._activeIndex === this.value.length - 1 || this.value.length <= this.d_numVisible;\n  }\n  firstItemAciveIndex() {\n    return this.totalShiftedItems * -1;\n  }\n  lastItemActiveIndex() {\n    return this.firstItemAciveIndex() + this.d_numVisible - 1;\n  }\n  isItemActive(index) {\n    return this.firstItemAciveIndex() <= index && this.lastItemActiveIndex() >= index;\n  }\n  bindDocumentListeners() {\n    if (isPlatformBrowser(this.platformId)) {\n      const window = this.document.defaultView || 'window';\n      this.documentResizeListener = this.renderer.listen(window, 'resize', () => {\n        this.calculatePosition();\n      });\n    }\n  }\n  unbindDocumentListeners() {\n    if (this.documentResizeListener) {\n      this.documentResizeListener();\n      this.documentResizeListener = null;\n    }\n  }\n  ngOnDestroy() {\n    if (this.responsiveOptions) {\n      this.unbindDocumentListeners();\n    }\n    if (this.thumbnailsStyle) {\n      this.thumbnailsStyle.parentNode?.removeChild(this.thumbnailsStyle);\n    }\n  }\n  ariaPrevButtonLabel() {\n    return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.prevPageLabel : undefined;\n  }\n  ariaNextButtonLabel() {\n    return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.nextPageLabel : undefined;\n  }\n  ariaPageLabel(value) {\n    return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.pageLabel.replace(/{page}/g, value) : undefined;\n  }\n  static ɵfac = function GalleriaThumbnails_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || GalleriaThumbnails)(i0.ɵɵdirectiveInject(Galleria), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: GalleriaThumbnails,\n    selectors: [[\"p-galleriaThumbnails\"]],\n    viewQuery: function GalleriaThumbnails_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c22, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.itemsContainer = _t.first);\n      }\n    },\n    inputs: {\n      containerId: \"containerId\",\n      value: \"value\",\n      isVertical: [2, \"isVertical\", \"isVertical\", booleanAttribute],\n      slideShowActive: [2, \"slideShowActive\", \"slideShowActive\", booleanAttribute],\n      circular: [2, \"circular\", \"circular\", booleanAttribute],\n      responsiveOptions: \"responsiveOptions\",\n      contentHeight: \"contentHeight\",\n      showThumbnailNavigators: \"showThumbnailNavigators\",\n      templates: \"templates\",\n      numVisible: \"numVisible\",\n      activeIndex: \"activeIndex\"\n    },\n    outputs: {\n      onActiveIndexChange: \"onActiveIndexChange\",\n      stopSlideShow: \"stopSlideShow\"\n    },\n    standalone: false,\n    features: [i0.ɵɵInputTransformsFeature],\n    decls: 8,\n    vars: 6,\n    consts: [[\"itemsContainer\", \"\"], [1, \"p-galleria-thumbnails\"], [1, \"p-galleria-thumbnails-content\"], [\"type\", \"button\", \"pRipple\", \"\", 3, \"ngClass\", \"disabled\", \"click\", 4, \"ngIf\"], [1, \"p-galleria-thumbnails-viewport\", 3, \"ngStyle\"], [\"role\", \"tablist\", 1, \"p-galleria-thumbnail-items\", 3, \"transitionend\", \"touchstart\", \"touchmove\"], [3, \"ngClass\", \"keydown\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"pRipple\", \"\", 3, \"click\", \"ngClass\", \"disabled\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"styleClass\"], [3, \"keydown\", \"ngClass\"], [1, \"p-galleria-thumbnail\", 3, \"click\", \"touchend\", \"keydown.enter\"], [\"type\", \"thumbnail\", 3, \"item\", \"templates\"], [3, \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\"]],\n    template: function GalleriaThumbnails_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n        i0.ɵɵtemplate(2, GalleriaThumbnails_button_2_Template, 3, 7, \"button\", 3);\n        i0.ɵɵelementStart(3, \"div\", 4)(4, \"div\", 5, 0);\n        i0.ɵɵlistener(\"transitionend\", function GalleriaThumbnails_Template_div_transitionend_4_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onTransitionEnd());\n        })(\"touchstart\", function GalleriaThumbnails_Template_div_touchstart_4_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onTouchStart($event));\n        })(\"touchmove\", function GalleriaThumbnails_Template_div_touchmove_4_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onTouchMove($event));\n        });\n        i0.ɵɵtemplate(6, GalleriaThumbnails_div_6_Template, 3, 15, \"div\", 6);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(7, GalleriaThumbnails_button_7_Template, 3, 7, \"button\", 3);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.showThumbnailNavigators);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c23, ctx.isVertical ? ctx.contentHeight : \"\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.value);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showThumbnailNavigators);\n      }\n    },\n    dependencies: () => [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, Ripple, ChevronRightIcon, ChevronUpIcon, ChevronDownIcon, ChevronLeftIcon, GalleriaItemSlot],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(GalleriaThumbnails, [{\n    type: Component,\n    args: [{\n      selector: 'p-galleriaThumbnails',\n      standalone: false,\n      template: `\n        <div class=\"p-galleria-thumbnails\">\n            <div class=\"p-galleria-thumbnails-content\">\n                <button\n                    *ngIf=\"showThumbnailNavigators\"\n                    type=\"button\"\n                    [ngClass]=\"{\n                        'p-galleria-thumbnail-prev-button p-galleria-thumbnail-nav-button': true,\n                        'p-disabled': this.isNavBackwardDisabled()\n                    }\"\n                    (click)=\"navBackward($event)\"\n                    [disabled]=\"isNavBackwardDisabled()\"\n                    pRipple\n                    [attr.aria-label]=\"ariaPrevButtonLabel()\"\n                >\n                    <ng-container *ngIf=\"!galleria.previousThumbnailIconTemplate && !galleria._previousThumbnailIconTemplate\">\n                        <ChevronLeftIcon *ngIf=\"!isVertical\" [styleClass]=\"'p-galleria-thumbnail-prev-icon'\" />\n                        <ChevronUpIcon *ngIf=\"isVertical\" [styleClass]=\"'p-galleria-thumbnail-prev-icon'\" />\n                    </ng-container>\n                    <ng-template *ngTemplateOutlet=\"galleria.previousThumbnailIconTemplate || galleria._previousThumbnailIconTemplate\"></ng-template>\n                </button>\n                <div class=\"p-galleria-thumbnails-viewport\" [ngStyle]=\"{ height: isVertical ? contentHeight : '' }\">\n                    <div #itemsContainer class=\"p-galleria-thumbnail-items\" (transitionend)=\"onTransitionEnd()\" (touchstart)=\"onTouchStart($event)\" (touchmove)=\"onTouchMove($event)\" role=\"tablist\">\n                        <div\n                            *ngFor=\"let item of value; let index = index\"\n                            [ngClass]=\"{\n                                'p-galleria-thumbnail-item': true,\n                                'p-galleria-thumbnail-item-current': activeIndex === index,\n                                'p-galleria-thumbnail-item-active': isItemActive(index),\n                                'p-galleria-thumbnail-item-start': firstItemAciveIndex() === index,\n                                'p-galleria-thumbnail-item-end': lastItemActiveIndex() === index\n                            }\"\n                            [attr.aria-selected]=\"activeIndex === index\"\n                            [attr.aria-controls]=\"containerId + '_item_' + index\"\n                            [attr.data-pc-section]=\"'thumbnailitem'\"\n                            [attr.data-p-active]=\"activeIndex === index\"\n                            (keydown)=\"onThumbnailKeydown($event, index)\"\n                        >\n                            <div\n                                class=\"p-galleria-thumbnail\"\n                                [attr.tabindex]=\"activeIndex === index ? 0 : -1\"\n                                [attr.aria-current]=\"activeIndex === index ? 'page' : undefined\"\n                                [attr.aria-label]=\"ariaPageLabel(index + 1)\"\n                                (click)=\"onItemClick(index)\"\n                                (touchend)=\"onItemClick(index)\"\n                                (keydown.enter)=\"onItemClick(index)\"\n                            >\n                                <p-galleriaItemSlot type=\"thumbnail\" [item]=\"item\" [templates]=\"templates\"></p-galleriaItemSlot>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n                <button\n                    *ngIf=\"showThumbnailNavigators\"\n                    type=\"button\"\n                    [ngClass]=\"{\n                        'p-galleria-thumbnail-next-button p-galleria-thumbnail-nav-button': true,\n                        'p-disabled': this.isNavForwardDisabled()\n                    }\"\n                    (click)=\"navForward($event)\"\n                    [disabled]=\"isNavForwardDisabled()\"\n                    pRipple\n                    [attr.aria-label]=\"ariaNextButtonLabel()\"\n                >\n                    <ng-container *ngIf=\"!galleria.nextThumbnailIconTemplate && !galleria._nextThumbnailIconTemplate\">\n                        <ChevronRightIcon *ngIf=\"!isVertical\" [ngClass]=\"'p-galleria-thumbnail-next-icon'\" />\n                        <ChevronDownIcon *ngIf=\"isVertical\" [ngClass]=\"'p-galleria-thumbnail-next-icon'\" />\n                    </ng-container>\n                    <ng-template *ngTemplateOutlet=\"galleria.nextThumbnailIconTemplate || galleria._nextThumbnailIconTemplate\"></ng-template>\n                </button>\n            </div>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], () => [{\n    type: Galleria\n  }, {\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    containerId: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    isVertical: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    slideShowActive: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    circular: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    responsiveOptions: [{\n      type: Input\n    }],\n    contentHeight: [{\n      type: Input\n    }],\n    showThumbnailNavigators: [{\n      type: Input\n    }],\n    templates: [{\n      type: Input\n    }],\n    onActiveIndexChange: [{\n      type: Output\n    }],\n    stopSlideShow: [{\n      type: Output\n    }],\n    itemsContainer: [{\n      type: ViewChild,\n      args: ['itemsContainer']\n    }],\n    numVisible: [{\n      type: Input\n    }],\n    activeIndex: [{\n      type: Input\n    }]\n  });\n})();\nclass GalleriaModule {\n  static ɵfac = function GalleriaModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || GalleriaModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: GalleriaModule,\n    declarations: [Galleria, GalleriaContent, GalleriaItemSlot, GalleriaItem, GalleriaThumbnails],\n    imports: [CommonModule, SharedModule, Ripple, TimesIcon, ChevronRightIcon, ChevronUpIcon, ChevronDownIcon, ChevronLeftIcon, WindowMaximizeIcon, WindowMinimizeIcon, FocusTrap],\n    exports: [CommonModule, Galleria, GalleriaContent, GalleriaItemSlot, GalleriaItem, GalleriaThumbnails, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, SharedModule, TimesIcon, ChevronRightIcon, ChevronUpIcon, ChevronDownIcon, ChevronLeftIcon, WindowMaximizeIcon, WindowMinimizeIcon, CommonModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(GalleriaModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, SharedModule, Ripple, TimesIcon, ChevronRightIcon, ChevronUpIcon, ChevronDownIcon, ChevronLeftIcon, WindowMaximizeIcon, WindowMinimizeIcon, FocusTrap],\n      exports: [CommonModule, Galleria, GalleriaContent, GalleriaItemSlot, GalleriaItem, GalleriaThumbnails, SharedModule],\n      declarations: [Galleria, GalleriaContent, GalleriaItemSlot, GalleriaItem, GalleriaThumbnails]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Galleria, GalleriaClasses, GalleriaContent, GalleriaItem, GalleriaItemSlot, GalleriaModule, GalleriaStyle, GalleriaThumbnails };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,IAAM,MAAM,CAAC,QAAQ;AACrB,IAAM,MAAM,CAAC,QAAQ;AACrB,IAAM,MAAM,CAAC,WAAW;AACxB,IAAM,MAAM,CAAC,SAAS;AACtB,IAAM,MAAM,CAAC,WAAW;AACxB,IAAM,MAAM,CAAC,uBAAuB;AACpC,IAAM,MAAM,CAAC,mBAAmB;AAChC,IAAM,MAAM,CAAC,kBAAkB;AAC/B,IAAM,MAAM,CAAC,cAAc;AAC3B,IAAM,MAAM,CAAC,MAAM;AACnB,IAAM,OAAO,CAAC,WAAW;AACzB,IAAM,OAAO,CAAC,MAAM;AACpB,IAAM,OAAO,CAAC,WAAW;AACzB,IAAM,OAAO,OAAO;AAAA,EAClB,uDAAuD;AACzD;AACA,IAAM,OAAO,CAAC,IAAI,QAAQ;AAAA,EACxB,sBAAsB;AAAA,EACtB,sBAAsB;AACxB;AACA,IAAM,OAAO,SAAO;AAAA,EAClB,OAAO;AAAA,EACP,QAAQ;AACV;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,qBAAqB,CAAC;AAC3C,IAAG,WAAW,oBAAoB,SAAS,yGAAyG,QAAQ;AAC1J,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC,EAAE,mBAAmB,SAAS,wGAAwG,QAAQ;AAC7I,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,eAAe,MAAM,CAAC;AAAA,IACrD,CAAC,EAAE,YAAY,SAAS,0FAA0F;AAChH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,WAAW,CAAC;AAAA,IAC3C,CAAC,EAAE,oBAAoB,SAAS,gGAAgG,QAAQ;AACtI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,mBAAmB,MAAM,CAAC;AAAA,IACzD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,cAAiB,gBAAgB,GAAG,MAAS,gBAAgB,GAAG,MAAM,OAAO,uBAAuB,OAAO,qBAAqB,CAAC,CAAC,EAAE,SAAS,OAAO,KAAK,EAAE,eAAe,OAAO,WAAW,EAAE,cAAc,OAAO,mBAAmB,OAAO,UAAU,EAAE,WAAW,OAAO,cAAc,EAAE,cAAc,OAAO,UAAU;AAAA,EAC1U;AACF;AACA,SAAS,8BAA8B,IAAI,KAAK;AAC9C,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,IAAG,WAAW,GAAG,mDAAmD,GAAG,IAAI,qBAAqB,CAAC;AACjG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,SAAS;AAC9B,IAAG,WAAW,WAAc,gBAAgB,GAAG,IAAI,CAAC;AACpD,IAAG,YAAY,QAAQ,OAAO,aAAa,WAAW,QAAQ,EAAE,cAAc,OAAO,aAAa,SAAS,MAAS;AACpH,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,OAAO;AAAA,EACtC;AACF;AACA,SAAS,wBAAwB,IAAI,KAAK;AACxC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,MAAM,CAAC;AACnC,IAAG,WAAW,GAAG,+BAA+B,GAAG,GAAG,OAAO,CAAC;AAC9D,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,WAAW;AAAA,EAC1C;AACF;AACA,SAAS,gCAAgC,IAAI,KAAK;AAChD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,qBAAqB,CAAC;AAC3C,IAAG,WAAW,oBAAoB,SAAS,8EAA8E,QAAQ;AAC/H,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,mBAAmB,MAAM,CAAC;AAAA,IACzD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,SAAS,OAAO,KAAK,EAAE,eAAe,OAAO,WAAW,EAAE,cAAc,OAAO,mBAAmB,OAAO,UAAU;AAAA,EACnI;AACF;AACA,IAAM,OAAO,CAAC,aAAa;AAC3B,IAAM,OAAO,CAAC,IAAI,IAAI,QAAQ;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,+BAA+B;AACjC;AACA,IAAM,OAAO,OAAO,CAAC;AACrB,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,aAAa,EAAE;AAAA,EACjC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,uBAAuB;AAAA,EACrD;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AAAC;AAC3E,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,aAAa;AAAA,EAC/F;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,kEAAkE;AAChG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,SAAS,KAAK,CAAC;AAAA,IAC9C,CAAC;AACD,IAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,aAAa,CAAC,EAAE,GAAG,2CAA2C,GAAG,GAAG,MAAM,EAAE;AACxJ,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,cAAc,OAAO,eAAe,CAAC,EAAE,mBAAmB,aAAa;AACtF,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,SAAS,qBAAqB,CAAC,OAAO,SAAS,kBAAkB;AAC/F,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,SAAS,qBAAqB,OAAO,SAAS,kBAAkB;AAAA,EAC3G;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,UAAU,GAAG,sBAAsB,EAAE;AACxC,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,aAAa,OAAO,SAAS,SAAS;AAAA,EACtD;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,wBAAwB,EAAE;AAC/C,IAAG,WAAW,uBAAuB,SAAS,0GAA0G,QAAQ;AAC9J,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,oBAAoB,MAAM,CAAC;AAAA,IAC1D,CAAC,EAAE,iBAAiB,SAAS,sGAAsG;AACjI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,cAAc,CAAC;AAAA,IAC9C,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,eAAe,OAAO,EAAE,EAAE,SAAS,OAAO,KAAK,EAAE,eAAe,OAAO,WAAW,EAAE,aAAa,OAAO,SAAS,SAAS,EAAE,cAAc,OAAO,UAAU,EAAE,qBAAqB,OAAO,SAAS,iBAAiB,EAAE,YAAY,OAAO,SAAS,QAAQ,EAAE,cAAc,OAAO,WAAW,CAAC,EAAE,iBAAiB,OAAO,SAAS,+BAA+B,EAAE,2BAA2B,OAAO,SAAS,uBAAuB,EAAE,mBAAmB,OAAO,eAAe;AAAA,EAC7d;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,UAAU,GAAG,sBAAsB,EAAE;AACxC,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,aAAa,OAAO,SAAS,SAAS;AAAA,EACtD;AACF;AACA,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,yCAAyC,GAAG,GAAG,UAAU,CAAC,EAAE,GAAG,sCAAsC,GAAG,GAAG,OAAO,CAAC;AACpI,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,kBAAkB,CAAC;AACrD,IAAG,WAAW,uBAAuB,SAAS,6EAA6E,QAAQ;AACjI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,oBAAoB,MAAM,CAAC;AAAA,IAC1D,CAAC,EAAE,kBAAkB,SAAS,0EAA0E;AACtG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,eAAe,CAAC;AAAA,IAC/C,CAAC,EAAE,iBAAiB,SAAS,yEAAyE;AACpG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,CAAC;AAAA,IAC9C,CAAC;AACD,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,uDAAuD,GAAG,IAAI,wBAAwB,CAAC;AACxG,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,sCAAsC,GAAG,GAAG,OAAO,CAAC;AACrE,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,cAAc,CAAC;AACpC,IAAG,WAAW,WAAc,gBAAgB,IAAI,MAAM,OAAO,SAAS,YAAY,OAAO,SAAS,sBAAsB,OAAO,SAAS,6BAA6B,CAAC,OAAO,SAAS,UAAU,CAAC,EAAE,WAAW,CAAC,OAAO,SAAS,aAAa,OAAO,SAAS,iBAAoB,gBAAgB,IAAI,IAAI,CAAC,EAAE,sBAAsB,CAAC,OAAO,UAAU;AACnV,IAAG,YAAY,MAAM,OAAO,EAAE,EAAE,QAAQ,QAAQ;AAChD,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,SAAS,UAAU;AAChD,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,SAAS,cAAc,OAAO,SAAS,eAAe,OAAO,SAAS,eAAe;AAClH,IAAG,UAAU;AACb,IAAG,YAAY,aAAa,OAAO,SAAS,WAAW,WAAW,KAAK;AACvE,IAAG,UAAU;AACb,IAAG,WAAW,MAAM,OAAO,EAAE,EAAE,SAAS,OAAO,KAAK,EAAE,eAAe,OAAO,WAAW,EAAE,YAAY,OAAO,SAAS,QAAQ,EAAE,aAAa,OAAO,SAAS,SAAS,EAAE,kBAAkB,OAAO,SAAS,cAAc,EAAE,8BAA8B,OAAO,SAAS,0BAA0B,EAAE,kBAAkB,OAAO,SAAS,cAAc,EAAE,gBAAgB,OAAO,SAAS,YAAY,EAAE,sBAAsB,OAAO,SAAS,kBAAkB,EAAE,YAAY,OAAO,SAAS,QAAQ,EAAE,mBAAmB,OAAO,eAAe;AAC5gB,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,SAAS,cAAc;AACpD,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,mBAAmB,CAAC;AAAA,EACnD;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,gBAAgB,CAAC;AACjG,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,eAAe,EAAE,2BAA2B,OAAO,OAAO;AAAA,EACrG;AACF;AACA,IAAM,OAAO,SAAO;AAAA,EAClB,gDAAgD;AAAA,EAChD,cAAc;AAChB;AACA,IAAM,OAAO,SAAO;AAAA,EAClB,gDAAgD;AAAA,EAChD,cAAc;AAChB;AACA,IAAM,OAAO,SAAO;AAAA,EAClB,wBAAwB;AAAA,EACxB,+BAA+B;AACjC;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,mBAAmB,CAAC;AAAA,EACtC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,sBAAsB;AAAA,EACpD;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAAC;AAClE,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,aAAa;AAAA,EACtF;AACF;AACA,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,uDAAuD,QAAQ;AAC7F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,MAAM,CAAC;AAAA,IAClD,CAAC,EAAE,SAAS,SAAS,yDAAyD;AAC5E,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,MAAM,CAAC;AAAA,IACpD,CAAC,EAAE,QAAQ,SAAS,wDAAwD;AAC1E,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,aAAa,MAAM,CAAC;AAAA,IACnD,CAAC;AACD,IAAG,WAAW,GAAG,kDAAkD,GAAG,GAAG,mBAAmB,CAAC,EAAE,GAAG,kCAAkC,GAAG,GAAG,MAAM,CAAC;AACjJ,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAc,gBAAgB,GAAG,MAAM,OAAO,sBAAsB,CAAC,CAAC,EAAE,YAAY,OAAO,sBAAsB,CAAC;AAChI,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,SAAS,4BAA4B,CAAC,OAAO,SAAS,yBAAyB;AAC7G,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,SAAS,4BAA4B,OAAO,SAAS,yBAAyB;AAAA,EACzH;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,oBAAoB,CAAC;AAAA,EACvC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,sBAAsB;AAAA,EACpD;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAAC;AAClE,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,aAAa;AAAA,EACtF;AACF;AACA,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,uDAAuD,QAAQ;AAC7F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,WAAW,MAAM,CAAC;AAAA,IACjD,CAAC,EAAE,SAAS,SAAS,yDAAyD;AAC5E,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,OAAO,CAAC;AAAA,IACrD,CAAC,EAAE,QAAQ,SAAS,wDAAwD;AAC1E,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,aAAa,OAAO,CAAC;AAAA,IACpD,CAAC;AACD,IAAG,WAAW,GAAG,mDAAmD,GAAG,GAAG,oBAAoB,CAAC,EAAE,GAAG,kCAAkC,GAAG,GAAG,MAAM,CAAC;AACnJ,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAc,gBAAgB,GAAG,MAAM,OAAO,qBAAqB,CAAC,CAAC,EAAE,YAAY,OAAO,qBAAqB,CAAC;AAC9H,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,SAAS,wBAAwB,CAAC,OAAO,SAAS,qBAAqB;AACrG,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,SAAS,wBAAwB,OAAO,SAAS,qBAAqB;AAAA,EACjH;AACF;AACA,SAAS,4BAA4B,IAAI,KAAK;AAC5C,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,UAAU,GAAG,sBAAsB,EAAE;AACxC,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,UAAU,EAAE,aAAa,OAAO,SAAS;AAAA,EACxE;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,UAAU,EAAE;AAAA,EAC9B;AACF;AACA,SAAS,gCAAgC,IAAI,KAAK;AAChD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,EAAE;AAC7B,IAAG,WAAW,SAAS,SAAS,sDAAsD;AACpF,YAAM,WAAc,cAAc,GAAG,EAAE;AACvC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,iBAAiB,QAAQ,CAAC;AAAA,IACzD,CAAC,EAAE,cAAc,SAAS,2DAA2D;AACnF,YAAM,WAAc,cAAc,GAAG,EAAE;AACvC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,sBAAsB,QAAQ,CAAC;AAAA,IAC9D,CAAC,EAAE,WAAW,SAAS,sDAAsD,QAAQ;AACnF,YAAM,WAAc,cAAc,GAAG,EAAE;AACvC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,mBAAmB,QAAQ,QAAQ,CAAC;AAAA,IACnE,CAAC;AACD,IAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,UAAU,EAAE;AAC7E,IAAG,UAAU,GAAG,sBAAsB,EAAE;AACxC,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAc,gBAAgB,GAAG,MAAM,OAAO,sBAAsB,QAAQ,CAAC,CAAC;AAC5F,IAAG,YAAY,cAAc,OAAO,cAAc,WAAW,CAAC,CAAC,EAAE,iBAAiB,OAAO,gBAAgB,QAAQ,EAAE,iBAAiB,OAAO,KAAK,WAAW,QAAQ;AACnK,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,kBAAkB,CAAC,OAAO,SAAS,iBAAiB;AAClF,IAAG,UAAU;AACb,IAAG,WAAW,SAAS,QAAQ,EAAE,aAAa,OAAO,SAAS;AAAA,EAChE;AACF;AACA,SAAS,2BAA2B,IAAI,KAAK;AAC3C,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,EAAE;AAC7B,IAAG,WAAW,GAAG,iCAAiC,GAAG,GAAG,MAAM,EAAE;AAChE,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,KAAK;AAAA,EACvC;AACF;AACA,IAAM,OAAO,CAAC,gBAAgB;AAC9B,IAAM,OAAO,SAAO;AAAA,EAClB,QAAQ;AACV;AACA,IAAM,OAAO,SAAO;AAAA,EAClB,oEAAoE;AAAA,EACpE,cAAc;AAChB;AACA,IAAM,OAAO,CAAC,IAAI,IAAI,IAAI,QAAQ;AAAA,EAChC,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,iCAAiC;AACnC;AACA,IAAM,OAAO,SAAO;AAAA,EAClB,oEAAoE;AAAA,EACpE,cAAc;AAChB;AACA,SAAS,sEAAsE,IAAI,KAAK;AACtF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,mBAAmB,EAAE;AAAA,EACvC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,gCAAgC;AAAA,EAC9D;AACF;AACA,SAAS,oEAAoE,IAAI,KAAK;AACpF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,iBAAiB,EAAE;AAAA,EACrC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,gCAAgC;AAAA,EAC9D;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,uEAAuE,GAAG,GAAG,mBAAmB,EAAE,EAAE,GAAG,qEAAqE,GAAG,GAAG,iBAAiB,EAAE;AACtN,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,UAAU;AACxC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,UAAU;AAAA,EACzC;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AAAC;AACxE,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,sDAAsD,GAAG,GAAG,aAAa;AAAA,EAC5F;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,6DAA6D,QAAQ;AACnG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,MAAM,CAAC;AAAA,IAClD,CAAC;AACD,IAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,wCAAwC,GAAG,GAAG,MAAM,CAAC;AACvJ,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAc,gBAAgB,GAAG,MAAM,OAAO,sBAAsB,CAAC,CAAC,EAAE,YAAY,OAAO,sBAAsB,CAAC;AAChI,IAAG,YAAY,cAAc,OAAO,oBAAoB,CAAC;AACzD,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,SAAS,iCAAiC,CAAC,OAAO,SAAS,8BAA8B;AACvH,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,SAAS,iCAAiC,OAAO,SAAS,8BAA8B;AAAA,EACnI;AACF;AACA,SAAS,kCAAkC,IAAI,KAAK;AAClD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,WAAW,SAAS,yDAAyD,QAAQ;AACjG,YAAM,WAAc,cAAc,GAAG,EAAE;AACvC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,mBAAmB,QAAQ,QAAQ,CAAC;AAAA,IACnE,CAAC;AACD,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,SAAS,SAAS,yDAAyD;AACvF,YAAM,WAAc,cAAc,GAAG,EAAE;AACvC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,QAAQ,CAAC;AAAA,IACpD,CAAC,EAAE,YAAY,SAAS,4DAA4D;AAClF,YAAM,WAAc,cAAc,GAAG,EAAE;AACvC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,QAAQ,CAAC;AAAA,IACpD,CAAC,EAAE,iBAAiB,SAAS,iEAAiE;AAC5F,YAAM,WAAc,cAAc,GAAG,EAAE;AACvC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,QAAQ,CAAC;AAAA,IACpD,CAAC;AACD,IAAG,UAAU,GAAG,sBAAsB,EAAE;AACxC,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,WAAW,IAAI;AACrB,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAc,gBAAgB,IAAI,MAAM,OAAO,gBAAgB,UAAU,OAAO,aAAa,QAAQ,GAAG,OAAO,oBAAoB,MAAM,UAAU,OAAO,oBAAoB,MAAM,QAAQ,CAAC;AAC3M,IAAG,YAAY,iBAAiB,OAAO,gBAAgB,QAAQ,EAAE,iBAAiB,OAAO,cAAc,WAAW,QAAQ,EAAE,mBAAmB,eAAe,EAAE,iBAAiB,OAAO,gBAAgB,QAAQ;AAChN,IAAG,UAAU;AACb,IAAG,YAAY,YAAY,OAAO,gBAAgB,WAAW,IAAI,EAAE,EAAE,gBAAgB,OAAO,gBAAgB,WAAW,SAAS,MAAS,EAAE,cAAc,OAAO,cAAc,WAAW,CAAC,CAAC;AAC3L,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,EAAE,aAAa,OAAO,SAAS;AAAA,EAC9D;AACF;AACA,SAAS,uEAAuE,IAAI,KAAK;AACvF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,oBAAoB,EAAE;AAAA,EACxC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,WAAW,gCAAgC;AAAA,EAC3D;AACF;AACA,SAAS,sEAAsE,IAAI,KAAK;AACtF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,mBAAmB,EAAE;AAAA,EACvC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,WAAW,gCAAgC;AAAA,EAC3D;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,wEAAwE,GAAG,GAAG,oBAAoB,EAAE,EAAE,GAAG,uEAAuE,GAAG,GAAG,mBAAmB,EAAE;AAC5N,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,UAAU;AACxC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,UAAU;AAAA,EACzC;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AAAC;AACxE,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,sDAAsD,GAAG,GAAG,aAAa;AAAA,EAC5F;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,6DAA6D,QAAQ;AACnG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,WAAW,MAAM,CAAC;AAAA,IACjD,CAAC;AACD,IAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,wCAAwC,GAAG,GAAG,MAAM,CAAC;AACvJ,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAc,gBAAgB,GAAG,MAAM,OAAO,qBAAqB,CAAC,CAAC,EAAE,YAAY,OAAO,qBAAqB,CAAC;AAC9H,IAAG,YAAY,cAAc,OAAO,oBAAoB,CAAC;AACzD,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,SAAS,6BAA6B,CAAC,OAAO,SAAS,0BAA0B;AAC/G,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,SAAS,6BAA6B,OAAO,SAAS,0BAA0B;AAAA,EAC3H;AACF;AACA,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA;AAAA;AAAA,oBAIc,GAAG,uBAAuB,CAAC;AAAA,oBAC3B,GAAG,uBAAuB,CAAC;AAAA,qBAC1B,GAAG,wBAAwB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBA2B/B,GAAG,gCAAgC,CAAC;AAAA,aACzC,GAAG,2BAA2B,CAAC;AAAA,aAC/B,GAAG,0BAA0B,CAAC;AAAA,cAC7B,GAAG,0BAA0B,CAAC;AAAA,6BACf,GAAG,8BAA8B,CAAC,WAAW,GAAG,8BAA8B,CAAC,mBAAmB,GAAG,8BAA8B,CAAC,gBAAgB,GAAG,8BAA8B,CAAC;AAAA,6BACtL,GAAG,0BAA0B,CAAC,UAAU,GAAG,4BAA4B,CAAC,MAAM,GAAG,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBASzH,GAAG,sCAAsC,CAAC;AAAA,aAC/C,GAAG,iCAAiC,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIhC,GAAG,uCAAuC,CAAC;AAAA,eAC9C,GAAG,sCAAsC,CAAC,IAAI,GAAG,sCAAsC,CAAC,IAAI,GAAG,sCAAsC,CAAC;AAAA,sBAC/H,GAAG,uCAAuC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,iBAKhD,GAAG,wBAAwB,CAAC;AAAA,aAChC,GAAG,wBAAwB,CAAC;AAAA,cAC3B,GAAG,wBAAwB,CAAC;AAAA;AAAA;AAAA;AAAA,qBAIrB,GAAG,wCAAwC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,qBAK5C,GAAG,wCAAwC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BA2BvC,GAAG,8BAA8B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAiB1C,GAAG,6BAA6B,CAAC;AAAA,aACtC,GAAG,wBAAwB,CAAC;AAAA,eAC1B,GAAG,0BAA0B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAkB7B,GAAG,sCAAsC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAM7C,GAAG,qCAAqC,CAAC;AAAA,aACzC,GAAG,oCAAoC,CAAC;AAAA,cACvC,GAAG,oCAAoC,CAAC;AAAA,6BACzB,GAAG,8BAA8B,CAAC,WAAW,GAAG,8BAA8B,CAAC,mBAAmB,GAAG,8BAA8B,CAAC;AAAA;AAAA,qBAE5I,GAAG,6CAA6C,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIpD,GAAG,gDAAgD,CAAC;AAAA,aACzD,GAAG,2CAA2C,CAAC;AAAA;AAAA;AAAA;AAAA,kBAI1C,GAAG,iDAAiD,CAAC;AAAA,eACxD,GAAG,gDAAgD,CAAC,IAAI,GAAG,gDAAgD,CAAC,IAAI,GAAG,gDAAgD,CAAC;AAAA,sBAC7J,GAAG,iDAAiD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,iBAK1D,GAAG,yCAAyC,CAAC;AAAA,aACjD,GAAG,yCAAyC,CAAC;AAAA,cAC5C,GAAG,yCAAyC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAMzC,GAAG,wCAAwC,CAAC;AAAA,eAC/C,GAAG,qCAAqC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eA2EzC,GAAG,iCAAiC,CAAC;AAAA,WACzC,GAAG,6BAA6B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAQ1B,GAAG,sCAAsC,CAAC;AAAA,aAC/C,GAAG,iCAAiC,CAAC;AAAA,cACpC,GAAG,kCAAkC,CAAC;AAAA,6BACvB,GAAG,8BAA8B,CAAC,WAAW,GAAG,8BAA8B,CAAC,mBAAmB,GAAG,8BAA8B,CAAC,gBAAgB,GAAG,8BAA8B,CAAC;AAAA;AAAA,qBAE9L,GAAG,yCAAyC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAShD,GAAG,4CAA4C,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIhD,GAAG,6CAA6C,CAAC;AAAA,eACpD,GAAG,4CAA4C,CAAC,IAAI,GAAG,4CAA4C,CAAC,IAAI,GAAG,4CAA4C,CAAC;AAAA,sBACjJ,GAAG,6CAA6C,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIrD,GAAG,6CAA6C,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBA4BjD,GAAG,0CAA0C,CAAC;AAAA;AAAA;AAAA;AAAA,kBAI9C,GAAG,4CAA4C,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIhD,GAAG,kDAAkD,CAAC;AAAA;AAAA;AAAA;AAAA,kBAItD,GAAG,mDAAmD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAkD3D,GAAG,8BAA8B,CAAC;AAAA,kBAC9B,GAAG,kCAAkC,CAAC;AAAA,aAC3C,GAAG,6BAA6B,CAAC;AAAA,aACjC,GAAG,4BAA4B,CAAC;AAAA,cAC/B,GAAG,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,qBAKzB,GAAG,qCAAqC,CAAC;AAAA;AAAA,6BAEjC,GAAG,8BAA8B,CAAC,WAAW,GAAG,8BAA8B,CAAC,mBAAmB,GAAG,8BAA8B,CAAC;AAAA;AAAA;AAAA;AAAA,iBAIhJ,GAAG,iCAAiC,CAAC;AAAA,aACzC,GAAG,iCAAiC,CAAC;AAAA,cACpC,GAAG,iCAAiC,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIjC,GAAG,wCAAwC,CAAC;AAAA,aACjD,GAAG,mCAAmC,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIlC,GAAG,yCAAyC,CAAC;AAAA,eAChD,GAAG,wCAAwC,CAAC,IAAI,GAAG,wCAAwC,CAAC,IAAI,GAAG,wCAAwC,CAAC;AAAA,sBACrI,GAAG,yCAAyC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkCnE,IAAM,UAAU;AAAA,EACd,MAAM;AAAA,EACN,MAAM,CAAC;AAAA,IACL;AAAA,EACF,MAAM;AACJ,UAAM,qBAAqB,SAAS,OAAO,kBAAkB,SAAS,iBAAiB,yBAAyB,SAAS,OAAO,kBAAkB;AAClJ,UAAM,oBAAoB,SAAS,OAAO,kBAAkB,SAAS,iBAAiB,yBAAyB,SAAS,OAAO,kBAAkB;AACjJ,WAAO,CAAC,0BAA0B;AAAA,MAChC,yBAAyB,SAAS,OAAO;AAAA,MACzC,+BAA+B,SAAS,OAAO;AAAA,MAC/C,+BAA+B,SAAS,OAAO,6BAA6B,CAAC,SAAS,OAAO;AAAA,IAC/F,GAAG,oBAAoB,iBAAiB;AAAA,EAC1C;AAAA,EACA,aAAa;AAAA,EACb,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,OAAO;AAAA,EACP,YAAY,CAAC;AAAA,IACX;AAAA,EACF,MAAM,CAAC,gDAAgD;AAAA,IACrD,cAAc,SAAS,sBAAsB;AAAA,EAC/C,CAAC;AAAA,EACD,UAAU;AAAA,EACV,MAAM;AAAA,EACN,YAAY,CAAC;AAAA,IACX;AAAA,EACF,MAAM,CAAC,gDAAgD;AAAA,IACrD,cAAc,SAAS,qBAAqB;AAAA,EAC9C,CAAC;AAAA,EACD,UAAU;AAAA,EACV,SAAS;AAAA,EACT,eAAe;AAAA,EACf,WAAW,CAAC;AAAA,IACV;AAAA,IACA;AAAA,EACF,MAAM,CAAC,wBAAwB;AAAA,IAC7B,+BAA+B,SAAS,sBAAsB,KAAK;AAAA,EACrE,CAAC;AAAA,EACD,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,qBAAqB,CAAC;AAAA,IACpB;AAAA,EACF,MAAM,CAAC,oEAAoE;AAAA,IACzE,cAAc,SAAS,sBAAsB;AAAA,EAC/C,CAAC;AAAA,EACD,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe,CAAC;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,EACF,MAAM,CAAC,6BAA6B;AAAA,IAClC,qCAAqC,gBAAgB;AAAA,IACrD,oCAAoC,SAAS,aAAa,KAAK;AAAA,IAC/D,mCAAmC,SAAS,oBAAoB,MAAM;AAAA,IACtE,iCAAiC,SAAS,oBAAoB,MAAM;AAAA,EACtE,CAAC;AAAA,EACD,WAAW;AAAA,EACX,qBAAqB,CAAC;AAAA,IACpB;AAAA,EACF,MAAM,CAAC,qEAAqE;AAAA,IAC1E,cAAc,SAAS,qBAAqB;AAAA,EAC9C,CAAC;AAAA,EACD,mBAAmB;AACrB;AACA,IAAM,gBAAN,MAAM,uBAAsB,UAAU;AAAA,EACpC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,sBAAsB,mBAAmB;AACvD,cAAQ,+BAA+B,6BAAgC,sBAAsB,cAAa,IAAI,qBAAqB,cAAa;AAAA,IAClJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,eAAc;AAAA,EACzB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,kBAAiB;AAI1B,EAAAA,iBAAgB,MAAM,IAAI;AAI1B,EAAAA,iBAAgB,MAAM,IAAI;AAI1B,EAAAA,iBAAgB,aAAa,IAAI;AAIjC,EAAAA,iBAAgB,WAAW,IAAI;AAI/B,EAAAA,iBAAgB,QAAQ,IAAI;AAI5B,EAAAA,iBAAgB,SAAS,IAAI;AAI7B,EAAAA,iBAAgB,QAAQ,IAAI;AAI5B,EAAAA,iBAAgB,gBAAgB,IAAI;AAIpC,EAAAA,iBAAgB,OAAO,IAAI;AAI3B,EAAAA,iBAAgB,YAAY,IAAI;AAIhC,EAAAA,iBAAgB,UAAU,IAAI;AAI9B,EAAAA,iBAAgB,MAAM,IAAI;AAI1B,EAAAA,iBAAgB,YAAY,IAAI;AAIhC,EAAAA,iBAAgB,UAAU,IAAI;AAI9B,EAAAA,iBAAgB,SAAS,IAAI;AAI7B,EAAAA,iBAAgB,eAAe,IAAI;AAInC,EAAAA,iBAAgB,WAAW,IAAI;AAI/B,EAAAA,iBAAgB,iBAAiB,IAAI;AAIrC,EAAAA,iBAAgB,YAAY,IAAI;AAIhC,EAAAA,iBAAgB,kBAAkB,IAAI;AAItC,EAAAA,iBAAgB,yBAAyB,IAAI;AAI7C,EAAAA,iBAAgB,uBAAuB,IAAI;AAI3C,EAAAA,iBAAgB,oBAAoB,IAAI;AAIxC,EAAAA,iBAAgB,gBAAgB,IAAI;AAIpC,EAAAA,iBAAgB,eAAe,IAAI;AAInC,EAAAA,iBAAgB,WAAW,IAAI;AAI/B,EAAAA,iBAAgB,qBAAqB,IAAI;AAIzC,EAAAA,iBAAgB,mBAAmB,IAAI;AACzC,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAM5C,IAAM,WAAN,MAAM,kBAAiB,cAAc;AAAA,EACnC;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,aAAa;AAC3B,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMb;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,0BAA0B;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1B,4BAA4B;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5B,6BAA6B;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7B,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX,4BAA4B;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5B,qBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,qBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,kCAAkC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlC,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,qBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,SAAS;AACnB,SAAK,WAAW;AAChB,QAAI,KAAK,YAAY,CAAC,KAAK,aAAa;AACtC,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrC,gBAAgB,IAAI,aAAa;AAAA,EACjC;AAAA,EACA;AAAA,EACA,WAAW;AAAA,EACX,eAAe;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAc;AAAA,EACd,kBAAkB;AAAA,EAClB,kBAAkB,OAAO,aAAa;AAAA,EACtC,YAAY,YAAY,SAAS,IAAI;AACnC,UAAM;AACN,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,KAAK;AAAA,EACZ;AAAA,EACA;AAAA,EACA,qBAAqB;AACnB,SAAK,WAAW,QAAQ,UAAQ;AAC9B,cAAQ,KAAK,QAAQ,GAAG;AAAA,QACtB,KAAK;AACH,eAAK,cAAc,KAAK;AACxB;AAAA,QACF,KAAK;AACH,eAAK,cAAc,KAAK;AACxB;AAAA,QACF,KAAK;AACH,eAAK,iBAAiB,KAAK;AAC3B;AAAA,QACF,KAAK;AACH,eAAK,oBAAoB,KAAK;AAC9B;AAAA,QACF,KAAK;AACH,eAAK,uBAAuB,KAAK;AACjC;AAAA,QACF,KAAK;AACH,eAAK,2BAA2B,KAAK;AACrC;AAAA,QACF,KAAK;AACH,eAAK,gCAAgC,KAAK;AAC1C;AAAA,QACF,KAAK;AACH,eAAK,4BAA4B,KAAK;AACtC;AAAA,QACF,KAAK;AACH,eAAK,eAAe,KAAK;AACzB;AAAA,QACF,KAAK;AACH,eAAK,eAAe,KAAK;AACzB;AAAA,QACF,KAAK;AACH,eAAK,oBAAoB,KAAK;AAC9B;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,YAAY,eAAe;AACzB,UAAM,YAAY,aAAa;AAC/B,QAAI,cAAc,SAAS,cAAc,MAAM,cAAc,SAAS,KAAK,YAAY;AACrF,WAAK,kBAAkB,cAAc,MAAM,aAAa;AAAA,IAC1D,OAAO;AACL,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA,EACA,aAAa;AACX,SAAK,UAAU;AACf,SAAK,cAAc,KAAK,KAAK;AAAA,EAC/B;AAAA,EACA,mBAAmB,OAAO;AACxB,QAAI,KAAK,gBAAgB,OAAO;AAC9B,WAAK,cAAc;AACnB,WAAK,kBAAkB,KAAK,KAAK;AAAA,IACnC;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO;AACtB,YAAQ,MAAM,SAAS;AAAA,MACrB,KAAK;AACH,aAAK,eAAe;AACpB,mBAAW,MAAM;AACf,gBAAM,WAAW,KAAK,UAAU,eAAe,iCAAiC,CAAC;AAAA,QACnF,GAAG,EAAE;AACL;AAAA,MACF,KAAK;AACH,iBAAS,KAAK,MAAM,eAAe,sBAAsB;AACzD;AAAA,IACJ;AAAA,EACF;AAAA,EACA,eAAe,OAAO;AACpB,YAAQ,MAAM,SAAS;AAAA,MACrB,KAAK;AACH,aAAK,gBAAgB;AACrB;AAAA,IACJ;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,oBAAgB;AAChB,SAAK,GAAG,aAAa;AACrB,QAAI,KAAK,MAAM;AACb,kBAAY,IAAI,SAAS,KAAK,KAAK,eAAe,KAAK,cAAc,KAAK,OAAO,OAAO,KAAK;AAAA,IAC/F;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,sBAAkB;AAClB,SAAK,cAAc;AACnB,SAAK,GAAG,aAAa;AACrB,QAAI,KAAK,MAAM;AACb,kBAAY,MAAM,KAAK,KAAK,aAAa;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,YAAY;AACnB,kBAAY,KAAK,SAAS,MAAM,mBAAmB;AAAA,IACrD;AACA,QAAI,KAAK,MAAM;AACb,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,iBAAiB,mBAAmB;AACzD,WAAO,KAAK,qBAAqB,WAAa,kBAAkB,WAAW,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,iBAAiB,CAAC;AAAA,EAC/J;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,IAC1B,gBAAgB,SAAS,wBAAwB,IAAI,KAAK,UAAU;AAClE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,MAAM,CAAC;AACnC,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AACrE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AACrE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,oBAAoB,GAAG;AACxE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,kBAAkB,GAAG;AACtE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB,GAAG;AACzE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iCAAiC,GAAG;AACrF,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,6BAA6B,GAAG;AACjF,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,4BAA4B,GAAG;AAChF,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,wBAAwB,GAAG;AAC5E,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AACpE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB,GAAG;AACzE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW,SAAS,eAAe,IAAI,KAAK;AAC1C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,MAAM,CAAC;AACtB,QAAG,YAAY,MAAM,CAAC;AAAA,MACxB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,OAAO,GAAG;AAC3D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY,GAAG;AAAA,MAClE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,aAAa;AAAA,MACb,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,YAAY,CAAC,GAAG,cAAc,cAAc,eAAe;AAAA,MAC3D,mBAAmB;AAAA,MACnB,oBAAoB,CAAC,GAAG,sBAAsB,sBAAsB,gBAAgB;AAAA,MACpF,yBAAyB,CAAC,GAAG,2BAA2B,2BAA2B,gBAAgB;AAAA,MACnG,2BAA2B,CAAC,GAAG,6BAA6B,6BAA6B,gBAAgB;AAAA,MACzG,4BAA4B,CAAC,GAAG,8BAA8B,8BAA8B,gBAAgB;AAAA,MAC5G,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,2BAA2B,CAAC,GAAG,6BAA6B,6BAA6B,gBAAgB;AAAA,MACzG,oBAAoB,CAAC,GAAG,sBAAsB,sBAAsB,eAAe;AAAA,MACnF,gBAAgB,CAAC,GAAG,kBAAkB,kBAAkB,gBAAgB;AAAA,MACxE,oBAAoB;AAAA,MACpB,iCAAiC;AAAA,MACjC,gBAAgB,CAAC,GAAG,kBAAkB,kBAAkB,gBAAgB;AAAA,MACxE,sBAAsB,CAAC,GAAG,wBAAwB,wBAAwB,gBAAgB;AAAA,MAC1F,oBAAoB;AAAA,MACpB,YAAY,CAAC,GAAG,cAAc,cAAc,eAAe;AAAA,MAC3D,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,mBAAmB;AAAA,MACnB,eAAe;AAAA,IACjB;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,mBAAmB,CAAC,aAAa,CAAC,GAAM,0BAA6B,4BAA+B,oBAAoB;AAAA,IACtI,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,YAAY,EAAE,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,WAAW,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,SAAS,eAAe,cAAc,WAAW,cAAc,YAAY,oBAAoB,GAAG,MAAM,GAAG,CAAC,GAAG,YAAY,oBAAoB,SAAS,eAAe,cAAc,WAAW,YAAY,GAAG,CAAC,GAAG,oBAAoB,SAAS,eAAe,YAAY,CAAC;AAAA,IACzZ,UAAU,SAAS,kBAAkB,IAAI,KAAK;AAC5C,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,yBAAyB,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,iCAAiC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,MACvJ;AACA,UAAI,KAAK,GAAG;AACV,cAAM,cAAiB,YAAY,CAAC;AACpC,QAAG,WAAW,QAAQ,IAAI,UAAU,EAAE,YAAY,WAAW;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,cAAc,MAAM,CAAI,SAAY,MAAS,SAAS,eAAe;AAAA,IACrE,eAAe;AAAA,IACf,MAAM;AAAA,MACJ,WAAW,CAAC,QAAQ,aAAa,CAAC,WAAW,mBAAmB,CAAC,MAAM;AAAA,QACrE,WAAW;AAAA,QACX,SAAS;AAAA,MACX,CAAC,GAAG,QAAQ,0BAA0B,CAAC,CAAC,GAAG,WAAW,mBAAmB,CAAC,QAAQ,4BAA4B,MAAM;AAAA,QAClH,WAAW;AAAA,QACX,SAAS;AAAA,MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA0BV,YAAY,CAAC,QAAQ,aAAa,CAAC,WAAW,mBAAmB,CAAC,MAAM;AAAA,QACtE,WAAW;AAAA,QACX,SAAS;AAAA,MACX,CAAC,GAAG,QAAQ,0BAA0B,CAAC,CAAC,GAAG,WAAW,mBAAmB,CAAC,QAAQ,4BAA4B,MAAM;AAAA,QAClH,WAAW;AAAA,QACX,SAAS;AAAA,MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,MACP,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,aAAa;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,4BAA4B,CAAC;AAAA,MAC3B,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iCAAiC,CAAC;AAAA,MAChC,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,QACf,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,QACf,aAAa;AAAA,QACb,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gCAAgC,CAAC;AAAA,MAC/B,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,QAC9B,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,4BAA4B,CAAC;AAAA,MAC3B,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,QAC1B,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,QACzB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,QACrB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,QACb,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,aAAa;AAAA,QACb,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAN,MAAM,yBAAwB,cAAc;AAAA,EAC1C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,aAAa;AAC3B,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,QAAQ,CAAC;AAAA,EACT;AAAA,EACA;AAAA,EACA,WAAW,IAAI,aAAa;AAAA,EAC5B,mBAAmB,IAAI,aAAa;AAAA,EACpC;AAAA,EACA;AAAA,EACA,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,UAAU,IAAI,SAAS,YAAY;AAC7C,UAAM;AACN,SAAK,WAAW;AAChB,SAAK,KAAK;AACV,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,KAAK,KAAK,SAAS,MAAM,KAAK,QAAQ;AAC3C,SAAK,SAAS,KAAK,QAAQ,KAAK,KAAK,QAAQ,EAAE,OAAO;AAAA,EACxD;AAAA;AAAA,EAEA,uBAAuB,OAAO;AAC5B,QAAI,UAAU,sBAAsB,KAAK,WAAW,eAAe,SAAS,CAAC,GAAG;AAC9E,WAAK,aAAa;AAAA,IACpB,OAAO;AACL,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,YAAY;AACV,QAAI,kBAAkB,KAAK,SAAS,UAAU,GAAG;AAC/C,YAAM,UAAU,KAAK,OAAO,KAAK,KAAK,QAAQ;AAC9C,UAAI,WAAW,QAAQ,YAAY,SAAS,GAAG;AAK7C,aAAK,GAAG,aAAa;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,WAAO,KAAK,SAAS,eAAe,KAAK,SAAS,UAAU,QAAQ,EAAE,SAAS,KAAK,KAAK,SAAS;AAAA,EACpG;AAAA,EACA,gBAAgB;AACd,UAAM,qBAAqB,KAAK,SAAS,kBAAkB,KAAK,iBAAiB,yBAAyB,KAAK,SAAS,kBAAkB;AAC1I,UAAM,oBAAoB,KAAK,SAAS,kBAAkB,KAAK,iBAAiB,yBAAyB,KAAK,SAAS,kBAAkB;AACzI,YAAQ,KAAK,SAAS,iBAAiB,KAAK,SAAS,iBAAiB,MAAM,OAAO,qBAAqB,qBAAqB,MAAM,OAAO,oBAAoB,oBAAoB,MAAM;AAAA,EAC1L;AAAA,EACA,iBAAiB;AACf,QAAI,kBAAkB,KAAK,SAAS,UAAU,GAAG;AAC/C,WAAK,WAAW,YAAY,MAAM;AAChC,YAAI,cAAc,KAAK,SAAS,YAAY,KAAK,MAAM,SAAS,MAAM,KAAK,cAAc,IAAI,KAAK,cAAc;AAChH,aAAK,oBAAoB,WAAW;AACpC,aAAK,cAAc;AAAA,MACrB,GAAG,KAAK,SAAS,kBAAkB;AACnC,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,QAAI,KAAK,SAAS,YAAY,CAAC,KAAK,SAAS,2BAA2B;AACtE;AAAA,IACF;AACA,QAAI,KAAK,UAAU;AACjB,oBAAc,KAAK,QAAQ;AAAA,IAC7B;AACA,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,iBAAiB,cAAc,UAAU;AACvC,UAAM,YAAY,CAAC,OAAO,QAAQ,UAAU,OAAO;AACnD,UAAM,MAAM,UAAU,KAAK,UAAQ,SAAS,QAAQ;AACpD,WAAO,MAAM,GAAG,YAAY,IAAI,GAAG,KAAK;AAAA,EAC1C;AAAA,EACA,aAAa;AACX,WAAO,KAAK,SAAS,uBAAuB,UAAU,KAAK,SAAS,uBAAuB;AAAA,EAC7F;AAAA,EACA,oBAAoB,OAAO;AACzB,QAAI,KAAK,gBAAgB,OAAO;AAC9B,WAAK,cAAc;AACnB,WAAK,iBAAiB,KAAK,KAAK,WAAW;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,OAAO,YAAY,OAAO,KAAK,OAAO,YAAY,KAAK,QAAQ;AAAA,EAC7E;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAoB,kBAAkB,QAAQ,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,eAAe,GAAM,kBAAqB,UAAU,CAAC;AAAA,EAC7M;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,IACjC,WAAW,SAAS,sBAAsB,IAAI,KAAK;AACjD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,MAAM,CAAC;AAAA,MACxB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAAA,MACpE;AAAA,IACF;AAAA,IACA,cAAc,SAAS,6BAA6B,IAAI,KAAK;AAC3D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,oBAAoB,SAAS,oDAAoD,QAAQ;AACrG,iBAAO,IAAI,uBAAuB,MAAM;AAAA,QAC1C,GAAG,OAAU,iBAAiB;AAAA,MAChC;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,aAAa;AAAA,MACb,OAAO;AAAA,MACP,YAAY,CAAC,GAAG,cAAc,cAAc,eAAe;AAAA,MAC3D,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,IAC9D;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,kBAAkB;AAAA,IACpB;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,0BAA6B,0BAA0B;AAAA,IACrE,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,cAAc,IAAI,GAAG,WAAW,WAAW,SAAS,sBAAsB,GAAG,MAAM,GAAG,CAAC,cAAc,IAAI,GAAG,WAAW,WAAW,oBAAoB,GAAG,CAAC,QAAQ,UAAU,SAAS,2BAA2B,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,SAAS,qBAAqB,GAAG,MAAM,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,GAAG,uBAAuB,kBAAkB,iBAAiB,MAAM,SAAS,eAAe,YAAY,aAAa,kBAAkB,8BAA8B,kBAAkB,gBAAgB,sBAAsB,YAAY,iBAAiB,GAAG,CAAC,GAAG,eAAe,SAAS,eAAe,aAAa,cAAc,qBAAqB,YAAY,cAAc,iBAAiB,2BAA2B,mBAAmB,uBAAuB,iBAAiB,GAAG,MAAM,GAAG,CAAC,SAAS,qBAAqB,GAAG,MAAM,GAAG,CAAC,QAAQ,UAAU,GAAG,2BAA2B,GAAG,OAAO,GAAG,CAAC,GAAG,cAAc,GAAG,MAAM,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,QAAQ,UAAU,GAAG,WAAW,GAAG,CAAC,GAAG,uBAAuB,iBAAiB,eAAe,SAAS,eAAe,aAAa,cAAc,qBAAqB,YAAY,cAAc,iBAAiB,2BAA2B,iBAAiB,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,QAAQ,UAAU,GAAG,WAAW,CAAC;AAAA,IACvyC,UAAU,SAAS,yBAAyB,IAAI,KAAK;AACnD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,gCAAgC,GAAG,IAAI,OAAO,CAAC;AAAA,MAClE;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,QAAQ,IAAI,SAAS,IAAI,MAAM,SAAS,CAAC;AAAA,MACzD;AAAA,IACF;AAAA,IACA,cAAc,MAAM,CAAI,SAAY,MAAS,kBAAqB,SAAS,WAAW,WAAW,kBAAkB,cAAc,kBAAkB;AAAA,IACnJ,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAgEV,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B,CAAC,QAAQ,CAAC;AAAA,IAChD,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB;AAAA,EACA;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,eAAe;AACb,WAAO,KAAK,mBAAmB,KAAK,SAAS,iBAAiB,KAAK,SAAS,gBAAgB,KAAK,SAAS,mBAAmB,KAAK,SAAS,mBAAmB,KAAK,SAAS,gBAAgB,KAAK,SAAS,qBAAqB,KAAK,SAAS,sBAAsB,KAAK,SAAS;AAAA,EACnR;AAAA,EACA,WAAW,OAAO,QAAQ;AAAA,EAC1B,IAAI,KAAK,MAAM;AACb,SAAK,QAAQ;AACb,QAAI,KAAK,aAAa,KAAK,WAAW,QAAQ,EAAE,SAAS,GAAG;AAC1D,WAAK,UAAU,QAAQ,CAAAC,UAAQ;AAC7B,YAAIA,MAAK,QAAQ,MAAM,KAAK,MAAM;AAChC,kBAAQ,KAAK,MAAM;AAAA,YACjB,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AACH,mBAAK,UAAU;AAAA,gBACb,WAAW,KAAK;AAAA,cAClB;AACA,mBAAK,kBAAkBA,MAAK;AAC5B;AAAA,YACF,KAAK;AACH,mBAAK,UAAU;AAAA,gBACb,WAAW,KAAK;AAAA,cAClB;AACA,mBAAK,kBAAkBA,MAAK;AAC5B;AAAA,UACJ;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,YAAQ,KAAK,MAAM;AAAA,MACjB,KAAK;AACH,aAAK,UAAU;AAAA,UACb,WAAW,KAAK;AAAA,QAClB;AACA,aAAK,kBAAkB,KAAK,SAAS,iBAAiB,KAAK,SAAS;AACpE;AAAA,MACF,KAAK;AACH,aAAK,UAAU;AAAA,UACb,WAAW,KAAK;AAAA,QAClB;AACA,aAAK,kBAAkB,KAAK,SAAS,mBAAmB,KAAK,SAAS;AACtE;AAAA,MACF,KAAK;AACH,aAAK,UAAU;AAAA,UACb,WAAW,KAAK;AAAA,QAClB;AACA,aAAK,kBAAkB,KAAK,SAAS,qBAAqB,KAAK,SAAS;AACxE;AAAA,MACF,KAAK;AACH,aAAK,UAAU;AAAA,UACb,WAAW,KAAK;AAAA,QAClB;AACA,aAAK,kBAAkB,KAAK,SAAS,qBAAqB,KAAK,SAAS;AACxE;AAAA,MACF,KAAK;AACH,aAAK,UAAU;AAAA,UACb,WAAW,KAAK;AAAA,QAClB;AACA,aAAK,kBAAkB,KAAK,SAAS,kBAAkB,KAAK,SAAS;AACrE;AAAA,MACF;AACE,aAAK,UAAU;AAAA,UACb,WAAW,KAAK;AAAA,QAClB;AACA,aAAK,kBAAkB,KAAK,SAAS,iBAAiB,KAAK,SAAS;AAAA,IACxE;AAAA,EACF;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,aAAa,KAAK,UAAU,QAAQ,EAAE,SAAS,GAAG;AACzD,WAAK,WAAW,QAAQ,UAAQ;AAC9B,YAAI,KAAK,QAAQ,MAAM,KAAK,MAAM;AAChC,kBAAQ,KAAK,MAAM;AAAA,YACjB,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AACH,mBAAK,UAAU;AAAA,gBACb,WAAW,KAAK;AAAA,cAClB;AACA,mBAAK,kBAAkB,KAAK;AAC5B;AAAA,YACF,KAAK;AACH,mBAAK,UAAU;AAAA,gBACb,WAAW,KAAK;AAAA,cAClB;AACA,mBAAK,kBAAkB,KAAK;AAC5B;AAAA,YACF,KAAK;AACH,mBAAK,UAAU;AAAA,gBACb,WAAW,KAAK;AAAA,cAClB;AACA,mBAAK,kBAAkB,KAAK;AAC5B;AAAA,YACF;AACE,mBAAK,UAAU;AAAA,gBACb,WAAW,KAAK;AAAA,cAClB;AACA,mBAAK,kBAAkB,KAAK;AAC5B;AAAA,UACJ;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,oBAAoB,CAAC;AAAA,IAClC,QAAQ;AAAA,MACN,WAAW;AAAA,MACX,OAAO,CAAC,GAAG,SAAS,SAAS,eAAe;AAAA,MAC5C,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,wBAAwB;AAAA,IACtC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,CAAC;AAAA,IACxE,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,gBAAgB,CAAC;AAAA,MACpF;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,QAAQ,IAAI,aAAa,CAAC;AAAA,MAC1C;AAAA,IACF;AAAA,IACA,cAAc,CAAI,MAAS,gBAAgB;AAAA,IAC3C,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB;AAAA,EACA;AAAA,EACA,WAAW;AAAA,EACX;AAAA,EACA,qBAAqB;AAAA,EACrB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,6BAA6B;AAAA,EAC7B,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA,iBAAiB,IAAI,aAAa;AAAA,EAClC,gBAAgB,IAAI,aAAa;AAAA,EACjC,sBAAsB,IAAI,aAAa;AAAA,EACvC,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,aAAa;AAC3B,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK,SAAS,KAAK,MAAM,KAAK,YAAY;AAAA,EACnD;AAAA,EACA,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,qBAAqB;AAAA,EACrB,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,YAAY;AAAA,IACV;AAAA,EACF,GAAG;AACD,QAAI,UAAU,cAAc;AAC1B,WAAK,eAAe,KAAK;AAAA,IAC3B;AACA,QAAI,YAAY,SAAS,iBAAiB,OAAO;AAC/C,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,OAAO;AACL,QAAI,gBAAgB,KAAK,cAAc;AACvC,QAAI,cAAc,KAAK,YAAY,KAAK,MAAM,SAAS,MAAM,KAAK,cAAc,IAAI;AACpF,SAAK,oBAAoB,KAAK,WAAW;AAAA,EAC3C;AAAA,EACA,OAAO;AACL,QAAI,gBAAgB,KAAK,gBAAgB,IAAI,KAAK,cAAc,IAAI;AACpE,QAAI,cAAc,KAAK,YAAY,KAAK,gBAAgB,IAAI,KAAK,MAAM,SAAS,IAAI;AACpF,SAAK,oBAAoB,KAAK,WAAW;AAAA,EAC3C;AAAA,EACA,cAAc,KAAK;AACjB,QAAI,QAAQ,QAAQ;AAClB,WAAK,oBAAoB;AAAA,IAC3B,MAAO,MAAK,qBAAqB;AAAA,EACnC;AAAA,EACA,aAAa,KAAK;AAChB,QAAI,QAAQ,QAAQ;AAClB,WAAK,oBAAoB;AAAA,IAC3B,MAAO,MAAK,qBAAqB;AAAA,EACnC;AAAA,EACA,mBAAmB;AACjB,QAAI,KAAK,mBAAmB,KAAK,eAAe;AAC9C,WAAK,cAAc,KAAK;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,WAAW,GAAG;AACZ,SAAK,iBAAiB;AACtB,SAAK,KAAK;AACV,QAAI,KAAK,EAAE,YAAY;AACrB,QAAE,eAAe;AAAA,IACnB;AAAA,EACF;AAAA,EACA,YAAY,GAAG;AACb,SAAK,iBAAiB;AACtB,SAAK,KAAK;AACV,QAAI,KAAK,EAAE,YAAY;AACrB,QAAE,eAAe;AAAA,IACnB;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO;AACtB,SAAK,iBAAiB;AACtB,SAAK,oBAAoB,KAAK,KAAK;AAAA,EACrC;AAAA,EACA,sBAAsB,OAAO;AAC3B,QAAI,KAAK,4BAA4B;AACnC,WAAK,iBAAiB;AACtB,WAAK,oBAAoB,KAAK,KAAK;AAAA,IACrC;AAAA,EACF;AAAA,EACA,mBAAmB,OAAO,OAAO;AAC/B,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK;AAAA,MACL,KAAK;AACH,aAAK,iBAAiB;AACtB,aAAK,oBAAoB,KAAK,KAAK;AACnC,cAAM,eAAe;AACrB;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,cAAM,eAAe;AACrB;AAAA,MACF;AACE;AAAA,IACJ;AAAA,EACF;AAAA,EACA,uBAAuB;AACrB,WAAO,CAAC,KAAK,YAAY,KAAK,gBAAgB,KAAK,MAAM,SAAS;AAAA,EACpE;AAAA,EACA,wBAAwB;AACtB,WAAO,CAAC,KAAK,YAAY,KAAK,gBAAgB;AAAA,EAChD;AAAA,EACA,sBAAsB,OAAO;AAC3B,WAAO,KAAK,gBAAgB;AAAA,EAC9B;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,SAAS,OAAO,YAAY,OAAO,KAAK,SAAS,OAAO,YAAY,KAAK,QAAQ;AAAA,EAC/F;AAAA,EACA,gBAAgB,OAAO;AACrB,WAAO,KAAK,SAAS,OAAO,YAAY,OAAO,KAAK,SAAS,OAAO,YAAY,KAAK,YAAY,QAAQ,kBAAkB,KAAK,IAAI;AAAA,EACtI;AAAA,EACA,cAAc,OAAO;AACnB,WAAO,KAAK,SAAS,OAAO,YAAY,OAAO,KAAK,SAAS,OAAO,YAAY,KAAK,UAAU,QAAQ,WAAW,KAAK,IAAI;AAAA,EAC7H;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAiB,kBAAkB,QAAQ,CAAC;AAAA,EAC/E;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,gBAAgB,CAAC;AAAA,IAC9B,WAAW,CAAC,GAAG,4BAA4B;AAAA,IAC3C,QAAQ;AAAA,MACN,IAAI;AAAA,MACJ,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,OAAO;AAAA,MACP,oBAAoB,CAAC,GAAG,sBAAsB,sBAAsB,gBAAgB;AAAA,MACpF,gBAAgB,CAAC,GAAG,kBAAkB,kBAAkB,gBAAgB;AAAA,MACxE,iBAAiB,CAAC,GAAG,mBAAmB,mBAAmB,gBAAgB;AAAA,MAC3E,4BAA4B,CAAC,GAAG,8BAA8B,8BAA8B,gBAAgB;AAAA,MAC5G,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,aAAa;AAAA,IACf;AAAA,IACA,SAAS;AAAA,MACP,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,qBAAqB;AAAA,IACvB;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,0BAA6B,oBAAoB;AAAA,IAC/D,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,kBAAkB,GAAG,CAAC,QAAQ,UAAU,QAAQ,cAAc,GAAG,WAAW,YAAY,SAAS,SAAS,QAAQ,GAAG,MAAM,GAAG,CAAC,QAAQ,SAAS,GAAG,mBAAmB,GAAG,IAAI,GAAG,CAAC,QAAQ,QAAQ,GAAG,mBAAmB,GAAG,QAAQ,WAAW,GAAG,CAAC,SAAS,sBAAsB,GAAG,MAAM,GAAG,CAAC,SAAS,6BAA6B,GAAG,MAAM,GAAG,CAAC,QAAQ,UAAU,QAAQ,cAAc,GAAG,SAAS,SAAS,QAAQ,WAAW,UAAU,GAAG,CAAC,GAAG,cAAc,GAAG,MAAM,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,QAAQ,WAAW,GAAG,QAAQ,WAAW,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC,YAAY,KAAK,GAAG,WAAW,SAAS,cAAc,WAAW,GAAG,SAAS,SAAS,GAAG,CAAC,YAAY,KAAK,GAAG,SAAS,cAAc,WAAW,SAAS,GAAG,CAAC,QAAQ,UAAU,YAAY,MAAM,SAAS,+BAA+B,GAAG,MAAM,GAAG,CAAC,QAAQ,aAAa,GAAG,SAAS,WAAW,GAAG,CAAC,QAAQ,UAAU,YAAY,MAAM,GAAG,6BAA6B,CAAC;AAAA,IAC19B,UAAU,SAAS,sBAAsB,IAAI,KAAK;AAChD,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,WAAW,GAAG,gCAAgC,GAAG,GAAG,UAAU,CAAC;AAClE,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,UAAU,GAAG,sBAAsB,CAAC;AACvC,QAAG,aAAa;AAChB,QAAG,WAAW,GAAG,gCAAgC,GAAG,GAAG,UAAU,CAAC,EAAE,GAAG,6BAA6B,GAAG,GAAG,OAAO,CAAC;AAClH,QAAG,aAAa;AAChB,QAAG,WAAW,GAAG,4BAA4B,GAAG,GAAG,MAAM,CAAC;AAAA,MAC5D;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,kBAAkB;AAC5C,QAAG,UAAU;AACb,QAAG,WAAW,MAAM,IAAI,KAAK,WAAW,IAAI,WAAW;AACvD,QAAG,YAAY,cAAc,IAAI,gBAAgB,IAAI,cAAc,CAAC,CAAC,EAAE,wBAAwB,IAAI,eAAe,CAAC;AACnH,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,UAAU,EAAE,aAAa,IAAI,SAAS;AAChE,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,kBAAkB;AAC5C,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,gBAAgB,IAAI,SAAS,eAAe;AACtE,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,cAAc;AAAA,MAC1C;AAAA,IACF;AAAA,IACA,cAAc,MAAM,CAAI,SAAY,SAAY,MAAS,kBAAkB,kBAAkB,iBAAiB,gBAAgB;AAAA,IAC9H,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAoDV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG;AAAA,IACF,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,4BAA4B,CAAC;AAAA,MAC3B,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,WAAW;AAAA,EACX;AAAA,EACA,gBAAgB;AAAA,EAChB,0BAA0B;AAAA,EAC1B;AAAA,EACA,sBAAsB,IAAI,aAAa;AAAA,EACvC,gBAAgB,IAAI,aAAa;AAAA,EACjC;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,YAAY;AACzB,SAAK,cAAc;AACnB,SAAK,iBAAiB,KAAK;AAC3B,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,aAAa;AAC3B,SAAK,kBAAkB,KAAK;AAC5B,SAAK,eAAe;AAAA,EACtB;AAAA,EACA;AAAA,EACA,WAAW;AAAA,EACX,kBAAkB;AAAA,EAClB,0BAA0B;AAAA,EAC1B,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP;AAAA,EACA,cAAc;AAAA,EACd,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,YAAY,UAAUC,WAAU,YAAY,UAAU,IAAI;AACxD,SAAK,WAAW;AAChB,SAAK,WAAWA;AAChB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,WAAW;AACT,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,WAAK,YAAY;AACjB,UAAI,KAAK,mBAAmB;AAC1B,aAAK,sBAAsB;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA,EACA,wBAAwB;AACtB,QAAI,oBAAoB,KAAK;AAC7B,SAAK,KAAK,mBAAmB,KAAK,gBAAgB,KAAK,oBAAoB,KAAK,iBAAiB,KAAK,gBAAgB;AACpH,UAAI,KAAK,gBAAgB,KAAK,mBAAmB,GAAG;AAClD,4BAAoB;AAAA,MACtB,WAAW,KAAK,MAAM,SAAS,KAAK,eAAe,KAAK,mBAAmB,IAAI,KAAK,cAAc;AAChG,4BAAoB,KAAK,eAAe,KAAK,MAAM;AAAA,MACrD,WAAW,KAAK,MAAM,SAAS,KAAK,eAAe,KAAK,gBAAgB,KAAK,eAAe,MAAM,GAAG;AACnG,4BAAoB,KAAK,eAAe,KAAK,KAAK,mBAAmB,IAAI;AAAA,MAC3E,OAAO;AACL,4BAAoB,KAAK,eAAe,KAAK,KAAK,mBAAmB;AAAA,MACvE;AACA,UAAI,sBAAsB,KAAK,mBAAmB;AAChD,aAAK,oBAAoB;AAAA,MAC3B;AACA,UAAI,KAAK,kBAAkB,KAAK,eAAe,eAAe;AAC5D,aAAK,eAAe,cAAc,MAAM,YAAY,KAAK,aAAa,kBAAkB,qBAAqB,MAAM,KAAK,aAAa,UAAU,eAAe,qBAAqB,MAAM,KAAK,aAAa;AAAA,MAC7M;AACA,UAAI,KAAK,oBAAoB,KAAK,cAAc;AAC9C,oBAAY,KAAK,eAAe,eAAe,gBAAgB;AAC/D,aAAK,eAAe,cAAc,MAAM,aAAa;AAAA,MACvD;AACA,WAAK,kBAAkB,KAAK;AAC5B,WAAK,iBAAiB,KAAK;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkB,KAAK,SAAS,cAAc,OAAO;AAC1D,WAAK,SAAS,KAAK,YAAY,KAAK,eAAe;AAAA,IACrD;AACA,QAAI,YAAY;AAAA,eACL,KAAK,WAAW;AAAA,4BACH,MAAM,KAAK,YAAY;AAAA;AAAA;AAG/C,QAAI,KAAK,mBAAmB;AAC1B,WAAK,0BAA0B,CAAC,GAAG,KAAK,iBAAiB;AACzD,WAAK,wBAAwB,KAAK,CAAC,OAAO,UAAU;AAClD,cAAM,SAAS,MAAM;AACrB,cAAM,SAAS,MAAM;AACrB,YAAI,SAAS;AACb,YAAI,UAAU,QAAQ,UAAU,KAAM,UAAS;AAAA,iBAAY,UAAU,QAAQ,UAAU,KAAM,UAAS;AAAA,iBAAW,UAAU,QAAQ,UAAU,KAAM,UAAS;AAAA,iBAAW,OAAO,WAAW,YAAY,OAAO,WAAW,SAAU,UAAS,OAAO,cAAc,QAAQ,QAAW;AAAA,UAChR,SAAS;AAAA,QACX,CAAC;AAAA,YAAO,UAAS,SAAS,SAAS,KAAK,SAAS,SAAS,IAAI;AAC9D,eAAO,KAAK;AAAA,MACd,CAAC;AACD,eAAS,IAAI,GAAG,IAAI,KAAK,wBAAwB,QAAQ,KAAK;AAC5D,YAAI,MAAM,KAAK,wBAAwB,CAAC;AACxC,qBAAa;AAAA,oDAC+B,IAAI,UAAU;AAAA,2BACvC,KAAK,WAAW;AAAA,wCACH,MAAM,IAAI,UAAU;AAAA;AAAA;AAAA;AAAA,MAItD;AAAA,IACF;AACA,SAAK,gBAAgB,YAAY;AACjC,iBAAa,KAAK,iBAAiB,SAAS,KAAK,SAAS,QAAQ,IAAI,GAAG,KAAK;AAAA,EAChF;AAAA,EACA,oBAAoB;AAClB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,UAAI,KAAK,kBAAkB,KAAK,yBAAyB;AACvD,YAAI,cAAc,OAAO;AACzB,YAAI,wBAAwB;AAAA,UAC1B,YAAY,KAAK;AAAA,QACnB;AACA,iBAAS,IAAI,GAAG,IAAI,KAAK,wBAAwB,QAAQ,KAAK;AAC5D,cAAI,MAAM,KAAK,wBAAwB,CAAC;AACxC,cAAI,SAAS,IAAI,YAAY,EAAE,KAAK,aAAa;AAC/C,oCAAwB;AAAA,UAC1B;AAAA,QACF;AACA,YAAI,KAAK,iBAAiB,sBAAsB,YAAY;AAC1D,eAAK,eAAe,sBAAsB;AAC1C,eAAK,GAAG,aAAa;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,OAAO;AACjB,WAAO,KAAK,aAAa,KAAK,IAAI,IAAI;AAAA,EACxC;AAAA,EACA,WAAW,GAAG;AACZ,SAAK,iBAAiB;AACtB,QAAI,gBAAgB,KAAK,eAAe;AACxC,QAAI,gBAAgB,KAAK,oBAAoB,KAAK,mBAAmB,MAAM,KAAK,KAAK,oBAAoB,KAAK,mBAAmB,IAAI,KAAK,KAAK,WAAW;AACxJ,WAAK,KAAK,EAAE;AAAA,IACd;AACA,QAAI,cAAc,KAAK,YAAY,KAAK,MAAM,SAAS,MAAM,KAAK,eAAe,IAAI;AACrF,SAAK,oBAAoB,KAAK,WAAW;AACzC,QAAI,EAAE,YAAY;AAChB,QAAE,eAAe;AAAA,IACnB;AAAA,EACF;AAAA,EACA,YAAY,GAAG;AACb,SAAK,iBAAiB;AACtB,QAAI,gBAAgB,KAAK,iBAAiB,IAAI,KAAK,eAAe,IAAI;AACtE,QAAI,OAAO,gBAAgB,KAAK;AAChC,QAAI,KAAK,eAAe,OAAO,IAAI,KAAK,mBAAmB,MAAM,KAAK,KAAK,sBAAsB,KAAK,KAAK,WAAW;AACpH,WAAK,KAAK,CAAC;AAAA,IACb;AACA,QAAI,cAAc,KAAK,YAAY,KAAK,iBAAiB,IAAI,KAAK,MAAM,SAAS,IAAI;AACrF,SAAK,oBAAoB,KAAK,WAAW;AACzC,QAAI,EAAE,YAAY;AAChB,QAAE,eAAe;AAAA,IACnB;AAAA,EACF;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,iBAAiB;AACtB,QAAI,oBAAoB;AACxB,QAAI,sBAAsB,KAAK,cAAc;AAC3C,YAAM,OAAO,oBAAoB,KAAK;AACtC,UAAI,MAAM;AACV,UAAI,oBAAoB,KAAK,cAAc;AACzC,cAAM,KAAK,eAAe,OAAO,IAAI,KAAK,mBAAmB;AAC7D,YAAI,MAAM,KAAK,KAAK,KAAK,sBAAsB,GAAG;AAChD,eAAK,KAAK,GAAG;AAAA,QACf;AAAA,MACF,OAAO;AACL,cAAM,KAAK,mBAAmB,IAAI;AAClC,YAAI,MAAM,KAAK,KAAK,KAAK,oBAAoB,KAAK,mBAAmB,IAAI,GAAG;AAC1E,eAAK,KAAK,GAAG;AAAA,QACf;AAAA,MACF;AACA,WAAK,cAAc;AACnB,WAAK,oBAAoB,KAAK,KAAK,WAAW;AAAA,IAChD;AAAA,EACF;AAAA,EACA,mBAAmB,OAAO,OAAO;AAC/B,QAAI,MAAM,SAAS,WAAW,MAAM,SAAS,SAAS;AACpD,WAAK,YAAY,KAAK;AACtB,YAAM,eAAe;AAAA,IACvB;AACA,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK;AACH,aAAK,WAAW;AAChB;AAAA,MACF,KAAK;AACH,aAAK,UAAU;AACf;AAAA,MACF,KAAK;AACH,aAAK,UAAU;AACf,cAAM,eAAe;AACrB;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd,cAAM,eAAe;AACrB;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,cAAM,eAAe;AACrB;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd;AAAA,MACF;AACE;AAAA,IACJ;AAAA,EACF;AAAA,EACA,aAAa;AACX,UAAM,aAAa,KAAK,KAAK,eAAe,eAAe,mCAAmC;AAC9F,UAAM,cAAc,KAAK,0BAA0B;AACnD,SAAK,wBAAwB,aAAa,cAAc,MAAM,WAAW,SAAS,WAAW,SAAS,IAAI,cAAc,CAAC;AAAA,EAC3H;AAAA,EACA,YAAY;AACV,UAAM,cAAc,KAAK,0BAA0B;AACnD,SAAK,wBAAwB,aAAa,cAAc,KAAK,IAAI,IAAI,cAAc,CAAC;AAAA,EACtF;AAAA,EACA,YAAY;AACV,UAAM,cAAc,KAAK,0BAA0B;AACnD,SAAK,wBAAwB,aAAa,CAAC;AAAA,EAC7C;AAAA,EACA,WAAW;AACT,UAAM,aAAa,KAAK,KAAK,eAAe,eAAe,mCAAmC;AAC9F,UAAM,cAAc,KAAK,0BAA0B;AACnD,SAAK,wBAAwB,aAAa,WAAW,SAAS,CAAC;AAAA,EACjE;AAAA,EACA,WAAW;AACT,UAAM,aAAa,CAAC,GAAG,KAAK,KAAK,eAAe,eAAe,mCAAmC,CAAC;AACnG,UAAM,mBAAmB,WAAW,UAAU,SAAO,aAAa,KAAK,eAAe,MAAM,IAAI;AAChG,UAAM,kBAAkB,WAAW,KAAK,eAAe,eAAe,gBAAgB;AACtF,UAAM,cAAc,WAAW,UAAU,SAAO,QAAQ,gBAAgB,aAAa;AACrF,eAAW,WAAW,EAAE,SAAS,CAAC,EAAE,WAAW;AAC/C,eAAW,gBAAgB,EAAE,SAAS,CAAC,EAAE,WAAW;AAAA,EACtD;AAAA,EACA,4BAA4B;AAC1B,UAAM,aAAa,CAAC,GAAG,KAAK,KAAK,eAAe,eAAe,mCAAmC,CAAC;AACnG,UAAM,kBAAkB,WAAW,KAAK,eAAe,eAAe,oDAAoD;AAC1H,WAAO,WAAW,UAAU,SAAO,QAAQ,gBAAgB,aAAa;AAAA,EAC1E;AAAA,EACA,wBAAwB,SAAS,SAAS;AACxC,UAAM,aAAa,KAAK,KAAK,eAAe,eAAe,mCAAmC;AAC9F,eAAW,OAAO,EAAE,SAAS,CAAC,EAAE,WAAW;AAC3C,eAAW,OAAO,EAAE,SAAS,CAAC,EAAE,WAAW;AAC3C,eAAW,OAAO,EAAE,SAAS,CAAC,EAAE,MAAM;AAAA,EACxC;AAAA,EACA,KAAK,KAAK;AACR,QAAI,oBAAoB,KAAK,oBAAoB;AACjD,QAAI,MAAM,KAAK,KAAK,oBAAoB,KAAK,eAAe,KAAK,MAAM,SAAS,GAAG;AACjF,0BAAoB,KAAK,eAAe,KAAK,MAAM;AAAA,IACrD,WAAW,MAAM,KAAK,oBAAoB,GAAG;AAC3C,0BAAoB;AAAA,IACtB;AACA,QAAI,KAAK,UAAU;AACjB,UAAI,MAAM,KAAK,KAAK,MAAM,SAAS,MAAM,KAAK,cAAc;AAC1D,4BAAoB;AAAA,MACtB,WAAW,MAAM,KAAK,KAAK,iBAAiB,GAAG;AAC7C,4BAAoB,KAAK,eAAe,KAAK,MAAM;AAAA,MACrD;AAAA,IACF;AACA,QAAI,KAAK,gBAAgB;AACvB,kBAAY,KAAK,eAAe,eAAe,gBAAgB;AAC/D,WAAK,eAAe,cAAc,MAAM,YAAY,KAAK,aAAa,kBAAkB,qBAAqB,MAAM,KAAK,aAAa,UAAU,eAAe,qBAAqB,MAAM,KAAK,aAAa;AAC3M,WAAK,eAAe,cAAc,MAAM,aAAa;AAAA,IACvD;AACA,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,mBAAmB;AACjB,QAAI,KAAK,mBAAmB,KAAK,eAAe;AAC9C,WAAK,cAAc,KAAK;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,kBAAkB,GAAG,MAAM;AACzB,QAAI,OAAO,GAAG;AAEZ,WAAK,WAAW,CAAC;AAAA,IACnB,OAAO;AAEL,WAAK,YAAY,CAAC;AAAA,IACpB;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,WAAO,KAAK,MAAM,SAAS,KAAK,eAAe,KAAK,MAAM,SAAS,KAAK,eAAe,IAAI;AAAA,EAC7F;AAAA,EACA,qBAAqB;AACnB,QAAI,QAAQ,KAAK,MAAM,KAAK,eAAe,CAAC;AAC5C,WAAO,KAAK,eAAe,IAAI,QAAQ,QAAQ;AAAA,EACjD;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,kBAAkB,KAAK,eAAe,eAAe;AAC5D,eAAS,KAAK,eAAe,eAAe,gBAAgB;AAC5D,WAAK,eAAe,cAAc,MAAM,aAAa;AAAA,IACvD;AAAA,EACF;AAAA,EACA,WAAW,GAAG;AACZ,QAAI,WAAW,EAAE,eAAe,CAAC;AACjC,QAAI,KAAK,YAAY;AACnB,WAAK,kBAAkB,GAAG,SAAS,QAAQ,KAAK,SAAS,CAAC;AAAA,IAC5D,OAAO;AACL,WAAK,kBAAkB,GAAG,SAAS,QAAQ,KAAK,SAAS,CAAC;AAAA,IAC5D;AAAA,EACF;AAAA,EACA,YAAY,GAAG;AACb,QAAI,EAAE,YAAY;AAChB,QAAE,eAAe;AAAA,IACnB;AAAA,EACF;AAAA,EACA,aAAa,GAAG;AACd,QAAI,WAAW,EAAE,eAAe,CAAC;AACjC,SAAK,WAAW;AAAA,MACd,GAAG,SAAS;AAAA,MACZ,GAAG,SAAS;AAAA,IACd;AAAA,EACF;AAAA,EACA,wBAAwB;AACtB,WAAO,CAAC,KAAK,YAAY,KAAK,iBAAiB,KAAK,KAAK,MAAM,UAAU,KAAK;AAAA,EAChF;AAAA,EACA,uBAAuB;AACrB,WAAO,CAAC,KAAK,YAAY,KAAK,iBAAiB,KAAK,MAAM,SAAS,KAAK,KAAK,MAAM,UAAU,KAAK;AAAA,EACpG;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,oBAAoB;AAAA,EAClC;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,oBAAoB,IAAI,KAAK,eAAe;AAAA,EAC1D;AAAA,EACA,aAAa,OAAO;AAClB,WAAO,KAAK,oBAAoB,KAAK,SAAS,KAAK,oBAAoB,KAAK;AAAA,EAC9E;AAAA,EACA,wBAAwB;AACtB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,YAAMC,UAAS,KAAK,SAAS,eAAe;AAC5C,WAAK,yBAAyB,KAAK,SAAS,OAAOA,SAAQ,UAAU,MAAM;AACzE,aAAK,kBAAkB;AAAA,MACzB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,0BAA0B;AACxB,QAAI,KAAK,wBAAwB;AAC/B,WAAK,uBAAuB;AAC5B,WAAK,yBAAyB;AAAA,IAChC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,mBAAmB;AAC1B,WAAK,wBAAwB;AAAA,IAC/B;AACA,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB,YAAY,YAAY,KAAK,eAAe;AAAA,IACnE;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,SAAS,OAAO,YAAY,OAAO,KAAK,SAAS,OAAO,YAAY,KAAK,gBAAgB;AAAA,EACvG;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,SAAS,OAAO,YAAY,OAAO,KAAK,SAAS,OAAO,YAAY,KAAK,gBAAgB;AAAA,EACvG;AAAA,EACA,cAAc,OAAO;AACnB,WAAO,KAAK,SAAS,OAAO,YAAY,OAAO,KAAK,SAAS,OAAO,YAAY,KAAK,UAAU,QAAQ,WAAW,KAAK,IAAI;AAAA,EAC7H;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAuB,kBAAkB,QAAQ,GAAM,kBAAkB,QAAQ,GAAM,kBAAkB,WAAW,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,iBAAiB,CAAC;AAAA,EACxO;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,IACpC,WAAW,SAAS,yBAAyB,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,MAAM,CAAC;AAAA,MACxB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AAAA,MACvE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,aAAa;AAAA,MACb,OAAO;AAAA,MACP,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,iBAAiB,CAAC,GAAG,mBAAmB,mBAAmB,gBAAgB;AAAA,MAC3E,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,mBAAmB;AAAA,MACnB,eAAe;AAAA,MACf,yBAAyB;AAAA,MACzB,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,aAAa;AAAA,IACf;AAAA,IACA,SAAS;AAAA,MACP,qBAAqB;AAAA,MACrB,eAAe;AAAA,IACjB;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,wBAAwB;AAAA,IACtC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,kBAAkB,EAAE,GAAG,CAAC,GAAG,uBAAuB,GAAG,CAAC,GAAG,+BAA+B,GAAG,CAAC,QAAQ,UAAU,WAAW,IAAI,GAAG,WAAW,YAAY,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,kCAAkC,GAAG,SAAS,GAAG,CAAC,QAAQ,WAAW,GAAG,8BAA8B,GAAG,iBAAiB,cAAc,WAAW,GAAG,CAAC,GAAG,WAAW,WAAW,GAAG,SAAS,SAAS,GAAG,CAAC,QAAQ,UAAU,WAAW,IAAI,GAAG,SAAS,WAAW,UAAU,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,cAAc,GAAG,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,WAAW,SAAS,GAAG,CAAC,GAAG,wBAAwB,GAAG,SAAS,YAAY,eAAe,GAAG,CAAC,QAAQ,aAAa,GAAG,QAAQ,WAAW,GAAG,CAAC,GAAG,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC;AAAA,IACxtB,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,QAAG,WAAW,GAAG,sCAAsC,GAAG,GAAG,UAAU,CAAC;AACxE,QAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,GAAG,CAAC;AAC7C,QAAG,WAAW,iBAAiB,SAAS,2DAA2D;AACjG,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,gBAAgB,CAAC;AAAA,QAC7C,CAAC,EAAE,cAAc,SAAS,sDAAsD,QAAQ;AACtF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,aAAa,MAAM,CAAC;AAAA,QAChD,CAAC,EAAE,aAAa,SAAS,qDAAqD,QAAQ;AACpF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,YAAY,MAAM,CAAC;AAAA,QAC/C,CAAC;AACD,QAAG,WAAW,GAAG,mCAAmC,GAAG,IAAI,OAAO,CAAC;AACnE,QAAG,aAAa,EAAE;AAClB,QAAG,WAAW,GAAG,sCAAsC,GAAG,GAAG,UAAU,CAAC;AACxE,QAAG,aAAa,EAAE;AAAA,MACpB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,QAAQ,IAAI,uBAAuB;AACjD,QAAG,UAAU;AACb,QAAG,WAAW,WAAc,gBAAgB,GAAG,MAAM,IAAI,aAAa,IAAI,gBAAgB,EAAE,CAAC;AAC7F,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,WAAW,IAAI,KAAK;AAClC,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,uBAAuB;AAAA,MACnD;AAAA,IACF;AAAA,IACA,cAAc,MAAM,CAAI,SAAY,SAAY,MAAS,kBAAqB,SAAS,QAAQ,kBAAkB,eAAe,iBAAiB,iBAAiB,gBAAgB;AAAA,IAClL,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAyEV,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,cAAc,CAAC,UAAU,iBAAiB,kBAAkB,cAAc,kBAAkB;AAAA,IAC5F,SAAS,CAAC,cAAc,cAAc,QAAQ,WAAW,kBAAkB,eAAe,iBAAiB,iBAAiB,oBAAoB,oBAAoB,SAAS;AAAA,IAC7K,SAAS,CAAC,cAAc,UAAU,iBAAiB,kBAAkB,cAAc,oBAAoB,YAAY;AAAA,EACrH,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,cAAc,cAAc,WAAW,kBAAkB,eAAe,iBAAiB,iBAAiB,oBAAoB,oBAAoB,cAAc,YAAY;AAAA,EACxL,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,cAAc,QAAQ,WAAW,kBAAkB,eAAe,iBAAiB,iBAAiB,oBAAoB,oBAAoB,SAAS;AAAA,MAC7K,SAAS,CAAC,cAAc,UAAU,iBAAiB,kBAAkB,cAAc,oBAAoB,YAAY;AAAA,MACnH,cAAc,CAAC,UAAU,iBAAiB,kBAAkB,cAAc,kBAAkB;AAAA,IAC9F,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["GalleriaClasses", "item", "document", "window"]}
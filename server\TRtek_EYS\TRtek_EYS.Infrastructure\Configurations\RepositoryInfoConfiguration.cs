﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TRtek_EYS.Domain.Entities;

namespace TRtek_EYS.Infrastructure.Configurations
{
    public class RepositoryInfoConfiguration : IEntityTypeConfiguration<RepositoryInfo>
    {
        public void Configure(EntityTypeBuilder<RepositoryInfo> builder)
        {
            builder.ToTable("RepositoryInfos");

            builder.HasKey(r => r.Id);

            builder.Property(r => r.Url)
                   .IsRequired()
                   .HasMaxLength(300);

            builder.Property(r => r.Provider)
                   .HasMaxLength(100);

            builder.Property(r => r.IsPrivate)
                   .IsRequired();

            builder.Property(r => r.CreatedAt)
                   .IsRequired();

            builder.HasOne(r => r.Project)
                   .WithMany(p => p.Repositories)
                   .HasForeignKey(r => r.ProjectId)
                   .OnDelete(DeleteBehavior.Cascade);
        }
    }
}

import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil, debounceTime, Subscription } from 'rxjs';
import { ChartModule } from 'primeng/chart';
import { ButtonModule } from 'primeng/button';
import { SkeletonModule } from 'primeng/skeleton';
import { LayoutService } from '../../../layout/service/layout.service';

@Component({
    standalone: true,
    selector: 'app-monthly-performance-widget',
    imports: [CommonModule, ChartModule, ButtonModule, SkeletonModule],
    template: `<div class="card">
        <div class="flex justify-between align-items-center mb-5">
            <div class="font-semibold text-xl">Aylık Performans</div>
            <div class="flex gap-2">
                <p-button 
                    label="Projeler" 
                    [outlined]="selectedMetric !== 'projects'"
                    severity="secondary" 
                    size="small"
                    (onClick)="changeMetric('projects')">
                </p-button>
                <p-button 
                    label="Gelir" 
                    [outlined]="selectedMetric !== 'revenue'"
                    severity="secondary" 
                    size="small"
                    (onClick)="changeMetric('revenue')">
                </p-button>
            </div>
        </div>
        
        <div *ngIf="isLoading" class="flex justify-content-center align-items-center" style="height: 300px;">
            <p-skeleton width="100%" height="300px"></p-skeleton>
        </div>
        
        <p-chart 
            *ngIf="!isLoading && lineData" 
            type="line" 
            [data]="lineData" 
            [options]="lineOptions"
            height="300px">
        </p-chart>
    </div>`
})
export class MonthlyPerformanceWidget implements OnInit, OnDestroy {
    private destroy$ = new Subject<void>();
    private subscription: Subscription;
    
    lineData: any;
    lineOptions: any;
    isLoading = true;
    selectedMetric = 'projects';

    constructor(private layoutService: LayoutService) {
        this.subscription = this.layoutService.configUpdate$
            .pipe(debounceTime(25))
            .subscribe(() => {
                this.initChart();
            });
    }

    ngOnInit() {
        this.initChart();
        setTimeout(() => {
            this.isLoading = false;
        }, 1500);
    }

    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
        if (this.subscription) {
            this.subscription.unsubscribe();
        }
    }

    changeMetric(metric: string) {
        this.selectedMetric = metric;
        this.initChart();
    }

    initChart() {
        const documentStyle = getComputedStyle(document.documentElement);
        const textColor = documentStyle.getPropertyValue('--text-color');
        const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');
        const surfaceBorder = documentStyle.getPropertyValue('--surface-border');

        if (this.selectedMetric === 'projects') {
            this.lineData = {
                labels: ['Ocak', 'Şubat', 'Mart', 'Nisan', 'Mayıs', 'Haziran', 'Temmuz'],
                datasets: [
                    {
                        label: 'Tamamlanan Projeler',
                        data: [12, 19, 15, 25, 22, 18, 24],
                        fill: false,
                        backgroundColor: documentStyle.getPropertyValue('--p-primary-500'),
                        borderColor: documentStyle.getPropertyValue('--p-primary-500'),
                        tension: 0.4
                    },
                    {
                        label: 'Başlanan Projeler',
                        data: [8, 15, 12, 20, 18, 15, 20],
                        fill: false,
                        backgroundColor: documentStyle.getPropertyValue('--p-green-500'),
                        borderColor: documentStyle.getPropertyValue('--p-green-500'),
                        tension: 0.4
                    }
                ]
            };
        } else {
            this.lineData = {
                labels: ['Ocak', 'Şubat', 'Mart', 'Nisan', 'Mayıs', 'Haziran', 'Temmuz'],
                datasets: [
                    {
                        label: 'Gelir (₺)',
                        data: [450000, 520000, 480000, 650000, 580000, 620000, 720000],
                        fill: false,
                        backgroundColor: documentStyle.getPropertyValue('--p-cyan-500'),
                        borderColor: documentStyle.getPropertyValue('--p-cyan-500'),
                        tension: 0.4
                    },
                    {
                        label: 'Hedef (₺)',
                        data: [500000, 500000, 500000, 600000, 600000, 600000, 700000],
                        fill: false,
                        backgroundColor: documentStyle.getPropertyValue('--p-orange-500'),
                        borderColor: documentStyle.getPropertyValue('--p-orange-500'),
                        tension: 0.4,
                        borderDash: [5, 5]
                    }
                ]
            };
        }

        this.lineOptions = {
            maintainAspectRatio: false,
            aspectRatio: 0.8,
            plugins: {
                legend: {
                    labels: {
                        color: textColor
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context: any) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed.y !== null) {
                                if (context.dataset.label?.includes('₺')) {
                                    label += new Intl.NumberFormat('tr-TR', {
                                        style: 'currency',
                                        currency: 'TRY'
                                    }).format(context.parsed.y);
                                } else {
                                    label += context.parsed.y;
                                }
                            }
                            return label;
                        }
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: textColorSecondary,
                        font: {
                            weight: 500
                        }
                    },
                    grid: {
                        display: false,
                        drawBorder: false
                    }
                },
                y: {
                    ticks: {
                        color: textColorSecondary,
                        callback: function(value: any) {
                            if (typeof value === 'number' && value >= 1000) {
                                return new Intl.NumberFormat('tr-TR', {
                                    notation: 'compact',
                                    compactDisplay: 'short'
                                }).format(value);
                            }
                            return value;
                        }
                    },
                    grid: {
                        color: surfaceBorder,
                        drawBorder: false
                    }
                }
            }
        };
    }
}

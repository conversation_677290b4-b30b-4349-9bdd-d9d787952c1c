{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-carousel.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, inject, numberAttribute, booleanAttribute, ContentChildren, ContentChild, ViewChild, Output, Input, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { uuid, setAttribute, find, getAttribute, findSingle } from '@primeuix/utils';\nimport { SharedModule, Header, Footer, PrimeTemplate } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport * as i2 from 'primeng/button';\nimport { ButtonModule } from 'primeng/button';\nimport { ChevronRightIcon, ChevronLeftIcon, ChevronDownIcon, ChevronUpIcon } from 'primeng/icons';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"item\"];\nconst _c1 = [\"header\"];\nconst _c2 = [\"footer\"];\nconst _c3 = [\"previousicon\"];\nconst _c4 = [\"nexticon\"];\nconst _c5 = [\"itemsContainer\"];\nconst _c6 = [\"indicatorContent\"];\nconst _c7 = [[[\"p-header\"]], [[\"p-footer\"]]];\nconst _c8 = [\"p-header\", \"p-footer\"];\nconst _c9 = (a0, a1) => ({\n  \"p-carousel p-component\": true,\n  \"p-carousel-vertical\": a0,\n  \"p-carousel-horizontal\": a1\n});\nconst _c10 = a0 => ({\n  height: a0\n});\nconst _c11 = a0 => ({\n  \"p-carousel-prev-button\": true,\n  \"p-disabled\": a0\n});\nconst _c12 = (a0, a1, a2) => ({\n  \"p-carousel-item p-carousel-item-clone\": true,\n  \"p-carousel-item-active\": a0,\n  \"p-carousel-item-start\": a1,\n  \"p-carousel-item-end\": a2\n});\nconst _c13 = a0 => ({\n  $implicit: a0\n});\nconst _c14 = (a0, a1, a2) => ({\n  \"p-carousel-item\": true,\n  \"p-carousel-item-active\": a0,\n  \"p-carousel-item-start\": a1,\n  \"p-carousel-item-end\": a2\n});\nconst _c15 = a0 => ({\n  \"p-carousel-next-button\": true,\n  \"p-disabled\": a0\n});\nconst _c16 = a0 => ({\n  \"p-carousel-indicator\": true,\n  \"p-carousel-indicator-active\": a0\n});\nfunction Carousel_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Carousel_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵprojection(1);\n    i0.ɵɵtemplate(2, Carousel_div_1_ng_container_2_Template, 1, 0, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.headerTemplate);\n  }\n}\nfunction Carousel_p_button_4_ng_template_1_ng_container_0_ChevronLeftIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronLeftIcon\", 20);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"carousel-prev-icon\");\n  }\n}\nfunction Carousel_p_button_4_ng_template_1_ng_container_0_ChevronUpIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronUpIcon\", 20);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"carousel-prev-icon\");\n  }\n}\nfunction Carousel_p_button_4_ng_template_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Carousel_p_button_4_ng_template_1_ng_container_0_ChevronLeftIcon_1_Template, 1, 1, \"ChevronLeftIcon\", 19)(2, Carousel_p_button_4_ng_template_1_ng_container_0_ChevronUpIcon_2_Template, 1, 1, \"ChevronUpIcon\", 19);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isVertical());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isVertical());\n  }\n}\nfunction Carousel_p_button_4_ng_template_1_span_1_1_ng_template_0_Template(rf, ctx) {}\nfunction Carousel_p_button_4_ng_template_1_span_1_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Carousel_p_button_4_ng_template_1_span_1_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Carousel_p_button_4_ng_template_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtemplate(1, Carousel_p_button_4_ng_template_1_span_1_1_Template, 1, 0, null, 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.previousIconTemplate || ctx_r1._previousIconTemplate);\n  }\n}\nfunction Carousel_p_button_4_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Carousel_p_button_4_ng_template_1_ng_container_0_Template, 3, 2, \"ng-container\", 17)(1, Carousel_p_button_4_ng_template_1_span_1_Template, 2, 1, \"span\", 18);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.previousIconTemplate && !ctx_r1._previousIconTemplate && !(ctx_r1.prevButtonProps == null ? null : ctx_r1.prevButtonProps.icon));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previousIconTemplate || ctx_r1._previousIconTemplate) && !(ctx_r1.prevButtonProps == null ? null : ctx_r1.prevButtonProps.icon));\n  }\n}\nfunction Carousel_p_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 16);\n    i0.ɵɵlistener(\"click\", function Carousel_p_button_4_Template_p_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navBackward($event));\n    });\n    i0.ɵɵtemplate(1, Carousel_p_button_4_ng_template_1_Template, 2, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c11, ctx_r1.isBackwardNavDisabled()))(\"disabled\", ctx_r1.isBackwardNavDisabled())(\"text\", true)(\"buttonProps\", ctx_r1.prevButtonProps);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.ariaPrevButtonLabel());\n  }\n}\nfunction Carousel_div_8_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Carousel_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵtemplate(1, Carousel_div_8_ng_container_1_Template, 1, 0, \"ng-container\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const index_r5 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c12, ctx_r1.totalShiftedItems * -1 === ctx_r1.value.length, 0 === index_r5, ctx_r1.clonedItemsForStarting.length - 1 === index_r5));\n    i0.ɵɵattribute(\"aria-hidden\", !(ctx_r1.totalShiftedItems * -1 === ctx_r1.value.length))(\"aria-label\", ctx_r1.ariaSlideNumber(index_r5))(\"aria-roledescription\", ctx_r1.ariaSlideLabel());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.itemTemplate || ctx_r1._itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(10, _c13, item_r4));\n  }\n}\nfunction Carousel_div_9_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Carousel_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵtemplate(1, Carousel_div_9_ng_container_1_Template, 1, 0, \"ng-container\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const index_r7 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c14, ctx_r1.firstIndex() <= index_r7 && ctx_r1.lastIndex() >= index_r7, ctx_r1.firstIndex() === index_r7, ctx_r1.lastIndex() === index_r7));\n    i0.ɵɵattribute(\"aria-hidden\", !(ctx_r1.firstIndex() <= index_r7 && ctx_r1.lastIndex() >= index_r7))(\"aria-label\", ctx_r1.ariaSlideNumber(index_r7))(\"aria-roledescription\", ctx_r1.ariaSlideLabel());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.itemTemplate || ctx_r1._itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(10, _c13, item_r6));\n  }\n}\nfunction Carousel_div_10_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Carousel_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵtemplate(1, Carousel_div_10_ng_container_1_Template, 1, 0, \"ng-container\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    const index_r9 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(3, _c12, ctx_r1.totalShiftedItems * -1 === ctx_r1.numVisible, 0 === index_r9, ctx_r1.clonedItemsForFinishing.length - 1 === index_r9));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.itemTemplate || ctx_r1._itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(7, _c13, item_r8));\n  }\n}\nfunction Carousel_p_button_11_ng_template_1_ng_container_0_ChevronRightIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\", 20);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"carousel-next-icon\");\n  }\n}\nfunction Carousel_p_button_11_ng_template_1_ng_container_0_ChevronDownIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronDownIcon\", 20);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"carousel-next-icon\");\n  }\n}\nfunction Carousel_p_button_11_ng_template_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Carousel_p_button_11_ng_template_1_ng_container_0_ChevronRightIcon_1_Template, 1, 1, \"ChevronRightIcon\", 19)(2, Carousel_p_button_11_ng_template_1_ng_container_0_ChevronDownIcon_2_Template, 1, 1, \"ChevronDownIcon\", 19);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isVertical());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isVertical());\n  }\n}\nfunction Carousel_p_button_11_ng_template_1_span_1_1_ng_template_0_Template(rf, ctx) {}\nfunction Carousel_p_button_11_ng_template_1_span_1_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Carousel_p_button_11_ng_template_1_span_1_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Carousel_p_button_11_ng_template_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 25);\n    i0.ɵɵtemplate(1, Carousel_p_button_11_ng_template_1_span_1_1_Template, 1, 0, null, 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.nextIconTemplate || ctx_r1._nextIconTemplate);\n  }\n}\nfunction Carousel_p_button_11_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Carousel_p_button_11_ng_template_1_ng_container_0_Template, 3, 2, \"ng-container\", 17)(1, Carousel_p_button_11_ng_template_1_span_1_Template, 2, 1, \"span\", 24);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.nextIconTemplate && !ctx_r1._nextIconTemplate && !(ctx_r1.nextButtonProps == null ? null : ctx_r1.nextButtonProps.icon));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.nextIconTemplate || ctx_r1._nextIconTemplate && !(ctx_r1.nextButtonProps == null ? null : ctx_r1.nextButtonProps.icon));\n  }\n}\nfunction Carousel_p_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 23);\n    i0.ɵɵlistener(\"click\", function Carousel_p_button_11_Template_p_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navForward($event));\n    });\n    i0.ɵɵtemplate(1, Carousel_p_button_11_ng_template_1_Template, 2, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c15, ctx_r1.isForwardNavDisabled()))(\"disabled\", ctx_r1.isForwardNavDisabled())(\"buttonProps\", ctx_r1.nextButtonProps)(\"text\", true);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.ariaNextButtonLabel());\n  }\n}\nfunction Carousel_ul_12_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 5)(1, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function Carousel_ul_12_li_2_Template_button_click_1_listener($event) {\n      const i_r13 = i0.ɵɵrestoreView(_r12).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onDotClick($event, i_r13));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r13 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c16, ctx_r1._page === i_r13));\n    i0.ɵɵattribute(\"data-pc-section\", \"indicator\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1.indicatorStyleClass);\n    i0.ɵɵproperty(\"ngClass\", \"p-carousel-indicator-button\")(\"ngStyle\", ctx_r1.indicatorStyle)(\"tabindex\", ctx_r1._page === i_r13 ? 0 : -1);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.ariaPageLabel(i_r13 + 1))(\"aria-current\", ctx_r1._page === i_r13 ? \"page\" : undefined);\n  }\n}\nfunction Carousel_ul_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ul\", 26, 2);\n    i0.ɵɵlistener(\"keydown\", function Carousel_ul_12_Template_ul_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onIndicatorKeydown($event));\n    });\n    i0.ɵɵtemplate(2, Carousel_ul_12_li_2_Template, 2, 11, \"li\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.indicatorsContentClass);\n    i0.ɵɵproperty(\"ngClass\", \"p-carousel-indicator-list\")(\"ngStyle\", ctx_r1.indicatorsContentStyle);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.totalDotsArray());\n  }\n}\nfunction Carousel_div_13_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Carousel_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵtemplate(2, Carousel_div_13_ng_container_2_Template, 1, 0, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.footerTemplate || ctx_r1._footerTemplate);\n  }\n}\nconst theme = ({\n  dt\n}) => `\n.p-carousel {\n    display: flex;\n    flex-direction: column;\n}\n\n.p-carousel-content-container {\n    display: flex;\n    flex-direction: column;\n    overflow: auto;\n}\n\n.p-carousel-content {\n    display: flex;\n    flex-direction: row;\n    gap: ${dt('carousel.content.gap')};\n}\n\n.p-carousel-content:dir(rtl) {\n    flex-direction: row-reverse;\n}\n\n.p-carousel-viewport {\n    overflow: hidden;\n    width: 100%;\n}\n\n.p-carousel-item-list {\n    display: flex;\n    flex-direction: row;\n}\n\n.p-carousel-item-list:dir(rtl) {\n    flex-direction: row-reverse;\n}\n\n.p-carousel-prev-button,\n.p-carousel-next-button {\n    align-self: center;\n    flex-shrink: 0;\n}\n\n.p-carousel-indicator-list {\n    display: flex;\n    flex-direction: row;\n    justify-content: center;\n    flex-wrap: wrap;\n    padding: ${dt('carousel.indicator.list.padding')};\n    gap: ${dt('carousel.indicator.list.gap')};\n    margin: 0;\n    list-style: none;\n}\n\n.p-carousel-indicator-button {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    background: ${dt('carousel.indicator.background')};\n    width: ${dt('carousel.indicator.width')};\n    height: ${dt('carousel.indicator.height')};\n    border: 0 none;\n    transition: background ${dt('carousel.transition.duration')}, color ${dt('carousel.transition.duration')}, outline-color ${dt('carousel.transition.duration')}, box-shadow ${dt('carousel.transition.duration')};\n    outline-color: transparent;\n    border-radius: ${dt('carousel.indicator.border.radius')};\n    padding: 0;\n    margin: 0;\n    user-select: none;\n    cursor: pointer;\n}\n\n.p-carousel-indicator-button:focus-visible {\n    box-shadow: ${dt('carousel.indicator.focus.ring.shadow')};\n    outline: ${dt('carousel.indicator.focus.ring.width')} ${dt('carousel.indicator.focus.ring.style')} ${dt('carousel.indicator.focus.ring.color')};\n    outline-offset: ${dt('carousel.indicator.focus.ring.offset')};\n}\n\n.p-carousel-indicator-button:hover {\n    background: ${dt('carousel.indicator.hover.background')};\n}\n\n.p-carousel-indicator-active .p-carousel-indicator-button {\n    background: ${dt('carousel.indicator.active.background')};\n}\n\n.p-carousel-vertical .p-carousel-content {\n    flex-direction: column;\n}\n\n.p-carousel-vertical .p-carousel-item-list {\n    flex-direction: column;\n    height: 100%;\n}\n\n.p-items-hidden .p-carousel-item {\n    visibility: hidden;\n}\n\n.p-items-hidden .p-carousel-item.p-carousel-item-active {\n    visibility: visible;\n}\n`;\nconst classes = {\n  root: ({\n    instance\n  }) => ['p-carousel p-component', {\n    'p-carousel-vertical': instance.isVertical(),\n    'p-carousel-horizontal': !instance.isVertical()\n  }],\n  header: 'p-carousel-header',\n  contentContainer: 'p-carousel-content-container',\n  content: 'p-carousel-content',\n  pcPrevButton: ({\n    instance\n  }) => ['p-carousel-prev-button', {\n    'p-disabled': instance.backwardIsDisabled\n  }],\n  viewport: 'p-carousel-viewport',\n  itemList: 'p-carousel-item-list',\n  itemClone: ({\n    index,\n    value,\n    totalShiftedItems,\n    d_numVisible\n  }) => ['p-carousel-item p-carousel-item-clone', {\n    'p-carousel-item-active': totalShiftedItems * -1 === value.length + d_numVisible,\n    'p-carousel-item-start': index === 0,\n    'p-carousel-item-end': value.slice(-1 * d_numVisible).length - 1 === index\n  }],\n  item: ({\n    instance,\n    index\n  }) => ['p-carousel-item', {\n    'p-carousel-item-active': instance.firstIndex() <= index && instance.lastIndex() >= index,\n    'p-carousel-item-start': instance.firstIndex() === index,\n    'p-carousel-item-end': instance.lastIndex() === index\n  }],\n  pcNextButton: ({\n    instance\n  }) => ['p-carousel-next-button', {\n    'p-disabled': instance.forwardIsDisabled\n  }],\n  indicatorList: 'p-carousel-indicator-list',\n  indicator: ({\n    instance,\n    index\n  }) => ['p-carousel-indicator', {\n    'p-carousel-indicator-active': instance.d_page === index\n  }],\n  indicatorButton: 'p-carousel-indicator-button',\n  footer: 'p-carousel-footer'\n};\nclass CarouselStyle extends BaseStyle {\n  name = 'carousel';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵCarouselStyle_BaseFactory;\n    return function CarouselStyle_Factory(__ngFactoryType__) {\n      return (ɵCarouselStyle_BaseFactory || (ɵCarouselStyle_BaseFactory = i0.ɵɵgetInheritedFactory(CarouselStyle)))(__ngFactoryType__ || CarouselStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: CarouselStyle,\n    factory: CarouselStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CarouselStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * Carousel is a content slider featuring various customization options.\n *\n * [Live Demo](https://www.primeng.org/carousel/)\n *\n * @module carouselstyle\n *\n */\nvar CarouselClasses;\n(function (CarouselClasses) {\n  /**\n   * Class name of the root element\n   */\n  CarouselClasses[\"root\"] = \"p-carousel\";\n  /**\n   * Class name of the header element\n   */\n  CarouselClasses[\"header\"] = \"p-carousel-header\";\n  /**\n   * Class name of the content container element\n   */\n  CarouselClasses[\"contentContainer\"] = \"p-carousel-content-container\";\n  /**\n   * Class name of the content element\n   */\n  CarouselClasses[\"content\"] = \"p-carousel-content\";\n  /**\n   * Class name of the previous button element\n   */\n  CarouselClasses[\"pcPrevButton\"] = \"p-carousel-prev-button\";\n  /**\n   * Class name of the viewport element\n   */\n  CarouselClasses[\"viewport\"] = \"p-carousel-viewport\";\n  /**\n   * Class name of the item list element\n   */\n  CarouselClasses[\"itemList\"] = \"p-carousel-item-list\";\n  /**\n   * Class name of the item clone element\n   */\n  CarouselClasses[\"itemClone\"] = \"p-carousel-item-clone\";\n  /**\n   * Class name of the item element\n   */\n  CarouselClasses[\"item\"] = \"p-carousel-item\";\n  /**\n   * Class name of the next button element\n   */\n  CarouselClasses[\"pcNextButton\"] = \"p-carousel-next-button\";\n  /**\n   * Class name of the indicator list element\n   */\n  CarouselClasses[\"indicatorList\"] = \"p-carousel-indicator-list\";\n  /**\n   * Class name of the indicator element\n   */\n  CarouselClasses[\"indicator\"] = \"p-carousel-indicator\";\n  /**\n   * Class name of the indicator button element\n   */\n  CarouselClasses[\"indicatorButton\"] = \"p-carousel-indicator-button\";\n  /**\n   * Class name of the footer element\n   */\n  CarouselClasses[\"footer\"] = \"p-carousel-footer\";\n})(CarouselClasses || (CarouselClasses = {}));\n\n/**\n * Carousel is a content slider featuring various customization options.\n * @group Components\n */\nclass Carousel extends BaseComponent {\n  el;\n  zone;\n  /**\n   * Index of the first item.\n   * @defaultValue 0\n   * @group Props\n   */\n  get page() {\n    return this._page;\n  }\n  set page(val) {\n    if (this.isCreated && val !== this._page) {\n      if (this.autoplayInterval) {\n        this.stopAutoplay();\n      }\n      if (val > this._page && val <= this.totalDots() - 1) {\n        this.step(-1, val);\n      } else if (val < this._page) {\n        this.step(1, val);\n      }\n    }\n    this._page = val;\n  }\n  /**\n   * Number of items per page.\n   * @defaultValue 1\n   * @group Props\n   */\n  get numVisible() {\n    return this._numVisible;\n  }\n  set numVisible(val) {\n    this._numVisible = val;\n  }\n  /**\n   * Number of items to scroll.\n   * @defaultValue 1\n   * @group Props\n   */\n  get numScroll() {\n    return this._numVisible;\n  }\n  set numScroll(val) {\n    this._numScroll = val;\n  }\n  /**\n   * An array of options for responsive design.\n   * @see {CarouselResponsiveOptions}\n   * @group Props\n   */\n  responsiveOptions;\n  /**\n   * Specifies the layout of the component.\n   * @group Props\n   */\n  orientation = 'horizontal';\n  /**\n   * Height of the viewport in vertical layout.\n   * @group Props\n   */\n  verticalViewPortHeight = '300px';\n  /**\n   * Style class of main content.\n   * @group Props\n   */\n  contentClass = '';\n  /**\n   * Style class of the indicator items.\n   * @group Props\n   */\n  indicatorsContentClass = '';\n  /**\n   * Inline style of the indicator items.\n   * @group Props\n   */\n  indicatorsContentStyle;\n  /**\n   * Style class of the indicators.\n   * @group Props\n   */\n  indicatorStyleClass = '';\n  /**\n   * Style of the indicators.\n   * @group Props\n   */\n  indicatorStyle;\n  /**\n   * An array of objects to display.\n   * @defaultValue null\n   * @group Props\n   */\n  get value() {\n    return this._value;\n  }\n  set value(val) {\n    this._value = val;\n  }\n  /**\n   * Defines if scrolling would be infinite.\n   * @group Props\n   */\n  circular = false;\n  /**\n   * Whether to display indicator container.\n   * @group Props\n   */\n  showIndicators = true;\n  /**\n   * Whether to display navigation buttons in container.\n   * @group Props\n   */\n  showNavigators = true;\n  /**\n   * Time in milliseconds to scroll items automatically.\n   * @group Props\n   */\n  autoplayInterval = 0;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the viewport container.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Used to pass all properties of the ButtonProps to the Button component.\n   * @group Props\n   */\n  prevButtonProps = {\n    severity: 'secondary',\n    text: true,\n    rounded: true\n  };\n  /**\n   * Used to pass all properties of the ButtonProps to the Button component.\n   * @group Props\n   */\n  nextButtonProps = {\n    severity: 'secondary',\n    text: true,\n    rounded: true\n  };\n  /**\n   * Callback to invoke after scroll.\n   * @param {CarouselPageEvent} event - Custom page event.\n   * @group Emits\n   */\n  onPage = new EventEmitter();\n  itemsContainer;\n  indicatorContent;\n  headerFacet;\n  footerFacet;\n  _numVisible = 1;\n  _numScroll = 1;\n  _oldNumScroll = 0;\n  prevState = {\n    numScroll: 0,\n    numVisible: 0,\n    value: []\n  };\n  defaultNumScroll = 1;\n  defaultNumVisible = 1;\n  _page = 0;\n  _value;\n  carouselStyle;\n  id;\n  totalShiftedItems;\n  isRemainingItemsAdded = false;\n  animationTimeout;\n  translateTimeout;\n  remainingItems = 0;\n  _items;\n  startPos;\n  documentResizeListener;\n  clonedItemsForStarting;\n  clonedItemsForFinishing;\n  allowAutoplay;\n  interval;\n  isCreated;\n  swipeThreshold = 20;\n  /**\n   * Template for carousel items.\n   * @group Templates\n   */\n  itemTemplate;\n  /**\n   * Template for the carousel header.\n   * @group Templates\n   */\n  headerTemplate;\n  /**\n   * Template for the carousel footer.\n   * @group Templates\n   */\n  footerTemplate;\n  /**\n   * Template for the previous button icon.\n   * @group Templates\n   */\n  previousIconTemplate;\n  /**\n   * Template for the next button icon.\n   * @group Templates\n   */\n  nextIconTemplate;\n  _itemTemplate;\n  _headerTemplate;\n  _footerTemplate;\n  _previousIconTemplate;\n  _nextIconTemplate;\n  window;\n  _componentStyle = inject(CarouselStyle);\n  constructor(el, zone) {\n    super();\n    this.el = el;\n    this.zone = zone;\n    this.totalShiftedItems = this.page * this.numScroll * -1;\n    this.window = this.document.defaultView;\n  }\n  ngOnChanges(simpleChange) {\n    if (isPlatformBrowser(this.platformId)) {\n      if (simpleChange.value) {\n        if (this.circular && this._value) {\n          this.setCloneItems();\n        }\n      }\n      if (this.isCreated) {\n        if (simpleChange.numVisible) {\n          if (this.responsiveOptions) {\n            this.defaultNumVisible = this.numVisible;\n          }\n          if (this.isCircular()) {\n            this.setCloneItems();\n          }\n          this.createStyle();\n          this.calculatePosition();\n        }\n        if (simpleChange.numScroll) {\n          if (this.responsiveOptions) {\n            this.defaultNumScroll = this.numScroll;\n          }\n        }\n      }\n    }\n    this.cd.markForCheck();\n  }\n  templates;\n  ngAfterContentInit() {\n    this.id = uuid('pn_id_');\n    if (isPlatformBrowser(this.platformId)) {\n      this.allowAutoplay = !!this.autoplayInterval;\n      if (this.circular) {\n        this.setCloneItems();\n      }\n      if (this.responsiveOptions) {\n        this.defaultNumScroll = this._numScroll;\n        this.defaultNumVisible = this._numVisible;\n      }\n      this.createStyle();\n      this.calculatePosition();\n      if (this.responsiveOptions) {\n        this.bindDocumentListeners();\n      }\n    }\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'item':\n          this._itemTemplate = item.template;\n          break;\n        case 'header':\n          this._headerTemplate = item.template;\n          break;\n        case 'footer':\n          this._footerTemplate = item.template;\n          break;\n        case 'previousicon':\n          this._previousIconTemplate = item.template;\n          break;\n        case 'nexticon':\n          this._nextIconTemplate = item.template;\n          break;\n        default:\n          this._itemTemplate = item.template;\n          break;\n      }\n    });\n    this.cd.detectChanges();\n  }\n  ngAfterContentChecked() {\n    if (isPlatformBrowser(this.platformId)) {\n      const isCircular = this.isCircular();\n      let totalShiftedItems = this.totalShiftedItems;\n      if (this.value && this.itemsContainer && (this.prevState.numScroll !== this._numScroll || this.prevState.numVisible !== this._numVisible || this.prevState.value.length !== this.value.length)) {\n        if (this.autoplayInterval) {\n          this.stopAutoplay(false);\n        }\n        this.remainingItems = (this.value.length - this._numVisible) % this._numScroll;\n        let page = this._page;\n        if (this.totalDots() !== 0 && page >= this.totalDots()) {\n          page = this.totalDots() - 1;\n          this._page = page;\n          this.onPage.emit({\n            page: this.page\n          });\n        }\n        totalShiftedItems = page * this._numScroll * -1;\n        if (isCircular) {\n          totalShiftedItems -= this._numVisible;\n        }\n        if (page === this.totalDots() - 1 && this.remainingItems > 0) {\n          totalShiftedItems += -1 * this.remainingItems + this._numScroll;\n          this.isRemainingItemsAdded = true;\n        } else {\n          this.isRemainingItemsAdded = false;\n        }\n        if (totalShiftedItems !== this.totalShiftedItems) {\n          this.totalShiftedItems = totalShiftedItems;\n        }\n        this._oldNumScroll = this._numScroll;\n        this.prevState.numScroll = this._numScroll;\n        this.prevState.numVisible = this._numVisible;\n        this.prevState.value = [...this._value];\n        if (this.totalDots() > 0 && this.itemsContainer.nativeElement) {\n          this.itemsContainer.nativeElement.style.transform = this.isVertical() ? `translate3d(0, ${totalShiftedItems * (100 / this._numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this._numVisible)}%, 0, 0)`;\n        }\n        this.isCreated = true;\n        if (this.autoplayInterval && this.isAutoplay()) {\n          this.startAutoplay();\n        }\n      }\n      if (isCircular) {\n        if (this.page === 0) {\n          totalShiftedItems = -1 * this._numVisible;\n        } else if (totalShiftedItems === 0) {\n          totalShiftedItems = -1 * this.value.length;\n          if (this.remainingItems > 0) {\n            this.isRemainingItemsAdded = true;\n          }\n        }\n        if (totalShiftedItems !== this.totalShiftedItems) {\n          this.totalShiftedItems = totalShiftedItems;\n        }\n      }\n    }\n  }\n  createStyle() {\n    if (!this.carouselStyle) {\n      this.carouselStyle = this.renderer.createElement('style');\n      this.carouselStyle.type = 'text/css';\n      setAttribute(this.carouselStyle, 'nonce', this.config?.csp()?.nonce);\n      this.renderer.appendChild(this.document.head, this.carouselStyle);\n    }\n    let innerHTML = `\n            #${this.id} .p-carousel-item {\n\t\t\t\tflex: 1 0 ${100 / this.numVisible}%\n\t\t\t}\n        `;\n    if (this.responsiveOptions) {\n      this.responsiveOptions.sort((data1, data2) => {\n        const value1 = data1.breakpoint;\n        const value2 = data2.breakpoint;\n        let result = null;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2, undefined, {\n          numeric: true\n        });else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return -1 * result;\n      });\n      for (let i = 0; i < this.responsiveOptions.length; i++) {\n        let res = this.responsiveOptions[i];\n        innerHTML += `\n                    @media screen and (max-width: ${res.breakpoint}) {\n                        #${this.id} .p-carousel-item {\n                            flex: 1 0 ${100 / res.numVisible}%\n                        }\n                    }\n                `;\n      }\n    }\n    this.carouselStyle.innerHTML = innerHTML;\n  }\n  calculatePosition() {\n    if (this.responsiveOptions) {\n      let matchedResponsiveData = {\n        numVisible: this.defaultNumVisible,\n        numScroll: this.defaultNumScroll\n      };\n      if (typeof window !== 'undefined') {\n        let windowWidth = window.innerWidth;\n        for (let i = 0; i < this.responsiveOptions.length; i++) {\n          let res = this.responsiveOptions[i];\n          if (parseInt(res.breakpoint, 10) >= windowWidth) {\n            matchedResponsiveData = res;\n          }\n        }\n      }\n      if (this._numScroll !== matchedResponsiveData.numScroll) {\n        let page = this._page;\n        page = Math.floor(page * this._numScroll / matchedResponsiveData.numScroll);\n        let totalShiftedItems = matchedResponsiveData.numScroll * this.page * -1;\n        if (this.isCircular()) {\n          totalShiftedItems -= matchedResponsiveData.numVisible;\n        }\n        this.totalShiftedItems = totalShiftedItems;\n        this._numScroll = matchedResponsiveData.numScroll;\n        this._page = page;\n        this.onPage.emit({\n          page: this.page\n        });\n      }\n      if (this._numVisible !== matchedResponsiveData.numVisible) {\n        this._numVisible = matchedResponsiveData.numVisible;\n        this.setCloneItems();\n      }\n      this.cd.markForCheck();\n    }\n  }\n  setCloneItems() {\n    this.clonedItemsForStarting = [];\n    this.clonedItemsForFinishing = [];\n    if (this.isCircular()) {\n      this.clonedItemsForStarting.push(...this.value.slice(-1 * this._numVisible));\n      this.clonedItemsForFinishing.push(...this.value.slice(0, this._numVisible));\n    }\n  }\n  firstIndex() {\n    return this.isCircular() ? -1 * (this.totalShiftedItems + this.numVisible) : this.totalShiftedItems * -1;\n  }\n  lastIndex() {\n    return this.firstIndex() + this.numVisible - 1;\n  }\n  totalDots() {\n    return this.value?.length ? Math.ceil((this.value.length - this._numVisible) / this._numScroll) + 1 : 0;\n  }\n  totalDotsArray() {\n    const totalDots = this.totalDots();\n    return totalDots <= 0 ? [] : Array(totalDots).fill(0);\n  }\n  isVertical() {\n    return this.orientation === 'vertical';\n  }\n  isCircular() {\n    return this.circular && this.value && this.value.length >= this.numVisible;\n  }\n  isAutoplay() {\n    return this.autoplayInterval && this.allowAutoplay;\n  }\n  isForwardNavDisabled() {\n    return this.isEmpty() || this._page >= this.totalDots() - 1 && !this.isCircular();\n  }\n  isBackwardNavDisabled() {\n    return this.isEmpty() || this._page <= 0 && !this.isCircular();\n  }\n  isEmpty() {\n    return !this.value || this.value.length === 0;\n  }\n  navForward(e, index) {\n    if (this.isCircular() || this._page < this.totalDots() - 1) {\n      this.step(-1, index);\n    }\n    if (this.autoplayInterval) {\n      this.stopAutoplay();\n    }\n    if (e && e.cancelable) {\n      e.preventDefault();\n    }\n  }\n  navBackward(e, index) {\n    if (this.isCircular() || this._page !== 0) {\n      this.step(1, index);\n    }\n    if (this.autoplayInterval) {\n      this.stopAutoplay();\n    }\n    if (e && e.cancelable) {\n      e.preventDefault();\n    }\n  }\n  onDotClick(e, index) {\n    let page = this._page;\n    if (this.autoplayInterval) {\n      this.stopAutoplay();\n    }\n    if (index > page) {\n      this.navForward(e, index);\n    } else if (index < page) {\n      this.navBackward(e, index);\n    }\n  }\n  onIndicatorKeydown(event) {\n    switch (event.code) {\n      case 'ArrowRight':\n        this.onRightKey();\n        break;\n      case 'ArrowLeft':\n        this.onLeftKey();\n        break;\n    }\n  }\n  onRightKey() {\n    const indicators = [...find(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"]')];\n    const activeIndex = this.findFocusedIndicatorIndex();\n    this.changedFocusedIndicator(activeIndex, activeIndex + 1 === indicators.length ? indicators.length - 1 : activeIndex + 1);\n  }\n  onLeftKey() {\n    const activeIndex = this.findFocusedIndicatorIndex();\n    this.changedFocusedIndicator(activeIndex, activeIndex - 1 <= 0 ? 0 : activeIndex - 1);\n  }\n  onHomeKey() {\n    const activeIndex = this.findFocusedIndicatorIndex();\n    this.changedFocusedIndicator(activeIndex, 0);\n  }\n  onEndKey() {\n    const indicators = [...find(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"]r')];\n    const activeIndex = this.findFocusedIndicatorIndex();\n    this.changedFocusedIndicator(activeIndex, indicators.length - 1);\n  }\n  onTabKey() {\n    const indicators = [...find(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"]')];\n    const highlightedIndex = indicators.findIndex(ind => getAttribute(ind, 'data-p-highlight') === true);\n    const activeIndicator = findSingle(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"] > button[tabindex=\"0\"]');\n    const activeIndex = indicators.findIndex(ind => ind === activeIndicator.parentElement);\n    indicators[activeIndex].children[0].tabIndex = '-1';\n    indicators[highlightedIndex].children[0].tabIndex = '0';\n  }\n  findFocusedIndicatorIndex() {\n    const indicators = [...find(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"]')];\n    const activeIndicator = findSingle(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"] > button[tabindex=\"0\"]');\n    return indicators.findIndex(ind => ind === activeIndicator.parentElement);\n  }\n  changedFocusedIndicator(prevInd, nextInd) {\n    const indicators = [...find(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"]')];\n    indicators[prevInd].children[0].tabIndex = '-1';\n    indicators[nextInd].children[0].tabIndex = '0';\n    indicators[nextInd].children[0].focus();\n  }\n  step(dir, page) {\n    let totalShiftedItems = this.totalShiftedItems;\n    const isCircular = this.isCircular();\n    if (page != null) {\n      totalShiftedItems = this._numScroll * page * -1;\n      if (isCircular) {\n        totalShiftedItems -= this._numVisible;\n      }\n      this.isRemainingItemsAdded = false;\n    } else {\n      totalShiftedItems += this._numScroll * dir;\n      if (this.isRemainingItemsAdded) {\n        totalShiftedItems += this.remainingItems - this._numScroll * dir;\n        this.isRemainingItemsAdded = false;\n      }\n      let originalShiftedItems = isCircular ? totalShiftedItems + this._numVisible : totalShiftedItems;\n      page = Math.abs(Math.floor(originalShiftedItems / this._numScroll));\n    }\n    if (isCircular && this.page === this.totalDots() - 1 && dir === -1) {\n      totalShiftedItems = -1 * (this.value.length + this._numVisible);\n      page = 0;\n    } else if (isCircular && this.page === 0 && dir === 1) {\n      totalShiftedItems = 0;\n      page = this.totalDots() - 1;\n    } else if (page === this.totalDots() - 1 && this.remainingItems > 0) {\n      totalShiftedItems += this.remainingItems * -1 - this._numScroll * dir;\n      this.isRemainingItemsAdded = true;\n    }\n    if (this.itemsContainer) {\n      this.itemsContainer.nativeElement.style.transform = this.isVertical() ? `translate3d(0, ${totalShiftedItems * (100 / this._numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this._numVisible)}%, 0, 0)`;\n      this.itemsContainer.nativeElement.style.transition = 'transform 500ms ease 0s';\n    }\n    this.totalShiftedItems = totalShiftedItems;\n    this._page = page;\n    this.onPage.emit({\n      page: this.page\n    });\n    this.cd.markForCheck();\n  }\n  startAutoplay() {\n    this.interval = setInterval(() => {\n      if (this.totalDots() > 0) {\n        if (this.page === this.totalDots() - 1) {\n          this.step(-1, 0);\n        } else {\n          this.step(-1, this.page + 1);\n        }\n      }\n    }, this.autoplayInterval);\n    this.allowAutoplay = true;\n    this.cd.markForCheck();\n  }\n  stopAutoplay(changeAllow = true) {\n    if (this.interval) {\n      clearInterval(this.interval);\n      this.interval = undefined;\n      if (changeAllow) {\n        this.allowAutoplay = false;\n      }\n    }\n    this.cd.markForCheck();\n  }\n  isPlaying() {\n    return !!this.interval;\n  }\n  onTransitionEnd() {\n    if (this.itemsContainer) {\n      this.itemsContainer.nativeElement.style.transition = '';\n      if ((this.page === 0 || this.page === this.totalDots() - 1) && this.isCircular()) {\n        this.itemsContainer.nativeElement.style.transform = this.isVertical() ? `translate3d(0, ${this.totalShiftedItems * (100 / this._numVisible)}%, 0)` : `translate3d(${this.totalShiftedItems * (100 / this._numVisible)}%, 0, 0)`;\n      }\n    }\n  }\n  onTouchStart(e) {\n    let touchobj = e.changedTouches[0];\n    this.startPos = {\n      x: touchobj.pageX,\n      y: touchobj.pageY\n    };\n  }\n  onTouchMove(e) {\n    if (e.cancelable) {\n      e.preventDefault();\n    }\n  }\n  onTouchEnd(e) {\n    let touchobj = e.changedTouches[0];\n    if (this.isVertical()) {\n      this.changePageOnTouch(e, touchobj.pageY - this.startPos.y);\n    } else {\n      this.changePageOnTouch(e, touchobj.pageX - this.startPos.x);\n    }\n  }\n  changePageOnTouch(e, diff) {\n    if (Math.abs(diff) > this.swipeThreshold) {\n      if (diff < 0) {\n        this.navForward(e);\n      } else {\n        this.navBackward(e);\n      }\n    }\n  }\n  ariaPrevButtonLabel() {\n    return this.config.translation.aria ? this.config.translation.aria.prevPageLabel : undefined;\n  }\n  ariaSlideLabel() {\n    return this.config.translation.aria ? this.config.translation.aria.slide : undefined;\n  }\n  ariaNextButtonLabel() {\n    return this.config.translation.aria ? this.config.translation.aria.nextPageLabel : undefined;\n  }\n  ariaSlideNumber(value) {\n    return this.config.translation.aria ? this.config.translation.aria.slideNumber.replace(/{slideNumber}/g, value) : undefined;\n  }\n  ariaPageLabel(value) {\n    return this.config.translation.aria ? this.config.translation.aria.pageLabel.replace(/{page}/g, value) : undefined;\n  }\n  bindDocumentListeners() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.documentResizeListener) {\n        this.documentResizeListener = this.renderer.listen(this.window, 'resize', event => {\n          this.calculatePosition();\n        });\n      }\n    }\n  }\n  unbindDocumentListeners() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (this.documentResizeListener) {\n        this.documentResizeListener();\n        this.documentResizeListener = null;\n      }\n    }\n  }\n  ngOnDestroy() {\n    if (this.responsiveOptions) {\n      this.unbindDocumentListeners();\n    }\n    if (this.autoplayInterval) {\n      this.stopAutoplay();\n    }\n  }\n  static ɵfac = function Carousel_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Carousel)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Carousel,\n    selectors: [[\"p-carousel\"]],\n    contentQueries: function Carousel_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, Header, 5);\n        i0.ɵɵcontentQuery(dirIndex, Footer, 5);\n        i0.ɵɵcontentQuery(dirIndex, _c0, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c1, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c2, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c3, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c4, 4);\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerFacet = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerFacet = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.itemTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.previousIconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nextIconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function Carousel_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c5, 5);\n        i0.ɵɵviewQuery(_c6, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.itemsContainer = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.indicatorContent = _t.first);\n      }\n    },\n    inputs: {\n      page: \"page\",\n      numVisible: \"numVisible\",\n      numScroll: \"numScroll\",\n      responsiveOptions: \"responsiveOptions\",\n      orientation: \"orientation\",\n      verticalViewPortHeight: \"verticalViewPortHeight\",\n      contentClass: \"contentClass\",\n      indicatorsContentClass: \"indicatorsContentClass\",\n      indicatorsContentStyle: \"indicatorsContentStyle\",\n      indicatorStyleClass: \"indicatorStyleClass\",\n      indicatorStyle: \"indicatorStyle\",\n      value: \"value\",\n      circular: [2, \"circular\", \"circular\", booleanAttribute],\n      showIndicators: [2, \"showIndicators\", \"showIndicators\", booleanAttribute],\n      showNavigators: [2, \"showNavigators\", \"showNavigators\", booleanAttribute],\n      autoplayInterval: [2, \"autoplayInterval\", \"autoplayInterval\", numberAttribute],\n      style: \"style\",\n      styleClass: \"styleClass\",\n      prevButtonProps: \"prevButtonProps\",\n      nextButtonProps: \"nextButtonProps\"\n    },\n    outputs: {\n      onPage: \"onPage\"\n    },\n    features: [i0.ɵɵProvidersFeature([CarouselStyle]), i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c8,\n    decls: 14,\n    vars: 23,\n    consts: [[\"itemsContainer\", \"\"], [\"icon\", \"\"], [\"indicatorContent\", \"\"], [\"role\", \"region\", 3, \"ngClass\", \"ngStyle\"], [\"class\", \"p-carousel-header\", 4, \"ngIf\"], [3, \"ngClass\"], [1, \"p-carousel-content\"], [3, \"ngClass\", \"disabled\", \"text\", \"buttonProps\", \"click\", 4, \"ngIf\"], [1, \"p-carousel-viewport\", 3, \"touchend\", \"touchstart\", \"touchmove\", \"ngStyle\"], [1, \"p-carousel-item-list\", 3, \"transitionend\"], [3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 3, \"ngClass\", \"disabled\", \"buttonProps\", \"text\", \"click\", 4, \"ngIf\"], [3, \"ngClass\", \"class\", \"ngStyle\", \"keydown\", 4, \"ngIf\"], [\"class\", \"p-carousel-footer\", 4, \"ngIf\"], [1, \"p-carousel-header\"], [4, \"ngTemplateOutlet\"], [3, \"click\", \"ngClass\", \"disabled\", \"text\", \"buttonProps\"], [4, \"ngIf\"], [\"class\", \"p-carousel-prev-icon\", 4, \"ngIf\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"styleClass\"], [1, \"p-carousel-prev-icon\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"type\", \"button\", 3, \"click\", \"ngClass\", \"disabled\", \"buttonProps\", \"text\"], [\"class\", \"next\", 4, \"ngIf\"], [1, \"next\"], [3, \"keydown\", \"ngClass\", \"ngStyle\"], [\"type\", \"button\", 3, \"click\", \"ngClass\", \"ngStyle\", \"tabindex\"], [1, \"p-carousel-footer\"]],\n    template: function Carousel_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef(_c7);\n        i0.ɵɵelementStart(0, \"div\", 3);\n        i0.ɵɵtemplate(1, Carousel_div_1_Template, 3, 1, \"div\", 4);\n        i0.ɵɵelementStart(2, \"div\", 5)(3, \"div\", 6);\n        i0.ɵɵtemplate(4, Carousel_p_button_4_Template, 3, 7, \"p-button\", 7);\n        i0.ɵɵelementStart(5, \"div\", 8);\n        i0.ɵɵlistener(\"touchend\", function Carousel_Template_div_touchend_5_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onTouchEnd($event));\n        })(\"touchstart\", function Carousel_Template_div_touchstart_5_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onTouchStart($event));\n        })(\"touchmove\", function Carousel_Template_div_touchmove_5_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onTouchMove($event));\n        });\n        i0.ɵɵelementStart(6, \"div\", 9, 0);\n        i0.ɵɵlistener(\"transitionend\", function Carousel_Template_div_transitionend_6_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onTransitionEnd());\n        });\n        i0.ɵɵtemplate(8, Carousel_div_8_Template, 2, 12, \"div\", 10)(9, Carousel_div_9_Template, 2, 12, \"div\", 10)(10, Carousel_div_10_Template, 2, 9, \"div\", 10);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(11, Carousel_p_button_11_Template, 3, 7, \"p-button\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(12, Carousel_ul_12_Template, 3, 5, \"ul\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(13, Carousel_div_13_Template, 3, 1, \"div\", 13);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(18, _c9, ctx.isVertical(), !ctx.isVertical()))(\"ngStyle\", ctx.style);\n        i0.ɵɵattribute(\"id\", ctx.id);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.headerFacet || ctx.headerTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵclassMap(ctx.contentClass);\n        i0.ɵɵproperty(\"ngClass\", \"p-carousel-content-container\");\n        i0.ɵɵadvance();\n        i0.ɵɵattribute(\"aria-live\", ctx.allowAutoplay ? \"polite\" : \"off\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showNavigators);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(21, _c10, ctx.isVertical() ? ctx.verticalViewPortHeight : \"auto\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.clonedItemsForStarting);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngForOf\", ctx.value);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngForOf\", ctx.clonedItemsForFinishing);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showNavigators);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showIndicators);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.footerFacet || ctx.footerTemplate || ctx._footerTemplate);\n      }\n    },\n    dependencies: [CommonModule, i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, ChevronRightIcon, ButtonModule, i2.Button, ChevronLeftIcon, ChevronDownIcon, ChevronUpIcon, SharedModule],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Carousel, [{\n    type: Component,\n    args: [{\n      selector: 'p-carousel',\n      standalone: true,\n      imports: [CommonModule, ChevronRightIcon, ButtonModule, ChevronLeftIcon, ChevronDownIcon, ChevronUpIcon, SharedModule],\n      template: `\n        <div [attr.id]=\"id\" [ngClass]=\"{ 'p-carousel p-component': true, 'p-carousel-vertical': isVertical(), 'p-carousel-horizontal': !isVertical() }\" [ngStyle]=\"style\" [class]=\"styleClass\" role=\"region\">\n            <div class=\"p-carousel-header\" *ngIf=\"headerFacet || headerTemplate\">\n                <ng-content select=\"p-header\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n            </div>\n            <div [class]=\"contentClass\" [ngClass]=\"'p-carousel-content-container'\">\n                <div class=\"p-carousel-content\" [attr.aria-live]=\"allowAutoplay ? 'polite' : 'off'\">\n                    <p-button\n                        *ngIf=\"showNavigators\"\n                        [ngClass]=\"{ 'p-carousel-prev-button': true, 'p-disabled': isBackwardNavDisabled() }\"\n                        [disabled]=\"isBackwardNavDisabled()\"\n                        [attr.aria-label]=\"ariaPrevButtonLabel()\"\n                        (click)=\"navBackward($event)\"\n                        [text]=\"true\"\n                        [buttonProps]=\"prevButtonProps\"\n                    >\n                        <ng-template #icon>\n                            <ng-container *ngIf=\"!previousIconTemplate && !_previousIconTemplate && !prevButtonProps?.icon\">\n                                <ChevronLeftIcon *ngIf=\"!isVertical()\" [styleClass]=\"'carousel-prev-icon'\" />\n                                <ChevronUpIcon *ngIf=\"isVertical()\" [styleClass]=\"'carousel-prev-icon'\" />\n                            </ng-container>\n                            <span *ngIf=\"(previousIconTemplate || _previousIconTemplate) && !prevButtonProps?.icon\" class=\"p-carousel-prev-icon\">\n                                <ng-template *ngTemplateOutlet=\"previousIconTemplate || _previousIconTemplate\"></ng-template>\n                            </span>\n                        </ng-template>\n                    </p-button>\n                    <div class=\"p-carousel-viewport\" [ngStyle]=\"{ height: isVertical() ? verticalViewPortHeight : 'auto' }\" (touchend)=\"onTouchEnd($event)\" (touchstart)=\"onTouchStart($event)\" (touchmove)=\"onTouchMove($event)\">\n                        <div #itemsContainer class=\"p-carousel-item-list\" (transitionend)=\"onTransitionEnd()\">\n                            <div\n                                *ngFor=\"let item of clonedItemsForStarting; let index = index\"\n                                [ngClass]=\"{\n                                    'p-carousel-item p-carousel-item-clone': true,\n                                    'p-carousel-item-active': totalShiftedItems * -1 === value.length,\n                                    'p-carousel-item-start': 0 === index,\n                                    'p-carousel-item-end': clonedItemsForStarting.length - 1 === index\n                                }\"\n                                [attr.aria-hidden]=\"!(totalShiftedItems * -1 === value.length)\"\n                                [attr.aria-label]=\"ariaSlideNumber(index)\"\n                                [attr.aria-roledescription]=\"ariaSlideLabel()\"\n                            >\n                                <ng-container *ngTemplateOutlet=\"itemTemplate || _itemTemplate; context: { $implicit: item }\"></ng-container>\n                            </div>\n                            <div\n                                *ngFor=\"let item of value; let index = index\"\n                                [ngClass]=\"{\n                                    'p-carousel-item': true,\n                                    'p-carousel-item-active': firstIndex() <= index && lastIndex() >= index,\n                                    'p-carousel-item-start': firstIndex() === index,\n                                    'p-carousel-item-end': lastIndex() === index\n                                }\"\n                                [attr.aria-hidden]=\"!(firstIndex() <= index && lastIndex() >= index)\"\n                                [attr.aria-label]=\"ariaSlideNumber(index)\"\n                                [attr.aria-roledescription]=\"ariaSlideLabel()\"\n                            >\n                                <ng-container *ngTemplateOutlet=\"itemTemplate || _itemTemplate; context: { $implicit: item }\"></ng-container>\n                            </div>\n                            <div\n                                *ngFor=\"let item of clonedItemsForFinishing; let index = index\"\n                                [ngClass]=\"{\n                                    'p-carousel-item p-carousel-item-clone': true,\n                                    'p-carousel-item-active': totalShiftedItems * -1 === numVisible,\n                                    'p-carousel-item-start': 0 === index,\n                                    'p-carousel-item-end': clonedItemsForFinishing.length - 1 === index\n                                }\"\n                            >\n                                <ng-container *ngTemplateOutlet=\"itemTemplate || _itemTemplate; context: { $implicit: item }\"></ng-container>\n                            </div>\n                        </div>\n                    </div>\n                    <p-button\n                        type=\"button\"\n                        *ngIf=\"showNavigators\"\n                        [ngClass]=\"{ 'p-carousel-next-button': true, 'p-disabled': isForwardNavDisabled() }\"\n                        [disabled]=\"isForwardNavDisabled()\"\n                        (click)=\"navForward($event)\"\n                        [attr.aria-label]=\"ariaNextButtonLabel()\"\n                        [buttonProps]=\"nextButtonProps\"\n                        [text]=\"true\"\n                    >\n                        <ng-template #icon>\n                            <ng-container *ngIf=\"!nextIconTemplate && !_nextIconTemplate && !nextButtonProps?.icon\">\n                                <ChevronRightIcon *ngIf=\"!isVertical()\" [styleClass]=\"'carousel-next-icon'\" />\n                                <ChevronDownIcon *ngIf=\"isVertical()\" [styleClass]=\"'carousel-next-icon'\" />\n                            </ng-container>\n                            <span *ngIf=\"nextIconTemplate || (_nextIconTemplate && !nextButtonProps?.icon)\" class=\"next\">\n                                <ng-template *ngTemplateOutlet=\"nextIconTemplate || _nextIconTemplate\"></ng-template>\n                            </span>\n                        </ng-template>\n                    </p-button>\n                </div>\n                <ul #indicatorContent [ngClass]=\"'p-carousel-indicator-list'\" [class]=\"indicatorsContentClass\" [ngStyle]=\"indicatorsContentStyle\" *ngIf=\"showIndicators\" (keydown)=\"onIndicatorKeydown($event)\">\n                    <li *ngFor=\"let totalDot of totalDotsArray(); let i = index\" [ngClass]=\"{ 'p-carousel-indicator': true, 'p-carousel-indicator-active': _page === i }\" [attr.data-pc-section]=\"'indicator'\">\n                        <button\n                            type=\"button\"\n                            [ngClass]=\"'p-carousel-indicator-button'\"\n                            (click)=\"onDotClick($event, i)\"\n                            [class]=\"indicatorStyleClass\"\n                            [ngStyle]=\"indicatorStyle\"\n                            [attr.aria-label]=\"ariaPageLabel(i + 1)\"\n                            [attr.aria-current]=\"_page === i ? 'page' : undefined\"\n                            [tabindex]=\"_page === i ? 0 : -1\"\n                        ></button>\n                    </li>\n                </ul>\n            </div>\n            <div class=\"p-carousel-footer\" *ngIf=\"footerFacet || footerTemplate || _footerTemplate\">\n                <ng-content select=\"p-footer\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"footerTemplate || _footerTemplate\"></ng-container>\n            </div>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [CarouselStyle]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.NgZone\n  }], {\n    page: [{\n      type: Input\n    }],\n    numVisible: [{\n      type: Input\n    }],\n    numScroll: [{\n      type: Input\n    }],\n    responsiveOptions: [{\n      type: Input\n    }],\n    orientation: [{\n      type: Input\n    }],\n    verticalViewPortHeight: [{\n      type: Input\n    }],\n    contentClass: [{\n      type: Input\n    }],\n    indicatorsContentClass: [{\n      type: Input\n    }],\n    indicatorsContentStyle: [{\n      type: Input\n    }],\n    indicatorStyleClass: [{\n      type: Input\n    }],\n    indicatorStyle: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    circular: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    showIndicators: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    showNavigators: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    autoplayInterval: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    prevButtonProps: [{\n      type: Input\n    }],\n    nextButtonProps: [{\n      type: Input\n    }],\n    onPage: [{\n      type: Output\n    }],\n    itemsContainer: [{\n      type: ViewChild,\n      args: ['itemsContainer']\n    }],\n    indicatorContent: [{\n      type: ViewChild,\n      args: ['indicatorContent']\n    }],\n    headerFacet: [{\n      type: ContentChild,\n      args: [Header]\n    }],\n    footerFacet: [{\n      type: ContentChild,\n      args: [Footer]\n    }],\n    itemTemplate: [{\n      type: ContentChild,\n      args: ['item', {\n        descendants: false\n      }]\n    }],\n    headerTemplate: [{\n      type: ContentChild,\n      args: ['header', {\n        descendants: false\n      }]\n    }],\n    footerTemplate: [{\n      type: ContentChild,\n      args: ['footer', {\n        descendants: false\n      }]\n    }],\n    previousIconTemplate: [{\n      type: ContentChild,\n      args: ['previousicon', {\n        descendants: false\n      }]\n    }],\n    nextIconTemplate: [{\n      type: ContentChild,\n      args: ['nexticon', {\n        descendants: false\n      }]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass CarouselModule {\n  static ɵfac = function CarouselModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CarouselModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: CarouselModule,\n    imports: [Carousel, SharedModule],\n    exports: [Carousel, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [Carousel, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CarouselModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Carousel, SharedModule],\n      exports: [Carousel, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Carousel, CarouselClasses, CarouselModule, CarouselStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWA,IAAM,MAAM,CAAC,MAAM;AACnB,IAAM,MAAM,CAAC,QAAQ;AACrB,IAAM,MAAM,CAAC,QAAQ;AACrB,IAAM,MAAM,CAAC,cAAc;AAC3B,IAAM,MAAM,CAAC,UAAU;AACvB,IAAM,MAAM,CAAC,gBAAgB;AAC7B,IAAM,MAAM,CAAC,kBAAkB;AAC/B,IAAM,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;AAC3C,IAAM,MAAM,CAAC,YAAY,UAAU;AACnC,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,yBAAyB;AAC3B;AACA,IAAM,OAAO,SAAO;AAAA,EAClB,QAAQ;AACV;AACA,IAAM,OAAO,SAAO;AAAA,EAClB,0BAA0B;AAAA,EAC1B,cAAc;AAChB;AACA,IAAM,OAAO,CAAC,IAAI,IAAI,QAAQ;AAAA,EAC5B,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,uBAAuB;AACzB;AACA,IAAM,OAAO,SAAO;AAAA,EAClB,WAAW;AACb;AACA,IAAM,OAAO,CAAC,IAAI,IAAI,QAAQ;AAAA,EAC5B,mBAAmB;AAAA,EACnB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,uBAAuB;AACzB;AACA,IAAM,OAAO,SAAO;AAAA,EAClB,0BAA0B;AAAA,EAC1B,cAAc;AAChB;AACA,IAAM,OAAO,SAAO;AAAA,EAClB,wBAAwB;AAAA,EACxB,+BAA+B;AACjC;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,wBAAwB,IAAI,KAAK;AACxC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,aAAa,CAAC;AACjB,IAAG,WAAW,GAAG,wCAAwC,GAAG,GAAG,gBAAgB,EAAE;AACjF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,OAAO,cAAc;AAAA,EACzD;AACF;AACA,SAAS,4EAA4E,IAAI,KAAK;AAC5F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,mBAAmB,EAAE;AAAA,EACvC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,oBAAoB;AAAA,EAClD;AACF;AACA,SAAS,0EAA0E,IAAI,KAAK;AAC1F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,iBAAiB,EAAE;AAAA,EACrC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,oBAAoB;AAAA,EAClD;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,6EAA6E,GAAG,GAAG,mBAAmB,EAAE,EAAE,GAAG,2EAA2E,GAAG,GAAG,iBAAiB,EAAE;AAClO,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,WAAW,CAAC;AAC1C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,WAAW,CAAC;AAAA,EAC3C;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAAC;AACrF,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,mEAAmE,GAAG,GAAG,aAAa;AAAA,EACzG;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,MAAM,EAAE;AACpF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,wBAAwB,OAAO,qBAAqB;AAAA,EAC/F;AACF;AACA,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,2DAA2D,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,mDAAmD,GAAG,GAAG,QAAQ,EAAE;AAAA,EAC9K;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,QAAQ,CAAC,OAAO,wBAAwB,CAAC,OAAO,yBAAyB,EAAE,OAAO,mBAAmB,OAAO,OAAO,OAAO,gBAAgB,KAAK;AAC7J,IAAG,UAAU;AACb,IAAG,WAAW,SAAS,OAAO,wBAAwB,OAAO,0BAA0B,EAAE,OAAO,mBAAmB,OAAO,OAAO,OAAO,gBAAgB,KAAK;AAAA,EAC/J;AACF;AACA,SAAS,6BAA6B,IAAI,KAAK;AAC7C,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,YAAY,EAAE;AACnC,IAAG,WAAW,SAAS,SAAS,uDAAuD,QAAQ;AAC7F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,MAAM,CAAC;AAAA,IAClD,CAAC;AACD,IAAG,WAAW,GAAG,4CAA4C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AACpH,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAc,gBAAgB,GAAG,MAAM,OAAO,sBAAsB,CAAC,CAAC,EAAE,YAAY,OAAO,sBAAsB,CAAC,EAAE,QAAQ,IAAI,EAAE,eAAe,OAAO,eAAe;AACrL,IAAG,YAAY,cAAc,OAAO,oBAAoB,CAAC;AAAA,EAC3D;AACF;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,wBAAwB,IAAI,KAAK;AACxC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,wCAAwC,GAAG,GAAG,gBAAgB,EAAE;AACjF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,WAAW,IAAI;AACrB,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAc,gBAAgB,GAAG,MAAM,OAAO,oBAAoB,OAAO,OAAO,MAAM,QAAQ,MAAM,UAAU,OAAO,uBAAuB,SAAS,MAAM,QAAQ,CAAC;AAClL,IAAG,YAAY,eAAe,EAAE,OAAO,oBAAoB,OAAO,OAAO,MAAM,OAAO,EAAE,cAAc,OAAO,gBAAgB,QAAQ,CAAC,EAAE,wBAAwB,OAAO,eAAe,CAAC;AACvL,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,gBAAgB,OAAO,aAAa,EAAE,2BAA8B,gBAAgB,IAAI,MAAM,OAAO,CAAC;AAAA,EACjJ;AACF;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,wBAAwB,IAAI,KAAK;AACxC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,wCAAwC,GAAG,GAAG,gBAAgB,EAAE;AACjF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,WAAW,IAAI;AACrB,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAc,gBAAgB,GAAG,MAAM,OAAO,WAAW,KAAK,YAAY,OAAO,UAAU,KAAK,UAAU,OAAO,WAAW,MAAM,UAAU,OAAO,UAAU,MAAM,QAAQ,CAAC;AAC1L,IAAG,YAAY,eAAe,EAAE,OAAO,WAAW,KAAK,YAAY,OAAO,UAAU,KAAK,SAAS,EAAE,cAAc,OAAO,gBAAgB,QAAQ,CAAC,EAAE,wBAAwB,OAAO,eAAe,CAAC;AACnM,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,gBAAgB,OAAO,aAAa,EAAE,2BAA8B,gBAAgB,IAAI,MAAM,OAAO,CAAC;AAAA,EACjJ;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,yBAAyB,IAAI,KAAK;AACzC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,yCAAyC,GAAG,GAAG,gBAAgB,EAAE;AAClF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,WAAW,IAAI;AACrB,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAc,gBAAgB,GAAG,MAAM,OAAO,oBAAoB,OAAO,OAAO,YAAY,MAAM,UAAU,OAAO,wBAAwB,SAAS,MAAM,QAAQ,CAAC;AACjL,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,gBAAgB,OAAO,aAAa,EAAE,2BAA8B,gBAAgB,GAAG,MAAM,OAAO,CAAC;AAAA,EAChJ;AACF;AACA,SAAS,8EAA8E,IAAI,KAAK;AAC9F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,oBAAoB,EAAE;AAAA,EACxC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,oBAAoB;AAAA,EAClD;AACF;AACA,SAAS,6EAA6E,IAAI,KAAK;AAC7F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,mBAAmB,EAAE;AAAA,EACvC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,oBAAoB;AAAA,EAClD;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,+EAA+E,GAAG,GAAG,oBAAoB,EAAE,EAAE,GAAG,8EAA8E,GAAG,GAAG,mBAAmB,EAAE;AAC1O,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,WAAW,CAAC;AAC1C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,WAAW,CAAC;AAAA,EAC3C;AACF;AACA,SAAS,mEAAmE,IAAI,KAAK;AAAC;AACtF,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,oEAAoE,GAAG,GAAG,aAAa;AAAA,EAC1G;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,WAAW,GAAG,sDAAsD,GAAG,GAAG,MAAM,EAAE;AACrF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,oBAAoB,OAAO,iBAAiB;AAAA,EACvF;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,oDAAoD,GAAG,GAAG,QAAQ,EAAE;AAAA,EAChL;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,QAAQ,CAAC,OAAO,oBAAoB,CAAC,OAAO,qBAAqB,EAAE,OAAO,mBAAmB,OAAO,OAAO,OAAO,gBAAgB,KAAK;AACrJ,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,oBAAoB,OAAO,qBAAqB,EAAE,OAAO,mBAAmB,OAAO,OAAO,OAAO,gBAAgB,KAAK;AAAA,EACrJ;AACF;AACA,SAAS,8BAA8B,IAAI,KAAK;AAC9C,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,YAAY,EAAE;AACnC,IAAG,WAAW,SAAS,SAAS,wDAAwD,QAAQ;AAC9F,MAAG,cAAc,IAAI;AACrB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,WAAW,MAAM,CAAC;AAAA,IACjD,CAAC;AACD,IAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AACrH,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAc,gBAAgB,GAAG,MAAM,OAAO,qBAAqB,CAAC,CAAC,EAAE,YAAY,OAAO,qBAAqB,CAAC,EAAE,eAAe,OAAO,eAAe,EAAE,QAAQ,IAAI;AACnL,IAAG,YAAY,cAAc,OAAO,oBAAoB,CAAC;AAAA,EAC3D;AACF;AACA,SAAS,6BAA6B,IAAI,KAAK;AAC7C,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,MAAM,CAAC,EAAE,GAAG,UAAU,EAAE;AAC7C,IAAG,WAAW,SAAS,SAAS,qDAAqD,QAAQ;AAC3F,YAAM,QAAW,cAAc,IAAI,EAAE;AACrC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,WAAW,QAAQ,KAAK,CAAC;AAAA,IACxD,CAAC;AACD,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,QAAQ,IAAI;AAClB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAc,gBAAgB,GAAG,MAAM,OAAO,UAAU,KAAK,CAAC;AAC5E,IAAG,YAAY,mBAAmB,WAAW;AAC7C,IAAG,UAAU;AACb,IAAG,WAAW,OAAO,mBAAmB;AACxC,IAAG,WAAW,WAAW,6BAA6B,EAAE,WAAW,OAAO,cAAc,EAAE,YAAY,OAAO,UAAU,QAAQ,IAAI,EAAE;AACrI,IAAG,YAAY,cAAc,OAAO,cAAc,QAAQ,CAAC,CAAC,EAAE,gBAAgB,OAAO,UAAU,QAAQ,SAAS,MAAS;AAAA,EAC3H;AACF;AACA,SAAS,wBAAwB,IAAI,KAAK;AACxC,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,MAAM,IAAI,CAAC;AAChC,IAAG,WAAW,WAAW,SAAS,8CAA8C,QAAQ;AACtF,MAAG,cAAc,IAAI;AACrB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,mBAAmB,MAAM,CAAC;AAAA,IACzD,CAAC;AACD,IAAG,WAAW,GAAG,8BAA8B,GAAG,IAAI,MAAM,EAAE;AAC9D,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,sBAAsB;AAC3C,IAAG,WAAW,WAAW,2BAA2B,EAAE,WAAW,OAAO,sBAAsB;AAC9F,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,OAAO,eAAe,CAAC;AAAA,EAClD;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,yBAAyB,IAAI,KAAK;AACzC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,aAAa,GAAG,CAAC;AACpB,IAAG,WAAW,GAAG,yCAAyC,GAAG,GAAG,gBAAgB,EAAE;AAClF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,OAAO,kBAAkB,OAAO,eAAe;AAAA,EACnF;AACF;AACA,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WAeK,GAAG,sBAAsB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAgCtB,GAAG,iCAAiC,CAAC;AAAA,WACzC,GAAG,6BAA6B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAS1B,GAAG,+BAA+B,CAAC;AAAA,aACxC,GAAG,0BAA0B,CAAC;AAAA,cAC7B,GAAG,2BAA2B,CAAC;AAAA;AAAA,6BAEhB,GAAG,8BAA8B,CAAC,WAAW,GAAG,8BAA8B,CAAC,mBAAmB,GAAG,8BAA8B,CAAC,gBAAgB,GAAG,8BAA8B,CAAC;AAAA;AAAA,qBAE9L,GAAG,kCAAkC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAQzC,GAAG,sCAAsC,CAAC;AAAA,eAC7C,GAAG,qCAAqC,CAAC,IAAI,GAAG,qCAAqC,CAAC,IAAI,GAAG,qCAAqC,CAAC;AAAA,sBAC5H,GAAG,sCAAsC,CAAC;AAAA;AAAA;AAAA;AAAA,kBAI9C,GAAG,qCAAqC,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIzC,GAAG,sCAAsC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoB5D,IAAM,UAAU;AAAA,EACd,MAAM,CAAC;AAAA,IACL;AAAA,EACF,MAAM,CAAC,0BAA0B;AAAA,IAC/B,uBAAuB,SAAS,WAAW;AAAA,IAC3C,yBAAyB,CAAC,SAAS,WAAW;AAAA,EAChD,CAAC;AAAA,EACD,QAAQ;AAAA,EACR,kBAAkB;AAAA,EAClB,SAAS;AAAA,EACT,cAAc,CAAC;AAAA,IACb;AAAA,EACF,MAAM,CAAC,0BAA0B;AAAA,IAC/B,cAAc,SAAS;AAAA,EACzB,CAAC;AAAA,EACD,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW,CAAC;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,MAAM,CAAC,yCAAyC;AAAA,IAC9C,0BAA0B,oBAAoB,OAAO,MAAM,SAAS;AAAA,IACpE,yBAAyB,UAAU;AAAA,IACnC,uBAAuB,MAAM,MAAM,KAAK,YAAY,EAAE,SAAS,MAAM;AAAA,EACvE,CAAC;AAAA,EACD,MAAM,CAAC;AAAA,IACL;AAAA,IACA;AAAA,EACF,MAAM,CAAC,mBAAmB;AAAA,IACxB,0BAA0B,SAAS,WAAW,KAAK,SAAS,SAAS,UAAU,KAAK;AAAA,IACpF,yBAAyB,SAAS,WAAW,MAAM;AAAA,IACnD,uBAAuB,SAAS,UAAU,MAAM;AAAA,EAClD,CAAC;AAAA,EACD,cAAc,CAAC;AAAA,IACb;AAAA,EACF,MAAM,CAAC,0BAA0B;AAAA,IAC/B,cAAc,SAAS;AAAA,EACzB,CAAC;AAAA,EACD,eAAe;AAAA,EACf,WAAW,CAAC;AAAA,IACV;AAAA,IACA;AAAA,EACF,MAAM,CAAC,wBAAwB;AAAA,IAC7B,+BAA+B,SAAS,WAAW;AAAA,EACrD,CAAC;AAAA,EACD,iBAAiB;AAAA,EACjB,QAAQ;AACV;AACA,IAAM,gBAAN,MAAM,uBAAsB,UAAU;AAAA,EACpC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,sBAAsB,mBAAmB;AACvD,cAAQ,+BAA+B,6BAAgC,sBAAsB,cAAa,IAAI,qBAAqB,cAAa;AAAA,IAClJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,eAAc;AAAA,EACzB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,kBAAiB;AAI1B,EAAAA,iBAAgB,MAAM,IAAI;AAI1B,EAAAA,iBAAgB,QAAQ,IAAI;AAI5B,EAAAA,iBAAgB,kBAAkB,IAAI;AAItC,EAAAA,iBAAgB,SAAS,IAAI;AAI7B,EAAAA,iBAAgB,cAAc,IAAI;AAIlC,EAAAA,iBAAgB,UAAU,IAAI;AAI9B,EAAAA,iBAAgB,UAAU,IAAI;AAI9B,EAAAA,iBAAgB,WAAW,IAAI;AAI/B,EAAAA,iBAAgB,MAAM,IAAI;AAI1B,EAAAA,iBAAgB,cAAc,IAAI;AAIlC,EAAAA,iBAAgB,eAAe,IAAI;AAInC,EAAAA,iBAAgB,WAAW,IAAI;AAI/B,EAAAA,iBAAgB,iBAAiB,IAAI;AAIrC,EAAAA,iBAAgB,QAAQ,IAAI;AAC9B,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAM5C,IAAM,WAAN,MAAM,kBAAiB,cAAc;AAAA,EACnC;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,KAAK;AACZ,QAAI,KAAK,aAAa,QAAQ,KAAK,OAAO;AACxC,UAAI,KAAK,kBAAkB;AACzB,aAAK,aAAa;AAAA,MACpB;AACA,UAAI,MAAM,KAAK,SAAS,OAAO,KAAK,UAAU,IAAI,GAAG;AACnD,aAAK,KAAK,IAAI,GAAG;AAAA,MACnB,WAAW,MAAM,KAAK,OAAO;AAC3B,aAAK,KAAK,GAAG,GAAG;AAAA,MAClB;AAAA,IACF;AACA,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,KAAK;AAClB,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,KAAK;AACjB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,KAAK;AACb,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAAA,IAChB,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAAA,IAChB,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,IAAI,aAAa;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAc;AAAA,EACd,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,YAAY;AAAA,IACV,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,OAAO,CAAC;AAAA,EACV;AAAA,EACA,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAkB,OAAO,aAAa;AAAA,EACtC,YAAY,IAAI,MAAM;AACpB,UAAM;AACN,SAAK,KAAK;AACV,SAAK,OAAO;AACZ,SAAK,oBAAoB,KAAK,OAAO,KAAK,YAAY;AACtD,SAAK,SAAS,KAAK,SAAS;AAAA,EAC9B;AAAA,EACA,YAAY,cAAc;AACxB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,UAAI,aAAa,OAAO;AACtB,YAAI,KAAK,YAAY,KAAK,QAAQ;AAChC,eAAK,cAAc;AAAA,QACrB;AAAA,MACF;AACA,UAAI,KAAK,WAAW;AAClB,YAAI,aAAa,YAAY;AAC3B,cAAI,KAAK,mBAAmB;AAC1B,iBAAK,oBAAoB,KAAK;AAAA,UAChC;AACA,cAAI,KAAK,WAAW,GAAG;AACrB,iBAAK,cAAc;AAAA,UACrB;AACA,eAAK,YAAY;AACjB,eAAK,kBAAkB;AAAA,QACzB;AACA,YAAI,aAAa,WAAW;AAC1B,cAAI,KAAK,mBAAmB;AAC1B,iBAAK,mBAAmB,KAAK;AAAA,UAC/B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA,EACA;AAAA,EACA,qBAAqB;AACnB,SAAK,KAAK,KAAK,QAAQ;AACvB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,WAAK,gBAAgB,CAAC,CAAC,KAAK;AAC5B,UAAI,KAAK,UAAU;AACjB,aAAK,cAAc;AAAA,MACrB;AACA,UAAI,KAAK,mBAAmB;AAC1B,aAAK,mBAAmB,KAAK;AAC7B,aAAK,oBAAoB,KAAK;AAAA,MAChC;AACA,WAAK,YAAY;AACjB,WAAK,kBAAkB;AACvB,UAAI,KAAK,mBAAmB;AAC1B,aAAK,sBAAsB;AAAA,MAC7B;AAAA,IACF;AACA,SAAK,WAAW,QAAQ,UAAQ;AAC9B,cAAQ,KAAK,QAAQ,GAAG;AAAA,QACtB,KAAK;AACH,eAAK,gBAAgB,KAAK;AAC1B;AAAA,QACF,KAAK;AACH,eAAK,kBAAkB,KAAK;AAC5B;AAAA,QACF,KAAK;AACH,eAAK,kBAAkB,KAAK;AAC5B;AAAA,QACF,KAAK;AACH,eAAK,wBAAwB,KAAK;AAClC;AAAA,QACF,KAAK;AACH,eAAK,oBAAoB,KAAK;AAC9B;AAAA,QACF;AACE,eAAK,gBAAgB,KAAK;AAC1B;AAAA,MACJ;AAAA,IACF,CAAC;AACD,SAAK,GAAG,cAAc;AAAA,EACxB;AAAA,EACA,wBAAwB;AACtB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,YAAM,aAAa,KAAK,WAAW;AACnC,UAAI,oBAAoB,KAAK;AAC7B,UAAI,KAAK,SAAS,KAAK,mBAAmB,KAAK,UAAU,cAAc,KAAK,cAAc,KAAK,UAAU,eAAe,KAAK,eAAe,KAAK,UAAU,MAAM,WAAW,KAAK,MAAM,SAAS;AAC9L,YAAI,KAAK,kBAAkB;AACzB,eAAK,aAAa,KAAK;AAAA,QACzB;AACA,aAAK,kBAAkB,KAAK,MAAM,SAAS,KAAK,eAAe,KAAK;AACpE,YAAI,OAAO,KAAK;AAChB,YAAI,KAAK,UAAU,MAAM,KAAK,QAAQ,KAAK,UAAU,GAAG;AACtD,iBAAO,KAAK,UAAU,IAAI;AAC1B,eAAK,QAAQ;AACb,eAAK,OAAO,KAAK;AAAA,YACf,MAAM,KAAK;AAAA,UACb,CAAC;AAAA,QACH;AACA,4BAAoB,OAAO,KAAK,aAAa;AAC7C,YAAI,YAAY;AACd,+BAAqB,KAAK;AAAA,QAC5B;AACA,YAAI,SAAS,KAAK,UAAU,IAAI,KAAK,KAAK,iBAAiB,GAAG;AAC5D,+BAAqB,KAAK,KAAK,iBAAiB,KAAK;AACrD,eAAK,wBAAwB;AAAA,QAC/B,OAAO;AACL,eAAK,wBAAwB;AAAA,QAC/B;AACA,YAAI,sBAAsB,KAAK,mBAAmB;AAChD,eAAK,oBAAoB;AAAA,QAC3B;AACA,aAAK,gBAAgB,KAAK;AAC1B,aAAK,UAAU,YAAY,KAAK;AAChC,aAAK,UAAU,aAAa,KAAK;AACjC,aAAK,UAAU,QAAQ,CAAC,GAAG,KAAK,MAAM;AACtC,YAAI,KAAK,UAAU,IAAI,KAAK,KAAK,eAAe,eAAe;AAC7D,eAAK,eAAe,cAAc,MAAM,YAAY,KAAK,WAAW,IAAI,kBAAkB,qBAAqB,MAAM,KAAK,YAAY,UAAU,eAAe,qBAAqB,MAAM,KAAK,YAAY;AAAA,QAC7M;AACA,aAAK,YAAY;AACjB,YAAI,KAAK,oBAAoB,KAAK,WAAW,GAAG;AAC9C,eAAK,cAAc;AAAA,QACrB;AAAA,MACF;AACA,UAAI,YAAY;AACd,YAAI,KAAK,SAAS,GAAG;AACnB,8BAAoB,KAAK,KAAK;AAAA,QAChC,WAAW,sBAAsB,GAAG;AAClC,8BAAoB,KAAK,KAAK,MAAM;AACpC,cAAI,KAAK,iBAAiB,GAAG;AAC3B,iBAAK,wBAAwB;AAAA,UAC/B;AAAA,QACF;AACA,YAAI,sBAAsB,KAAK,mBAAmB;AAChD,eAAK,oBAAoB;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,CAAC,KAAK,eAAe;AACvB,WAAK,gBAAgB,KAAK,SAAS,cAAc,OAAO;AACxD,WAAK,cAAc,OAAO;AAC1B,mBAAa,KAAK,eAAe,SAAS,KAAK,QAAQ,IAAI,GAAG,KAAK;AACnE,WAAK,SAAS,YAAY,KAAK,SAAS,MAAM,KAAK,aAAa;AAAA,IAClE;AACA,QAAI,YAAY;AAAA,eACL,KAAK,EAAE;AAAA,gBACN,MAAM,KAAK,UAAU;AAAA;AAAA;AAGjC,QAAI,KAAK,mBAAmB;AAC1B,WAAK,kBAAkB,KAAK,CAAC,OAAO,UAAU;AAC5C,cAAM,SAAS,MAAM;AACrB,cAAM,SAAS,MAAM;AACrB,YAAI,SAAS;AACb,YAAI,UAAU,QAAQ,UAAU,KAAM,UAAS;AAAA,iBAAY,UAAU,QAAQ,UAAU,KAAM,UAAS;AAAA,iBAAW,UAAU,QAAQ,UAAU,KAAM,UAAS;AAAA,iBAAW,OAAO,WAAW,YAAY,OAAO,WAAW,SAAU,UAAS,OAAO,cAAc,QAAQ,QAAW;AAAA,UAChR,SAAS;AAAA,QACX,CAAC;AAAA,YAAO,UAAS,SAAS,SAAS,KAAK,SAAS,SAAS,IAAI;AAC9D,eAAO,KAAK;AAAA,MACd,CAAC;AACD,eAAS,IAAI,GAAG,IAAI,KAAK,kBAAkB,QAAQ,KAAK;AACtD,YAAI,MAAM,KAAK,kBAAkB,CAAC;AAClC,qBAAa;AAAA,oDAC+B,IAAI,UAAU;AAAA,2BACvC,KAAK,EAAE;AAAA,wCACM,MAAM,IAAI,UAAU;AAAA;AAAA;AAAA;AAAA,MAItD;AAAA,IACF;AACA,SAAK,cAAc,YAAY;AAAA,EACjC;AAAA,EACA,oBAAoB;AAClB,QAAI,KAAK,mBAAmB;AAC1B,UAAI,wBAAwB;AAAA,QAC1B,YAAY,KAAK;AAAA,QACjB,WAAW,KAAK;AAAA,MAClB;AACA,UAAI,OAAO,WAAW,aAAa;AACjC,YAAI,cAAc,OAAO;AACzB,iBAAS,IAAI,GAAG,IAAI,KAAK,kBAAkB,QAAQ,KAAK;AACtD,cAAI,MAAM,KAAK,kBAAkB,CAAC;AAClC,cAAI,SAAS,IAAI,YAAY,EAAE,KAAK,aAAa;AAC/C,oCAAwB;AAAA,UAC1B;AAAA,QACF;AAAA,MACF;AACA,UAAI,KAAK,eAAe,sBAAsB,WAAW;AACvD,YAAI,OAAO,KAAK;AAChB,eAAO,KAAK,MAAM,OAAO,KAAK,aAAa,sBAAsB,SAAS;AAC1E,YAAI,oBAAoB,sBAAsB,YAAY,KAAK,OAAO;AACtE,YAAI,KAAK,WAAW,GAAG;AACrB,+BAAqB,sBAAsB;AAAA,QAC7C;AACA,aAAK,oBAAoB;AACzB,aAAK,aAAa,sBAAsB;AACxC,aAAK,QAAQ;AACb,aAAK,OAAO,KAAK;AAAA,UACf,MAAM,KAAK;AAAA,QACb,CAAC;AAAA,MACH;AACA,UAAI,KAAK,gBAAgB,sBAAsB,YAAY;AACzD,aAAK,cAAc,sBAAsB;AACzC,aAAK,cAAc;AAAA,MACrB;AACA,WAAK,GAAG,aAAa;AAAA,IACvB;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,SAAK,yBAAyB,CAAC;AAC/B,SAAK,0BAA0B,CAAC;AAChC,QAAI,KAAK,WAAW,GAAG;AACrB,WAAK,uBAAuB,KAAK,GAAG,KAAK,MAAM,MAAM,KAAK,KAAK,WAAW,CAAC;AAC3E,WAAK,wBAAwB,KAAK,GAAG,KAAK,MAAM,MAAM,GAAG,KAAK,WAAW,CAAC;AAAA,IAC5E;AAAA,EACF;AAAA,EACA,aAAa;AACX,WAAO,KAAK,WAAW,IAAI,MAAM,KAAK,oBAAoB,KAAK,cAAc,KAAK,oBAAoB;AAAA,EACxG;AAAA,EACA,YAAY;AACV,WAAO,KAAK,WAAW,IAAI,KAAK,aAAa;AAAA,EAC/C;AAAA,EACA,YAAY;AACV,WAAO,KAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM,SAAS,KAAK,eAAe,KAAK,UAAU,IAAI,IAAI;AAAA,EACxG;AAAA,EACA,iBAAiB;AACf,UAAM,YAAY,KAAK,UAAU;AACjC,WAAO,aAAa,IAAI,CAAC,IAAI,MAAM,SAAS,EAAE,KAAK,CAAC;AAAA,EACtD;AAAA,EACA,aAAa;AACX,WAAO,KAAK,gBAAgB;AAAA,EAC9B;AAAA,EACA,aAAa;AACX,WAAO,KAAK,YAAY,KAAK,SAAS,KAAK,MAAM,UAAU,KAAK;AAAA,EAClE;AAAA,EACA,aAAa;AACX,WAAO,KAAK,oBAAoB,KAAK;AAAA,EACvC;AAAA,EACA,uBAAuB;AACrB,WAAO,KAAK,QAAQ,KAAK,KAAK,SAAS,KAAK,UAAU,IAAI,KAAK,CAAC,KAAK,WAAW;AAAA,EAClF;AAAA,EACA,wBAAwB;AACtB,WAAO,KAAK,QAAQ,KAAK,KAAK,SAAS,KAAK,CAAC,KAAK,WAAW;AAAA,EAC/D;AAAA,EACA,UAAU;AACR,WAAO,CAAC,KAAK,SAAS,KAAK,MAAM,WAAW;AAAA,EAC9C;AAAA,EACA,WAAW,GAAG,OAAO;AACnB,QAAI,KAAK,WAAW,KAAK,KAAK,QAAQ,KAAK,UAAU,IAAI,GAAG;AAC1D,WAAK,KAAK,IAAI,KAAK;AAAA,IACrB;AACA,QAAI,KAAK,kBAAkB;AACzB,WAAK,aAAa;AAAA,IACpB;AACA,QAAI,KAAK,EAAE,YAAY;AACrB,QAAE,eAAe;AAAA,IACnB;AAAA,EACF;AAAA,EACA,YAAY,GAAG,OAAO;AACpB,QAAI,KAAK,WAAW,KAAK,KAAK,UAAU,GAAG;AACzC,WAAK,KAAK,GAAG,KAAK;AAAA,IACpB;AACA,QAAI,KAAK,kBAAkB;AACzB,WAAK,aAAa;AAAA,IACpB;AACA,QAAI,KAAK,EAAE,YAAY;AACrB,QAAE,eAAe;AAAA,IACnB;AAAA,EACF;AAAA,EACA,WAAW,GAAG,OAAO;AACnB,QAAI,OAAO,KAAK;AAChB,QAAI,KAAK,kBAAkB;AACzB,WAAK,aAAa;AAAA,IACpB;AACA,QAAI,QAAQ,MAAM;AAChB,WAAK,WAAW,GAAG,KAAK;AAAA,IAC1B,WAAW,QAAQ,MAAM;AACvB,WAAK,YAAY,GAAG,KAAK;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,mBAAmB,OAAO;AACxB,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK;AACH,aAAK,WAAW;AAChB;AAAA,MACF,KAAK;AACH,aAAK,UAAU;AACf;AAAA,IACJ;AAAA,EACF;AAAA,EACA,aAAa;AACX,UAAM,aAAa,CAAC,GAAG,KAAK,KAAK,iBAAiB,eAAe,+BAA+B,CAAC;AACjG,UAAM,cAAc,KAAK,0BAA0B;AACnD,SAAK,wBAAwB,aAAa,cAAc,MAAM,WAAW,SAAS,WAAW,SAAS,IAAI,cAAc,CAAC;AAAA,EAC3H;AAAA,EACA,YAAY;AACV,UAAM,cAAc,KAAK,0BAA0B;AACnD,SAAK,wBAAwB,aAAa,cAAc,KAAK,IAAI,IAAI,cAAc,CAAC;AAAA,EACtF;AAAA,EACA,YAAY;AACV,UAAM,cAAc,KAAK,0BAA0B;AACnD,SAAK,wBAAwB,aAAa,CAAC;AAAA,EAC7C;AAAA,EACA,WAAW;AACT,UAAM,aAAa,CAAC,GAAG,KAAK,KAAK,iBAAiB,eAAe,gCAAgC,CAAC;AAClG,UAAM,cAAc,KAAK,0BAA0B;AACnD,SAAK,wBAAwB,aAAa,WAAW,SAAS,CAAC;AAAA,EACjE;AAAA,EACA,WAAW;AACT,UAAM,aAAa,CAAC,GAAG,KAAK,KAAK,iBAAiB,eAAe,+BAA+B,CAAC;AACjG,UAAM,mBAAmB,WAAW,UAAU,SAAO,aAAa,KAAK,kBAAkB,MAAM,IAAI;AACnG,UAAM,kBAAkB,WAAW,KAAK,iBAAiB,eAAe,sDAAsD;AAC9H,UAAM,cAAc,WAAW,UAAU,SAAO,QAAQ,gBAAgB,aAAa;AACrF,eAAW,WAAW,EAAE,SAAS,CAAC,EAAE,WAAW;AAC/C,eAAW,gBAAgB,EAAE,SAAS,CAAC,EAAE,WAAW;AAAA,EACtD;AAAA,EACA,4BAA4B;AAC1B,UAAM,aAAa,CAAC,GAAG,KAAK,KAAK,iBAAiB,eAAe,+BAA+B,CAAC;AACjG,UAAM,kBAAkB,WAAW,KAAK,iBAAiB,eAAe,sDAAsD;AAC9H,WAAO,WAAW,UAAU,SAAO,QAAQ,gBAAgB,aAAa;AAAA,EAC1E;AAAA,EACA,wBAAwB,SAAS,SAAS;AACxC,UAAM,aAAa,CAAC,GAAG,KAAK,KAAK,iBAAiB,eAAe,+BAA+B,CAAC;AACjG,eAAW,OAAO,EAAE,SAAS,CAAC,EAAE,WAAW;AAC3C,eAAW,OAAO,EAAE,SAAS,CAAC,EAAE,WAAW;AAC3C,eAAW,OAAO,EAAE,SAAS,CAAC,EAAE,MAAM;AAAA,EACxC;AAAA,EACA,KAAK,KAAK,MAAM;AACd,QAAI,oBAAoB,KAAK;AAC7B,UAAM,aAAa,KAAK,WAAW;AACnC,QAAI,QAAQ,MAAM;AAChB,0BAAoB,KAAK,aAAa,OAAO;AAC7C,UAAI,YAAY;AACd,6BAAqB,KAAK;AAAA,MAC5B;AACA,WAAK,wBAAwB;AAAA,IAC/B,OAAO;AACL,2BAAqB,KAAK,aAAa;AACvC,UAAI,KAAK,uBAAuB;AAC9B,6BAAqB,KAAK,iBAAiB,KAAK,aAAa;AAC7D,aAAK,wBAAwB;AAAA,MAC/B;AACA,UAAI,uBAAuB,aAAa,oBAAoB,KAAK,cAAc;AAC/E,aAAO,KAAK,IAAI,KAAK,MAAM,uBAAuB,KAAK,UAAU,CAAC;AAAA,IACpE;AACA,QAAI,cAAc,KAAK,SAAS,KAAK,UAAU,IAAI,KAAK,QAAQ,IAAI;AAClE,0BAAoB,MAAM,KAAK,MAAM,SAAS,KAAK;AACnD,aAAO;AAAA,IACT,WAAW,cAAc,KAAK,SAAS,KAAK,QAAQ,GAAG;AACrD,0BAAoB;AACpB,aAAO,KAAK,UAAU,IAAI;AAAA,IAC5B,WAAW,SAAS,KAAK,UAAU,IAAI,KAAK,KAAK,iBAAiB,GAAG;AACnE,2BAAqB,KAAK,iBAAiB,KAAK,KAAK,aAAa;AAClE,WAAK,wBAAwB;AAAA,IAC/B;AACA,QAAI,KAAK,gBAAgB;AACvB,WAAK,eAAe,cAAc,MAAM,YAAY,KAAK,WAAW,IAAI,kBAAkB,qBAAqB,MAAM,KAAK,YAAY,UAAU,eAAe,qBAAqB,MAAM,KAAK,YAAY;AAC3M,WAAK,eAAe,cAAc,MAAM,aAAa;AAAA,IACvD;AACA,SAAK,oBAAoB;AACzB,SAAK,QAAQ;AACb,SAAK,OAAO,KAAK;AAAA,MACf,MAAM,KAAK;AAAA,IACb,CAAC;AACD,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA,EACA,gBAAgB;AACd,SAAK,WAAW,YAAY,MAAM;AAChC,UAAI,KAAK,UAAU,IAAI,GAAG;AACxB,YAAI,KAAK,SAAS,KAAK,UAAU,IAAI,GAAG;AACtC,eAAK,KAAK,IAAI,CAAC;AAAA,QACjB,OAAO;AACL,eAAK,KAAK,IAAI,KAAK,OAAO,CAAC;AAAA,QAC7B;AAAA,MACF;AAAA,IACF,GAAG,KAAK,gBAAgB;AACxB,SAAK,gBAAgB;AACrB,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA,EACA,aAAa,cAAc,MAAM;AAC/B,QAAI,KAAK,UAAU;AACjB,oBAAc,KAAK,QAAQ;AAC3B,WAAK,WAAW;AAChB,UAAI,aAAa;AACf,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AACA,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA,EACA,YAAY;AACV,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,gBAAgB;AACvB,WAAK,eAAe,cAAc,MAAM,aAAa;AACrD,WAAK,KAAK,SAAS,KAAK,KAAK,SAAS,KAAK,UAAU,IAAI,MAAM,KAAK,WAAW,GAAG;AAChF,aAAK,eAAe,cAAc,MAAM,YAAY,KAAK,WAAW,IAAI,kBAAkB,KAAK,qBAAqB,MAAM,KAAK,YAAY,UAAU,eAAe,KAAK,qBAAqB,MAAM,KAAK,YAAY;AAAA,MACvN;AAAA,IACF;AAAA,EACF;AAAA,EACA,aAAa,GAAG;AACd,QAAI,WAAW,EAAE,eAAe,CAAC;AACjC,SAAK,WAAW;AAAA,MACd,GAAG,SAAS;AAAA,MACZ,GAAG,SAAS;AAAA,IACd;AAAA,EACF;AAAA,EACA,YAAY,GAAG;AACb,QAAI,EAAE,YAAY;AAChB,QAAE,eAAe;AAAA,IACnB;AAAA,EACF;AAAA,EACA,WAAW,GAAG;AACZ,QAAI,WAAW,EAAE,eAAe,CAAC;AACjC,QAAI,KAAK,WAAW,GAAG;AACrB,WAAK,kBAAkB,GAAG,SAAS,QAAQ,KAAK,SAAS,CAAC;AAAA,IAC5D,OAAO;AACL,WAAK,kBAAkB,GAAG,SAAS,QAAQ,KAAK,SAAS,CAAC;AAAA,IAC5D;AAAA,EACF;AAAA,EACA,kBAAkB,GAAG,MAAM;AACzB,QAAI,KAAK,IAAI,IAAI,IAAI,KAAK,gBAAgB;AACxC,UAAI,OAAO,GAAG;AACZ,aAAK,WAAW,CAAC;AAAA,MACnB,OAAO;AACL,aAAK,YAAY,CAAC;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,OAAO,YAAY,OAAO,KAAK,OAAO,YAAY,KAAK,gBAAgB;AAAA,EACrF;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,OAAO,YAAY,OAAO,KAAK,OAAO,YAAY,KAAK,QAAQ;AAAA,EAC7E;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,OAAO,YAAY,OAAO,KAAK,OAAO,YAAY,KAAK,gBAAgB;AAAA,EACrF;AAAA,EACA,gBAAgB,OAAO;AACrB,WAAO,KAAK,OAAO,YAAY,OAAO,KAAK,OAAO,YAAY,KAAK,YAAY,QAAQ,kBAAkB,KAAK,IAAI;AAAA,EACpH;AAAA,EACA,cAAc,OAAO;AACnB,WAAO,KAAK,OAAO,YAAY,OAAO,KAAK,OAAO,YAAY,KAAK,UAAU,QAAQ,WAAW,KAAK,IAAI;AAAA,EAC3G;AAAA,EACA,wBAAwB;AACtB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,UAAI,CAAC,KAAK,wBAAwB;AAChC,aAAK,yBAAyB,KAAK,SAAS,OAAO,KAAK,QAAQ,UAAU,WAAS;AACjF,eAAK,kBAAkB;AAAA,QACzB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,0BAA0B;AACxB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,UAAI,KAAK,wBAAwB;AAC/B,aAAK,uBAAuB;AAC5B,aAAK,yBAAyB;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,mBAAmB;AAC1B,WAAK,wBAAwB;AAAA,IAC/B;AACA,QAAI,KAAK,kBAAkB;AACzB,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,iBAAiB,mBAAmB;AACzD,WAAO,KAAK,qBAAqB,WAAa,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,CAAC;AAAA,EACjH;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,IAC1B,gBAAgB,SAAS,wBAAwB,IAAI,KAAK,UAAU;AAClE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,QAAQ,CAAC;AACrC,QAAG,eAAe,UAAU,QAAQ,CAAC;AACrC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAClE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAClE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AACnE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AACrE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AACrE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,uBAAuB,GAAG;AAC3E,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AACvE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW,SAAS,eAAe,IAAI,KAAK;AAC1C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AACrE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AAAA,MACzE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,mBAAmB;AAAA,MACnB,aAAa;AAAA,MACb,wBAAwB;AAAA,MACxB,cAAc;AAAA,MACd,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,qBAAqB;AAAA,MACrB,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,gBAAgB,CAAC,GAAG,kBAAkB,kBAAkB,gBAAgB;AAAA,MACxE,gBAAgB,CAAC,GAAG,kBAAkB,kBAAkB,gBAAgB;AAAA,MACxE,kBAAkB,CAAC,GAAG,oBAAoB,oBAAoB,eAAe;AAAA,MAC7E,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,IACnB;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,aAAa,CAAC,GAAM,0BAA6B,4BAA+B,oBAAoB;AAAA,IACtI,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,kBAAkB,EAAE,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,QAAQ,UAAU,GAAG,WAAW,SAAS,GAAG,CAAC,SAAS,qBAAqB,GAAG,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,GAAG,WAAW,YAAY,QAAQ,eAAe,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,uBAAuB,GAAG,YAAY,cAAc,aAAa,SAAS,GAAG,CAAC,GAAG,wBAAwB,GAAG,eAAe,GAAG,CAAC,GAAG,WAAW,GAAG,SAAS,SAAS,GAAG,CAAC,QAAQ,UAAU,GAAG,WAAW,YAAY,eAAe,QAAQ,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,WAAW,SAAS,WAAW,WAAW,GAAG,MAAM,GAAG,CAAC,SAAS,qBAAqB,GAAG,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,SAAS,WAAW,YAAY,QAAQ,aAAa,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,SAAS,wBAAwB,GAAG,MAAM,GAAG,CAAC,GAAG,cAAc,GAAG,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,QAAQ,UAAU,GAAG,SAAS,WAAW,YAAY,eAAe,MAAM,GAAG,CAAC,SAAS,QAAQ,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,WAAW,WAAW,SAAS,GAAG,CAAC,QAAQ,UAAU,GAAG,SAAS,WAAW,WAAW,UAAU,GAAG,CAAC,GAAG,mBAAmB,CAAC;AAAA,IAC/pC,UAAU,SAAS,kBAAkB,IAAI,KAAK;AAC5C,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,gBAAgB,GAAG;AACtB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,WAAW,GAAG,yBAAyB,GAAG,GAAG,OAAO,CAAC;AACxD,QAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,QAAG,WAAW,GAAG,8BAA8B,GAAG,GAAG,YAAY,CAAC;AAClE,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,WAAW,YAAY,SAAS,0CAA0C,QAAQ;AACnF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,WAAW,MAAM,CAAC;AAAA,QAC9C,CAAC,EAAE,cAAc,SAAS,4CAA4C,QAAQ;AAC5E,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,aAAa,MAAM,CAAC;AAAA,QAChD,CAAC,EAAE,aAAa,SAAS,2CAA2C,QAAQ;AAC1E,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,YAAY,MAAM,CAAC;AAAA,QAC/C,CAAC;AACD,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,WAAW,iBAAiB,SAAS,iDAAiD;AACvF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,gBAAgB,CAAC;AAAA,QAC7C,CAAC;AACD,QAAG,WAAW,GAAG,yBAAyB,GAAG,IAAI,OAAO,EAAE,EAAE,GAAG,yBAAyB,GAAG,IAAI,OAAO,EAAE,EAAE,IAAI,0BAA0B,GAAG,GAAG,OAAO,EAAE;AACvJ,QAAG,aAAa,EAAE;AAClB,QAAG,WAAW,IAAI,+BAA+B,GAAG,GAAG,YAAY,EAAE;AACrE,QAAG,aAAa;AAChB,QAAG,WAAW,IAAI,yBAAyB,GAAG,GAAG,MAAM,EAAE;AACzD,QAAG,aAAa;AAChB,QAAG,WAAW,IAAI,0BAA0B,GAAG,GAAG,OAAO,EAAE;AAC3D,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,UAAU;AAC5B,QAAG,WAAW,WAAc,gBAAgB,IAAI,KAAK,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,CAAC,CAAC,EAAE,WAAW,IAAI,KAAK;AAC/G,QAAG,YAAY,MAAM,IAAI,EAAE;AAC3B,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,eAAe,IAAI,cAAc;AAC3D,QAAG,UAAU;AACb,QAAG,WAAW,IAAI,YAAY;AAC9B,QAAG,WAAW,WAAW,8BAA8B;AACvD,QAAG,UAAU;AACb,QAAG,YAAY,aAAa,IAAI,gBAAgB,WAAW,KAAK;AAChE,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,cAAc;AACxC,QAAG,UAAU;AACb,QAAG,WAAW,WAAc,gBAAgB,IAAI,MAAM,IAAI,WAAW,IAAI,IAAI,yBAAyB,MAAM,CAAC;AAC7G,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,WAAW,IAAI,sBAAsB;AACnD,QAAG,UAAU;AACb,QAAG,WAAW,WAAW,IAAI,KAAK;AAClC,QAAG,UAAU;AACb,QAAG,WAAW,WAAW,IAAI,uBAAuB;AACpD,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,cAAc;AACxC,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,cAAc;AACxC,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,eAAe,IAAI,kBAAkB,IAAI,eAAe;AAAA,MACpF;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAiB,SAAY,SAAY,MAAS,kBAAqB,SAAS,kBAAkB,cAAiB,QAAQ,iBAAiB,iBAAiB,eAAe,YAAY;AAAA,IACvM,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,kBAAkB,cAAc,iBAAiB,iBAAiB,eAAe,YAAY;AAAA,MACrH,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAgHV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,aAAa;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,QACb,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,QACf,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,QACf,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,QACrB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,QACjB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,UAAU,YAAY;AAAA,IAChC,SAAS,CAAC,UAAU,YAAY;AAAA,EAClC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,UAAU,cAAc,YAAY;AAAA,EAChD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,UAAU,YAAY;AAAA,MAChC,SAAS,CAAC,UAAU,YAAY;AAAA,IAClC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["CarouselClasses"]}
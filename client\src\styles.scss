/* You can add global styles to this file, and also import other style files */
@use './tailwind.css';
@use './assets/layout/layout.scss';
@use 'primeicons/primeicons.css';
@use './assets/demo/demo.scss';

/* Custom UI Fixes */

/* Fix button shadow issues - remove clickable shadows */
.p-button {
    /* Remove any pseudo-element shadows that might be clickable */
    &::before,
    &::after {
        pointer-events: none !important;
    }

    /* Ensure proper spacing between button and text */
    &:not(:last-child) {
        margin-right: 0.5rem;
    }
}

/* Fix gap between buttons and adjacent text */
.flex.gap-2 .p-button {
    margin-right: 0; /* Reset margin since gap handles spacing */
}

/* Ensure buttons don't have overlapping clickable areas */
.p-button-group .p-button,
.flex .p-button {
    position: relative;
    z-index: 1;
}

/* Remove any shadow overlays that might be clickable */
.p-button .p-button-label {
    position: relative;
    z-index: 2;
}

/* Fix button spacing in detail page headers */
.card .flex.justify-content-between .flex.gap-2 {
    gap: 0.5rem !important;
}

.card .flex.justify-content-between .flex.gap-2 .p-button {
    margin: 0 !important;
}

/* Form submit buttons - make them rounded/oval */
form .flex.justify-content-end .p-button[type="submit"],
form .flex.justify-content-end .p-button.p-button-success {
    border-radius: 2rem !important; /* Oval shape */
    padding: 0.75rem 2rem !important; /* Better padding for oval buttons */
}

/* Fix form button visibility issues */
form .flex.justify-content-end {
    position: relative;
    z-index: 10;
    visibility: visible !important;
    opacity: 1 !important;
    display: flex !important;
}

form .flex.justify-content-end .p-button {
    visibility: visible !important;
    opacity: 1 !important;
    display: inline-flex !important;
}

/* Ensure form buttons are always visible regardless of form state */
.card form .flex.justify-content-end {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--surface-border);
    background: var(--surface-card);
    position: sticky;
    bottom: 0;
}

/* Fix any potential z-index issues with form elements */
.p-inputtext,
.p-dropdown,
.p-calendar,
.p-textarea,
.p-checkbox,
.p-radiobutton {
    position: relative;
    z-index: 1;
}

/* Ensure form buttons stay on top */
form .p-button {
    position: relative;
    z-index: 5;
}

/* Fix spacing between back button and text in headers */
.card .flex.justify-content-between .p-button[icon*="arrow-left"] {
    margin-right: 1rem;
}

/* Better spacing for header elements */
.card .flex.justify-content-between {
    gap: 1rem;
}

/* Ensure proper spacing in form headers */
.card .flex.justify-content-between h5,
.card .flex.justify-content-between h2 {
    margin: 0;
    flex-grow: 1;
}

/* Global form button styling - convert old style buttons to new theme style */
form .flex.justify-content-end .p-button[class*="p-button-secondary"] {
    /* Convert old secondary buttons to new rounded outlined style */
    border-radius: 50% !important;
    padding: 0.75rem !important;
    width: 3rem !important;
    height: 3rem !important;
    border: 1px solid var(--p-button-secondary-border-color) !important;
    background: transparent !important;
    color: var(--p-button-secondary-color) !important;
}

form .flex.justify-content-end .p-button[class*="p-button-success"] {
    /* Convert old success buttons to new rounded outlined style */
    border-radius: 50% !important;
    padding: 0.75rem !important;
    width: 3rem !important;
    height: 3rem !important;
    background: transparent !important;
    border: 1px solid var(--p-button-success-border-color) !important;
    color: var(--p-button-success-border-color) !important;
}

/* Hide button labels in forms, show only icons */
form .flex.justify-content-end .p-button .p-button-label {
    display: none !important;
}

/* Ensure icons are centered */
form .flex.justify-content-end .p-button .p-button-icon {
    margin: 0 !important;
}

/* Global styling for "New" buttons in list headers */
.card .flex.justify-content-between .p-button[icon*="plus"] {
    border-radius: 50% !important;
    padding: 0.75rem !important;
    width: 3rem !important;
    height: 3rem !important;
    background: transparent !important;
    border: 1px solid var(--p-button-primary-border-color) !important;
    color: var(--p-button-primary-border-color) !important;
}

/* Hide labels on "New" buttons, show only icons */
.card .flex.justify-content-between .p-button[icon*="plus"] .p-button-label {
    display: none !important;
}

/* Center icons in "New" buttons */
.card .flex.justify-content-between .p-button[icon*="plus"] .p-button-icon {
    margin: 0 !important;
}

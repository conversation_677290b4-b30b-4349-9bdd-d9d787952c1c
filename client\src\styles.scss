/* You can add global styles to this file, and also import other style files */
@use './tailwind.css';
@use './assets/layout/layout.scss';
@use 'primeicons/primeicons.css';
@use './assets/demo/demo.scss';

/* Custom UI Fixes */

/* Fix button shadow issues - remove clickable shadows */
.p-button {
    /* Remove any pseudo-element shadows that might be clickable */
    &::before,
    &::after {
        pointer-events: none !important;
    }

    /* Ensure proper spacing between button and text */
    &:not(:last-child) {
        margin-right: 0.5rem;
    }
}

/* Fix gap between buttons and adjacent text */
.flex.gap-2 .p-button {
    margin-right: 0; /* Reset margin since gap handles spacing */
}

/* Ensure buttons don't have overlapping clickable areas */
.p-button-group .p-button,
.flex .p-button {
    position: relative;
    z-index: 1;
}

/* Remove any shadow overlays that might be clickable */
.p-button .p-button-label {
    position: relative;
    z-index: 2;
}

/* Fix button spacing in detail page headers */
.card .flex.justify-content-between .flex.gap-2 {
    gap: 0.5rem !important;
}

.card .flex.justify-content-between .flex.gap-2 .p-button {
    margin: 0 !important;
}

/* Form submit buttons - make them rounded/oval */
form .flex.justify-content-end .p-button[type="submit"],
form .flex.justify-content-end .p-button.p-button-success {
    border-radius: 2rem !important; /* Oval shape */
    padding: 0.75rem 2rem !important; /* Better padding for oval buttons */
}

/* Fix form button visibility issues */
form .flex.justify-content-end {
    position: relative;
    z-index: 10;
    visibility: visible !important;
    opacity: 1 !important;
    display: flex !important;
}

form .flex.justify-content-end .p-button {
    visibility: visible !important;
    opacity: 1 !important;
    display: inline-flex !important;
}

/* Ensure form buttons are always visible regardless of form state */
.card form .flex.justify-content-end {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--surface-border);
    background: var(--surface-card);
    position: sticky;
    bottom: 0;
}

/* Fix any potential z-index issues with form elements */
.p-inputtext,
.p-dropdown,
.p-calendar,
.p-textarea,
.p-checkbox,
.p-radiobutton {
    position: relative;
    z-index: 1;
}

/* Ensure form buttons stay on top */
form .p-button {
    position: relative;
    z-index: 5;
}

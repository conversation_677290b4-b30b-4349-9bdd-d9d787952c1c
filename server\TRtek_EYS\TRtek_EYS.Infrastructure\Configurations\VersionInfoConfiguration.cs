﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TRtek_EYS.Domain.Entities;

namespace TRtek_EYS.Infrastructure.Configurations
{
    public class VersionInfoConfiguration : IEntityTypeConfiguration<VersionInfo>
    {
        public void Configure(EntityTypeBuilder<VersionInfo> builder)
        {
            builder.ToTable("VersionInfos");

            builder.HasKey(v => v.Id);

            builder.Property(v => v.VersionNumber)
                   .IsRequired()
                   .HasMaxLength(50);

            builder.Property(v => v.ReleaseDate)
                   .IsRequired();

            builder.Property(v => v.ReleaseNotes)
                   .HasMaxLength(1000);

            builder.Property(v => v.Status)
                   .IsRequired();

            builder.Property(v => v.CreatedAt)
                   .IsRequired();

            builder.HasOne(v => v.Project)
                   .WithMany(p => p.Versions)
                   .HasForeignKey(v => v.ProjectId)
                   .OnDelete(DeleteBehavior.Cascade);
        }
    }
}

﻿using MediatR;

namespace TRtek_EYS.Application.Features.Documents.Commands.Add
{
    public class AddDocumentCommand : IRequest<Guid>
    {
        public int Type { get; set; } // Enum (DocumentType)
        public string FileName { get; set; } = null!;
        public string FilePath { get; set; } = null!;
        public long FileSize { get; set; }
        public int VersionNumber { get; set; }
        public Guid ProjectId { get; set; }
    }
}

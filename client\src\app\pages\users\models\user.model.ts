export interface User {
    id: string;
    userCode: string;
    firstName: string;
    lastName: string;
    fullName: string;
    email: string;
    phone?: string;
    avatar?: string;
    department: Department;
    position: string;
    roles: UserRole[];
    permissions: Permission[];
    status: UserStatus;
    isActive: boolean;
    lastLoginDate?: Date;
    createdAt: Date;
    updatedAt: Date;
    createdBy: string;
    updatedBy?: string;
    employeeId?: string;
    hireDate?: Date;
    birthDate?: Date;
    address?: Address;
    emergencyContact?: EmergencyContact;
    skills: Skill[];
    languages: Language[];
    notes?: string;
}

export interface UserRole {
    id: string;
    name: string;
    code: string;
    description: string;
    permissions: Permission[];
    isActive: boolean;
}

export interface Permission {
    id: string;
    name: string;
    code: string;
    module: string;
    action: PermissionAction;
    resource: string;
    description: string;
}

export interface Address {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
}

export interface EmergencyContact {
    name: string;
    relationship: string;
    phone: string;
    email?: string;
}

export interface Skill {
    id: string;
    name: string;
    level: SkillLevel;
    category: SkillCategory;
    yearsOfExperience?: number;
    certifications?: string[];
}

export interface Language {
    id: string;
    name: string;
    level: LanguageLevel;
    isNative: boolean;
}

export interface UserFormData {
    userCode: string;
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    department: Department;
    position: string;
    status: UserStatus;
    employeeId?: string;
    hireDate?: Date;
    birthDate?: Date;
    notes?: string;
}

export interface UserFilter {
    search?: string;
    department?: Department;
    status?: UserStatus;
    roles?: string[];
    isActive?: boolean;
}

export enum UserStatus {
    ACTIVE = 'Aktif',
    INACTIVE = 'Pasif',
    SUSPENDED = 'Askıya Alındı',
    PENDING = 'Beklemede',
    TERMINATED = 'İşten Çıkarıldı'
}

export enum Department {
    DEVELOPMENT = 'Geliştirme',
    DESIGN = 'Tasarım',
    QA = 'Kalite Güvence',
    DEVOPS = 'DevOps',
    MANAGEMENT = 'Yönetim',
    BUSINESS_ANALYSIS = 'İş Analizi',
    SUPPORT = 'Destek',
    SALES = 'Satış',
    MARKETING = 'Pazarlama',
    HR = 'İnsan Kaynakları',
    FINANCE = 'Finans',
    OPERATIONS = 'Operasyon'
}

export enum PermissionAction {
    CREATE = 'Oluştur',
    READ = 'Oku',
    UPDATE = 'Güncelle',
    DELETE = 'Sil',
    APPROVE = 'Onayla',
    REJECT = 'Reddet',
    EXPORT = 'Dışa Aktar',
    IMPORT = 'İçe Aktar'
}

export enum SkillLevel {
    BEGINNER = 'Başlangıç',
    INTERMEDIATE = 'Orta',
    ADVANCED = 'İleri',
    EXPERT = 'Uzman'
}

export enum SkillCategory {
    PROGRAMMING = 'Programlama',
    FRAMEWORK = 'Framework',
    DATABASE = 'Veritabanı',
    CLOUD = 'Bulut',
    DEVOPS = 'DevOps',
    DESIGN = 'Tasarım',
    MANAGEMENT = 'Yönetim',
    SOFT_SKILLS = 'Soft Skills',
    LANGUAGE = 'Dil',
    CERTIFICATION = 'Sertifika'
}

export enum LanguageLevel {
    BEGINNER = 'Başlangıç',
    INTERMEDIATE = 'Orta',
    ADVANCED = 'İleri',
    NATIVE = 'Ana Dil'
}

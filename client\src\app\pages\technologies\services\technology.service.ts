import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Technology, TechnologyFormData, TechnologyType, TechnologyCategory, LicenseType, MaturityLevel, LearningCurve, FeatureImportance, DependencyType } from '../models/technology.model';
import { environment } from '../../../../environments/environment';

@Injectable({
    providedIn: 'root'
})
export class TechnologyService {
    private apiUrl = `${environment.apiUrl}/api/technologies`;
    private useV2Api = false; 

    constructor(private http: HttpClient) {}
    private mockTechnologies: Technology[] = [
        {
            id: '1',
            technologyCode: 'TECH-2024-001',
            name: 'Angular',
            description: 'Google tarafından geliştirilen TypeScript tabanlı web uygulama framework\'ü. Single Page Application (SPA) geliştirmek için kullanılır.',
            type: TechnologyType.FRAMEWORK,
            category: TechnologyCategory.FRONTEND,
            version: '17.0.0',
            latestVersion: '17.3.0',
            vendor: 'Google',
            website: 'https://angular.io',
            documentation: 'https://angular.io/docs',
            repository: 'https://github.com/angular/angular',
            licenseType: LicenseType.OPEN_SOURCE,
            maturityLevel: MaturityLevel.MATURE,
            popularityScore: 85,
            learningCurve: LearningCurve.MODERATE,
            projectCount: 8,
            teamCount: 3,
            isActive: true,
            isRecommended: true,
            isDeprecated: false,
            releaseDate: new Date('2016-09-14'),
            lastUpdateDate: new Date('2024-03-15'),
            createdAt: new Date('2024-01-01'),
            updatedAt: new Date('2024-07-16'),
            createdBy: 'Tech Lead',
            tags: ['typescript', 'spa', 'google', 'frontend'],
            features: [
                {
                    id: 'f1',
                    name: 'Component Architecture',
                    description: 'Modüler component yapısı',
                    isAvailable: true,
                    version: '17.0.0',
                    importance: FeatureImportance.CRITICAL
                },
                {
                    id: 'f2',
                    name: 'Dependency Injection',
                    description: 'Güçlü DI sistemi',
                    isAvailable: true,
                    version: '17.0.0',
                    importance: FeatureImportance.HIGH
                }
            ],
            alternatives: ['React', 'Vue.js', 'Svelte'],
            dependencies: [
                {
                    id: 'd1',
                    name: 'TypeScript',
                    type: DependencyType.RUNTIME,
                    version: '^5.0.0',
                    isRequired: true,
                    description: 'TypeScript derleyici'
                },
                {
                    id: 'd2',
                    name: 'RxJS',
                    type: DependencyType.RUNTIME,
                    version: '^7.8.0',
                    isRequired: true,
                    description: 'Reactive programming kütüphanesi'
                }
            ],
            pros: [
                'Güçlü TypeScript desteği',
                'Kapsamlı CLI araçları',
                'Enterprise-ready',
                'Büyük topluluk desteği'
            ],
            cons: [
                'Öğrenme eğrisi yüksek',
                'Bundle boyutu büyük olabilir',
                'Sık versiyon güncellemeleri'
            ],
            useCases: [
                'Enterprise web uygulamaları',
                'Single Page Applications',
                'Progressive Web Apps',
                'Admin panelleri'
            ],
            notes: 'Ana frontend framework\'ümüz'
        },
        {
            id: '2',
            technologyCode: 'TECH-2024-002',
            name: 'Node.js',
            description: 'Chrome V8 JavaScript engine üzerine kurulu JavaScript runtime. Server-side JavaScript geliştirme için kullanılır.',
            type: TechnologyType.RUNTIME,
            category: TechnologyCategory.BACKEND,
            version: '20.11.0',
            latestVersion: '21.7.1',
            vendor: 'Node.js Foundation',
            website: 'https://nodejs.org',
            documentation: 'https://nodejs.org/docs',
            repository: 'https://github.com/nodejs/node',
            licenseType: LicenseType.OPEN_SOURCE,
            maturityLevel: MaturityLevel.MATURE,
            popularityScore: 92,
            learningCurve: LearningCurve.EASY,
            projectCount: 12,
            teamCount: 4,
            isActive: true,
            isRecommended: true,
            isDeprecated: false,
            releaseDate: new Date('2009-05-27'),
            lastUpdateDate: new Date('2024-02-14'),
            createdAt: new Date('2024-01-05'),
            updatedAt: new Date('2024-07-10'),
            createdBy: 'Backend Team',
            tags: ['javascript', 'runtime', 'server-side', 'v8'],
            features: [
                {
                    id: 'f3',
                    name: 'Event-Driven Architecture',
                    description: 'Asenkron event-driven mimari',
                    isAvailable: true,
                    version: '20.11.0',
                    importance: FeatureImportance.CRITICAL
                },
                {
                    id: 'f4',
                    name: 'NPM Package Manager',
                    description: 'Büyük paket ekosistemi',
                    isAvailable: true,
                    version: '20.11.0',
                    importance: FeatureImportance.HIGH
                }
            ],
            alternatives: ['Deno', 'Bun', 'Python', 'Java'],
            dependencies: [
                {
                    id: 'd3',
                    name: 'V8 Engine',
                    type: DependencyType.RUNTIME,
                    version: '11.8',
                    isRequired: true,
                    description: 'JavaScript engine'
                }
            ],
            pros: [
                'Hızlı geliştirme',
                'Büyük NPM ekosistemi',
                'JavaScript bilgisi yeterli',
                'Yüksek performans'
            ],
            cons: [
                'Single-threaded',
                'CPU-intensive işlerde zayıf',
                'Callback hell riski'
            ],
            useCases: [
                'REST API\'ler',
                'Real-time uygulamalar',
                'Microservices',
                'Build araçları'
            ],
            notes: 'Ana backend runtime\'ımız'
        },
        {
            id: '3',
            technologyCode: 'TECH-2024-003',
            name: 'TypeScript',
            description: 'Microsoft tarafından geliştirilen JavaScript\'in statik tipli süper seti. Büyük ölçekli uygulamalar için tip güvenliği sağlar.',
            type: TechnologyType.PROGRAMMING_LANGUAGE,
            category: TechnologyCategory.WEB,
            version: '5.4.2',
            latestVersion: '5.4.5',
            vendor: 'Microsoft',
            website: 'https://typescriptlang.org',
            documentation: 'https://typescriptlang.org/docs',
            repository: 'https://github.com/microsoft/TypeScript',
            licenseType: LicenseType.OPEN_SOURCE,
            maturityLevel: MaturityLevel.MATURE,
            popularityScore: 88,
            learningCurve: LearningCurve.MODERATE,
            projectCount: 15,
            teamCount: 5,
            isActive: true,
            isRecommended: true,
            isDeprecated: false,
            releaseDate: new Date('2012-10-01'),
            lastUpdateDate: new Date('2024-03-06'),
            createdAt: new Date('2024-01-10'),
            updatedAt: new Date('2024-07-05'),
            createdBy: 'Development Team',
            tags: ['typescript', 'javascript', 'static-typing', 'microsoft'],
            features: [
                {
                    id: 'f5',
                    name: 'Static Typing',
                    description: 'Compile-time tip kontrolü',
                    isAvailable: true,
                    version: '5.4.2',
                    importance: FeatureImportance.CRITICAL
                },
                {
                    id: 'f6',
                    name: 'ES6+ Support',
                    description: 'Modern JavaScript özellikleri',
                    isAvailable: true,
                    version: '5.4.2',
                    importance: FeatureImportance.HIGH
                }
            ],
            alternatives: ['JavaScript', 'Flow', 'Dart'],
            dependencies: [
                {
                    id: 'd4',
                    name: 'Node.js',
                    type: DependencyType.BUILD_TIME,
                    version: '>=16.0.0',
                    isRequired: true,
                    description: 'Runtime environment'
                }
            ],
            pros: [
                'Tip güvenliği',
                'Daha iyi IDE desteği',
                'Refactoring kolaylığı',
                'Büyük projelerde avantaj'
            ],
            cons: [
                'Ek build adımı',
                'Öğrenme eğrisi',
                'Bazen verbose kod'
            ],
            useCases: [
                'Büyük ölçekli uygulamalar',
                'Team geliştirme',
                'Enterprise projeler',
                'Library geliştirme'
            ],
            notes: 'Tüm projelerde zorunlu'
        },
        {
            id: '4',
            technologyCode: 'TECH-2024-004',
            name: 'Docker',
            description: 'Konteynerleştirme platformu. Uygulamaları izole edilmiş ortamlarda paketlemek ve çalıştırmak için kullanılır.',
            type: TechnologyType.PLATFORM,
            category: TechnologyCategory.DEVOPS,
            version: '24.0.5',
            latestVersion: '24.0.7',
            vendor: 'Docker Inc.',
            website: 'https://docker.com',
            documentation: 'https://docs.docker.com',
            repository: 'https://github.com/docker',
            licenseType: LicenseType.OPEN_SOURCE,
            maturityLevel: MaturityLevel.MATURE,
            popularityScore: 95,
            learningCurve: LearningCurve.MODERATE,
            projectCount: 18,
            teamCount: 6,
            isActive: true,
            isRecommended: true,
            isDeprecated: false,
            releaseDate: new Date('2013-03-20'),
            lastUpdateDate: new Date('2024-01-19'),
            createdAt: new Date('2024-01-15'),
            updatedAt: new Date('2024-07-01'),
            createdBy: 'DevOps Team',
            tags: ['container', 'virtualization', 'deployment', 'microservices'],
            features: [
                {
                    id: 'f7',
                    name: 'Container Runtime',
                    description: 'Lightweight konteyner çalıştırma',
                    isAvailable: true,
                    version: '24.0.5',
                    importance: FeatureImportance.CRITICAL
                },
                {
                    id: 'f8',
                    name: 'Docker Compose',
                    description: 'Multi-container orchestration',
                    isAvailable: true,
                    version: '2.24.0',
                    importance: FeatureImportance.HIGH
                }
            ],
            alternatives: ['Podman', 'LXC', 'rkt'],
            dependencies: [
                {
                    id: 'd5',
                    name: 'Linux Kernel',
                    type: DependencyType.RUNTIME,
                    version: '>=3.10',
                    isRequired: true,
                    description: 'Container support için'
                }
            ],
            pros: [
                'Tutarlı deployment',
                'Hızlı başlatma',
                'Resource efficiency',
                'Mikroservis desteği'
            ],
            cons: [
                'Learning curve',
                'Security concerns',
                'Storage complexity'
            ],
            useCases: [
                'Mikroservis deployment',
                'CI/CD pipeline',
                'Development environment',
                'Cloud migration'
            ],
            notes: 'Tüm projelerde standart'
        },
        {
            id: '5',
            technologyCode: 'TECH-2024-005',
            name: 'React',
            description: 'Facebook tarafından geliştirilen JavaScript kütüphanesi. Kullanıcı arayüzleri oluşturmak için component-based yaklaşım kullanır.',
            type: TechnologyType.LIBRARY,
            category: TechnologyCategory.FRONTEND,
            version: '18.2.0',
            latestVersion: '18.3.1',
            vendor: 'Meta (Facebook)',
            website: 'https://react.dev',
            documentation: 'https://react.dev/learn',
            repository: 'https://github.com/facebook/react',
            licenseType: LicenseType.OPEN_SOURCE,
            maturityLevel: MaturityLevel.MATURE,
            popularityScore: 94,
            learningCurve: LearningCurve.EASY,
            projectCount: 5,
            teamCount: 2,
            isActive: true,
            isRecommended: false,
            isDeprecated: false,
            releaseDate: new Date('2013-05-29'),
            lastUpdateDate: new Date('2024-04-25'),
            createdAt: new Date('2024-02-01'),
            updatedAt: new Date('2024-06-20'),
            createdBy: 'Frontend Team',
            tags: ['react', 'javascript', 'ui', 'facebook'],
            features: [
                {
                    id: 'f9',
                    name: 'Virtual DOM',
                    description: 'Performanslı DOM manipülasyonu',
                    isAvailable: true,
                    version: '18.2.0',
                    importance: FeatureImportance.CRITICAL
                },
                {
                    id: 'f10',
                    name: 'Hooks',
                    description: 'Functional component state management',
                    isAvailable: true,
                    version: '18.2.0',
                    importance: FeatureImportance.HIGH
                }
            ],
            alternatives: ['Angular', 'Vue.js', 'Svelte'],
            dependencies: [
                {
                    id: 'd6',
                    name: 'React DOM',
                    type: DependencyType.RUNTIME,
                    version: '18.2.0',
                    isRequired: true,
                    description: 'DOM rendering'
                }
            ],
            pros: [
                'Kolay öğrenme',
                'Büyük ekosistem',
                'Flexible yapı',
                'Strong community'
            ],
            cons: [
                'Sadece view layer',
                'Hızlı değişimler',
                'JSX syntax'
            ],
            useCases: [
                'Single Page Apps',
                'Mobile apps (React Native)',
                'Static sites',
                'Component libraries'
            ],
            notes: 'Alternatif frontend seçeneği'
        },
        {
            id: '6',
            technologyCode: 'TECH-2024-006',
            name: 'jQuery',
            description: 'JavaScript kütüphanesi. DOM manipülasyonu ve AJAX işlemleri için kullanılır. Modern framework\'ler nedeniyle kullanımdan kaldırılıyor.',
            type: TechnologyType.LIBRARY,
            category: TechnologyCategory.FRONTEND,
            version: '3.6.0',
            latestVersion: '3.7.1',
            vendor: 'jQuery Foundation',
            website: 'https://jquery.com',
            documentation: 'https://api.jquery.com',
            repository: 'https://github.com/jquery/jquery',
            licenseType: LicenseType.OPEN_SOURCE,
            maturityLevel: MaturityLevel.LEGACY,
            popularityScore: 45,
            learningCurve: LearningCurve.EASY,
            projectCount: 2,
            teamCount: 1,
            isActive: false,
            isRecommended: false,
            isDeprecated: true,
            releaseDate: new Date('2006-08-26'),
            lastUpdateDate: new Date('2021-03-02'),
            endOfLifeDate: new Date('2024-12-31'),
            createdAt: new Date('2024-03-01'),
            updatedAt: new Date('2024-07-12'),
            createdBy: 'Legacy Team',
            updatedBy: 'Tech Lead',
            tags: ['jquery', 'javascript', 'dom', 'deprecated'],
            features: [
                {
                    id: 'f11',
                    name: 'DOM Manipulation',
                    description: 'Kolay DOM işlemleri',
                    isAvailable: false,
                    version: '3.6.0',
                    importance: FeatureImportance.LOW
                }
            ],
            alternatives: ['Vanilla JavaScript', 'Angular', 'React', 'Vue.js'],
            dependencies: [],
            pros: [
                'Kolay kullanım',
                'Geniş browser desteği',
                'Küçük boyut'
            ],
            cons: [
                'Modern standartlara uygun değil',
                'Performance sorunları',
                'Maintenance yükü'
            ],
            useCases: [
                'Legacy projeler',
                'Basit DOM işlemleri'
            ],
            notes: 'Angular ile değiştirilecek'
        }
    ];

    getTechnologies(): Observable<Technology[]> {
        if (this.useV2Api) {
            return this.http.get<Technology[]>(`${this.apiUrl}/v2`);
        }
        return of(this.mockTechnologies);
    }

    // Doğrudan V2 API'yi çağıran metod
    getTechnologiesV2(): Observable<any[]> {
        return this.http.get<any[]>(`${this.apiUrl}/v2`);
    }

    enableV2Api(): void {
        this.useV2Api = true;
    }

    disableV2Api(): void {
        this.useV2Api = false;
    }

    getTechnologyById(id: string): Observable<Technology | undefined> {
        return of(this.mockTechnologies.find(tech => tech.id === id));
    }

    createTechnology(technologyData: TechnologyFormData): Observable<Technology> {
        // Backend'e gönder
        return this.http.post<Technology>(this.apiUrl, technologyData).pipe(
            catchError(error => {
                console.error('Create technology error:', error);
                // Fallback: Mock data'ya ekle
                const newTechnology: Technology = {
                    id: Date.now().toString(),
                    ...technologyData,
                    projectCount: 0,
                    teamCount: 0,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    createdBy: 'Current User',
                    features: [],
                    dependencies: []
                };
                this.mockTechnologies.push(newTechnology);
                return of(newTechnology);
            })
        );
    }

    updateTechnology(id: string, technologyData: TechnologyFormData): Observable<Technology | undefined> {
        // Backend'e gönder
        return this.http.put<Technology>(`${this.apiUrl}/${id}`, technologyData).pipe(
            catchError((error: any) => {
                console.error('Update technology error:', error);
                // Fallback: Mock data'yı güncelle
                const index = this.mockTechnologies.findIndex(tech => tech.id === id);
                if (index === -1) return of(undefined);

                const existingTechnology = this.mockTechnologies[index];
                const updatedTechnology: Technology = {
                    ...existingTechnology,
                    ...technologyData,
                    updatedAt: new Date(),
                    updatedBy: 'Current User'
                };
                this.mockTechnologies[index] = updatedTechnology;
                return of(updatedTechnology);
            })
        );
    }

    deleteTechnology(id: string): Observable<boolean> {
        const index = this.mockTechnologies.findIndex(tech => tech.id === id);
        if (index === -1) return of(false);
        
        const technology = this.mockTechnologies[index];
        if (technology.projectCount > 0) {
            // Kullanımda olan teknolojiler silinemez
            return of(false);
        }
        
        this.mockTechnologies.splice(index, 1);
        return of(true);
    }

    toggleTechnologyStatus(id: string): Observable<boolean> {
        const technology = this.mockTechnologies.find(t => t.id === id);
        if (!technology) return of(false);
        
        technology.isActive = !technology.isActive;
        technology.updatedAt = new Date();
        technology.updatedBy = 'Current User';
        return of(true);
    }

    toggleRecommendation(id: string): Observable<boolean> {
        const technology = this.mockTechnologies.find(t => t.id === id);
        if (!technology) return of(false);
        
        technology.isRecommended = !technology.isRecommended;
        technology.updatedAt = new Date();
        technology.updatedBy = 'Current User';
        return of(true);
    }
}

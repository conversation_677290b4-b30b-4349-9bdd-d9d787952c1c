﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TRtek_EYS.Application.Common.Interfaces;

namespace TRtek_EYS.Application.Features.Users.Commands.Update
{
    public class UpdateUserCommandHandler : IRequestHandler<UpdateUserCommand, Guid>
    {
        private readonly IApplicationDbContext _context;

        public UpdateUserCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Guid> Handle(UpdateUserCommand request, CancellationToken cancellationToken)
        {
            var user = await _context.Users
                .FirstOrDefaultAsync(u => u.Id == request.Id, cancellationToken);

            if (user is null)
                throw new Exception("Kullanıcı bulunamadı.");

            user.Username = request.Username;
            user.PasswordHash = request.PasswordHash;
            user.FullName = request.FullName;
            user.Email = request.Email;
            user.IsActive = request.IsActive;

            await _context.SaveChangesAsync(cancellationToken);

            return user.Id;
        }
    }
}

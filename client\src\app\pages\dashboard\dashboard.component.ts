import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { ChartModule } from 'primeng/chart';
import { ProgressBar } from 'primeng/progressbar';
import { TagModule } from 'primeng/tag';
import { TimelineModule } from 'primeng/timeline';
import { TooltipModule } from 'primeng/tooltip';
import { SkeletonModule } from 'primeng/skeleton';
import { DashboardService, DashboardStats, RecentActivity, ChartData, PlatformUsage, TechnologyPopularity, DocumentStats } from './services/dashboard.service';

@Component({
    selector: 'app-dashboard',
    standalone: true,
    imports: [
        CommonModule,
        ButtonModule,
        CardModule,
        ChartModule,
        ProgressBar,
        TagModule,
        TimelineModule,
        TooltipModule,
        SkeletonModule
    ],
    template: `
        <div class="grid">
            <!-- Header -->
            <div class="col-12">
                <div class="card mb-0">
                    <div class="flex justify-content-between align-items-center mb-5">
                        <div>
                            <h5 class="m-0">TRtek EYS Dashboard</h5>
                            <p class="text-500 mt-1">Sistem genel durumu ve istatistikleri</p>
                        </div>
                        <div class="flex align-items-center gap-2">
                            <span class="text-500">{{ currentDate | date:'dd MMMM yyyy, HH:mm' }}</span>
                            <p-button
                                icon="pi pi-refresh"
                                class="p-button-rounded p-button-text"
                                pTooltip="Verileri Yenile"
                                (onClick)="refreshData()"
                                [loading]="isLoading">
                            </p-button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- İstatistik Kartları -->
            <div class="col-12 lg:col-6 xl:col-3">
                <div class="card mb-0 hover:shadow-3 transition-all transition-duration-300">
                    <div class="flex justify-content-between mb-3">
                        <div>
                            <span class="block text-500 font-medium mb-3">Kullanıcılar</span>
                            <div class="text-900 font-medium text-xl">
                                <p-skeleton *ngIf="isLoading" width="3rem" height="1.5rem"></p-skeleton>
                                <span *ngIf="!isLoading">{{ dashboardStats?.totalUsers || 0 }}</span>
                            </div>
                        </div>
                        <div class="flex align-items-center justify-content-center bg-blue-100 border-round" style="width:2.5rem;height:2.5rem">
                            <i class="pi pi-users text-blue-500 text-xl"></i>
                        </div>
                    </div>
                    <div *ngIf="!isLoading">
                        <span class="text-green-500 font-medium">{{ dashboardStats?.activeUsers || 0 }} aktif</span>
                        <span class="text-500"> kullanıcı</span>
                    </div>
                    <p-skeleton *ngIf="isLoading" width="8rem" height="1rem"></p-skeleton>
                </div>
            </div>

            <div class="col-12 lg:col-6 xl:col-3">
                <div class="card mb-0 hover:shadow-3 transition-all transition-duration-300">
                    <div class="flex justify-content-between mb-3">
                        <div>
                            <span class="block text-500 font-medium mb-3">Platformlar</span>
                            <div class="text-900 font-medium text-xl">
                                <p-skeleton *ngIf="isLoading" width="3rem" height="1.5rem"></p-skeleton>
                                <span *ngIf="!isLoading">{{ dashboardStats?.totalPlatforms || 0 }}</span>
                            </div>
                        </div>
                        <div class="flex align-items-center justify-content-center bg-orange-100 border-round" style="width:2.5rem;height:2.5rem">
                            <i class="pi pi-server text-orange-500 text-xl"></i>
                        </div>
                    </div>
                    <div *ngIf="!isLoading">
                        <span class="text-green-500 font-medium">{{ dashboardStats?.activePlatforms || 0 }} aktif</span>
                        <span class="text-500"> platform</span>
                    </div>
                    <p-skeleton *ngIf="isLoading" width="8rem" height="1rem"></p-skeleton>
                </div>
            </div>

            <div class="col-12 lg:col-6 xl:col-3">
                <div class="card mb-0 hover:shadow-3 transition-all transition-duration-300">
                    <div class="flex justify-content-between mb-3">
                        <div>
                            <span class="block text-500 font-medium mb-3">Teknolojiler</span>
                            <div class="text-900 font-medium text-xl">
                                <p-skeleton *ngIf="isLoading" width="3rem" height="1.5rem"></p-skeleton>
                                <span *ngIf="!isLoading">{{ dashboardStats?.totalTechnologies || 0 }}</span>
                            </div>
                        </div>
                        <div class="flex align-items-center justify-content-center bg-cyan-100 border-round" style="width:2.5rem;height:2.5rem">
                            <i class="pi pi-code text-cyan-500 text-xl"></i>
                        </div>
                    </div>
                    <div *ngIf="!isLoading">
                        <span class="text-green-500 font-medium">{{ dashboardStats?.recommendedTechnologies || 0 }} önerilen</span>
                        <span class="text-500"> teknoloji</span>
                    </div>
                    <p-skeleton *ngIf="isLoading" width="8rem" height="1rem"></p-skeleton>
                </div>
            </div>

            <div class="col-12 lg:col-6 xl:col-3">
                <div class="card mb-0 hover:shadow-3 transition-all transition-duration-300">
                    <div class="flex justify-content-between mb-3">
                        <div>
                            <span class="block text-500 font-medium mb-3">Dokümanlar</span>
                            <div class="text-900 font-medium text-xl">
                                <p-skeleton *ngIf="isLoading" width="3rem" height="1.5rem"></p-skeleton>
                                <span *ngIf="!isLoading">{{ dashboardStats?.totalDocuments || 0 }}</span>
                            </div>
                        </div>
                        <div class="flex align-items-center justify-content-center bg-purple-100 border-round" style="width:2.5rem;height:2.5rem">
                            <i class="pi pi-file text-purple-500 text-xl"></i>
                        </div>
                    </div>
                    <div *ngIf="!isLoading">
                        <span class="text-green-500 font-medium">{{ dashboardStats?.publishedDocuments || 0 }} yayınlı</span>
                        <span class="text-500"> doküman</span>
                    </div>
                    <p-skeleton *ngIf="isLoading" width="8rem" height="1rem"></p-skeleton>
                </div>
            </div>

            <!-- Grafikler -->
            <div class="col-12 xl:col-6">
                <p-card header="Platform Kullanımı" class="h-full">
                    <div *ngIf="isLoading" class="flex justify-content-center align-items-center" style="height: 300px;">
                        <p-skeleton width="100%" height="300px"></p-skeleton>
                    </div>
                    <p-chart
                        *ngIf="!isLoading && platformChart"
                        type="doughnut"
                        [data]="platformChart"
                        [options]="chartOptions.doughnut"
                        width="100%"
                        height="300px">
                    </p-chart>
                </p-card>
            </div>

            <div class="col-12 xl:col-6">
                <p-card header="Teknoloji Popülerliği" class="h-full">
                    <div *ngIf="isLoading" class="flex justify-content-center align-items-center" style="height: 300px;">
                        <p-skeleton width="100%" height="300px"></p-skeleton>
                    </div>
                    <p-chart
                        *ngIf="!isLoading && technologyChart"
                        type="bar"
                        [data]="technologyChart"
                        [options]="chartOptions.bar"
                        width="100%"
                        height="300px">
                    </p-chart>
                </p-card>
            </div>

            <div class="col-12 xl:col-6">
                <p-card header="Doküman Türleri" class="h-full">
                    <div *ngIf="isLoading" class="flex justify-content-center align-items-center" style="height: 300px;">
                        <p-skeleton width="100%" height="300px"></p-skeleton>
                    </div>
                    <p-chart
                        *ngIf="!isLoading && documentChart"
                        type="pie"
                        [data]="documentChart"
                        [options]="chartOptions.pie"
                        width="100%"
                        height="300px">
                    </p-chart>
                </p-card>
            </div>

            <div class="col-12 xl:col-6">
                <p-card header="Kullanıcı Durumu" class="h-full">
                    <div *ngIf="isLoading" class="flex justify-content-center align-items-center" style="height: 300px;">
                        <p-skeleton width="100%" height="300px"></p-skeleton>
                    </div>
                    <p-chart
                        *ngIf="!isLoading && userChart"
                        type="doughnut"
                        [data]="userChart"
                        [options]="chartOptions.doughnut"
                        width="100%"
                        height="300px">
                    </p-chart>
                </p-card>
            </div>

            <!-- Widget'lar -->
            <div class="col-12 xl:col-4">
                <p-card header="En Çok Kullanılan Platformlar" class="h-full">
                    <div *ngIf="isLoading">
                        <p-skeleton *ngFor="let item of [1,2,3,4,5]" width="100%" height="3rem" class="mb-2"></p-skeleton>
                    </div>
                    <div *ngIf="!isLoading">
                        <div *ngFor="let platform of topPlatforms" class="flex align-items-center justify-content-between mb-3 p-2 border-round hover:bg-gray-50">
                            <div class="flex align-items-center gap-2">
                                <i class="pi pi-server text-blue-500"></i>
                                <div>
                                    <div class="font-medium">{{ platform.name }}</div>
                                    <div class="text-sm text-500">{{ platform.projectCount }} proje</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="font-bold text-primary">{{ platform.percentage }}%</div>
                                <p-progressBar
                                    [value]="platform.percentage"
                                    [style]="{'height': '4px', 'width': '60px'}"
                                    [showValue]="false">
                                </p-progressBar>
                            </div>
                        </div>
                    </div>
                </p-card>
            </div>

            <div class="col-12 xl:col-4">
                <p-card header="Popüler Teknolojiler" class="h-full">
                    <div *ngIf="isLoading">
                        <p-skeleton *ngFor="let item of [1,2,3,4,5]" width="100%" height="3rem" class="mb-2"></p-skeleton>
                    </div>
                    <div *ngIf="!isLoading">
                        <div *ngFor="let tech of topTechnologies" class="flex align-items-center justify-content-between mb-3 p-2 border-round hover:bg-gray-50">
                            <div class="flex align-items-center gap-2">
                                <i class="pi pi-code text-purple-500"></i>
                                <div>
                                    <div class="font-medium">{{ tech.name }}</div>
                                    <div class="text-sm text-500">{{ tech.category }}</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="flex align-items-center gap-2">
                                    <span class="font-bold text-primary">{{ tech.popularityScore }}%</span>
                                    <p-tag
                                        *ngIf="tech.isRecommended"
                                        value="Önerilen"
                                        severity="success"
                                        class="text-xs">
                                    </p-tag>
                                </div>
                            </div>
                        </div>
                    </div>
                </p-card>
            </div>

            <div class="col-12 xl:col-4">
                <p-card header="Son Aktiviteler" class="h-full">
                    <div *ngIf="isLoading">
                        <p-skeleton *ngFor="let item of [1,2,3,4,5]" width="100%" height="3rem" class="mb-2"></p-skeleton>
                    </div>
                    <p-timeline
                        *ngIf="!isLoading && recentActivities"
                        [value]="recentActivities"
                        align="left"
                        class="customized-timeline">
                        <ng-template pTemplate="marker" let-activity>
                            <span class="flex w-2rem h-2rem align-items-center justify-content-center text-white border-circle z-1 shadow-1"
                                  [style]="{'background-color': getActivityColor(activity.severity)}">
                                <i [class]="activity.icon"></i>
                            </span>
                        </ng-template>
                        <ng-template pTemplate="content" let-activity>
                            <div class="p-2">
                                <div class="font-medium text-900">{{ activity.title }}</div>
                                <div class="text-500 text-sm mt-1">{{ activity.description }}</div>
                                <div class="text-500 text-xs mt-2">{{ activity.date | date:'dd/MM/yyyy HH:mm' }}</div>
                            </div>
                        </ng-template>
                    </p-timeline>
                </p-card>
            </div>
        </div>
    `,
    styles: [`
        .customized-timeline .p-timeline-event-content {
            line-height: 1;
        }

        .customized-timeline .p-timeline-event-marker {
            border: 2px solid var(--surface-ground);
        }

        .hover\\:shadow-3:hover {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .transition-all {
            transition: all 0.3s ease;
        }

        .transition-duration-300 {
            transition-duration: 300ms;
        }

        .hover\\:bg-gray-50:hover {
            background-color: #f9fafb;
        }

        .h-full {
            height: 100%;
        }

        .z-1 {
            z-index: 1;
        }
    `]
})
export class DashboardComponent implements OnInit, OnDestroy {
    private destroy$ = new Subject<void>();

    currentDate = new Date();
    isLoading = true;

    // Data properties
    dashboardStats: DashboardStats | null = null;
    recentActivities: RecentActivity[] = [];
    topPlatforms: PlatformUsage[] = [];
    topTechnologies: TechnologyPopularity[] = [];
    documentStats: DocumentStats[] = [];

    // Chart data
    platformChart: ChartData | null = null;
    technologyChart: ChartData | null = null;
    documentChart: ChartData | null = null;
    userChart: ChartData | null = null;

    // Chart options
    chartOptions = {
        doughnut: {
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                }
            },
            responsive: true,
            maintainAspectRatio: false
        },
        pie: {
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                }
            },
            responsive: true,
            maintainAspectRatio: false
        },
        bar: {
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value: any) {
                            return value + '%';
                        }
                    }
                }
            },
            responsive: true,
            maintainAspectRatio: false
        }
    };

    constructor(private dashboardService: DashboardService) {}

    ngOnInit() {
        this.loadDashboardData();

        // Auto refresh every 5 minutes
        setInterval(() => {
            this.loadDashboardData(false);
        }, 5 * 60 * 1000);
    }

    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
    }

    loadDashboardData(showLoading = true) {
        if (showLoading) {
            this.isLoading = true;
        }

        // Load all dashboard data
        this.dashboardService.getDashboardStats()
            .pipe(takeUntil(this.destroy$))
            .subscribe(stats => {
                this.dashboardStats = stats;
            });

        this.dashboardService.getRecentActivities()
            .pipe(takeUntil(this.destroy$))
            .subscribe(activities => {
                this.recentActivities = activities;
            });

        this.dashboardService.getTopPlatforms()
            .pipe(takeUntil(this.destroy$))
            .subscribe(platforms => {
                this.topPlatforms = platforms;
            });

        this.dashboardService.getTopTechnologies()
            .pipe(takeUntil(this.destroy$))
            .subscribe(technologies => {
                this.topTechnologies = technologies;
            });

        this.dashboardService.getDocumentStatsByType()
            .pipe(takeUntil(this.destroy$))
            .subscribe(docStats => {
                this.documentStats = docStats;
            });

        // Load chart data
        this.dashboardService.getPlatformUsageChart()
            .pipe(takeUntil(this.destroy$))
            .subscribe(chartData => {
                this.platformChart = chartData;
            });

        this.dashboardService.getTechnologyPopularityChart()
            .pipe(takeUntil(this.destroy$))
            .subscribe(chartData => {
                this.technologyChart = chartData;
            });

        this.dashboardService.getDocumentTypeChart()
            .pipe(takeUntil(this.destroy$))
            .subscribe(chartData => {
                this.documentChart = chartData;
            });

        this.dashboardService.getUserStatusChart()
            .pipe(takeUntil(this.destroy$))
            .subscribe(chartData => {
                this.userChart = chartData;
                if (showLoading) {
                    this.isLoading = false;
                }
            });
    }

    refreshData() {
        this.currentDate = new Date();
        this.loadDashboardData();
    }

    getActivityColor(severity: string): string {
        switch (severity) {
            case 'success':
                return '#10B981';
            case 'info':
                return '#3B82F6';
            case 'warning':
                return '#F59E0B';
            case 'danger':
                return '#EF4444';
            default:
                return '#6B7280';
        }
    }
}

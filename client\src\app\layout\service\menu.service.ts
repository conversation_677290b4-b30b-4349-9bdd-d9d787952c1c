import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { Permission } from '../../core/models/permission.model';
import { AuthService } from '../../core/services/auth.service';

export interface MenuItem {
    label?: string;
    icon?: string;
    routerLink?: string;
    permission?: Permission[];
    children?: MenuItem[];
}

@Injectable({
    providedIn: 'root'
})
export class MenuService {
    private menuItems = new BehaviorSubject<MenuItem[]>([]);
    menuItems$ = this.menuItems.asObservable();

    constructor(private authService: AuthService) {
        this.initializeMenu();
    }

    private initializeMenu() {
        const allMenuItems: MenuItem[] = [
            {
                label: 'Dashboard',
                icon: 'pi pi-home',
                routerLink: '/'
            },
            {
                label: 'Teams',
                icon: 'pi pi-users',
                permission: [Permission.TEAMS_VIEW],
                children: [
                    {
                        label: 'All Teams',
                        routerLink: '/teams',
                        permission: [Permission.TEAMS_VIEW]
                    },
                    {
                        label: 'Create Team',
                        routerLink: '/teams/create',
                        permission: [Permission.TEAMS_CREATE]
                    }
                ]
            },
            {
                label: 'Projects',
                icon: 'pi pi-folder',
                permission: [Permission.VIEW_PROJECTS],
                children: [
                    {
                        label: 'All Projects',
                        routerLink: '/projects',
                        permission: [Permission.VIEW_PROJECTS]
                    },
                    {
                        label: 'Create Project',
                        routerLink: '/projects/create',
                        permission: [Permission.MANAGE_PROJECTS]
                    },
                    {
                        label: 'Technologies',
                        routerLink: '/projects/technologies',
                        permission: [Permission.VIEW_TECHNOLOGIES]
                    }
                ]
            },
            {
                label: 'Customers',
                icon: 'pi pi-briefcase',
                permission: [Permission.VIEW_CUSTOMERS],
                children: [
                    {
                        label: 'All Customers',
                        routerLink: '/customers',
                        permission: [Permission.VIEW_CUSTOMERS]
                    },
                    {
                        label: 'Create Customer',
                        routerLink: '/customers/create',
                        permission: [Permission.MANAGE_CUSTOMERS]
                    },
                    {
                        label: 'Contracts',
                        routerLink: '/customers/contracts',
                        permission: [Permission.VIEW_CONTRACTS]
                    }
                ]
            }
        ];

        this.menuItems.next(this.filterMenuItemsByPermission(allMenuItems));
    }

    private filterMenuItemsByPermission(items: MenuItem[]): MenuItem[] {
        return items
            .filter(item => {
                // Ana menü öğesi için izin kontrolü
                if (item.permission) {
                    return this.authService.hasAnyPermission(item.permission);
                }
                return true;
            })
            .map(item => {
                // Alt menüler varsa onları da filtrele
                if (item.children) {
                    const filteredChildren = this.filterMenuItemsByPermission(item.children);
                    // Eğer tüm alt menüler filtrelenip boş kaldıysa, ana menüyü de kaldır
                    if (filteredChildren.length === 0) {
                        return null;
                    }
                    return { ...item, children: filteredChildren };
                }
                return item;
            })
            .filter(item => item !== null) as MenuItem[];
    }
}

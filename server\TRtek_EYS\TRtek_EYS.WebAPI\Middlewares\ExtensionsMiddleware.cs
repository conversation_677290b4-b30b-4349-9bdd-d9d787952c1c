﻿using TRtek_EYS.Infrastructure.Persistence;

namespace TRtek_EYS.WebAPI.Middlewares;

public static class ExtensionsMiddleware
{
    public static IApplicationBuilder UseGlobalExceptionHandler(this IApplicationBuilder app)
    {
        return app.UseMiddleware<ExceptionHandler>();
    }

    public static void CreateFirstUser(this IApplicationBuilder app)
    {
        using var scope = app.ApplicationServices.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

        if (!context.Users.Any())
        {
            // 1. Admin rolü var mı kontrol et
            var adminRole = context.Roles.FirstOrDefault(r => r.Name == "Admin");
            if (adminRole is null)
            {
                adminRole = new Role
                {
                    Id = Guid.NewGuid(),
                    Name = "Admin",
                    Description = "Sistem yöneticisi"
                };
                context.Roles.Add(adminRole);
            }

            // 2. Kullanıcı oluştur
            var adminUser = new User
            {
                Id = Guid.NewGuid(),
                FullName = "Sistem Yöneticisi",
                Username = "admin",
                Email = "<EMAIL>",
                PasswordHash = "hashed-password", // şifreyi hashle!
                IsActive = true
            };

            // 3. Kullanıcıya admin rolünü iliştir
            adminUser.UserRoles.Add(new UserRole
            {
                RoleId = adminRole.Id,
                UserId = adminUser.Id
            });

            context.Users.Add(adminUser);
            context.SaveChanges();
        }
    }

}

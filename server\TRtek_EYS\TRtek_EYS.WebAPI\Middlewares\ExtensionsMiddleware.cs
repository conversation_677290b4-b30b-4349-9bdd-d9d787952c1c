﻿using TRtek_EYS.Domain.Entities;
using TRtek_EYS.Infrastructure.Persistence;

namespace TRtek_EYS.WebAPI.Middlewares;

public static class ExtensionsMiddleware
{
    public static void CreateFirstUser(this IApplicationBuilder app)
    {
        using var scope = app.ApplicationServices.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

        if (!context.Users.Any())
        {
            // 1. Eğer hiç takım yoksa bir tane ekle
            var defaultTeam = context.Teams.FirstOrDefault();
            if (defaultTeam == null)
            {
                defaultTeam = new Team
                {
                    Id = Guid.NewGuid(),
                    Name = "Yazılım Ekibi",
                    Description = "İlk sistem takımı",
                    CreatedAt = DateTime.UtcNow
                };
                context.Teams.Add(defaultTeam);
            }

            // 2. Admin rolü var mı kontrol et
            var adminRole = context.Roles.FirstOrDefault(r => r.Name == "Admin");
            if (adminRole is null)
            {
                adminRole = new Role
                {
                    Id = Guid.NewGuid(),
                    Name = "Admin",
                    Description = "Sistem yöneticisi",
                    TeamId = defaultTeam.Id, // FK bağlantısı
                    CreatedAt = DateTime.UtcNow
                };
                context.Roles.Add(adminRole);
            }

            // 3. Admin kullanıcı oluştur
            var adminUser = new User
            {
                Id = Guid.NewGuid(),
                FullName = "Sistem Yöneticisi",
                Username = "admin",
                Email = "<EMAIL>",
                PasswordHash = "hashed-password", // şifreyi hashle!
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            // 4. Kullanıcıya rolü iliştir
            adminUser.UserRoles.Add(new UserRole
            {
                RoleId = adminRole.Id,
                UserId = adminUser.Id
            });

            context.Users.Add(adminUser);
            context.SaveChanges();
        }
    }

}

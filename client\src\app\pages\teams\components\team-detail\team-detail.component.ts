import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { TagModule } from 'primeng/tag';
import { AvatarModule } from 'primeng/avatar';
import { TooltipModule } from 'primeng/tooltip';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToastModule } from 'primeng/toast';
import { ConfirmationService, MessageService } from 'primeng/api';
import { TeamService } from '../../services/team.service';
import { Team } from '../../models/team.model';

@Component({
    standalone: true,
    selector: 'app-team-detail',
    imports: [
        CommonModule,
        RouterModule,
        ButtonModule,
        CardModule,
        TagModule,
        AvatarModule,
        TooltipModule,
        ConfirmDialogModule,
        ToastModule
    ],
    providers: [ConfirmationService, MessageService],
    template: `
        <div class="card" *ngIf="team">
            <div class="flex justify-between align-items-center mb-5">
                <div class="flex align-items-center gap-3">
                    <h2 class="text-2xl font-semibold m-0">{{team.name}}</h2>
                    <p-tag [value]="team.status"
                        [severity]="getStatusSeverity(team.status)">
                    </p-tag>
                </div>
                <div class="flex gap-2">
                    <button pButton 
                        icon="pi pi-arrow-left" 
                        label="Back to Teams"
                        class="p-button-outlined"
                        routerLink=".."></button>
                    <button pButton 
                        icon="pi pi-pencil" 
                        label="Edit"
                        class="p-button-outlined p-button-warning"
                        [routerLink]="['../','edit']"></button>
                    <button pButton 
                        icon="pi pi-trash" 
                        class="p-button-outlined p-button-danger"
                        (click)="confirmDelete()"></button>
                </div>
            </div>

            <div class="grid">
                <div class="col-12 md:col-6">
                    <p-card header="Team Information">
                        <div class="flex flex-column gap-4">
                            <div>
                                <label class="font-medium block mb-2">Description</label>
                                <p class="m-0 text-gray-600">{{team.description}}</p>
                            </div>
                            <div class="flex gap-4">
                                <div>
                                    <label class="font-medium block mb-2">Active Projects</label>
                                    <span class="text-xl text-primary">{{team.activeProjects}}</span>
                                </div>
                                <div>
                                    <label class="font-medium block mb-2">Completed Projects</label>
                                    <span class="text-xl">{{team.completedProjects}}</span>
                                </div>
                            </div>
                            <div>
                                <label class="font-medium block mb-2">Created</label>
                                <p class="m-0 text-gray-600">{{team.createdAt | date:'medium'}}</p>
                            </div>
                            <div>
                                <label class="font-medium block mb-2">Last Updated</label>
                                <p class="m-0 text-gray-600">{{team.updatedAt | date:'medium'}}</p>
                            </div>
                        </div>
                    </p-card>
                </div>

                <div class="col-12 md:col-6">
                    <p-card header="Team Members">
                        <div class="flex flex-column gap-3">
                            <div *ngFor="let member of team.members" 
                                class="flex align-items-center justify-content-between p-3 border-round-lg surface-hover">
                                <div class="flex align-items-center gap-3">
                                    <p-avatar [label]="member.name.charAt(0)"
                                        shape="circle"
                                        [style]="{'background-color': '#6366F1'}"></p-avatar>
                                    <div>
                                        <div class="font-medium">{{member.name}}</div>
                                        <div class="text-sm text-gray-600">{{member.role}}</div>
                                    </div>
                                </div>
                                <p-tag [value]="member.status"
                                    [severity]="getMemberStatusSeverity(member.status)">
                                </p-tag>
                            </div>

                            <div *ngIf="team.members.length === 0" 
                                class="text-center p-4 text-gray-600">
                                No members in this team
                            </div>
                        </div>
                    </p-card>
                </div>
            </div>
        </div>

        <div class="card flex justify-content-center align-items-center" 
            *ngIf="!team" style="min-height: 400px">
            <div class="text-center">
                <i class="pi pi-exclamation-circle text-4xl text-gray-400 mb-3"></i>
                <p class="text-xl text-gray-600">Team not found</p>
                <button pButton 
                    icon="pi pi-arrow-left" 
                    label="Back to Teams"
                    class="p-button-outlined mt-3"
                    routerLink=".."></button>
            </div>
        </div>

        <p-confirmDialog header="Delete Team" 
            icon="pi pi-exclamation-triangle"
            acceptButtonStyleClass="p-button-danger"
            [style]="{width: '450px'}"></p-confirmDialog>
            
        <p-toast></p-toast>
    `
})
export class TeamDetailComponent implements OnInit {
    team?: Team;

    constructor(
        private teamService: TeamService,
        private route: ActivatedRoute,
        private router: Router,
        private confirmationService: ConfirmationService,
        private messageService: MessageService
    ) {}

    ngOnInit() {
        const teamId = this.route.snapshot.paramMap.get('id');
        if (teamId) {
            this.loadTeam(teamId);
        }
    }

    private loadTeam(id: string) {
        this.teamService.getTeamById(id).subscribe(team => {
            this.team = team;
            if (!team) {
                this.messageService.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Team not found'
                });
            }
        });
    }

    confirmDelete() {
        this.confirmationService.confirm({
            message: 'Are you sure you want to delete this team?',
            accept: () => {
                if (this.team) {
                    this.teamService.deleteTeam(this.team.id).subscribe({
                        next: () => {
                            this.messageService.add({
                                severity: 'success',
                                summary: 'Success',
                                detail: 'Team deleted successfully'
                            });
                            setTimeout(() => {
                                this.router.navigate(['..'], { relativeTo: this.route });
                            }, 1500);
                        },
                        error: () => {
                            this.messageService.add({
                                severity: 'error',
                                summary: 'Error',
                                detail: 'Failed to delete team'
                            });
                        }
                    });
                }
            }
        });
    }

    getStatusSeverity(status: any): string {
        switch (status) {
            case 'Aktif':
                return 'success';
            case 'Pasif':
                return 'secondary';
            case 'Askıya Alındı':
                return 'warning';
            case 'Dağıtıldı':
                return 'danger';
            default:
                return 'info';
        }
    }

    getMemberStatusSeverity(status: any): string {
        switch (status) {
            case 'Aktif':
                return 'success';
            case 'Pasif':
                return 'secondary';
            case 'İzinli':
                return 'warning';
            case 'Transfer Edildi':
                return 'info';
            default:
                return 'info';
        }
    }
}

﻿using MediatR;

namespace TRtek_EYS.Application.Features.Users.Commands.Update
{
    public class UpdateUserCommand : IRequest<Guid>
    {
        public Guid Id { get; set; }
        public string Username { get; set; } = null!;
        public string PasswordHash { get; set; } = null!;
        public string FullName { get; set; } = null!;
        public string? Email { get; set; }
        public bool IsActive { get; set; }
    }
}

import { Routes } from '@angular/router';

export default [
    {
        path: '',
        loadComponent: () => import('./components/users-list/users-list.component').then(m => m.UsersListComponent)
    },
    {
        path: 'new',
        loadComponent: () => import('./components/user-form/user-form.component').then(m => m.UserFormComponent)
    },
    {
        path: ':id',
        loadComponent: () => import('./components/user-detail/user-detail.component').then(m => m.UserDetailComponent)
    },
    {
        path: ':id/edit',
        loadComponent: () => import('./components/user-form/user-form.component').then(m => m.UserFormComponent)
    }
] as Routes;

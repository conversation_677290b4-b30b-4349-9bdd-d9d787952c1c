export interface Document {
    id: string;
    documentCode: string;
    title: string;
    description: string;
    type: DocumentType;
    category: DocumentCategory;
    format: DocumentFormat;
    version: string;
    status: DocumentStatus;
    visibility: DocumentVisibility;
    filePath?: string;
    fileSize?: number;
    fileName?: string;
    url?: string;
    content?: string;
    projectId?: string;
    projectName?: string;
    authorId: string;
    authorName: string;
    reviewerId?: string;
    reviewerName?: string;
    approvedById?: string;
    approvedByName?: string;
    isTemplate: boolean;
    isArchived: boolean;
    downloadCount: number;
    viewCount: number;
    createdAt: Date;
    updatedAt: Date;
    publishedAt?: Date;
    reviewedAt?: Date;
    approvedAt?: Date;
    expiryDate?: Date;
    tags: string[];
    attachments: DocumentAttachment[];
    revisions: DocumentRevision[];
    comments: DocumentComment[];
    metadata: DocumentMetadata;
    notes?: string;
}

export interface DocumentAttachment {
    id: string;
    name: string;
    fileName: string;
    filePath: string;
    fileSize: number;
    mimeType: string;
    uploadedAt: Date;
    uploadedBy: string;
}

export interface DocumentRevision {
    id: string;
    version: string;
    description: string;
    changes: string[];
    createdAt: Date;
    createdBy: string;
    filePath?: string;
}

export interface DocumentComment {
    id: string;
    content: string;
    authorId: string;
    authorName: string;
    createdAt: Date;
    updatedAt?: Date;
    isResolved: boolean;
    parentId?: string;
    replies: DocumentComment[];
}

export interface DocumentMetadata {
    keywords: string[];
    language: string;
    audience: string[];
    difficulty: DifficultyLevel;
    estimatedReadTime: number;
    lastReviewDate?: Date;
    nextReviewDate?: Date;
    relatedDocuments: string[];
}

export interface DocumentFormData {
    documentCode: string;
    title: string;
    description: string;
    type: DocumentType;
    category: DocumentCategory;
    format: DocumentFormat;
    version: string;
    status: DocumentStatus;
    visibility: DocumentVisibility;
    url?: string;
    content?: string;
    projectId?: string;
    reviewerId?: string;
    isTemplate: boolean;
    expiryDate?: Date;
    tags: string[];
    notes?: string;
}

export interface DocumentFilter {
    search?: string;
    type?: DocumentType;
    category?: DocumentCategory;
    status?: DocumentStatus;
    authorId?: string;
    projectId?: string;
    isTemplate?: boolean;
    isArchived?: boolean;
}

export enum DocumentType {
    SPECIFICATION = 'Spesifikasyon',
    DESIGN = 'Tasarım',
    TECHNICAL = 'Teknik',
    USER_MANUAL = 'Kullanıcı Kılavuzu',
    API_DOCUMENTATION = 'API Dokümantasyonu',
    TUTORIAL = 'Eğitim',
    POLICY = 'Politika',
    PROCEDURE = 'Prosedür',
    REPORT = 'Rapor',
    PRESENTATION = 'Sunum',
    TEMPLATE = 'Şablon',
    MEETING_NOTES = 'Toplantı Notları',
    REQUIREMENTS = 'Gereksinimler',
    TEST_PLAN = 'Test Planı',
    DEPLOYMENT_GUIDE = 'Deployment Kılavuzu'
}

export enum DocumentCategory {
    PROJECT_MANAGEMENT = 'Proje Yönetimi',
    DEVELOPMENT = 'Geliştirme',
    DESIGN = 'Tasarım',
    TESTING = 'Test',
    DEPLOYMENT = 'Deployment',
    MAINTENANCE = 'Bakım',
    TRAINING = 'Eğitim',
    COMPLIANCE = 'Uyumluluk',
    SECURITY = 'Güvenlik',
    ARCHITECTURE = 'Mimari',
    BUSINESS = 'İş',
    LEGAL = 'Hukuki',
    HR = 'İnsan Kaynakları',
    FINANCE = 'Finans',
    MARKETING = 'Pazarlama',
    LEGACY = "LEGACY",
    DATABASE = "DATABASE"
}

export enum DocumentFormat {
    PDF = 'PDF',
    WORD = 'Word',
    EXCEL = 'Excel',
    POWERPOINT = 'PowerPoint',
    MARKDOWN = 'Markdown',
    HTML = 'HTML',
    TEXT = 'Text',
    IMAGE = 'Resim',
    VIDEO = 'Video',
    AUDIO = 'Ses',
    ARCHIVE = 'Arşiv',
    CODE = 'Kod',
    DIAGRAM = 'Diyagram',
    SPREADSHEET = 'Elektronik Tablo',
    PRESENTATION = 'Sunum'
}

export enum DocumentStatus {
    DRAFT = 'Taslak',
    IN_REVIEW = 'İncelemede',
    APPROVED = 'Onaylandı',
    PUBLISHED = 'Yayınlandı',
    ARCHIVED = 'Arşivlendi',
    REJECTED = 'Reddedildi',
    EXPIRED = 'Süresi Doldu',
    WITHDRAWN = 'Geri Çekildi'
}

export enum DocumentVisibility {
    PUBLIC = 'Herkese Açık',
    INTERNAL = 'Dahili',
    TEAM = 'Takım',
    PROJECT = 'Proje',
    CONFIDENTIAL = 'Gizli',
    RESTRICTED = 'Kısıtlı'
}

export enum DifficultyLevel {
    BEGINNER = 'Başlangıç',
    INTERMEDIATE = 'Orta',
    ADVANCED = 'İleri',
    EXPERT = 'Uzman'
}

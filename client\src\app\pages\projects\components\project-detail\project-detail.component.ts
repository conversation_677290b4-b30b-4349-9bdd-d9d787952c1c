import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { TabViewModule } from 'primeng/tabview';
import { TagModule } from 'primeng/tag';
import { TableModule } from 'primeng/table';
import { CardModule } from 'primeng/card';
import { DividerModule } from 'primeng/divider';
import { Project, ProjectStatus, Priority } from '../../models/project.model';

@Component({
    selector: 'app-project-detail',
    standalone: true,
    imports: [
        CommonModule,
        ButtonModule,
        TabViewModule,
        TagModule,
        TableModule,
        CardModule,
        DividerModule
    ],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <div>
                            <h5 class="m-0">{{ project?.name }}</h5>
                            <p class="text-500 mt-1">{{ project?.projectCode }}</p>
                        </div>
                        <div class="flex gap-2">
                            <p-button 
                                label="Düzenle" 
                                icon="pi pi-pencil" 
                                class="p-button-warning"
                                (onClick)="editProject()">
                            </p-button>
                            <p-button 
                                label="Geri" 
                                icon="pi pi-arrow-left" 
                                class="p-button-secondary"
                                (onClick)="goBack()">
                            </p-button>
                        </div>
                    </div>

                    <p-tabView *ngIf="project">
                        <p-tabPanel header="Genel Bilgiler" leftIcon="pi pi-info-circle">
                            <div class="grid">
                                <div class="col-12 md:col-6">
                                    <p-card header="Proje Bilgileri">
                                        <div class="field">
                                            <label class="font-medium">Proje Kodu:</label>
                                            <div class="mt-1">{{ project.projectCode }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Proje Adı:</label>
                                            <div class="mt-1">{{ project.name }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Açıklama:</label>
                                            <div class="mt-1">{{ project.description }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Durum:</label>
                                            <div class="mt-1">
                                                <p-tag 
                                                    [value]="project.status" 
                                                    [severity]="getStatusSeverity(project.status)">
                                                </p-tag>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Öncelik:</label>
                                            <div class="mt-1">
                                                <p-tag 
                                                    [value]="project.priority" 
                                                    [severity]="getPrioritySeverity(project.priority)">
                                                </p-tag>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                                <div class="col-12 md:col-6">
                                    <p-card header="Teknik Bilgiler">
                                        <div class="field">
                                            <label class="font-medium">Platformlar:</label>
                                            <div class="mt-1 flex gap-1">
                                                <p-tag 
                                                    *ngFor="let platform of project.platforms" 
                                                    [value]="platform" 
                                                    severity="info">
                                                </p-tag>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Mevcut Versiyon:</label>
                                            <div class="mt-1">{{ project.currentVersion }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Repository:</label>
                                            <div class="mt-1">
                                                <a [href]="project.repositoryUrl" target="_blank" class="text-primary">
                                                    {{ project.repositoryUrl }}
                                                </a>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Proje Yöneticisi:</label>
                                            <div class="mt-1">{{ project.projectManager || '-' }}</div>
                                        </div>
                                        <div class="field">
                                            <label class="font-medium">Oluşturma Tarihi:</label>
                                            <div class="mt-1">{{ project.createdDate | date:'dd/MM/yyyy' }}</div>
                                        </div>
                                    </p-card>
                                </div>
                            </div>
                        </p-tabPanel>

                        <p-tabPanel header="Müşteriler" leftIcon="pi pi-building">
                            <p-table [value]="project.customers" [tableStyle]="{ 'min-width': '50rem' }">
                                <ng-template pTemplate="header">
                                    <tr>
                                        <th>Müşteri Adı</th>
                                        <th>İletişim Kişisi</th>
                                        <th>E-posta</th>
                                        <th>Sözleşme Tarihi</th>
                                        <th>Lisans Türü</th>
                                        <th>Durum</th>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="body" let-customer>
                                    <tr>
                                        <td>{{ customer.name }}</td>
                                        <td>{{ customer.contactPerson }}</td>
                                        <td>{{ customer.email }}</td>
                                        <td>{{ customer.contractDate | date:'dd/MM/yyyy' }}</td>
                                        <td>{{ customer.licenseType }}</td>
                                        <td>
                                            <p-tag 
                                                [value]="customer.isActive ? 'Aktif' : 'Pasif'" 
                                                [severity]="customer.isActive ? 'success' : 'danger'">
                                            </p-tag>
                                        </td>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="emptymessage">
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <div class="text-500">Bu projeye henüz müşteri atanmamış</div>
                                        </td>
                                    </tr>
                                </ng-template>
                            </p-table>
                        </p-tabPanel>

                        <p-tabPanel header="Takım Üyeleri" leftIcon="pi pi-users">
                            <p-table [value]="project.teamMembers" [tableStyle]="{ 'min-width': '50rem' }">
                                <ng-template pTemplate="header">
                                    <tr>
                                        <th>Kullanıcı Adı</th>
                                        <th>Rol</th>
                                        <th>Atanma Tarihi</th>
                                        <th>Durum</th>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="body" let-member>
                                    <tr>
                                        <td>{{ member.userName }}</td>
                                        <td>{{ member.role }}</td>
                                        <td>{{ member.assignedDate | date:'dd/MM/yyyy' }}</td>
                                        <td>
                                            <p-tag 
                                                [value]="member.isActive ? 'Aktif' : 'Pasif'" 
                                                [severity]="member.isActive ? 'success' : 'danger'">
                                            </p-tag>
                                        </td>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="emptymessage">
                                    <tr>
                                        <td colspan="4" class="text-center py-4">
                                            <div class="text-500">Bu projeye henüz takım üyesi atanmamış</div>
                                        </td>
                                    </tr>
                                </ng-template>
                            </p-table>
                        </p-tabPanel>
                    </p-tabView>
                </div>
            </div>
        </div>
    `
})
export class ProjectDetailComponent implements OnInit {
    project: Project | null = null;
    projectId: string = '';

    constructor(
        private route: ActivatedRoute,
        private router: Router
    ) {}

    ngOnInit() {
        this.projectId = this.route.snapshot.params['id'];
        this.loadProject();
    }

    loadProject() {
        this.project = {
            id: '1',
            projectCode: 'PRJ-2025-001',
            name: 'E-Ticaret Platformu',
            description: 'Kapsamlı e-ticaret çözümü. Ürün yönetimi, sipariş takibi, ödeme entegrasyonu ve müşteri yönetimi modülleri içerir. Modern teknolojiler kullanılarak geliştirilmiş, ölçeklenebilir ve güvenli bir platform.',
            status: ProjectStatus.DEVELOPMENT,
            platforms: ['Web' as any, 'Mobile' as any],
            technologies: [],
            customers: [
                { id: '1', name: 'ABC Şirketi', contactPerson: 'Ahmet Yılmaz', email: '<EMAIL>', contractDate: new Date('2025-01-15'), licenseType: 'PERPETUAL' as any, isActive: true },
                { id: '2', name: 'XYZ Ltd.', contactPerson: 'Ayşe Demir', email: '<EMAIL>', contractDate: new Date('2025-02-01'), licenseType: 'SUBSCRIPTION' as any, isActive: true }
            ],
            teamMembers: [
                { id: '1', userId: 'u1', userName: 'Mehmet Özkan', role: 'Proje Yöneticisi', assignedDate: new Date('2025-01-01'), isActive: true },
                { id: '2', userId: 'u2', userName: 'Zeynep Arslan', role: 'Frontend Developer', assignedDate: new Date('2025-01-02'), isActive: true },
                { id: '3', userId: 'u3', userName: 'Ali Çelik', role: 'Backend Developer', assignedDate: new Date('2025-01-02'), isActive: true }
            ],
            repositoryUrl: 'https://github.com/company/ecommerce-platform',
            currentVersion: 'v2.1.0',
            createdDate: new Date('2025-01-01'),
            lastUpdated: new Date('2025-01-15'),
            startDate: new Date('2025-01-01'),
            priority: Priority.HIGH,
            projectManager: 'Mehmet Özkan'
        };
    }

    editProject() {
        this.router.navigate(['/pages/projects', this.projectId, 'edit']);
    }

    goBack() {
        this.router.navigate(['/pages/projects']);
    }

    getStatusSeverity(status: ProjectStatus): string {
        switch (status) {
            case ProjectStatus.COMPLETED:
                return 'success';
            case ProjectStatus.DEVELOPMENT:
                return 'info';
            case ProjectStatus.TESTING:
                return 'warning';
            case ProjectStatus.MAINTENANCE:
                return 'secondary';
            case ProjectStatus.CANCELLED:
                return 'danger';
            default:
                return 'info';
        }
    }

    getPrioritySeverity(priority: Priority): string {
        switch (priority) {
            case Priority.CRITICAL:
                return 'danger';
            case Priority.HIGH:
                return 'warning';
            case Priority.MEDIUM:
                return 'info';
            case Priority.LOW:
                return 'secondary';
            default:
                return 'info';
        }
    }
}

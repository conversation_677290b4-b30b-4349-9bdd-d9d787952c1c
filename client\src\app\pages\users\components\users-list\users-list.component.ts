import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Table, TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { InputIconModule } from 'primeng/inputicon';
import { IconFieldModule } from 'primeng/iconfield';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToastModule } from 'primeng/toast';
import { AvatarModule } from 'primeng/avatar';
import { ConfirmationService, MessageService } from 'primeng/api';
import { User, UserStatus, Department } from '../../models/user.model';
import { UserService } from '../../services/user.service';

@Component({
    selector: 'app-users-list',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        TableModule,
        ButtonModule,
        InputTextModule,
        InputIconModule,
        IconFieldModule,
        TagModule,
        TooltipModule,
        ConfirmDialogModule,
        ToastModule,
        AvatarModule
    ],
    providers: [ConfirmationService, MessageService],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <h5>Kullanıcı Yönetimi</h5>
                        <p-button 
                            label="Yeni Kullanıcı" 
                            icon="pi pi-plus" 
                            (onClick)="createUser()"
                            severity="primary"
                            size="small">
                        </p-button>
                    </div>

                    <p-table 
                        #dt 
                        [value]="users" 
                        [rows]="10" 
                        [paginator]="true"
                        [globalFilterFields]="['userCode', 'fullName', 'email', 'department', 'position']"
                        [tableStyle]="{ 'min-width': '75rem' }"
                        [rowHover]="true"
                        dataKey="id"
                        currentPageReportTemplate="{first} - {last} / {totalRecords} kullanıcı"
                        [showCurrentPageReport]="true">
                        
                        <ng-template pTemplate="caption">
                            <div class="flex align-items-center justify-content-between">
                                <h5 class="m-0">Kullanıcılar</h5>
                                <p-iconfield iconPosition="left">
                                    <p-inputicon>
                                        <i class="pi pi-search"></i>
                                    </p-inputicon>
                                    <input 
                                        pInputText 
                                        type="text" 
                                        (input)="dt.filterGlobal($any($event.target).value, 'contains')" 
                                        placeholder="Kullanıcı ara..." />
                                </p-iconfield>
                            </div>
                        </ng-template>

                        <ng-template pTemplate="header">
                            <tr>
                                <th pSortableColumn="userCode">
                                    Kullanıcı Kodu <p-sortIcon field="userCode"></p-sortIcon>
                                </th>
                                <th pSortableColumn="fullName">
                                    Ad Soyad <p-sortIcon field="fullName"></p-sortIcon>
                                </th>
                                <th pSortableColumn="email">
                                    E-posta <p-sortIcon field="email"></p-sortIcon>
                                </th>
                                <th pSortableColumn="department">
                                    Departman <p-sortIcon field="department"></p-sortIcon>
                                </th>
                                <th pSortableColumn="position">
                                    Pozisyon <p-sortIcon field="position"></p-sortIcon>
                                </th>
                                <th>Roller</th>
                                <th pSortableColumn="lastLoginDate">
                                    Son Giriş <p-sortIcon field="lastLoginDate"></p-sortIcon>
                                </th>
                                <th pSortableColumn="status">
                                    Durum <p-sortIcon field="status"></p-sortIcon>
                                </th>
                                <th>İşlemler</th>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="body" let-user>
                            <tr>
                                <td>
                                    <span class="font-medium">{{ user.userCode }}</span>
                                </td>
                                <td>
                                    <div class="flex align-items-center gap-2">
                                        <p-avatar 
                                            [label]="getInitials(user.fullName)"
                                            shape="circle"
                                            size="normal">
                                        </p-avatar>
                                        <div>
                                            <span class="font-medium">{{ user.fullName }}</span>
                                            <div class="text-sm text-500 mt-1">{{ user.employeeId }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <span>{{ user.email }}</span>
                                        <div class="text-sm text-500 mt-1" *ngIf="user.phone">{{ user.phone }}</div>
                                    </div>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="user.department" 
                                        severity="info">
                                    </p-tag>
                                </td>
                                <td>
                                    <span>{{ user.position }}</span>
                                </td>
                                <td>
                                    <div class="flex flex-wrap gap-1">
                                        <p-tag 
                                            *ngFor="let role of user.roles.slice(0, 2)" 
                                            [value]="role.name"
                                            severity="secondary"
                                            class="text-xs">
                                        </p-tag>
                                        <p-tag 
                                            *ngIf="user.roles.length > 2"
                                            [value]="'+' + (user.roles.length - 2)"
                                            severity="secondary"
                                            class="text-xs">
                                        </p-tag>
                                    </div>
                                </td>
                                <td>
                                    <span *ngIf="user.lastLoginDate">{{ user.lastLoginDate | date:'dd/MM/yyyy HH:mm' }}</span>
                                    <span *ngIf="!user.lastLoginDate" class="text-500">Hiç giriş yapmadı</span>
                                </td>
                                <td>
                                    <p-tag 
                                        [value]="user.status" 
                                        [severity]="getStatusSeverity(user.status)">
                                    </p-tag>
                                </td>
                                <td>
                                    <div class="flex gap-2">
                                        <p-button 
                                            icon="pi pi-eye" 
                                            class="p-button-rounded p-button-text p-button-info"
                                            pTooltip="Detay"
                                            (onClick)="viewUser(user.id)">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-pencil" 
                                            class="p-button-rounded p-button-text p-button-warning"
                                            pTooltip="Düzenle"
                                            (onClick)="editUser(user.id)">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-pause" 
                                            class="p-button-rounded p-button-text p-button-secondary"
                                            pTooltip="Askıya Al"
                                            (onClick)="suspendUser(user)"
                                            *ngIf="user.status === 'Aktif'">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-play" 
                                            class="p-button-rounded p-button-text p-button-success"
                                            pTooltip="Aktifleştir"
                                            (onClick)="activateUser(user)"
                                            *ngIf="user.status === 'Askıya Alındı'">
                                        </p-button>
                                        <p-button 
                                            icon="pi pi-trash" 
                                            class="p-button-rounded p-button-text p-button-danger"
                                            pTooltip="Sil"
                                            (onClick)="deleteUser(user)">
                                        </p-button>
                                    </div>
                                </td>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="emptymessage">
                            <tr>
                                <td colspan="9" class="text-center py-4">
                                    <i class="pi pi-info-circle text-4xl text-500 mb-3"></i>
                                    <div class="text-500 font-medium">Henüz kullanıcı bulunmuyor</div>
                                    <div class="text-500">Yeni kullanıcı eklemek için "Yeni Kullanıcı" butonunu kullanın</div>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                </div>
            </div>
        </div>

        <p-confirmDialog></p-confirmDialog>
        <p-toast></p-toast>
    `
})
export class UsersListComponent implements OnInit {
    @ViewChild('dt') table!: Table;
    
    users: User[] = [];

    constructor(
        private userService: UserService,
        private router: Router,
        private confirmationService: ConfirmationService,
        private messageService: MessageService
    ) {}

    ngOnInit() {
        this.loadUsers();
    }

    loadUsers() {
        this.userService.getUsers().subscribe(users => {
            this.users = users;
        });
    }

    createUser() {
        this.router.navigate(['/pages/users/new']);
    }

    viewUser(id: string) {
        this.router.navigate(['/pages/users', id]);
    }

    editUser(id: string) {
        this.router.navigate(['/pages/users', id, 'edit']);
    }

    suspendUser(user: User) {
        this.confirmationService.confirm({
            message: `"${user.fullName}" kullanıcısını askıya almak istediğinizden emin misiniz?`,
            header: 'Kullanıcı Askıya Alma Onayı',
            icon: 'pi pi-pause',
            acceptLabel: 'Evet',
            rejectLabel: 'Hayır',
            accept: () => {
                this.userService.suspendUser(user.id).subscribe(() => {
                    this.loadUsers();
                    this.messageService.add({
                        severity: 'info',
                        summary: 'Başarılı',
                        detail: 'Kullanıcı askıya alındı'
                    });
                });
            }
        });
    }

    activateUser(user: User) {
        this.confirmationService.confirm({
            message: `"${user.fullName}" kullanıcısını aktifleştirmek istediğinizden emin misiniz?`,
            header: 'Kullanıcı Aktifleştirme Onayı',
            icon: 'pi pi-play',
            acceptLabel: 'Evet',
            rejectLabel: 'Hayır',
            accept: () => {
                this.userService.activateUser(user.id).subscribe(() => {
                    this.loadUsers();
                    this.messageService.add({
                        severity: 'success',
                        summary: 'Başarılı',
                        detail: 'Kullanıcı aktifleştirildi'
                    });
                });
            }
        });
    }

    deleteUser(user: User) {
        this.confirmationService.confirm({
            message: `"${user.fullName}" kullanıcısını silmek istediğinizden emin misiniz?`,
            header: 'Kullanıcı Silme Onayı',
            icon: 'pi pi-exclamation-triangle',
            acceptLabel: 'Evet',
            rejectLabel: 'Hayır',
            accept: () => {
                this.userService.deleteUser(user.id).subscribe(() => {
                    this.users = this.users.filter(u => u.id !== user.id);
                    this.messageService.add({
                        severity: 'success',
                        summary: 'Başarılı',
                        detail: 'Kullanıcı başarıyla silindi'
                    });
                });
            }
        });
    }

    getInitials(fullName: string): string {
        return fullName.split(' ').map(name => name.charAt(0)).join('').toUpperCase();
    }

    getStatusSeverity(status: UserStatus): string {
        switch (status) {
            case UserStatus.ACTIVE:
                return 'success';
            case UserStatus.INACTIVE:
                return 'secondary';
            case UserStatus.SUSPENDED:
                return 'warning';
            case UserStatus.PENDING:
                return 'info';
            case UserStatus.TERMINATED:
                return 'danger';
            default:
                return 'info';
        }
    }
}

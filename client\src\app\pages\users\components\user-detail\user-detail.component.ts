import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { TabViewModule } from 'primeng/tabview';
import { TagModule } from 'primeng/tag';
import { CardModule } from 'primeng/card';
import { AvatarModule } from 'primeng/avatar';
import { User, UserStatus } from '../../models/user.model';
import { UserService } from '../../services/user.service';

@Component({
    selector: 'app-user-detail',
    standalone: true,
    imports: [
        CommonModule,
        ButtonModule,
        TabViewModule,
        TagModule,
        CardModule,
        AvatarModule
    ],
    template: `
        <div class="grid">
            <div class="col-12">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-4">
                        <div>
                            <h5 class="m-0"><PERSON><PERSON><PERSON><PERSON><PERSON> Detayı</h5>
                            <p class="text-500 mt-1">{{ user?.userCode }}</p>
                        </div>
                        <div class="flex gap-2">
                            <p-button 
                                label="Düzenle" 
                                icon="pi pi-pencil" 
                                class="p-button-warning"
                                (onClick)="editUser()">
                            </p-button>
                            <p-button 
                                label="Geri" 
                                icon="pi pi-arrow-left" 
                                class="p-button-secondary"
                                (onClick)="goBack()">
                            </p-button>
                        </div>
                    </div>

                    <p-tabView *ngIf="user">
                        <p-tabPanel header="Kişisel Bilgiler" leftIcon="pi pi-user">
                            <div class="grid">
                                <div class="col-12 md:col-4">
                                    <p-card header="Profil">
                                        <div class="text-center mb-4">
                                            <p-avatar 
                                                [label]="getInitials(user.fullName)"
                                                size="xlarge"
                                                shape="circle"
                                                class="mb-3">
                                            </p-avatar>
                                            <div class="font-bold text-xl">{{ user.fullName }}</div>
                                            <div class="text-500 mb-2">{{ user.position }}</div>
                                            <p-tag 
                                                [value]="user.status" 
                                                [severity]="getStatusSeverity(user.status)">
                                            </p-tag>
                                        </div>
                                    </p-card>
                                </div>
                                <div class="col-12 md:col-8">
                                    <p-card header="İletişim Bilgileri">
                                        <div class="grid">
                                            <div class="col-12 md:col-6">
                                                <div class="field">
                                                    <label class="font-medium">E-posta:</label>
                                                    <div class="mt-1">{{ user.email }}</div>
                                                </div>
                                                <div class="field" *ngIf="user.phone">
                                                    <label class="font-medium">Telefon:</label>
                                                    <div class="mt-1">{{ user.phone }}</div>
                                                </div>
                                                <div class="field">
                                                    <label class="font-medium">Departman:</label>
                                                    <div class="mt-1">
                                                        <p-tag [value]="user.department" severity="info"></p-tag>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-12 md:col-6">
                                                <div class="field" *ngIf="user.employeeId">
                                                    <label class="font-medium">Personel No:</label>
                                                    <div class="mt-1">{{ user.employeeId }}</div>
                                                </div>
                                                <div class="field" *ngIf="user.hireDate">
                                                    <label class="font-medium">İşe Başlama:</label>
                                                    <div class="mt-1">{{ user.hireDate | date:'dd/MM/yyyy' }}</div>
                                                </div>
                                                <div class="field" *ngIf="user.lastLoginDate">
                                                    <label class="font-medium">Son Giriş:</label>
                                                    <div class="mt-1">{{ user.lastLoginDate | date:'dd/MM/yyyy HH:mm' }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                            </div>
                        </p-tabPanel>

                        <p-tabPanel header="Roller ve Yetkiler" leftIcon="pi pi-shield">
                            <div class="grid">
                                <div class="col-12 md:col-6">
                                    <p-card header="Roller">
                                        <div *ngIf="user.roles.length === 0" class="text-500">
                                            Henüz rol atanmamış
                                        </div>
                                        <div *ngFor="let role of user.roles" class="mb-3">
                                            <div class="flex align-items-center justify-content-between">
                                                <div>
                                                    <div class="font-medium">{{ role.name }}</div>
                                                    <div class="text-sm text-500">{{ role.description }}</div>
                                                </div>
                                                <p-tag 
                                                    [value]="role.isActive ? 'Aktif' : 'Pasif'" 
                                                    [severity]="role.isActive ? 'success' : 'secondary'">
                                                </p-tag>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                                <div class="col-12 md:col-6">
                                    <p-card header="Yetkiler">
                                        <div *ngIf="user.permissions.length === 0" class="text-500">
                                            Henüz yetki atanmamış
                                        </div>
                                        <div *ngFor="let permission of user.permissions" class="mb-2">
                                            <div class="flex align-items-center gap-2">
                                                <i class="pi pi-check text-green-500"></i>
                                                <div>
                                                    <span class="font-medium">{{ permission.name }}</span>
                                                    <div class="text-sm text-500">{{ permission.module }} - {{ permission.action }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                            </div>
                        </p-tabPanel>

                        <p-tabPanel header="Yetenekler" leftIcon="pi pi-star">
                            <div class="grid">
                                <div class="col-12 md:col-6">
                                    <p-card header="Teknik Yetenekler">
                                        <div *ngIf="user.skills.length === 0" class="text-500">
                                            Henüz yetenek tanımlanmamış
                                        </div>
                                        <div *ngFor="let skill of user.skills" class="mb-3">
                                            <div class="flex align-items-center justify-content-between">
                                                <div>
                                                    <div class="font-medium">{{ skill.name }}</div>
                                                    <div class="text-sm text-500">{{ skill.category }}</div>
                                                </div>
                                                <div class="text-right">
                                                    <p-tag [value]="skill.level" severity="info"></p-tag>
                                                    <div class="text-sm text-500 mt-1" *ngIf="skill.yearsOfExperience">
                                                        {{ skill.yearsOfExperience }} yıl deneyim
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                                <div class="col-12 md:col-6">
                                    <p-card header="Diller">
                                        <div *ngIf="user.languages.length === 0" class="text-500">
                                            Henüz dil tanımlanmamış
                                        </div>
                                        <div *ngFor="let language of user.languages" class="mb-3">
                                            <div class="flex align-items-center justify-content-between">
                                                <div>
                                                    <div class="font-medium">{{ language.name }}</div>
                                                    <div class="text-sm text-500" *ngIf="language.isNative">Ana Dil</div>
                                                </div>
                                                <p-tag [value]="language.level" severity="secondary"></p-tag>
                                            </div>
                                        </div>
                                    </p-card>
                                </div>
                            </div>
                        </p-tabPanel>

                        <p-tabPanel header="Notlar" leftIcon="pi pi-file-edit">
                            <p-card>
                                <div *ngIf="!user.notes" class="text-500">
                                    Henüz not eklenmemiş
                                </div>
                                <div *ngIf="user.notes" class="white-space-pre-line">
                                    {{ user.notes }}
                                </div>
                            </p-card>
                        </p-tabPanel>
                    </p-tabView>
                </div>
            </div>
        </div>
    `
})
export class UserDetailComponent implements OnInit {
    user: User | null = null;
    userId: string = '';

    constructor(
        private route: ActivatedRoute,
        private router: Router,
        private userService: UserService
    ) {}

    ngOnInit() {
        this.userId = this.route.snapshot.params['id'];
        this.loadUser();
    }

    loadUser() {
        this.userService.getUserById(this.userId).subscribe(user => {
            this.user = user || null;
        });
    }

    editUser() {
        this.router.navigate(['/pages/users', this.userId, 'edit']);
    }

    goBack() {
        this.router.navigate(['/pages/users']);
    }

    getInitials(fullName: string): string {
        return fullName.split(' ').map(name => name.charAt(0)).join('').toUpperCase();
    }

    getStatusSeverity(status: UserStatus): string {
        switch (status) {
            case UserStatus.ACTIVE:
                return 'success';
            case UserStatus.INACTIVE:
                return 'secondary';
            case UserStatus.SUSPENDED:
                return 'warning';
            case UserStatus.PENDING:
                return 'info';
            case UserStatus.TERMINATED:
                return 'danger';
            default:
                return 'info';
        }
    }
}

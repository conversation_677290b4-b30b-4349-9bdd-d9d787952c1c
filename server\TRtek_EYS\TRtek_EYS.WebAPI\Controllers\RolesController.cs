﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using TRtek_EYS.Application.Features.Roles.Commands.Add;
using TRtek_EYS.Application.Features.Roles.Commands.Delete;
using TRtek_EYS.Application.Features.Roles.Commands.Update;
using TRtek_EYS.Application.Features.Roles.Queries.GetAll;
using TRtek_EYS.Application.Features.Roles.Queries.GetById;

namespace TRtek_EYS.WebAPI.Controllers;

[Route("api/[controller]")]
[ApiController]
public class RolesController : ApiController
{
    public RolesController(IMediator mediator) : base(mediator) { }

    [HttpGet]
    public async Task<IActionResult> GetAll()
    {
        var result = await Mediator.Send(new GetAllRolesQuery());
        return Ok(result); 
    }

    [HttpGet("{id:guid}")]
    public async Task<IActionResult> GetById(Guid id)
    {
        var result = await Mediator.Send(new GetRoleByIdQuery(id));
        return result is null ? NotFound() : Ok(result); 
    }

    [HttpPost]
    public async Task<IActionResult> Add([FromBody] AddRoleCommand command)
    {
        var id = await Mediator.Send(command);
        return CreatedAtAction(nameof(GetById), new { id }, null);
    }

    [HttpPut("{id:guid}")]
    public async Task<IActionResult> Update(Guid id, [FromBody] UpdateRoleCommand command)
    {
        if (id != command.Id)
            return BadRequest("ID uyuşmazlığı.");

        await Mediator.Send(command);
        return NoContent();
    }

    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> Delete(Guid id)
    {
        await Mediator.Send(new DeleteRoleCommand { Id = id });
        return NoContent();
    }
}

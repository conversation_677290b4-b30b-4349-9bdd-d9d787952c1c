import { Routes } from '@angular/router';
import { AppLayout } from './component/app.layout';
import { permissionGuard } from '../core/guards/permission.guard';

export const routes: Routes = [
    {
        path: '',
        component: AppLayout,
        children: [
            {
                path: '',
                loadComponent: () => import('../pages/dashboard/dashboard.component').then(m => m.DashboardComponent)
            },
            {
                path: '',
                loadChildren: () => import('../pages/pages.routes').then(m => m.default)
            }
        ]
    }
];

export interface Configuration {
    id: string;
    configurationCode: string;
    customerId: string;
    customerName: string;
    projectId: string;
    projectName: string;
    projectCode: string;
    configurationName: string;
    description: string;
    environment: Environment;
    status: ConfigurationStatus;
    version: string;
    settings: ConfigurationSetting[];
    deploymentInfo: DeploymentInfo;
    createdBy: string;
    createdDate: Date;
    lastModifiedBy?: string;
    lastModifiedDate?: Date;
    isActive: boolean;
    notes?: string;
}

export interface ConfigurationSetting {
    id: string;
    category: SettingCategory;
    key: string;
    value: string;
    dataType: DataType;
    isEncrypted: boolean;
    description?: string;
    defaultValue?: string;
    isRequired: boolean;
    validationRule?: string;
}

export interface DeploymentInfo {
    serverUrl?: string;
    databaseConnection?: string;
    apiEndpoints: ApiEndpoint[];
    environmentVariables: EnvironmentVariable[];
    lastDeploymentDate?: Date;
    deploymentStatus: DeploymentStatus;
    deployedBy?: string;
}

export interface ApiEndpoint {
    id: string;
    name: string;
    url: string;
    method: HttpMethod;
    isActive: boolean;
    description?: string;
}

export interface EnvironmentVariable {
    id: string;
    key: string;
    value: string;
    isSecret: boolean;
    description?: string;
}

export enum Environment {
    DEVELOPMENT = 'Geliştirme',
    TESTING = 'Test',
    STAGING = 'Hazırlık',
    PRODUCTION = 'Üretim',
    DEMO = 'Demo'
}

export enum ConfigurationStatus {
    DRAFT = 'Taslak',
    ACTIVE = 'Aktif',
    INACTIVE = 'Pasif',
    PENDING = 'Beklemede',
    DEPRECATED = 'Kullanımdan Kaldırıldı',
    ARCHIVED = 'Arşivlendi'
}

export enum SettingCategory {
    DATABASE = 'Veritabanı',
    API = 'API',
    SECURITY = 'Güvenlik',
    UI = 'Kullanıcı Arayüzü',
    INTEGRATION = 'Entegrasyon',
    PERFORMANCE = 'Performans',
    LOGGING = 'Loglama',
    EMAIL = 'E-posta',
    PAYMENT = 'Ödeme',
    OTHER = 'Diğer'
}

export enum DataType {
    STRING = 'Metin',
    NUMBER = 'Sayı',
    BOOLEAN = 'Boolean',
    JSON = 'JSON',
    URL = 'URL',
    EMAIL = 'E-posta',
    PASSWORD = 'Şifre'
}

export enum DeploymentStatus {
    NOT_DEPLOYED = 'Dağıtılmadı',
    DEPLOYING = 'Dağıtılıyor',
    DEPLOYED = 'Dağıtıldı',
    FAILED = 'Başarısız',
    ROLLBACK = 'Geri Alındı'
}

export enum HttpMethod {
    GET = 'GET',
    POST = 'POST',
    PUT = 'PUT',
    DELETE = 'DELETE',
    PATCH = 'PATCH'
}
